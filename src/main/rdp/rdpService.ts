/**
 * RDP连接服务
 * 提供Windows远程桌面连接、会话管理和交互功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { spawn, ChildProcess } from 'child_process'
import { EventEmitter } from 'events'
import { writeFileSync, unlinkSync, existsSync } from 'fs'
import { join } from 'path'
import { tmpdir } from 'os'
import {
  RdpConnection,
  RdpConnectionConfig,
  RdpConnectionInfo,
  RdpConnectionStatus,
  RdpSessionInfo,
  RdpMouseEvent,
  RdpKeyboardEvent,
  RdpDisplaySettings,
  RdpStatistics
} from './types'

/**
 * RDP连接服务类
 * 继承自RdpConnection，实现RDP特定的连接逻辑
 */
export class RdpService extends RdpConnection {
  private rdpProcess?: ChildProcess
  private isConnecting: boolean = false
  private reconnectTimer?: NodeJS.Timeout
  private configFilePath?: string
  private statistics: RdpStatistics
  private lastError?: string

  /**
   * 构造函数
   * @param id 连接ID
   * @param config 连接配置
   */
  constructor(id: string, config: RdpConnectionConfig) {
    super(id, config)
    this.validateConfig()
    this.statistics = {
      bytesSent: 0,
      bytesReceived: 0,
      packetsSent: 0,
      packetsReceived: 0,
      averageLatency: 0,
      packetLoss: 0,
      bandwidthUsage: 0
    }
  }

  /**
   * 建立RDP连接
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.status === RdpConnectionStatus.CONNECTED) {
      return
    }

    this.isConnecting = true
    this.status = RdpConnectionStatus.CONNECTING
    this.emit('statusChange', this.status)

    try {
      // 创建RDP配置文件
      this.configFilePath = await this.createRdpConfigFile()

      // 启动RDP客户端进程
      await this.startRdpClient()

      // 等待连接建立
      await this.waitForConnection()

      this.status = RdpConnectionStatus.CONNECTED
      this.startedAt = new Date()
      this.lastActivityAt = new Date()
      this.isConnecting = false

      // 创建会话信息
      this.sessionInfo = {
        sessionId: `rdp-${this.id}-${Date.now()}`,
        clientVersion: 'Chaterm RDP Client 1.0',
        actualDisplaySettings: this.config.displaySettings,
        supportedFeatures: ['clipboard', 'audio', 'drives'],
        startTime: new Date(),
        lastActivity: new Date(),
        statistics: this.statistics
      }

      this.emit('statusChange', this.status)
      this.emit('connect', this.sessionInfo)
    } catch (error) {
      this.isConnecting = false
      this.status = RdpConnectionStatus.ERROR
      this.lastError = error instanceof Error ? error.message : '连接失败'
      this.emit('statusChange', this.status)
      this.emit('error', error instanceof Error ? error : new Error('RDP连接失败'))

      // 清理资源
      await this.cleanup()

      // 如果启用自动重连，则尝试重连
      if (this.config.autoReconnect) {
        this.scheduleReconnect()
      }

      throw error
    }
  }

  /**
   * 断开RDP连接
   */
  public async disconnect(): Promise<void> {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = undefined
    }

    // 终止RDP进程
    if (this.rdpProcess) {
      this.rdpProcess.kill('SIGTERM')

      // 如果进程没有正常退出，强制杀死
      setTimeout(() => {
        if (this.rdpProcess && !this.rdpProcess.killed) {
          this.rdpProcess.kill('SIGKILL')
        }
      }, 5000)
    }

    await this.cleanup()

    this.status = RdpConnectionStatus.DISCONNECTED
    this.sessionInfo = undefined
    this.emit('statusChange', this.status)
    this.emit('disconnect', '主动断开')
  }

  /**
   * 发送鼠标事件
   * @param event 鼠标事件
   */
  public async sendMouseEvent(event: RdpMouseEvent): Promise<void> {
    if (!this.isAlive()) {
      throw new Error('RDP连接未建立')
    }

    // 这里需要根据具体的RDP客户端实现来发送鼠标事件
    // 由于node-rdp库的限制，这里提供一个基础实现框架
    this.lastActivityAt = new Date()
    this.emit('mouseEvent', event)
  }

  /**
   * 发送键盘事件
   * @param event 键盘事件
   */
  public async sendKeyboardEvent(event: RdpKeyboardEvent): Promise<void> {
    if (!this.isAlive()) {
      throw new Error('RDP连接未建立')
    }

    // 这里需要根据具体的RDP客户端实现来发送键盘事件
    this.lastActivityAt = new Date()
    this.emit('keyboardEvent', event)
  }

  /**
   * 发送剪贴板数据
   * @param data 剪贴板数据
   */
  public async sendClipboardData(data: string): Promise<void> {
    if (!this.isAlive()) {
      throw new Error('RDP连接未建立')
    }

    // 这里需要根据具体的RDP客户端实现来发送剪贴板数据
    this.lastActivityAt = new Date()
    this.emit('clipboardEvent', data)
  }

  /**
   * 获取屏幕截图
   * @param region 截图区域（可选）
   */
  public async getScreenshot(region?: { x: number; y: number; width: number; height: number }): Promise<Buffer> {
    if (!this.isAlive()) {
      throw new Error('RDP连接未建立')
    }

    // 这里需要根据具体的RDP客户端实现来获取屏幕截图
    // 返回一个空的Buffer作为占位符
    return Buffer.alloc(0)
  }

  /**
   * 获取连接信息
   */
  public getConnectionInfo(): RdpConnectionInfo {
    return {
      id: this.id,
      assetId: this.config.assetId,
      name: this.config.name,
      status: this.status,
      config: this.config,
      sessionInfo: this.sessionInfo,
      startedAt: this.startedAt || new Date(),
      lastActivityAt: this.lastActivityAt || new Date(),
      reconnectCount: this.reconnectCount,
      lastError: this.lastError
    }
  }

  /**
   * 检查连接是否活跃
   */
  public isAlive(): boolean {
    return this.rdpProcess !== undefined && !this.rdpProcess.killed && this.status === RdpConnectionStatus.CONNECTED
  }

  /**
   * 重新调整显示设置
   * @param settings 新的显示设置
   */
  public async resizeDisplay(settings: Partial<RdpDisplaySettings>): Promise<void> {
    if (!this.isAlive()) {
      throw new Error('RDP连接未建立')
    }

    // 更新配置
    Object.assign(this.config.displaySettings, settings)

    // 这里需要根据具体的RDP客户端实现来调整显示设置
    // 可能需要重新连接才能生效
    this.lastActivityAt = new Date()
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    if (!this.config.host) {
      throw new Error('RDP主机地址不能为空')
    }

    if (!this.config.port || this.config.port <= 0 || this.config.port > 65535) {
      throw new Error('RDP端口必须在1-65535之间')
    }

    if (!this.config.username) {
      throw new Error('RDP用户名不能为空')
    }

    if (!this.config.displaySettings) {
      throw new Error('显示设置不能为空')
    }

    if (this.config.displaySettings.width <= 0 || this.config.displaySettings.height <= 0) {
      throw new Error('显示分辨率必须大于0')
    }
  }

  /**
   * 创建RDP配置文件
   */
  private async createRdpConfigFile(): Promise<string> {
    const configPath = join(tmpdir(), `rdp-${this.id}-${Date.now()}.rdp`)

    const rdpConfig = this.generateRdpConfig()

    try {
      writeFileSync(configPath, rdpConfig, 'utf8')
      return configPath
    } catch (error) {
      throw new Error(`创建RDP配置文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 生成RDP配置内容
   */
  private generateRdpConfig(): string {
    const config = this.config
    const display = config.displaySettings
    const security = config.securitySettings
    const performance = config.performanceSettings
    const redirection = config.redirectionSettings

    let rdpConfig = ''

    // 基本连接设置
    rdpConfig += `full address:s:${config.host}:${config.port}\n`
    rdpConfig += `username:s:${config.username}\n`
    if (config.domain) {
      rdpConfig += `domain:s:${config.domain}\n`
    }

    // 显示设置
    rdpConfig += `screen mode id:i:${display.fullscreen ? 2 : 1}\n`
    rdpConfig += `desktopwidth:i:${display.width}\n`
    rdpConfig += `desktopheight:i:${display.height}\n`
    rdpConfig += `session bpp:i:${display.colorDepth}\n`

    // 安全设置
    rdpConfig += `authentication level:i:${security.networkLevelAuth ? 2 : 0}\n`
    rdpConfig += `enablecredsspsupport:i:${security.credSSP ? 1 : 0}\n`
    rdpConfig += `negotiate security layer:i:1\n`

    // 性能设置
    if (performance) {
      rdpConfig += `connection type:i:${this.getConnectionTypeId(performance.connectionType)}\n`
      rdpConfig += `bitmapcachepersistenable:i:${performance.persistentBitmapCache ? 1 : 0}\n`
      rdpConfig += `compression:i:${performance.compression ? 1 : 0}\n`
      rdpConfig += `keyboardhook:i:2\n`
    }

    // 重定向设置
    if (redirection) {
      rdpConfig += `redirectclipboard:i:${redirection.clipboard ? 1 : 0}\n`
      rdpConfig += `redirectprinters:i:${redirection.printers ? 1 : 0}\n`
      rdpConfig += `redirectcomports:i:${redirection.serialPorts ? 1 : 0}\n`
      rdpConfig += `redirectsmartcards:i:${redirection.smartCards ? 1 : 0}\n`
      rdpConfig += `redirectdrives:i:${redirection.drives ? 1 : 0}\n`

      if (redirection.audio) {
        const audioMode = redirection.audio === 'local' ? 0 : redirection.audio === 'remote' ? 1 : 2
        rdpConfig += `audiomode:i:${audioMode}\n`
      }
    }

    // 其他设置
    rdpConfig += `prompt for credentials:i:0\n`
    rdpConfig += `administrative session:i:0\n`
    rdpConfig += `autoreconnection enabled:i:${config.autoReconnect ? 1 : 0}\n`

    return rdpConfig
  }

  /**
   * 获取连接类型ID
   */
  private getConnectionTypeId(connectionType: string): number {
    const typeMap: { [key: string]: number } = {
      modem: 1,
      'broadband-low': 2,
      satellite: 3,
      'broadband-high': 4,
      wan: 5,
      lan: 6
    }
    return typeMap[connectionType] || 6
  }

  /**
   * 启动RDP客户端进程
   */
  private async startRdpClient(): Promise<void> {
    if (!this.configFilePath) {
      throw new Error('RDP配置文件未创建')
    }

    return new Promise((resolve, reject) => {
      // 根据操作系统选择RDP客户端
      const platform = process.platform
      let command: string
      let args: string[]

      switch (platform) {
        case 'win32':
          command = 'mstsc'
          args = [this.configFilePath]
          break
        case 'darwin':
          // macOS 可以使用 Microsoft Remote Desktop 或 FreeRDP
          command = 'open'
          args = ['-a', 'Microsoft Remote Desktop', this.configFilePath]
          break
        case 'linux':
          // Linux 使用 FreeRDP
          command = 'xfreerdp'
          args = this.buildFreeRdpArgs()
          break
        default:
          reject(new Error(`不支持的操作系统: ${platform}`))
          return
      }

      try {
        this.rdpProcess = spawn(command, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          detached: false
        })

        this.setupProcessEvents(resolve, reject)
      } catch (error) {
        reject(new Error(`启动RDP客户端失败: ${error instanceof Error ? error.message : '未知错误'}`))
      }
    })
  }

  /**
   * 构建FreeRDP参数
   */
  private buildFreeRdpArgs(): string[] {
    const config = this.config
    const display = config.displaySettings

    const args = [
      `/v:${config.host}:${config.port}`,
      `/u:${config.username}`,
      `/w:${display.width}`,
      `/h:${display.height}`,
      `/bpp:${display.colorDepth}`
    ]

    if (config.password) {
      args.push(`/p:${config.password}`)
    }

    if (config.domain) {
      args.push(`/d:${config.domain}`)
    }

    // 添加其他FreeRDP特定参数
    args.push('/cert-ignore')
    args.push('/compression')
    args.push('/clipboard')

    if (display.fullscreen) {
      args.push('/f')
    }

    return args
  }

  /**
   * 设置进程事件监听
   */
  private setupProcessEvents(resolve: () => void, reject: (error: Error) => void): void {
    if (!this.rdpProcess) {
      reject(new Error('RDP进程未创建'))
      return
    }

    let resolved = false

    // 进程启动成功
    this.rdpProcess.on('spawn', () => {
      if (!resolved) {
        resolved = true
        resolve()
      }
    })

    // 进程错误
    this.rdpProcess.on('error', (error) => {
      if (!resolved) {
        resolved = true
        reject(new Error(`RDP进程错误: ${error.message}`))
      } else {
        this.emit('error', new Error(`RDP进程错误: ${error.message}`))
      }
    })

    // 进程退出
    this.rdpProcess.on('exit', (code, signal) => {
      const reason = signal ? `信号: ${signal}` : `退出码: ${code}`

      if (!resolved) {
        resolved = true
        reject(new Error(`RDP进程异常退出: ${reason}`))
      } else {
        this.status = RdpConnectionStatus.DISCONNECTED
        this.emit('statusChange', this.status)
        this.emit('disconnect', reason)

        if (this.config.autoReconnect && this.reconnectCount < (this.config.maxReconnectAttempts || 5)) {
          this.scheduleReconnect()
        }
      }
    })

    // 标准输出
    if (this.rdpProcess.stdout) {
      this.rdpProcess.stdout.on('data', (data) => {
        // 处理RDP客户端输出
        console.log(`[RDP-${this.id}] stdout:`, data.toString())
      })
    }

    // 标准错误
    if (this.rdpProcess.stderr) {
      this.rdpProcess.stderr.on('data', (data) => {
        // 处理RDP客户端错误输出
        console.error(`[RDP-${this.id}] stderr:`, data.toString())
      })
    }
  }

  /**
   * 等待连接建立
   */
  private async waitForConnection(): Promise<void> {
    const timeout = this.config.timeout || 30000

    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`RDP连接超时: ${timeout}ms`))
      }, timeout)

      // 简单的连接检测，实际实现可能需要更复杂的逻辑
      const checkConnection = () => {
        if (this.rdpProcess && !this.rdpProcess.killed) {
          clearTimeout(timer)
          resolve()
        } else {
          setTimeout(checkConnection, 1000)
        }
      }

      setTimeout(checkConnection, 2000) // 给进程一些启动时间
    })
  }

  /**
   * 清理资源
   */
  private async cleanup(): Promise<void> {
    // 删除临时配置文件
    if (this.configFilePath && existsSync(this.configFilePath)) {
      try {
        unlinkSync(this.configFilePath)
      } catch (error) {
        console.warn(`删除RDP配置文件失败: ${error instanceof Error ? error.message : '未知错误'}`)
      }
      this.configFilePath = undefined
    }

    this.rdpProcess = undefined
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return
    }

    const interval = this.config.reconnectInterval || 5000
    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = undefined
      this.reconnectCount++

      this.emit('reconnect', this.reconnectCount)

      try {
        await this.connect()
        this.reconnectCount = 0 // 重连成功后重置计数
      } catch (error) {
        // 重连失败，如果还有重连次数则继续尝试
        if (this.reconnectCount < (this.config.maxReconnectAttempts || 5)) {
          this.scheduleReconnect()
        } else {
          this.emit('error', new Error('重连次数已达上限'))
        }
      }
    }, interval)
  }
}
