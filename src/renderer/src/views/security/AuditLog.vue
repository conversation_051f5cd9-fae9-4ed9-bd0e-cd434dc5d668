<template>
  <div class="audit-log">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <FileText class="title-icon" />
          审计日志
        </h1>
        <p class="page-description">查看系统操作记录、安全事件和用户行为审计</p>
      </div>
      <div class="header-actions">
        <a-button
          :loading="refreshing"
          @click="refreshLogs"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </a-button>
        <a-button
          type="primary"
          @click="exportLogs"
        >
          <Download class="btn-icon" />
          导出日志
        </a-button>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="audit-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日操作"
              :value="todayStats.total"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Activity class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="安全事件"
              :value="todayStats.security"
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <Shield class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="登录次数"
              :value="todayStats.logins"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <LogIn class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="失败操作"
              :value="todayStats.failures"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <AlertTriangle class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card>
        <a-form
          layout="inline"
          :model="filterForm"
        >
          <a-form-item label="时间范围">
            <a-range-picker
              v-model:value="filterForm.dateRange"
              :placeholder="['开始时间', '结束时间']"
              format="YYYY-MM-DD HH:mm:ss"
              show-time
              @change="handleDateRangeChange"
            />
          </a-form-item>
          <a-form-item label="操作类型">
            <a-select
              v-model:value="filterForm.actionType"
              placeholder="选择操作类型"
              style="width: 150px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部类型</a-select-option>
              <a-select-option value="login">登录</a-select-option>
              <a-select-option value="logout">登出</a-select-option>
              <a-select-option value="create">创建</a-select-option>
              <a-select-option value="update">更新</a-select-option>
              <a-select-option value="delete">删除</a-select-option>
              <a-select-option value="sync">同步</a-select-option>
              <a-select-option value="config">配置</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="用户">
            <a-select
              v-model:value="filterForm.userId"
              placeholder="选择用户"
              style="width: 150px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部用户</a-select-option>
              <a-select-option
                v-for="user in users"
                :key="user.id"
                :value="user.id"
              >
                {{ user.username }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="结果">
            <a-select
              v-model:value="filterForm.result"
              placeholder="操作结果"
              style="width: 120px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部结果</a-select-option>
              <a-select-option value="success">成功</a-select-option>
              <a-select-option value="failure">失败</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-input-search
              v-model:value="filterForm.keyword"
              placeholder="搜索关键词"
              style="width: 200px"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-button @click="resetFilters">
              <RotateCcw class="btn-icon" />
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 日志列表 -->
    <div class="logs-section">
      <a-card>
        <template #title>
          <div class="logs-header">
            <span>审计日志</span>
            <div class="logs-info">
              <span class="log-count">共 {{ filteredLogs.length }} 条记录</span>
              <a-switch
                v-model:checked="autoRefresh"
                size="small"
              />
              <span class="auto-refresh-label">自动刷新</span>
            </div>
          </div>
        </template>

        <a-table
          :columns="logColumns"
          :data-source="paginatedLogs"
          :pagination="tablePagination"
          :loading="loading"
          row-key="id"
          size="small"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'timestamp'">
              <div class="timestamp-cell">
                <div class="timestamp-date">{{ formatDate(record.timestamp) }}</div>
                <div class="timestamp-time">{{ formatTime(record.timestamp) }}</div>
              </div>
            </template>
            <template v-else-if="column.key === 'user'">
              <div class="user-cell">
                <a-avatar size="small">
                  {{ record.username?.charAt(0).toUpperCase() || 'S' }}
                </a-avatar>
                <span class="username">{{ record.username || 'System' }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'action'">
              <div class="action-cell">
                <div class="action-icon">
                  <LogIn v-if="record.action_type === 'login'" />
                  <LogOut v-else-if="record.action_type === 'logout'" />
                  <Plus v-else-if="record.action_type === 'create'" />
                  <Edit v-else-if="record.action_type === 'update'" />
                  <Trash2 v-else-if="record.action_type === 'delete'" />
                  <RefreshCw v-else-if="record.action_type === 'sync'" />
                  <Settings v-else-if="record.action_type === 'config'" />
                  <Activity v-else />
                </div>
                <div class="action-info">
                  <div class="action-type">{{ getActionTypeLabel(record.action_type) }}</div>
                  <div class="action-description">{{ record.description }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'resource'">
              <div class="resource-cell">
                <a-tag color="blue">{{ record.resource_type }}</a-tag>
                <span class="resource-name">{{ record.resource_name || '-' }}</span>
              </div>
            </template>
            <template v-else-if="column.key === 'result'">
              <a-badge
                :status="getResultStatus(record.result)"
                :text="getResultText(record.result)"
              />
            </template>
            <template v-else-if="column.key === 'ip_address'">
              <span class="ip-address">{{ record.ip_address || '-' }}</span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button
                  size="small"
                  @click="viewLogDetails(record)"
                >
                  <Eye class="action-icon" />
                </a-button>
                <a-button
                  size="small"
                  @click="exportSingleLog(record)"
                >
                  <Download class="action-icon" />
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 日志详情模态框 -->
    <a-modal
      v-model:open="showLogDetailsModal"
      title="日志详情"
      width="800px"
      :footer="null"
    >
      <div
        v-if="selectedLog"
        class="log-details"
      >
        <a-descriptions
          :column="2"
          bordered
          size="small"
        >
          <a-descriptions-item label="时间戳">
            {{ formatFullTime(selectedLog.timestamp) }}
          </a-descriptions-item>
          <a-descriptions-item label="用户">
            {{ selectedLog.username || 'System' }}
          </a-descriptions-item>
          <a-descriptions-item label="操作类型">
            {{ getActionTypeLabel(selectedLog.action_type) }}
          </a-descriptions-item>
          <a-descriptions-item label="结果">
            <a-badge
              :status="getResultStatus(selectedLog.result)"
              :text="getResultText(selectedLog.result)"
            />
          </a-descriptions-item>
          <a-descriptions-item label="资源类型">
            {{ selectedLog.resource_type || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="资源名称">
            {{ selectedLog.resource_name || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="IP地址">
            {{ selectedLog.ip_address || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="用户代理">
            {{ selectedLog.user_agent || '-' }}
          </a-descriptions-item>
          <a-descriptions-item
            label="描述"
            :span="2"
          >
            {{ selectedLog.description }}
          </a-descriptions-item>
        </a-descriptions>

        <div
          v-if="selectedLog.details"
          class="log-extra-details"
        >
          <h4>详细信息</h4>
          <pre class="details-json">{{ JSON.stringify(selectedLog.details, null, 2) }}</pre>
        </div>

        <div
          v-if="selectedLog.error_message"
          class="log-error"
        >
          <h4>错误信息</h4>
          <a-alert
            :message="selectedLog.error_message"
            type="error"
            show-icon
          />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { message, notification } from 'ant-design-vue'
import {
  FileText,
  RefreshCw,
  Download,
  Activity,
  Shield,
  LogIn,
  LogOut,
  AlertTriangle,
  RotateCcw,
  Plus,
  Edit,
  Trash2,
  Settings,
  Eye
} from 'lucide-vue-next'
import { format, formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 审计日志页面
 * 功能：查看系统操作记录、安全事件和用户行为审计
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 响应式数据
const refreshing = ref(false)
const loading = ref(false)
const autoRefresh = ref(false)
const showLogDetailsModal = ref(false)
const selectedLog = ref(null)

// 统计数据
const todayStats = reactive({
  total: 1247,
  security: 8,
  logins: 156,
  failures: 23
})

// 用户列表
const users = ref([
  { id: '1', username: 'admin' },
  { id: '2', username: 'john.doe' },
  { id: '3', username: 'jane.smith' }
])

// 筛选表单
const filterForm = reactive({
  dateRange: null,
  actionType: '',
  userId: '',
  result: '',
  keyword: ''
})

// 审计日志数据
const auditLogs = ref([
  {
    id: '1',
    timestamp: new Date('2025-01-15T10:30:00Z'),
    user_id: '1',
    username: 'admin',
    action_type: 'login',
    description: '管理员登录系统',
    resource_type: 'system',
    resource_name: null,
    result: 'success',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    details: {
      session_id: 'sess_123456',
      login_method: 'password'
    },
    error_message: null
  },
  {
    id: '2',
    timestamp: new Date('2025-01-15T10:25:00Z'),
    user_id: '2',
    username: 'john.doe',
    action_type: 'create',
    description: '创建新的主机配置',
    resource_type: 'host',
    resource_name: 'web-server-02',
    result: 'success',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    details: {
      host_ip: '*********',
      host_port: 22,
      environment: 'production'
    },
    error_message: null
  },
  {
    id: '3',
    timestamp: new Date('2025-01-15T10:20:00Z'),
    user_id: '3',
    username: 'jane.smith',
    action_type: 'sync',
    description: '同步个人资源到云端',
    resource_type: 'sync',
    resource_name: 'personal-sync',
    result: 'failure',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    details: {
      sync_type: 'personal',
      backend: 'onedrive'
    },
    error_message: '网络连接超时，同步失败'
  },
  {
    id: '4',
    timestamp: new Date('2025-01-15T10:15:00Z'),
    user_id: null,
    username: null,
    action_type: 'config',
    description: '系统自动更新配置',
    resource_type: 'system',
    resource_name: 'sync-config',
    result: 'success',
    ip_address: '127.0.0.1',
    user_agent: 'System/1.0',
    details: {
      config_type: 'sync_interval',
      old_value: 300,
      new_value: 600
    },
    error_message: null
  },
  {
    id: '5',
    timestamp: new Date('2025-01-15T10:10:00Z'),
    user_id: '1',
    username: 'admin',
    action_type: 'delete',
    description: '删除过期的密钥链',
    resource_type: 'keychain',
    resource_name: 'old-ssh-key',
    result: 'success',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    details: {
      keychain_id: 'key_789',
      expiry_date: '2025-01-01'
    },
    error_message: null
  }
])

// 表格列配置
const logColumns = [
  {
    title: '时间',
    key: 'timestamp',
    width: 150,
    sorter: (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  },
  {
    title: '用户',
    key: 'user',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 250
  },
  {
    title: '资源',
    key: 'resource',
    width: 150
  },
  {
    title: '结果',
    key: 'result',
    width: 80
  },
  {
    title: 'IP地址',
    key: 'ip_address',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    fixed: 'right'
  }
]

// 表格分页配置
const tablePagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const filteredLogs = computed(() => {
  let filtered = auditLogs.value

  // 时间范围过滤
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [start, end] = filterForm.dateRange
    filtered = filtered.filter((log) => {
      const logTime = new Date(log.timestamp)
      return logTime >= start && logTime <= end
    })
  }

  // 操作类型过滤
  if (filterForm.actionType) {
    filtered = filtered.filter((log) => log.action_type === filterForm.actionType)
  }

  // 用户过滤
  if (filterForm.userId) {
    filtered = filtered.filter((log) => log.user_id === filterForm.userId)
  }

  // 结果过滤
  if (filterForm.result) {
    filtered = filtered.filter((log) => log.result === filterForm.result)
  }

  // 关键词搜索
  if (filterForm.keyword) {
    const keyword = filterForm.keyword.toLowerCase()
    filtered = filtered.filter(
      (log) =>
        log.description.toLowerCase().includes(keyword) ||
        log.resource_name?.toLowerCase().includes(keyword) ||
        log.username?.toLowerCase().includes(keyword)
    )
  }

  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

const paginatedLogs = computed(() => {
  const start = (tablePagination.current - 1) * tablePagination.pageSize
  const end = start + tablePagination.pageSize
  return filteredLogs.value.slice(start, end)
})

// 方法
const formatDate = (date: Date) => {
  return format(date, 'MM-dd', { locale: zhCN })
}

const formatTime = (date: Date) => {
  return format(date, 'HH:mm:ss', { locale: zhCN })
}

const formatFullTime = (date: Date) => {
  return format(date, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
}

const getActionTypeLabel = (actionType: string) => {
  const labels = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    sync: '同步',
    config: '配置'
  }
  return labels[actionType] || actionType
}

const getResultStatus = (result: string) => {
  return result === 'success' ? 'success' : 'error'
}

const getResultText = (result: string) => {
  return result === 'success' ? '成功' : '失败'
}

const refreshLogs = async () => {
  refreshing.value = true
  try {
    // 模拟刷新数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
    message.success('日志刷新成功')
  } finally {
    refreshing.value = false
  }
}

const exportLogs = () => {
  // 实现导出日志功能
  const data = filteredLogs.value.map((log) => ({
    时间: formatFullTime(log.timestamp),
    用户: log.username || 'System',
    操作类型: getActionTypeLabel(log.action_type),
    描述: log.description,
    资源类型: log.resource_type,
    资源名称: log.resource_name || '-',
    结果: getResultText(log.result),
    IP地址: log.ip_address || '-'
  }))

  // 这里应该实现实际的导出逻辑
  message.success(`导出 ${data.length} 条日志记录`)
}

const exportSingleLog = (log: any) => {
  // 导出单条日志
  message.success(`导出日志: ${log.description}`)
}

const handleDateRangeChange = () => {
  handleFilterChange()
}

const handleFilterChange = () => {
  tablePagination.current = 1
  tablePagination.total = filteredLogs.value.length
}

const handleSearch = () => {
  handleFilterChange()
}

const resetFilters = () => {
  Object.assign(filterForm, {
    dateRange: null,
    actionType: '',
    userId: '',
    result: '',
    keyword: ''
  })
  handleFilterChange()
}

const handleTableChange = (pagination: any) => {
  tablePagination.current = pagination.current
  tablePagination.pageSize = pagination.pageSize
}

const viewLogDetails = (log: any) => {
  selectedLog.value = log
  showLogDetailsModal.value = true
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }

  if (autoRefresh.value) {
    refreshTimer = setInterval(() => {
      // 模拟新日志
      const newLog = {
        id: Date.now().toString(),
        timestamp: new Date(),
        user_id: '1',
        username: 'admin',
        action_type: 'sync',
        description: '自动同步任务执行',
        resource_type: 'sync',
        resource_name: 'auto-sync',
        result: 'success',
        ip_address: '127.0.0.1',
        user_agent: 'System/1.0',
        details: { type: 'scheduled' },
        error_message: null
      }
      auditLogs.value.unshift(newLog)

      // 保持日志数量在合理范围内
      if (auditLogs.value.length > 1000) {
        auditLogs.value = auditLogs.value.slice(0, 1000)
      }
    }, 30000) // 30秒刷新一次
  }
}

// 监听自动刷新开关
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  tablePagination.total = filteredLogs.value.length

  // 监听自动刷新开关变化
  const unwatch = computed(() => autoRefresh.value)
  unwatch.effect = () => {
    if (autoRefresh.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.audit-log {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.audit-overview {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.filter-section {
  margin-bottom: 24px;
}

.logs-section {
  margin-bottom: 24px;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.logs-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.log-count {
  color: #6b7280;
  font-size: 14px;
}

.auto-refresh-label {
  color: #6b7280;
  font-size: 12px;
}

.timestamp-cell {
  text-align: center;
}

.timestamp-date {
  font-size: 12px;
  color: #1f2937;
  font-weight: 500;
}

.timestamp-time {
  font-size: 11px;
  color: #6b7280;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-size: 12px;
  color: #1f2937;
}

.action-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-icon {
  width: 16px;
  height: 16px;
  color: #1890ff;
}

.action-info {
  flex: 1;
}

.action-type {
  font-size: 12px;
  font-weight: 500;
  color: #1f2937;
}

.action-description {
  font-size: 11px;
  color: #6b7280;
}

.resource-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.resource-name {
  font-size: 11px;
  color: #6b7280;
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #1f2937;
}

.log-details {
  padding: 8px;
}

.log-extra-details {
  margin-top: 24px;
}

.log-extra-details h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
}

.details-json {
  background: #f5f5f5;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
  font-size: 12px;
  color: #1f2937;
  overflow-x: auto;
}

.log-error {
  margin-top: 24px;
}

.log-error h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 14px;
  font-weight: 600;
}
</style>
