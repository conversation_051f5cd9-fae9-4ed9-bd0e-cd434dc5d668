<template>
  <div
    class="automation-manager"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Bot class="title-icon" />
            自动化运维管理
          </h1>
          <p class="page-description">自动化脚本管理、任务调度和批量操作工具</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="createScript">
          <Plus class="btn-icon" />
          创建脚本
        </a-button>
        <a-button @click="createTask">
          <Calendar class="btn-icon" />
          创建任务
        </a-button>
        <a-button
          type="primary"
          @click="showBatchOperations"
        >
          <Layers class="btn-icon" />
          批量操作
        </a-button>
      </div>
    </div>

    <!-- 概览统计 -->
    <div class="overview-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <FileCode class="stat-icon" />
              脚本总数
            </div>
            <div class="stat-value">{{ automationStats.totalScripts }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">活跃:</span>
              <span class="detail-value active">{{ automationStats.activeScripts }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">停用:</span>
              <span class="detail-value inactive">{{ automationStats.inactiveScripts }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Clock class="stat-icon" />
              调度任务
            </div>
            <div class="stat-value">{{ automationStats.totalTasks }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">运行中:</span>
              <span class="detail-value running">{{ automationStats.runningTasks }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">等待中:</span>
              <span class="detail-value waiting">{{ automationStats.waitingTasks }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Activity class="stat-icon" />
              执行统计
            </div>
            <div class="stat-value">{{ automationStats.todayExecutions }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">成功:</span>
              <span class="detail-value success">{{ automationStats.successfulExecutions }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">失败:</span>
              <span class="detail-value failed">{{ automationStats.failedExecutions }}</span>
            </span>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">
              <Zap class="stat-icon" />
              API集成
            </div>
            <div class="stat-value">{{ automationStats.apiIntegrations }}</div>
          </div>
          <div class="stat-details">
            <span class="stat-detail">
              <span class="detail-label">在线:</span>
              <span class="detail-value online">{{ automationStats.onlineApis }}</span>
            </span>
            <span class="stat-detail">
              <span class="detail-label">离线:</span>
              <span class="detail-value offline">{{ automationStats.offlineApis }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-tabs
        v-model:active-key="activeTab"
        type="card"
      >
        <!-- 脚本管理 -->
        <a-tab-pane
          key="scripts"
          tab="脚本管理"
        >
          <div class="scripts-section">
            <div class="section-header">
              <h2 class="section-title">自动化脚本</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedScriptType"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部类型</a-select-option>
                  <a-select-option value="shell">Shell脚本</a-select-option>
                  <a-select-option value="python">Python脚本</a-select-option>
                  <a-select-option value="powershell">PowerShell</a-select-option>
                  <a-select-option value="ansible">Ansible</a-select-option>
                </a-select>
                <a-select
                  v-model:value="selectedScriptStatus"
                  style="width: 100px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="active">活跃</a-select-option>
                  <a-select-option value="inactive">停用</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="scripts-grid">
              <div
                v-for="script in filteredScripts"
                :key="script.id"
                class="script-card"
                :class="script.status"
              >
                <div class="script-header">
                  <div class="script-info">
                    <component
                      :is="getScriptIcon(script.type)"
                      class="script-icon"
                    />
                    <div class="script-details">
                      <h3 class="script-name">{{ script.name }}</h3>
                      <p class="script-description">{{ script.description }}</p>
                    </div>
                  </div>
                  <div
                    class="script-status"
                    :class="script.status"
                  >
                    <div class="status-dot"></div>
                    <span>{{ getScriptStatusLabel(script.status) }}</span>
                  </div>
                </div>

                <div class="script-metrics">
                  <div class="metric-item">
                    <span class="metric-label">执行次数</span>
                    <span class="metric-value">{{ script.executionCount }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value">{{ script.successRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">最后执行</span>
                    <span class="metric-value">{{ script.lastExecution }}</span>
                  </div>
                </div>

                <div class="script-actions">
                  <a-button
                    size="small"
                    @click="executeScript(script)"
                    :loading="script.executing"
                  >
                    <Play class="btn-icon" />
                    执行
                  </a-button>
                  <a-button
                    size="small"
                    @click="editScript(script)"
                  >
                    <Edit class="btn-icon" />
                    编辑
                  </a-button>
                  <a-button
                    size="small"
                    danger
                    @click="deleteScript(script)"
                  >
                    <Trash2 class="btn-icon" />
                    删除
                  </a-button>
                  <a-button
                    size="small"
                    @click="viewScriptLogs(script)"
                  >
                    <FileText class="btn-icon" />
                    日志
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 任务调度 -->
        <a-tab-pane
          key="tasks"
          tab="任务调度"
        >
          <div class="tasks-section">
            <div class="section-header">
              <h2 class="section-title">调度任务</h2>
              <div class="section-filters">
                <a-select
                  v-model:value="selectedTaskStatus"
                  style="width: 120px"
                >
                  <a-select-option value="all">全部状态</a-select-option>
                  <a-select-option value="running">运行中</a-select-option>
                  <a-select-option value="waiting">等待中</a-select-option>
                  <a-select-option value="completed">已完成</a-select-option>
                  <a-select-option value="failed">失败</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="tasks-table">
              <a-table
                :columns="taskColumns"
                :data-source="filteredTasks"
                :loading="loading"
                :pagination="{ pageSize: 10 }"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getTaskStatusColor(record.status)">
                      {{ getTaskStatusLabel(record.status) }}
                    </a-tag>
                  </template>
                  <template v-if="column.key === 'schedule'">
                    <div class="schedule-info">
                      <Clock class="schedule-icon" />
                      <span>{{ record.schedule }}</span>
                    </div>
                  </template>
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button
                        size="small"
                        @click="runTask(record)"
                        :disabled="record.status === 'running'"
                      >
                        <Play class="btn-icon" />
                        运行
                      </a-button>
                      <a-button
                        size="small"
                        @click="editTask(record)"
                      >
                        <Edit class="btn-icon" />
                        编辑
                      </a-button>
                      <a-button
                        size="small"
                        @click="viewTaskHistory(record)"
                      >
                        <History class="btn-icon" />
                        历史
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 批量操作 -->
        <a-tab-pane
          key="batch"
          tab="批量操作"
        >
          <div class="batch-section">
            <div class="batch-tools">
              <div class="tool-card">
                <div class="tool-header">
                  <Layers class="tool-icon" />
                  <h3>批量设备操作</h3>
                </div>
                <p class="tool-description">对多个设备执行相同的操作命令</p>
                <a-button
                  block
                  @click="showBatchDeviceModal"
                >
                  开始批量操作
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <Upload class="tool-icon" />
                  <h3>批量配置部署</h3>
                </div>
                <p class="tool-description">批量部署配置文件到多个设备</p>
                <a-button
                  block
                  @click="showBatchConfigModal"
                >
                  批量部署配置
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <Download class="tool-icon" />
                  <h3>批量数据收集</h3>
                </div>
                <p class="tool-description">从多个设备收集状态和配置信息</p>
                <a-button
                  block
                  @click="showBatchCollectModal"
                >
                  批量数据收集
                </a-button>
              </div>

              <div class="tool-card">
                <div class="tool-header">
                  <RefreshCw class="tool-icon" />
                  <h3>批量重启服务</h3>
                </div>
                <p class="tool-description">批量重启指定的系统服务</p>
                <a-button
                  block
                  @click="showBatchRestartModal"
                >
                  批量重启服务
                </a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- API集成 -->
        <a-tab-pane
          key="api"
          tab="API集成"
        >
          <div class="api-section">
            <div class="section-header">
              <h2 class="section-title">API集成管理</h2>
              <a-button @click="addApiIntegration">
                <Plus class="btn-icon" />
                添加API集成
              </a-button>
            </div>

            <div class="api-grid">
              <div
                v-for="api in apiIntegrations"
                :key="api.id"
                class="api-card"
                :class="api.status"
              >
                <div class="api-header">
                  <div class="api-info">
                    <component
                      :is="getApiIcon(api.type)"
                      class="api-icon"
                    />
                    <div class="api-details">
                      <h3 class="api-name">{{ api.name }}</h3>
                      <p class="api-url">{{ api.url }}</p>
                    </div>
                  </div>
                  <div
                    class="api-status"
                    :class="api.status"
                  >
                    <div class="status-dot"></div>
                    <span>{{ getApiStatusLabel(api.status) }}</span>
                  </div>
                </div>

                <div class="api-metrics">
                  <div class="metric-item">
                    <span class="metric-label">请求数</span>
                    <span class="metric-value">{{ api.requestCount }}</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value">{{ api.successRate }}%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">平均响应</span>
                    <span class="metric-value">{{ api.avgResponseTime }}ms</span>
                  </div>
                </div>

                <div class="api-actions">
                  <a-button
                    size="small"
                    @click="testApi(api)"
                    :loading="api.testing"
                  >
                    <Zap class="btn-icon" />
                    测试
                  </a-button>
                  <a-button
                    size="small"
                    @click="editApi(api)"
                  >
                    <Edit class="btn-icon" />
                    编辑
                  </a-button>
                  <a-button
                    size="small"
                    @click="viewApiLogs(api)"
                  >
                    <FileText class="btn-icon" />
                    日志
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 创建脚本模态框 -->
    <a-modal
      v-model:open="showCreateScriptModal"
      title="创建自动化脚本"
      width="800px"
      @ok="handleCreateScript"
      @cancel="cancelCreateScript"
    >
      <a-form
        ref="scriptFormRef"
        :model="scriptForm"
        layout="vertical"
      >
        <a-form-item
          label="脚本名称"
          name="name"
          :rules="[{ required: true, message: '请输入脚本名称' }]"
        >
          <a-input
            v-model:value="scriptForm.name"
            placeholder="请输入脚本名称"
          />
        </a-form-item>

        <a-form-item
          label="脚本描述"
          name="description"
        >
          <a-textarea
            v-model:value="scriptForm.description"
            placeholder="请输入脚本描述，例如：检查系统磁盘使用情况并清理临时文件"
            :rows="3"
            @input="onDescriptionChange"
          />
          <div class="ai-generate-hint">
            <a-button
              type="link"
              size="small"
              @click="generateScriptWithAI"
              :loading="aiGenerating"
              :disabled="!scriptForm.description.trim()"
            >
              <Bot class="ai-icon" />
              AI智能生成脚本
            </a-button>
            <span class="hint-text">填写详细描述后，AI将自动生成对应的脚本代码</span>
          </div>
        </a-form-item>

        <a-form-item
          label="脚本类型"
          name="type"
          :rules="[{ required: true, message: '请选择脚本类型' }]"
        >
          <a-select
            v-model:value="scriptForm.type"
            placeholder="请选择脚本类型"
          >
            <a-select-option value="shell">Shell脚本</a-select-option>
            <a-select-option value="python">Python脚本</a-select-option>
            <a-select-option value="powershell">PowerShell</a-select-option>
            <a-select-option value="ansible">Ansible</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="脚本内容"
          name="content"
          :rules="[{ required: true, message: '请输入脚本内容' }]"
        >
          <div class="script-content-section">
            <div class="content-header">
              <span class="content-label">脚本代码</span>
              <a-button
                type="primary"
                size="small"
                :loading="aiGenerating"
                :disabled="!scriptForm.description || !scriptForm.type"
                @click="generateScriptWithAI"
              >
                <Bot class="btn-icon" />
                AI生成脚本
              </a-button>
            </div>
            <a-textarea
              v-model:value="scriptForm.content"
              placeholder="请输入脚本内容，或填写脚本描述后点击'AI生成脚本'按钮自动生成"
              :rows="10"
              style="font-family: 'Courier New', monospace"
            />
            <div
              v-if="aiGenerating"
              class="ai-generating-indicator"
            >
              <a-spin size="small" />
              <span>AI正在生成脚本代码...</span>
            </div>
          </div>
        </a-form-item>

        <a-form-item
          label="执行超时"
          name="timeout"
        >
          <a-input-number
            v-model:value="scriptForm.timeout"
            :min="1"
            :max="3600"
            placeholder="秒"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="标签"
          name="tags"
        >
          <a-select
            v-model:value="scriptForm.tags"
            mode="tags"
            placeholder="添加标签"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建任务模态框 -->
    <a-modal
      v-model:open="showCreateTaskModal"
      title="创建调度任务"
      width="700px"
      @ok="handleCreateTask"
      @cancel="cancelCreateTask"
    >
      <a-form
        ref="taskFormRef"
        :model="taskForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item
          label="任务名称"
          name="name"
          :rules="[{ required: true, message: '请输入任务名称' }]"
        >
          <a-input
            v-model:value="taskForm.name"
            placeholder="请输入任务名称"
          />
        </a-form-item>

        <a-form-item
          label="关联脚本"
          name="scriptId"
          :rules="[{ required: true, message: '请选择关联脚本' }]"
        >
          <a-select
            v-model:value="taskForm.scriptId"
            placeholder="请选择关联脚本"
          >
            <a-select-option
              v-for="script in scripts"
              :key="script.id"
              :value="script.id"
            >
              {{ script.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="调度规则"
          name="schedule"
          :rules="[{ required: true, message: '请输入调度规则' }]"
        >
          <a-input
            v-model:value="taskForm.schedule"
            placeholder="例如: 0 6 * * * (每天6点执行)"
          />
        </a-form-item>

        <a-form-item
          label="任务描述"
          name="description"
        >
          <a-textarea
            v-model:value="taskForm.description"
            placeholder="请输入任务描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item
          label="启用状态"
          name="enabled"
        >
          <a-switch
            v-model:checked="taskForm.enabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- API集成配置模态框 -->
    <a-modal
      v-model:open="showApiIntegrationModal"
      title="添加API集成"
      width="800px"
      @ok="handleCreateApiIntegration"
      @cancel="cancelCreateApiIntegration"
    >
      <a-form
        ref="apiFormRef"
        :model="apiForm"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="集成名称"
              name="name"
              :rules="[{ required: true, message: '请输入集成名称' }]"
            >
              <a-input
                v-model:value="apiForm.name"
                placeholder="请输入API集成名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="API类型"
              name="type"
              :rules="[{ required: true, message: '请选择API类型' }]"
            >
              <a-select
                v-model:value="apiForm.type"
                placeholder="请选择API类型"
              >
                <a-select-option value="rest">REST API</a-select-option>
                <a-select-option value="graphql">GraphQL</a-select-option>
                <a-select-option value="soap">SOAP</a-select-option>
                <a-select-option value="webhook">Webhook</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="apiForm.description"
            placeholder="请输入API集成描述"
            :rows="2"
          />
        </a-form-item>

        <a-form-item
          label="基础URL"
          name="baseUrl"
          :rules="[{ required: true, message: '请输入基础URL' }]"
        >
          <a-input
            v-model:value="apiForm.baseUrl"
            placeholder="https://api.example.com"
          />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              label="认证方式"
              name="authType"
            >
              <a-select
                v-model:value="apiForm.authType"
                placeholder="请选择认证方式"
              >
                <a-select-option value="none">无认证</a-select-option>
                <a-select-option value="apikey">API Key</a-select-option>
                <a-select-option value="basic">Basic Auth</a-select-option>
                <a-select-option value="bearer">Bearer Token</a-select-option>
                <a-select-option value="oauth2">OAuth 2.0</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="超时时间(ms)"
              name="timeout"
            >
              <a-input-number
                v-model:value="apiForm.timeout"
                :min="1000"
                :max="300000"
                :step="1000"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              label="重试次数"
              name="retryCount"
            >
              <a-input-number
                v-model:value="apiForm.retryCount"
                :min="0"
                :max="10"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 认证信息 -->
        <div v-if="apiForm.authType === 'apikey'">
          <a-form-item
            label="API Key"
            name="apiKey"
            :rules="[{ required: true, message: '请输入API Key' }]"
          >
            <a-input-password
              v-model:value="apiForm.apiKey"
              placeholder="请输入API Key"
            />
          </a-form-item>
        </div>

        <div v-if="apiForm.authType === 'basic'">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item
                label="用户名"
                name="username"
                :rules="[{ required: true, message: '请输入用户名' }]"
              >
                <a-input
                  v-model:value="apiForm.username"
                  placeholder="请输入用户名"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="密码"
                name="password"
                :rules="[{ required: true, message: '请输入密码' }]"
              >
                <a-input-password
                  v-model:value="apiForm.password"
                  placeholder="请输入密码"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <div v-if="apiForm.authType === 'bearer'">
          <a-form-item
            label="Bearer Token"
            name="apiKey"
            :rules="[{ required: true, message: '请输入Bearer Token' }]"
          >
            <a-input-password
              v-model:value="apiForm.apiKey"
              placeholder="请输入Bearer Token"
            />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>

    <!-- 脚本执行配置模态框 -->
    <a-modal
      v-model:open="showExecuteScriptModal"
      title="执行脚本配置"
      width="800px"
      @ok="handleExecuteScript"
      @cancel="cancelExecuteScript"
    >
      <a-form
        ref="executeFormRef"
        :model="executeForm"
        layout="vertical"
      >
        <a-form-item label="脚本信息">
          <div class="script-info">
            <span class="script-name">{{ executeForm.scriptName }}</span>
            <span class="script-id">ID: {{ executeForm.scriptId }}</span>
          </div>
        </a-form-item>

        <a-form-item
          label="执行模式"
          name="executionMode"
          :rules="[{ required: true, message: '请选择执行模式' }]"
        >
          <a-radio-group v-model:value="executeForm.executionMode">
            <a-radio value="single">单个主机</a-radio>
            <a-radio value="batch">批量主机</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 单个主机模式 -->
        <div v-if="executeForm.executionMode === 'single'">
          <a-form-item
            label="目标主机"
            name="targetHost"
            :rules="[{ required: true, message: '请选择目标主机' }]"
          >
            <a-select
              v-model:value="executeForm.targetHost"
              placeholder="请选择要执行脚本的主机"
              show-search
              :filter-option="filterHostOption"
              :loading="loadingHosts"
            >
              <a-select-option
                v-for="host in availableHosts"
                :key="host.value"
                :value="host.value"
              >
                {{ host.label }}
                <span class="host-type-tag">{{ getTypeLabel(host.type) }}</span>
                <span
                  class="host-status-tag"
                  :class="host.status"
                  >{{ getStatusLabel(host.status) }}</span
                >
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <!-- 批量主机模式 -->
        <div v-if="executeForm.executionMode === 'batch'">
          <a-form-item
            label="主机组"
            name="selectedHostGroup"
          >
            <a-select
              v-model:value="executeForm.selectedHostGroup"
              placeholder="选择预定义的主机组（可选）"
            >
              <a-select-option value="web-servers">Web服务器组</a-select-option>
              <a-select-option value="db-servers">数据库服务器组</a-select-option>
              <a-select-option value="app-servers">应用服务器组</a-select-option>
              <a-select-option value="test-servers">测试服务器组</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item
            label="目标主机列表"
            name="targetHosts"
            :rules="[{ required: true, message: '请选择至少一个目标主机' }]"
          >
            <a-select
              v-model:value="executeForm.targetHosts"
              mode="multiple"
              placeholder="请选择要执行脚本的主机"
              show-search
              :filter-option="filterHostOption"
              :loading="loadingHosts"
            >
              <a-select-option
                v-for="host in availableHosts"
                :key="host.value"
                :value="host.value"
              >
                {{ host.label }}
                <span class="host-type-tag">{{ getTypeLabel(host.type) }}</span>
                <span
                  class="host-status-tag"
                  :class="host.status"
                  >{{ getStatusLabel(host.status) }}</span
                >
              </a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="执行超时(秒)"
              name="timeout"
            >
              <a-input-number
                v-model:value="executeForm.timeout"
                :min="30"
                :max="3600"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="日志级别"
              name="logLevel"
            >
              <a-select v-model:value="executeForm.logLevel">
                <a-select-option value="debug">Debug</a-select-option>
                <a-select-option value="info">Info</a-select-option>
                <a-select-option value="warn">Warning</a-select-option>
                <a-select-option value="error">Error</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item>
          <a-checkbox v-model:checked="executeForm.enableLog"> 启用详细日志记录 </a-checkbox>
        </a-form-item>

        <a-form-item label="执行参数（可选）">
          <a-textarea
            v-model:value="executeForm.customParameters"
            placeholder="输入自定义参数，例如：--env=production --debug=true"
            :rows="2"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { networkAssetsService, type HostOption } from '@/services/networkAssetsService'
import eventBus from '@/utils/eventBus'
import {
  ArrowLeft,
  Bot,
  RefreshCw,
  Plus,
  Calendar,
  Layers,
  FileCode,
  Clock,
  Activity,
  Zap,
  Play,
  Edit,
  FileText,
  History,
  Upload,
  Download,
  Search,
  Trash2
} from 'lucide-vue-next'

const router = useRouter()
const loading = ref(false)
const activeTab = ref('scripts')
const selectedScriptType = ref('all')
const selectedScriptStatus = ref('all')
const selectedTaskStatus = ref('all')

// 网络资产相关状态
const loadingHosts = ref(false)
const availableHosts = ref<HostOption[]>([])

// 模态框状态
const showCreateScriptModal = ref(false)
const showCreateTaskModal = ref(false)
const showApiIntegrationModal = ref(false)
const showExecuteScriptModal = ref(false)

// AI生成状态
const aiGenerating = ref(false)

// 表单引用
const scriptFormRef = ref()
const taskFormRef = ref()
const apiFormRef = ref()
const executeFormRef = ref()

// 脚本表单数据
const scriptForm = reactive({
  id: null as number | null,
  name: '',
  description: '',
  type: '',
  content: '',
  timeout: 300,
  tags: []
})

// 任务表单数据
const taskForm = reactive({
  id: null as number | null,
  name: '',
  scriptId: null,
  schedule: '',
  description: '',
  enabled: true
})

// API集成表单数据
const apiForm = reactive({
  id: null as number | null,
  name: '',
  description: '',
  type: '',
  baseUrl: '',
  authType: 'none',
  apiKey: '',
  username: '',
  password: '',
  headers: [],
  timeout: 30000,
  retryCount: 3
})

// 脚本执行表单数据
const executeForm = reactive({
  scriptId: null as number | null,
  scriptName: '',
  executionMode: 'single', // single: 单个主机, batch: 批量主机
  targetHost: '', // 单个主机模式的目标主机
  targetHosts: [], // 批量主机模式的目标主机列表
  selectedHostGroup: '',
  parameters: {},
  timeout: 300,
  enableLog: true,
  logLevel: 'info',
  customParameters: '' // 自定义参数
})

// 自动化统计数据
const automationStats = reactive({
  totalScripts: 45,
  activeScripts: 38,
  inactiveScripts: 7,
  totalTasks: 23,
  runningTasks: 5,
  waitingTasks: 12,
  todayExecutions: 156,
  successfulExecutions: 142,
  failedExecutions: 14,
  apiIntegrations: 12,
  onlineApis: 10,
  offlineApis: 2
})

// 脚本数据
const scripts = ref([
  {
    id: 1,
    name: '系统健康检查',
    description: '检查系统CPU、内存、磁盘使用情况',
    type: 'shell',
    status: 'active',
    executionCount: 245,
    successRate: 98.5,
    lastExecution: '2024-01-15 14:30',
    executing: false
  },
  {
    id: 2,
    name: '日志清理脚本',
    description: '清理超过30天的系统日志文件',
    type: 'python',
    status: 'active',
    executionCount: 89,
    successRate: 100,
    lastExecution: '2024-01-15 02:00',
    executing: false
  },
  {
    id: 3,
    name: '备份数据库',
    description: '自动备份MySQL数据库',
    type: 'shell',
    status: 'inactive',
    executionCount: 156,
    successRate: 95.2,
    lastExecution: '2024-01-14 23:00',
    executing: false
  }
])

// 任务数据
const tasks = ref([
  {
    id: 1,
    name: '每日系统检查',
    script: '系统健康检查',
    schedule: '每天 06:00',
    status: 'running',
    nextRun: '2024-01-16 06:00',
    lastRun: '2024-01-15 06:00'
  },
  {
    id: 2,
    name: '周末日志清理',
    script: '日志清理脚本',
    schedule: '每周日 02:00',
    status: 'waiting',
    nextRun: '2024-01-21 02:00',
    lastRun: '2024-01-14 02:00'
  }
])

// API集成数据
const apiIntegrations = ref([
  {
    id: 1,
    name: 'Zabbix监控API',
    type: 'monitoring',
    url: 'https://zabbix.company.com/api',
    status: 'online',
    requestCount: 1247,
    successRate: 99.2,
    avgResponseTime: 156,
    testing: false
  },
  {
    id: 2,
    name: 'LDAP用户同步',
    type: 'authentication',
    url: 'ldap://ldap.company.com',
    status: 'offline',
    requestCount: 89,
    successRate: 85.4,
    avgResponseTime: 234,
    testing: false
  }
])

// 任务表格列定义
const taskColumns = [
  { title: '任务名称', dataIndex: 'name', key: 'name' },
  { title: '关联脚本', dataIndex: 'script', key: 'script' },
  { title: '调度规则', key: 'schedule' },
  { title: '状态', key: 'status' },
  { title: '下次运行', dataIndex: 'nextRun', key: 'nextRun' },
  { title: '操作', key: 'actions' }
]

// 计算属性
const filteredScripts = computed(() => {
  return scripts.value.filter((script) => {
    const typeMatch = selectedScriptType.value === 'all' || script.type === selectedScriptType.value
    const statusMatch = selectedScriptStatus.value === 'all' || script.status === selectedScriptStatus.value
    return typeMatch && statusMatch
  })
})

const filteredTasks = computed(() => {
  return tasks.value.filter((task) => {
    return selectedTaskStatus.value === 'all' || task.status === selectedTaskStatus.value
  })
})

// 加载可用主机列表
const loadAvailableHosts = async () => {
  try {
    loadingHosts.value = true
    const hosts = await networkAssetsService.getHostOptions()
    availableHosts.value = hosts
  } catch (error) {
    console.error('加载主机列表失败:', error)
  } finally {
    loadingHosts.value = false
  }
}

// 获取设备类型标签
const getTypeLabel = (type: string): string => {
  const typeLabels: Record<string, string> = {
    switch: '交换机',
    server: '服务器',
    router: '路由器',
    firewall: '防火墙',
    ap: '无线AP'
  }
  return typeLabels[type] || type
}

// 获取状态标签
const getStatusLabel = (status: string): string => {
  const statusLabels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中'
  }
  return statusLabels[status] || status
}

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const createScript = () => {
  showCreateScriptModal.value = true
}

const createTask = () => {
  showCreateTaskModal.value = true
}

// 处理创建/编辑脚本
const handleCreateScript = async () => {
  try {
    await scriptFormRef.value.validate()

    if (scriptForm.id) {
      // 编辑模式：更新现有脚本
      const existingScriptIndex = scripts.value.findIndex((s) => s.id === scriptForm.id)
      if (existingScriptIndex !== -1) {
        const existingScript = scripts.value[existingScriptIndex]

        // 更新脚本数据
        Object.assign(existingScript, {
          name: scriptForm.name,
          description: scriptForm.description,
          type: scriptForm.type,
          content: scriptForm.content,
          timeout: scriptForm.timeout,
          tags: scriptForm.tags,
          updatedAt: new Date().toLocaleString('zh-CN')
        })

        console.log('脚本更新成功:', existingScript)
      }
    } else {
      // 创建模式：添加新脚本
      const newScript = {
        id: scripts.value.length + 1,
        name: scriptForm.name,
        description: scriptForm.description,
        type: scriptForm.type,
        content: scriptForm.content,
        timeout: scriptForm.timeout,
        tags: scriptForm.tags,
        status: 'active',
        executionCount: 0,
        successRate: 100,
        lastExecution: '从未执行',
        executing: false,
        createdAt: new Date().toLocaleString('zh-CN'),
        createdBy: '当前用户'
      }

      // 添加到脚本列表
      scripts.value.push(newScript)

      // 更新统计数据
      automationStats.totalScripts++
      automationStats.activeScripts++

      console.log('脚本创建成功:', newScript)
    }

    // 重置表单并关闭模态框
    resetScriptForm()
    showCreateScriptModal.value = false
  } catch (error) {
    console.error('脚本操作失败:', error)
  }
}

// 处理创建任务
const handleCreateTask = async () => {
  try {
    await taskFormRef.value.validate()

    // 查找关联的脚本
    const associatedScript = scripts.value.find((s) => s.id === taskForm.scriptId)

    if (taskForm.id) {
      // 编辑模式：更新现有任务
      const existingTaskIndex = tasks.value.findIndex((t) => t.id === taskForm.id)
      if (existingTaskIndex !== -1) {
        const existingTask = tasks.value[existingTaskIndex]

        // 更新任务数据
        Object.assign(existingTask, {
          name: taskForm.name,
          script: associatedScript?.name || '未知脚本',
          scriptId: taskForm.scriptId,
          schedule: taskForm.schedule,
          description: taskForm.description,
          status: taskForm.enabled ? 'waiting' : 'disabled',
          nextRun: calculateNextRun(taskForm.schedule),
          updatedAt: new Date().toLocaleString('zh-CN')
        })

        console.log('任务更新成功:', existingTask)
      }
    } else {
      // 创建模式：添加新任务
      const newTask = {
        id: tasks.value.length + 1,
        name: taskForm.name,
        script: associatedScript?.name || '未知脚本',
        scriptId: taskForm.scriptId,
        schedule: taskForm.schedule,
        description: taskForm.description,
        status: taskForm.enabled ? 'waiting' : 'disabled',
        nextRun: calculateNextRun(taskForm.schedule),
        lastRun: '从未运行',
        createdAt: new Date().toLocaleString('zh-CN'),
        createdBy: '当前用户'
      }

      // 添加到任务列表
      tasks.value.push(newTask)

      // 更新统计数据
      automationStats.totalTasks++
      if (taskForm.enabled) {
        automationStats.waitingTasks++
      }

      console.log('任务创建成功:', newTask)
    }

    // 重置表单并关闭模态框
    resetTaskForm()
    showCreateTaskModal.value = false
  } catch (error) {
    console.error('任务操作失败:', error)
  }
}

// 取消创建脚本
const cancelCreateScript = () => {
  resetScriptForm()
  showCreateScriptModal.value = false
}

// 取消创建任务
const cancelCreateTask = () => {
  resetTaskForm()
  showCreateTaskModal.value = false
}

// 重置脚本表单
const resetScriptForm = () => {
  Object.assign(scriptForm, {
    id: null,
    name: '',
    description: '',
    type: '',
    content: '',
    timeout: 300,
    tags: []
  })
}

// 重置任务表单
const resetTaskForm = () => {
  Object.assign(taskForm, {
    id: null,
    name: '',
    scriptId: null,
    schedule: '',
    description: '',
    enabled: true
  })
}

// 计算下次运行时间（简化实现）
const calculateNextRun = (schedule: string) => {
  // 这里应该根据cron表达式计算下次运行时间
  // 简化实现，返回一个小时后的时间
  const nextRun = new Date()
  nextRun.setHours(nextRun.getHours() + 1)
  return nextRun.toLocaleString('zh-CN')
}

// AI生成脚本功能
const generateScriptWithAI = async () => {
  if (!scriptForm.description || !scriptForm.type) {
    console.warn('请先填写脚本描述和选择脚本类型')
    return
  }

  aiGenerating.value = true

  try {
    // 构建AI提示词
    const prompt = buildAIPrompt(scriptForm.description, scriptForm.type)

    // 调用AI API生成脚本
    const generatedScript = await callAIAPI(prompt)

    // 将生成的脚本填入表单
    scriptForm.content = generatedScript

    console.log('AI脚本生成成功:', {
      description: scriptForm.description,
      type: scriptForm.type,
      generatedLength: generatedScript.length
    })
  } catch (error) {
    console.error('AI脚本生成失败:', error)
    // 这里可以显示错误提示
  } finally {
    aiGenerating.value = false
  }
}

// 构建AI提示词
const buildAIPrompt = (description: string, scriptType: string) => {
  const typeMap = {
    shell: 'Bash Shell',
    python: 'Python',
    powershell: 'PowerShell',
    ansible: 'Ansible Playbook'
  }

  const scriptTypeName = typeMap[scriptType] || scriptType

  return `请根据以下描述生成一个${scriptTypeName}脚本：

描述：${description}

要求：
1. 脚本应该是完整可执行的
2. 包含必要的错误处理
3. 添加适当的注释说明
4. 遵循最佳实践和安全规范
5. 只返回脚本代码，不要包含其他解释文字

请生成脚本代码：`
}

// 调用AI API
const callAIAPI = async (prompt: string): Promise<string> => {
  // 这里应该调用实际的AI API
  // 目前使用模拟实现

  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 2000))

  // 根据脚本类型和描述生成模拟脚本
  const scriptType = scriptForm.type
  const description = scriptForm.description.toLowerCase()

  if (scriptType === 'shell') {
    if (
      description.includes('系统监控') ||
      description.includes('健康检查') ||
      description.includes('cpu') ||
      description.includes('内存') ||
      description.includes('磁盘')
    ) {
      return `#!/bin/bash
# 系统健康检查脚本
# 描述：${scriptForm.description}

echo "开始系统健康检查..."

# 检查CPU使用率
echo "=== CPU使用率 ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}'

# 检查内存使用情况
echo "=== 内存使用情况 ==="
free -h

# 检查磁盘使用情况
echo "=== 磁盘使用情况 ==="
df -h

# 检查系统负载
echo "=== 系统负载 ==="
uptime

# 检查重要服务状态
echo "=== 服务状态检查 ==="
services=("ssh" "nginx" "mysql")
for service in "\${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "$service: 运行中"
    else
        echo "$service: 未运行"
    fi
done

echo "系统健康检查完成！"`
    } else if (description.includes('备份') || description.includes('数据库')) {
      return `#!/bin/bash
# 数据库备份脚本
# 描述：${scriptForm.description}

# 配置变量
DB_NAME="your_database"
DB_USER="your_username"
DB_PASS="your_password"
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/\${DB_NAME}_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

echo "开始备份数据库: $DB_NAME"

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "数据库备份成功: $BACKUP_FILE"

    # 压缩备份文件
    gzip $BACKUP_FILE
    echo "备份文件已压缩: $BACKUP_FILE.gz"

    # 删除7天前的备份
    find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
    echo "已清理7天前的备份文件"
else
    echo "数据库备份失败！"
    exit 1
fi`
    }
  } else if (scriptType === 'python') {
    if (description.includes('日志') || description.includes('清理')) {
      return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志清理脚本
描述：${scriptForm.description}
"""

import os
import sys
import time
import glob
from datetime import datetime, timedelta

def clean_logs(log_dir, days_to_keep=30):
    """
    清理指定目录下超过指定天数的日志文件

    Args:
        log_dir (str): 日志目录路径
        days_to_keep (int): 保留天数
    """
    try:
        # 计算截止日期
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_timestamp = cutoff_date.timestamp()

        # 查找日志文件
        log_patterns = ['*.log', '*.log.*', '*.out']
        deleted_files = 0
        total_size = 0

        for pattern in log_patterns:
            log_files = glob.glob(os.path.join(log_dir, pattern))

            for log_file in log_files:
                try:
                    # 获取文件修改时间
                    file_mtime = os.path.getmtime(log_file)

                    if file_mtime < cutoff_timestamp:
                        # 获取文件大小
                        file_size = os.path.getsize(log_file)

                        # 删除文件
                        os.remove(log_file)

                        deleted_files += 1
                        total_size += file_size

                        print(f"已删除: {log_file}")

                except OSError as e:
                    print(f"删除文件失败 {log_file}: {e}")

        print(f"\\n清理完成:")
        print(f"删除文件数: {deleted_files}")
        print(f"释放空间: {total_size / (1024*1024):.2f} MB")

    except Exception as e:
        print(f"日志清理失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 配置参数
    LOG_DIRECTORIES = [
        "/var/log",
        "/opt/app/logs",
        "/tmp"
    ]

    DAYS_TO_KEEP = 30

    print(f"开始清理 {DAYS_TO_KEEP} 天前的日志文件...")

    for log_dir in LOG_DIRECTORIES:
        if os.path.exists(log_dir):
            print(f"\\n清理目录: {log_dir}")
            clean_logs(log_dir, DAYS_TO_KEEP)
        else:
            print(f"目录不存在: {log_dir}")

    print("\\n日志清理任务完成！")`
    }
  } else if (scriptType === 'powershell') {
    return `# PowerShell脚本
# 描述：${scriptForm.description}

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始执行PowerShell脚本..." -ForegroundColor Green

try {
    # 获取系统信息
    Write-Host "=== 系统信息 ===" -ForegroundColor Yellow
    Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory

    # 获取磁盘使用情况
    Write-Host "=== 磁盘使用情况 ===" -ForegroundColor Yellow
    Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, Size, FreeSpace, @{Name="UsedSpace";Expression={$_.Size - $_.FreeSpace}}

    # 获取服务状态
    Write-Host "=== 重要服务状态 ===" -ForegroundColor Yellow
    $services = @("Spooler", "BITS", "Themes")
    foreach ($service in $services) {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc) {
            Write-Host "$service : $($svc.Status)" -ForegroundColor $(if($svc.Status -eq "Running"){"Green"}else{"Red"})
        }
    }

    Write-Host "脚本执行完成！" -ForegroundColor Green
}
catch {
    Write-Host "脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}`
  } else if (scriptType === 'ansible') {
    return `---
# Ansible Playbook
# 描述：${scriptForm.description}

- name: ${scriptForm.description}
  hosts: all
  become: yes
  vars:
    app_name: "example_app"
    app_user: "appuser"

  tasks:
    - name: 更新系统包
      package:
        name: "*"
        state: latest
      when: ansible_os_family == "RedHat"

    - name: 安装必要的包
      package:
        name:
          - curl
          - wget
          - vim
          - htop
        state: present

    - name: 创建应用用户
      user:
        name: "{{ app_user }}"
        shell: /bin/bash
        create_home: yes

    - name: 创建应用目录
      file:
        path: "/opt/{{ app_name }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0755'

    - name: 配置防火墙规则
      firewalld:
        port: "8080/tcp"
        permanent: yes
        state: enabled
        immediate: yes
      when: ansible_os_family == "RedHat"

    - name: 启动并启用服务
      systemd:
        name: "{{ app_name }}"
        state: started
        enabled: yes
        daemon_reload: yes
      ignore_errors: yes

    - name: 验证服务状态
      command: systemctl is-active "{{ app_name }}"
      register: service_status
      ignore_errors: yes

    - name: 显示部署结果
      debug:
        msg: "应用 {{ app_name }} 部署完成，状态: {{ service_status.stdout | default('未知') }}"`
  }

  // 根据描述生成智能逻辑
  const generateLogicByDescription = (desc: string, type: string) => {
    if (desc.includes('文件') || desc.includes('目录') || desc.includes('清理')) {
      if (type === 'shell') {
        return `    # 文件和目录操作
    echo "检查目录结构..."
    ls -la /tmp

    # 清理临时文件
    echo "清理临时文件..."
    find /tmp -type f -name "*.tmp" -mtime +7 -delete

    echo "文件操作完成"`
      } else if (type === 'python') {
        return `        # 文件和目录操作
        import glob
        import shutil

        print("检查目录结构...")
        for file in glob.glob("/tmp/*.tmp"):
            print(f"找到临时文件: {file}")
            os.remove(file)

        print("文件操作完成")`
      }
    } else if (desc.includes('网络') || desc.includes('连接') || desc.includes('端口')) {
      if (type === 'shell') {
        return `    # 网络连接检查
    echo "检查网络连接..."
    ping -c 3 8.8.8.8

    # 检查端口状态
    echo "检查端口状态..."
    netstat -tuln | grep :80

    echo "网络检查完成"`
      } else if (type === 'python') {
        return `        # 网络连接检查
        import socket
        import subprocess

        print("检查网络连接...")
        try:
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            print("网络连接正常")
        except OSError:
            print("网络连接异常")

        print("网络检查完成")`
      }
    } else if (desc.includes('服务') || desc.includes('进程') || desc.includes('状态')) {
      if (type === 'shell') {
        return `    # 服务状态检查
    echo "检查系统服务..."
    systemctl status nginx || echo "nginx服务未运行"
    systemctl status mysql || echo "mysql服务未运行"

    # 检查进程
    echo "检查重要进程..."
    ps aux | grep -E "(nginx|mysql)" | grep -v grep

    echo "服务检查完成"`
      } else if (type === 'python') {
        return `        # 服务状态检查
        import subprocess

        services = ['nginx', 'mysql', 'ssh']
        for service in services:
            try:
                result = subprocess.run(['systemctl', 'is-active', service],
                                      capture_output=True, text=True)
                status = result.stdout.strip()
                print(f"{service}: {status}")
            except Exception as e:
                print(f"检查{service}服务失败: {e}")

        print("服务检查完成")`
      }
    } else {
      // 通用逻辑
      if (type === 'shell') {
        return `    # 执行自定义任务
    echo "正在执行: ${desc}"

    # 在这里添加具体的业务逻辑
    # 例如：文件操作、系统命令、网络请求等

    echo "任务执行完成"`
      } else if (type === 'python') {
        return `        # 执行自定义任务
        print("正在执行: ${desc}")

        # 在这里添加具体的业务逻辑
        # 例如：文件操作、API调用、数据处理等

        print("任务执行完成")`
      } else if (type === 'powershell') {
        return `    # 执行自定义任务
    Write-Host "正在执行: ${desc}" -ForegroundColor Cyan

    # 在这里添加具体的业务逻辑
    # 例如：文件操作、注册表操作、服务管理等

    Write-Host "任务执行完成" -ForegroundColor Green`
      }
    }
    return ''
  }

  // 默认返回基础模板，根据脚本类型生成
  if (scriptType === 'shell') {
    return `#!/bin/bash
# ${scriptForm.description}
# 自动生成的Shell脚本

set -e  # 遇到错误时退出

echo "开始执行脚本: ${scriptForm.description}"
echo "执行时间: $(date)"

# 主要逻辑区域
main() {
    echo "正在执行主要任务..."

${generateLogicByDescription(description, 'shell')}

    echo "任务执行完成"
}

# 错误处理函数
error_handler() {
    echo "错误: 脚本执行失败，行号: $1"
    exit 1
}

# 设置错误处理
trap 'error_handler $LINENO' ERR

# 执行主函数
main

echo "脚本执行完成: $(date)"`
  } else if (scriptType === 'python') {
    return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
${scriptForm.description}
自动生成的Python脚本
"""

import os
import sys
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """主函数"""
    logging.info(f"开始执行脚本: ${scriptForm.description}")

    try:
        # 主要逻辑区域
${generateLogicByDescription(description, 'python')}

        logging.info("脚本执行完成")

    except Exception as e:
        logging.error(f"脚本执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()`
  } else if (scriptType === 'powershell') {
    return `# ${scriptForm.description}
# 自动生成的PowerShell脚本

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始执行脚本: ${scriptForm.description}" -ForegroundColor Green
Write-Host "执行时间: $(Get-Date)" -ForegroundColor Yellow

try {
    # 主要逻辑区域
${generateLogicByDescription(description, 'powershell')}

    Write-Host "脚本执行完成: $(Get-Date)" -ForegroundColor Green
}
catch {
    Write-Host "脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}`
  } else {
    return `# ${scriptForm.description}
# 自动生成的${scriptType}脚本

echo "开始执行脚本..."

# 在这里添加您的脚本逻辑

echo "脚本执行完成！"`
  }
}

// 主机选项过滤
const filterHostOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 处理脚本执行
const handleExecuteScript = async () => {
  try {
    await executeFormRef.value.validate()

    // 获取目标脚本
    const script = scripts.value.find((s) => s.id === executeForm.scriptId)
    if (!script) {
      console.error('脚本不存在')
      return
    }

    // 设置脚本执行状态
    script.executing = true

    // 构建执行配置
    const executionConfig = {
      scriptId: executeForm.scriptId,
      scriptName: executeForm.scriptName,
      mode: executeForm.executionMode,
      targets: executeForm.executionMode === 'single' ? [executeForm.targetHost] : executeForm.targetHosts,
      timeout: executeForm.timeout,
      logLevel: executeForm.logLevel,
      enableLog: executeForm.enableLog,
      parameters: executeForm.customParameters || '',
      startTime: new Date().toLocaleString('zh-CN')
    }

    console.log('开始执行脚本:', executionConfig)

    // 关闭模态框
    showExecuteScriptModal.value = false
    resetExecuteForm()

    // 模拟脚本执行过程
    await simulateScriptExecution(script, executionConfig)
  } catch (error) {
    console.error('脚本执行配置验证失败:', error)
  }
}

// 模拟脚本执行过程
const simulateScriptExecution = async (script: any, config: any) => {
  const targets = config.targets
  const totalTargets = targets.length

  console.log(`开始在 ${totalTargets} 个目标上执行脚本 "${script.name}"`)

  // 为每个目标模拟执行
  const results = []

  for (let i = 0; i < targets.length; i++) {
    const target = targets[i]
    const startTime = Date.now()

    console.log(`[${i + 1}/${totalTargets}] 在 ${target} 上执行脚本...`)

    // 模拟执行时间
    const executionTime = Math.random() * 3000 + 1000 // 1-4秒
    await new Promise((resolve) => setTimeout(resolve, executionTime))

    const endTime = Date.now()
    const duration = endTime - startTime

    // 随机决定执行结果
    const isSuccess = Math.random() > 0.1 // 90%成功率

    const result = {
      target: target,
      success: isSuccess,
      duration: duration,
      output: isSuccess
        ? `脚本执行成功\n执行时间: ${duration}ms\n退出码: 0\n输出: 任务完成`
        : `脚本执行失败\n执行时间: ${duration}ms\n退出码: 1\n错误: ${getRandomError()}`,
      timestamp: new Date().toLocaleString('zh-CN')
    }

    results.push(result)
    console.log(`[${i + 1}/${totalTargets}] ${target}: ${isSuccess ? '成功' : '失败'} (${duration}ms)`)
  }

  // 更新脚本统计
  script.executing = false
  script.executionCount++
  script.lastExecution = new Date().toLocaleString('zh-CN')

  // 计算成功率
  const successCount = results.filter((r) => r.success).length
  script.successRate = Math.round((successCount / totalTargets) * 100)

  // 显示执行结果摘要
  console.log('脚本执行完成:', {
    script: script.name,
    totalTargets: totalTargets,
    successCount: successCount,
    failureCount: totalTargets - successCount,
    successRate: script.successRate + '%',
    results: results
  })

  // 这里可以打开一个执行结果查看模态框
  console.table(results)
}

// 取消脚本执行
const cancelExecuteScript = () => {
  resetExecuteForm()
  showExecuteScriptModal.value = false
}

// 重置执行表单
const resetExecuteForm = () => {
  Object.assign(executeForm, {
    scriptId: null,
    scriptName: '',
    executionMode: 'single',
    targetHost: '',
    targetHosts: [],
    selectedHostGroup: '',
    parameters: {},
    timeout: 300,
    enableLog: true,
    logLevel: 'info',
    customParameters: ''
  })
}

// 获取随机错误信息
const getRandomError = () => {
  const errors = ['连接超时', '权限不足', '文件不存在', '网络异常', '服务不可用', '配置错误', '磁盘空间不足', '内存不足', '端口被占用', '依赖缺失']
  return errors[Math.floor(Math.random() * errors.length)]
}

// 描述变化处理
const onDescriptionChange = () => {
  // 可以在这里添加实时提示或建议
  console.log('描述已更新:', scriptForm.description)
}

const showBatchOperations = () => {
  activeTab.value = 'batch'
}

const executeScript = (script: any) => {
  // 填充执行表单数据
  Object.assign(executeForm, {
    scriptId: script.id,
    scriptName: script.name,
    executionMode: 'single',
    targetHosts: [],
    selectedHostGroup: '',
    parameters: {},
    timeout: script.timeout || 300,
    enableLog: true,
    logLevel: 'info'
  })

  // 显示执行配置模态框
  showExecuteScriptModal.value = true
}

const editScript = (script: any) => {
  // 填充表单数据
  Object.assign(scriptForm, {
    name: script.name,
    description: script.description,
    type: script.type,
    content: script.content || '',
    timeout: script.timeout || 300,
    tags: script.tags || []
  })

  // 设置编辑模式
  scriptForm.id = script.id
  showCreateScriptModal.value = true
}

const viewScriptLogs = (script: any) => {
  // 这里应该打开日志查看器
  // 简化实现，显示模拟日志
  const logs = [
    `[${new Date().toLocaleString()}] 脚本 "${script.name}" 开始执行`,
    `[${new Date().toLocaleString()}] 执行环境: ${script.type}`,
    `[${new Date().toLocaleString()}] 脚本执行成功`,
    `[${new Date().toLocaleString()}] 执行耗时: 2.3秒`
  ]

  console.log('脚本执行日志:', logs)
  // 这里可以打开一个日志查看模态框
}

const deleteScript = (script: any) => {
  // 显示删除确认对话框
  const confirmed = confirm(`确定要删除脚本 "${script.name}" 吗？\n\n删除后将无法恢复，请谨慎操作。`)

  if (confirmed) {
    // 从脚本列表中移除
    const index = scripts.value.findIndex((item) => item.id === script.id)
    if (index !== -1) {
      scripts.value.splice(index, 1)

      // 更新统计数据
      automationStats.totalScripts--
      if (script.status === 'active') {
        automationStats.activeScripts--
      } else {
        automationStats.inactiveScripts--
      }

      console.log('脚本删除成功:', script.name)

      // 这里可以显示成功提示
      // message.success(`脚本 "${script.name}" 已删除`)
    }
  }
}

const runTask = (task: any) => {
  // 更新任务状态为运行中
  task.status = 'running'
  task.lastRun = new Date().toLocaleString('zh-CN')

  // 模拟任务执行
  console.log('开始运行任务:', task.name)

  // 更新统计数据
  automationStats.runningTasks++
  if (automationStats.waitingTasks > 0) {
    automationStats.waitingTasks--
  }

  // 模拟任务执行时间
  setTimeout(
    () => {
      // 随机决定任务成功或失败
      const isSuccess = Math.random() > 0.2 // 80%成功率

      if (isSuccess) {
        task.status = 'completed'
        automationStats.successfulExecutions++
        console.log('任务执行成功:', task.name)
      } else {
        task.status = 'failed'
        automationStats.failedExecutions++
        console.log('任务执行失败:', task.name)
      }

      // 更新统计数据
      automationStats.runningTasks--
      automationStats.todayExecutions++

      // 计算下次运行时间
      task.nextRun = calculateNextRun(task.schedule)

      // 如果任务是启用状态，重新设置为等待
      if (task.enabled !== false) {
        setTimeout(() => {
          task.status = 'waiting'
          automationStats.waitingTasks++
        }, 1000)
      }
    },
    3000 + Math.random() * 2000
  ) // 3-5秒执行时间
}

const editTask = (task: any) => {
  // 填充任务表单数据
  Object.assign(taskForm, {
    id: task.id,
    name: task.name,
    scriptId: task.scriptId,
    schedule: task.schedule,
    description: task.description || '',
    enabled: task.status !== 'disabled'
  })

  // 显示编辑模态框
  showCreateTaskModal.value = true
}

const viewTaskHistory = (task: any) => {
  // 生成模拟的任务历史记录
  const history = generateTaskHistory(task)

  // 显示任务历史（这里可以打开一个新的模态框）
  console.log('任务历史记录:', history)

  // 简化实现：在控制台显示历史
  console.table(history)
}

// 生成任务历史记录
const generateTaskHistory = (task: any) => {
  const history = []
  const now = new Date()

  // 生成最近10次执行记录
  for (let i = 0; i < 10; i++) {
    const executionTime = new Date(now.getTime() - (i + 1) * 24 * 60 * 60 * 1000) // 每天一次
    const isSuccess = Math.random() > 0.15 // 85%成功率
    const duration = Math.floor(Math.random() * 300) + 30 // 30-330秒

    history.push({
      id: i + 1,
      taskName: task.name,
      executionTime: executionTime.toLocaleString('zh-CN'),
      status: isSuccess ? '成功' : '失败',
      duration: `${duration}秒`,
      output: isSuccess
        ? `任务执行成功\n执行时间: ${duration}秒\n处理记录: ${Math.floor(Math.random() * 100) + 1}条`
        : `任务执行失败\n错误信息: ${getRandomError()}\n执行时间: ${duration}秒`,
      triggeredBy: '系统调度'
    })
  }

  return history.reverse() // 最新的在前面
}

const showBatchDeviceModal = () => {
  // 实现批量设备操作
  const selectedDevices = ['192.168.1.10', '192.168.1.11', '192.168.1.12']
  const command = 'systemctl status nginx'

  console.log('开始批量设备操作:', {
    devices: selectedDevices,
    command: command,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量执行
  selectedDevices.forEach((device, index) => {
    setTimeout(
      () => {
        console.log(`设备 ${device} 执行完成: ${command}`)
      },
      (index + 1) * 1000
    )
  })
}

const showBatchConfigModal = () => {
  // 实现批量配置部署
  const configFile = '/etc/nginx/nginx.conf'
  const targetDevices = ['web-server-01', 'web-server-02', 'web-server-03']

  console.log('开始批量配置部署:', {
    configFile: configFile,
    targets: targetDevices,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量部署
  targetDevices.forEach((device, index) => {
    setTimeout(
      () => {
        console.log(`配置已部署到 ${device}: ${configFile}`)
      },
      (index + 1) * 800
    )
  })
}

const showBatchCollectModal = () => {
  // 实现批量数据收集
  const collectCommands = ['df -h', 'free -m', 'ps aux | head -10', 'netstat -tuln']
  const targetServers = ['server-01', 'server-02', 'server-03']

  console.log('开始批量数据收集:', {
    commands: collectCommands,
    servers: targetServers,
    timestamp: new Date().toLocaleString()
  })

  // 模拟数据收集
  targetServers.forEach((server, serverIndex) => {
    collectCommands.forEach((cmd, cmdIndex) => {
      setTimeout(
        () => {
          console.log(`从 ${server} 收集数据: ${cmd}`)
        },
        (serverIndex * collectCommands.length + cmdIndex + 1) * 500
      )
    })
  })
}

const showBatchRestartModal = () => {
  // 实现批量重启服务
  const services = ['nginx', 'mysql', 'redis']
  const targetServers = ['prod-server-01', 'prod-server-02']

  console.log('开始批量重启服务:', {
    services: services,
    servers: targetServers,
    timestamp: new Date().toLocaleString()
  })

  // 模拟批量重启
  targetServers.forEach((server, serverIndex) => {
    services.forEach((service, serviceIndex) => {
      setTimeout(
        () => {
          console.log(`在 ${server} 上重启服务: ${service}`)
        },
        (serverIndex * services.length + serviceIndex + 1) * 1200
      )
    })
  })
}

const addApiIntegration = () => {
  // 显示API集成配置模态框
  showApiIntegrationModal.value = true
}

// 处理创建API集成
const handleCreateApiIntegration = async () => {
  try {
    await apiFormRef.value.validate()

    // 创建新的API集成
    const newApiIntegration = {
      id: apiIntegrations.value.length + 1,
      name: apiForm.name,
      type: apiForm.type,
      url: apiForm.baseUrl,
      status: 'active',
      requestCount: 0,
      successRate: 100,
      avgResponseTime: 0,
      testing: false,
      // 扩展属性
      description: apiForm.description,
      authType: apiForm.authType,
      lastTest: '从未测试',
      createdAt: new Date().toLocaleString('zh-CN'),
      createdBy: '当前用户'
    }

    // 添加到API集成列表
    apiIntegrations.value.push(newApiIntegration)

    // 更新统计数据
    automationStats.apiIntegrations++

    // 重置表单并关闭模态框
    resetApiForm()
    showApiIntegrationModal.value = false

    console.log('API集成创建成功:', newApiIntegration)
  } catch (error) {
    console.error('API集成创建失败:', error)
  }
}

// 取消创建API集成
const cancelCreateApiIntegration = () => {
  resetApiForm()
  showApiIntegrationModal.value = false
}

// 重置API表单
const resetApiForm = () => {
  Object.assign(apiForm, {
    id: null,
    name: '',
    description: '',
    type: '',
    baseUrl: '',
    authType: 'none',
    apiKey: '',
    username: '',
    password: '',
    headers: [],
    timeout: 30000,
    retryCount: 3
  })
}

const testApi = async (api: any) => {
  // 设置测试状态
  api.testing = true

  console.log('开始测试API:', api.name)

  try {
    // 模拟API测试
    const startTime = Date.now()

    // 模拟网络请求延迟
    await new Promise((resolve) => setTimeout(resolve, 1000 + Math.random() * 2000))

    const endTime = Date.now()
    const responseTime = endTime - startTime

    // 随机决定测试结果
    const isSuccess = Math.random() > 0.1 // 90%成功率

    if (isSuccess) {
      api.status = 'active'
      api.avgResponseTime = responseTime
      api.lastTest = new Date().toLocaleString('zh-CN')
      api.requestCount++

      console.log('API测试成功:', {
        name: api.name,
        responseTime: responseTime + 'ms',
        status: 'success'
      })
    } else {
      api.status = 'error'
      api.lastTest = new Date().toLocaleString('zh-CN')

      console.log('API测试失败:', {
        name: api.name,
        error: '连接超时或服务不可用'
      })
    }
  } catch (error) {
    api.status = 'error'
    console.error('API测试异常:', error)
  } finally {
    api.testing = false
  }
}

const editApi = (api: any) => {
  // 填充API表单数据
  Object.assign(apiForm, {
    id: api.id,
    name: api.name,
    description: api.description || '',
    type: api.type,
    baseUrl: api.url,
    authType: api.authType || 'none',
    apiKey: '',
    username: '',
    password: '',
    headers: [],
    timeout: 30000,
    retryCount: 3
  })

  // 显示编辑模态框
  showApiIntegrationModal.value = true
}

const viewApiLogs = (api: any) => {
  // 生成模拟的API调用日志
  const logs = generateApiLogs(api)

  console.log('API调用日志:', logs)
  console.table(logs)
}

// 生成API调用日志
const generateApiLogs = (api: any) => {
  const logs = []
  const now = new Date()

  // 生成最近20次调用记录
  for (let i = 0; i < 20; i++) {
    const callTime = new Date(now.getTime() - i * 30 * 60 * 1000) // 每30分钟一次
    const isSuccess = Math.random() > 0.1 // 90%成功率
    const responseTime = Math.floor(Math.random() * 2000) + 100 // 100-2100ms
    const statusCode = isSuccess ? 200 : Math.random() > 0.5 ? 404 : 500

    logs.push({
      id: i + 1,
      apiName: api.name,
      method: 'GET',
      endpoint: '/api/data',
      timestamp: callTime.toLocaleString('zh-CN'),
      statusCode: statusCode,
      responseTime: responseTime + 'ms',
      status: isSuccess ? '成功' : '失败',
      requestSize: Math.floor(Math.random() * 1000) + 100 + 'B',
      responseSize: Math.floor(Math.random() * 5000) + 500 + 'B'
    })
  }

  return logs.reverse() // 最新的在前面
}

const getScriptIcon = (type: string) => {
  const icons: Record<string, any> = {
    shell: FileCode,
    python: FileCode,
    powershell: FileCode,
    ansible: FileCode
  }
  return icons[type] || FileCode
}

const getScriptStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活跃',
    inactive: '停用'
  }
  return labels[status] || status
}

const getTaskStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    running: 'blue',
    waiting: 'orange',
    completed: 'green',
    failed: 'red'
  }
  return colors[status] || 'default'
}

const getTaskStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    running: '运行中',
    waiting: '等待中',
    completed: '已完成',
    failed: '失败'
  }
  return labels[status] || status
}

const getApiIcon = (type: string) => {
  const icons: Record<string, any> = {
    monitoring: Activity,
    authentication: Zap,
    database: FileCode,
    notification: FileCode
  }
  return icons[type] || Zap
}

const getApiStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线'
  }
  return labels[status] || status
}

onMounted(() => {
  refreshData()
  loadAvailableHosts()
})
</script>

<style scoped>
.automation-manager {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.overview-stats {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-icon {
  width: 16px;
  height: 16px;
}

.stat-value {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
}

.stat-details {
  display: flex;
  gap: 16px;
}

.stat-detail {
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #666;
}

.detail-value {
  font-size: 12px;
  font-weight: 600;
}

.detail-value.active {
  color: #52c41a;
}

.detail-value.inactive {
  color: #ff4d4f;
}

.detail-value.running {
  color: #1890ff;
}

.detail-value.waiting {
  color: #fa8c16;
}

.detail-value.success {
  color: #52c41a;
}

.detail-value.failed {
  color: #ff4d4f;
}

.detail-value.online {
  color: #52c41a;
}

.detail-value.offline {
  color: #ff4d4f;
}

.main-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: no-drag;
}

.scripts-section,
.tasks-section,
.batch-section,
.api-section {
  padding: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
  font-weight: 600;
}

.section-filters {
  display: flex;
  gap: 12px;
}

.scripts-grid,
.api-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.script-card,
.api-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.script-card:hover,
.api-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.script-card.active,
.api-card.online {
  border-left: 4px solid #52c41a;
}

.script-card.inactive,
.api-card.offline {
  border-left: 4px solid #ff4d4f;
}

.script-header,
.api-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.script-info,
.api-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.script-icon,
.api-icon {
  width: 32px;
  height: 32px;
  color: #1890ff;
  flex-shrink: 0;
}

.script-details,
.api-details {
  flex: 1;
}

.script-name,
.api-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 600;
}

.script-description {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.api-url {
  margin: 0;
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.script-status,
.api-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.script-status.active,
.api-status.online {
  color: #52c41a;
}

.script-status.inactive,
.api-status.offline {
  color: #ff4d4f;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.script-metrics,
.api-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.metric-label {
  font-size: 10px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.script-actions,
.api-actions {
  display: flex;
  gap: 8px;
}

.tasks-table {
  background: white;
}

.schedule-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.schedule-icon {
  width: 14px;
  height: 14px;
  color: #666;
}

.batch-tools {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.tool-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.tool-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
}

.tool-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  color: #1890ff;
}

.tool-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.tool-description {
  margin: 0 0 16px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

/* AI脚本生成相关样式 */
.script-content-section {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.content-label {
  font-weight: 500;
  color: #333;
}

.script-content-section .ant-input {
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.script-content-section .ant-input:focus {
  border: none;
  box-shadow: none;
}

.ai-generating-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border-top: 1px solid #e8e8e8;
  color: #1890ff;
  font-size: 12px;
}

/* AI生成提示样式 */
.ai-generate-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
}

.ai-icon {
  width: 14px;
  height: 14px;
}

.hint-text {
  color: #52c41a;
  font-size: 12px;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

/* 脚本执行配置样式 */
.script-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
}

.script-name {
  font-weight: 600;
  color: #24292e;
  font-size: 14px;
}

.script-id {
  color: #586069;
  font-size: 12px;
  background: #e1e4e8;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 主机选择项样式 */
.host-type-tag {
  margin-left: 8px;
  padding: 2px 6px;
  background: #e6f7ff;
  color: #1890ff;
  border-radius: 3px;
  font-size: 11px;
}

.host-status-tag {
  margin-left: 4px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.host-status-tag.online {
  background: #f6ffed;
  color: #52c41a;
}

.host-status-tag.offline {
  background: #fff2e8;
  color: #fa8c16;
}

.host-status-tag.maintenance {
  background: #f0f0f0;
  color: #8c8c8c;
}
</style>
