<template>
  <div class="connection-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Network class="title-icon" />
          连接管理
        </h1>
        <p class="page-description">管理所有活动的Console和RDP连接，监控连接状态和性能</p>
      </div>
      <div class="header-actions">
        <a-button
          type="primary"
          @click="refreshConnections"
        >
          <ReloadOutlined />
          刷新
        </a-button>
        <a-button @click="showSettings = true">
          <SettingOutlined />
          设置
        </a-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon console">
            <Terminal />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ consoleConnections.length }}</div>
            <div class="stat-label">Console连接</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon rdp">
            <Monitor />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ rdpConnections.length }}</div>
            <div class="stat-label">RDP连接</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon active">
            <Activity />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ activeConnections.length }}</div>
            <div class="stat-label">活动连接</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon error">
            <AlertCircle />
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ errorConnections.length }}</div>
            <div class="stat-label">错误连接</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 连接列表 -->
    <div class="connections-section">
      <div class="section-header">
        <h2>活动连接</h2>
        <div class="section-actions">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索连接..."
            style="width: 300px"
            @search="handleSearch"
          />
          <a-select
            v-model:value="filterType"
            placeholder="筛选类型"
            style="width: 120px; margin-left: 12px"
            @change="handleFilter"
          >
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="console">Console</a-select-option>
            <a-select-option value="rdp">RDP</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="connections-grid">
        <div
          v-for="connection in filteredConnections"
          :key="connection.id"
          class="connection-card"
          :class="{
            'status-connected': connection.status === 'connected',
            'status-connecting': connection.status === 'connecting',
            'status-disconnected': connection.status === 'disconnected',
            'status-error': connection.status === 'error'
          }"
        >
          <div class="connection-header">
            <div class="connection-type">
              <CodeOutlined
                v-if="connection.type === 'console'"
                class="type-icon"
              />
              <DesktopOutlined
                v-else-if="connection.type === 'rdp'"
                class="type-icon"
              />
              <span class="type-label">{{ getConnectionTypeLabel(connection.type) }}</span>
            </div>
            <div class="connection-status">
              <div
                class="status-indicator"
                :class="`status-${connection.status}`"
              ></div>
              <span class="status-text">{{ getStatusLabel(connection.status) }}</span>
            </div>
          </div>

          <div class="connection-info">
            <div class="info-row">
              <span class="info-label">目标地址:</span>
              <span class="info-value">{{ connection.host }}:{{ connection.port }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">连接时长:</span>
              <span class="info-value">{{ formatDuration(connection.connectedAt) }}</span>
            </div>
            <div
              v-if="connection.user"
              class="info-row"
            >
              <span class="info-label">用户:</span>
              <span class="info-value">{{ connection.user }}</span>
            </div>
          </div>

          <div class="connection-actions">
            <a-button
              size="small"
              @click="viewConnection(connection)"
            >
              <EyeOutlined />
              查看
            </a-button>
            <a-button
              size="small"
              :disabled="connection.status !== 'connected'"
              @click="pauseConnection(connection)"
            >
              <PauseCircleOutlined />
              暂停
            </a-button>
            <a-button
              size="small"
              danger
              :disabled="connection.status === 'disconnected'"
              @click="disconnectConnection(connection)"
            >
              <DisconnectOutlined />
              断开
            </a-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="filteredConnections.length === 0"
        class="empty-state"
      >
        <div class="empty-icon">
          <GlobalOutlined />
        </div>
        <div class="empty-text">
          <h3>暂无活动连接</h3>
          <p>当前没有活动的Console或RDP连接</p>
        </div>
      </div>
    </div>

    <!-- 连接详情抽屉 -->
    <a-drawer
      v-model:open="detailDrawerVisible"
      title="连接详情"
      width="500"
      placement="right"
    >
      <div
        v-if="selectedConnection"
        class="connection-detail"
      >
        <div class="detail-section">
          <h3>基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">连接ID:</span>
              <span class="detail-value">{{ selectedConnection.id }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">连接类型:</span>
              <span class="detail-value">{{ getConnectionTypeLabel(selectedConnection.type) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">目标地址:</span>
              <span class="detail-value">{{ selectedConnection.host }}:{{ selectedConnection.port }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <span
                class="detail-value status"
                :class="`status-${selectedConnection.status}`"
              >
                {{ getStatusLabel(selectedConnection.status) }}
              </span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3>连接统计</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">建立时间:</span>
              <span class="detail-value">{{ formatTime(selectedConnection.connectedAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">连接时长:</span>
              <span class="detail-value">{{ formatDuration(selectedConnection.connectedAt) }}</span>
            </div>
            <div
              v-if="selectedConnection.bytesReceived"
              class="detail-item"
            >
              <span class="detail-label">接收数据:</span>
              <span class="detail-value">{{ formatBytes(selectedConnection.bytesReceived) }}</span>
            </div>
            <div
              v-if="selectedConnection.bytesSent"
              class="detail-item"
            >
              <span class="detail-label">发送数据:</span>
              <span class="detail-value">{{ formatBytes(selectedConnection.bytesSent) }}</span>
            </div>
          </div>
        </div>

        <div
          v-if="selectedConnection.config"
          class="detail-section"
        >
          <h3>连接配置</h3>
          <div class="config-display">
            <pre>{{ JSON.stringify(selectedConnection.config, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 设置抽屉 -->
    <a-drawer
      v-model:open="showSettings"
      title="连接管理设置"
      width="600"
      placement="right"
    >
      <div class="settings-content">
        <div class="settings-section">
          <h3>刷新设置</h3>
          <div class="setting-item">
            <span class="setting-label">自动刷新:</span>
            <a-switch
              v-model:checked="autoRefresh"
              @change="handleAutoRefreshChange"
            />
          </div>
          <div
            v-if="autoRefresh"
            class="setting-item"
          >
            <span class="setting-label">刷新间隔:</span>
            <a-select
              v-model:value="refreshInterval"
              style="width: 120px"
            >
              <a-select-option :value="5000">5秒</a-select-option>
              <a-select-option :value="10000">10秒</a-select-option>
              <a-select-option :value="30000">30秒</a-select-option>
              <a-select-option :value="60000">1分钟</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="settings-section">
          <h3>显示设置</h3>
          <div class="setting-item">
            <span class="setting-label">显示断开的连接:</span>
            <a-switch v-model:checked="showDisconnected" />
          </div>
          <div class="setting-item">
            <span class="setting-label">显示连接统计:</span>
            <a-switch v-model:checked="showStats" />
          </div>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import {
  GlobalOutlined,
  CodeOutlined,
  DesktopOutlined,
  ActivityOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  PauseCircleOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 连接数据接口
interface Connection {
  id: string
  type: 'console' | 'rdp'
  host: string
  port: number
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  connectedAt: string
  user?: string
  bytesReceived?: number
  bytesSent?: number
  config?: any
}

// 响应式数据
const connections = ref<Connection[]>([])
const searchKeyword = ref('')
const filterType = ref('all')
const detailDrawerVisible = ref(false)
const selectedConnection = ref<Connection | null>(null)
const showSettings = ref(false)
const autoRefresh = ref(true)
const refreshInterval = ref(10000)
const showDisconnected = ref(true)
const showStats = ref(true)

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 计算属性
const consoleConnections = computed(() => connections.value.filter((conn) => conn.type === 'console'))

const rdpConnections = computed(() => connections.value.filter((conn) => conn.type === 'rdp'))

const activeConnections = computed(() => connections.value.filter((conn) => conn.status === 'connected'))

const errorConnections = computed(() => connections.value.filter((conn) => conn.status === 'error'))

const filteredConnections = computed(() => {
  let filtered = connections.value

  // 类型筛选
  if (filterType.value !== 'all') {
    filtered = filtered.filter((conn) => conn.type === filterType.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (conn) =>
        conn.host.toLowerCase().includes(keyword) ||
        conn.id.toLowerCase().includes(keyword) ||
        (conn.user && conn.user.toLowerCase().includes(keyword))
    )
  }

  // 是否显示断开的连接
  if (!showDisconnected.value) {
    filtered = filtered.filter((conn) => conn.status !== 'disconnected')
  }

  return filtered
})

// 方法
const refreshConnections = async () => {
  try {
    const result = await window.api.connectionsList()
    if (result.success) {
      connections.value = result.data || []
    } else {
      message.error('获取连接列表失败: ' + result.error)
    }
  } catch (error) {
    console.error('刷新连接列表失败:', error)
    message.error('刷新连接列表失败')
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const viewConnection = (connection: Connection) => {
  selectedConnection.value = connection
  detailDrawerVisible.value = true
}

const pauseConnection = async (connection: Connection) => {
  try {
    // 暂停连接的逻辑（如果支持的话）
    message.info('暂停功能开发中')
  } catch (error) {
    message.error('暂停连接失败')
  }
}

const disconnectConnection = async (connection: Connection) => {
  try {
    const result = await window.api.connectionsDisconnect(connection.id)
    if (result.success) {
      message.success('连接已断开')
      await refreshConnections()
    } else {
      message.error('断开连接失败: ' + result.error)
    }
  } catch (error) {
    console.error('断开连接失败:', error)
    message.error('断开连接失败')
  }
}

const handleAutoRefreshChange = (checked: boolean) => {
  if (checked) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = setInterval(refreshConnections, refreshInterval.value)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 工具函数
const getConnectionTypeLabel = (type: string) => {
  const labels = {
    console: 'Console',
    rdp: 'RDP'
  }
  return labels[type] || type
}

const getStatusLabel = (status: string) => {
  const labels = {
    connecting: '连接中',
    connected: '已连接',
    disconnected: '已断开',
    error: '错误'
  }
  return labels[status] || status
}

const formatDuration = (startTime: string) => {
  if (!startTime) return '-'
  const start = new Date(startTime).getTime()
  const now = Date.now()
  const duration = now - start

  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)

  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString('zh-CN')
}

const formatBytes = (bytes: number) => {
  if (!bytes) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i]
}

// 生命周期
onMounted(async () => {
  await refreshConnections()

  if (autoRefresh.value) {
    startAutoRefresh()
  }

  // 监听连接状态变化
  const unsubscribeStatusChanged = window.api.onConnectionStatusChanged((data) => {
    refreshConnections()
  })

  const unsubscribeEstablished = window.api.onConnectionEstablished((data) => {
    refreshConnections()
    message.success(`连接已建立: ${data.host}:${data.port}`)
  })

  const unsubscribeDisconnected = window.api.onConnectionDisconnected((data) => {
    refreshConnections()
    message.info(`连接已断开: ${data.host}:${data.port}`)
  })

  const unsubscribeError = window.api.onConnectionError((data) => {
    refreshConnections()
    message.error(`连接错误: ${data.message}`)
  })

  // 组件卸载时清理
  onUnmounted(() => {
    stopAutoRefresh()
    unsubscribeStatusChanged()
    unsubscribeEstablished()
    unsubscribeDisconnected()
    unsubscribeError()
  })
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="less" scoped>
.connection-management {
  padding: 24px;
  background: var(--bg-color);
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  .page-title {
    display: flex;
    align-items: center;
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);

    .title-icon {
      margin-right: 12px;
      font-size: 28px;
      color: var(--primary-color);
    }
  }

  .page-description {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 14px;
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.2s;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;

  &.console {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
  }

  &.rdp {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }

  &.active {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
  }

  &.error {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
  }
}

.stat-content {
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    line-height: 1;
  }

  .stat-label {
    font-size: 14px;
    color: var(--text-color-secondary);
    margin-top: 4px;
  }
}

.connections-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-color);
    }

    .section-actions {
      display: flex;
      align-items: center;
    }
  }
}

.connections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.connection-card {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.status-connected {
    border-left: 4px solid #52c41a;
  }

  &.status-connecting {
    border-left: 4px solid #faad14;
  }

  &.status-disconnected {
    border-left: 4px solid #d9d9d9;
  }

  &.status-error {
    border-left: 4px solid #ff4d4f;
  }
}

.connection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.connection-type {
  display: flex;
  align-items: center;

  .type-icon {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
  }

  .type-label {
    font-weight: 500;
    color: var(--text-color);
  }
}

.connection-status {
  display: flex;
  align-items: center;

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;

    &.status-connected {
      background: #52c41a;
    }

    &.status-connecting {
      background: #faad14;
    }

    &.status-disconnected {
      background: #d9d9d9;
    }

    &.status-error {
      background: #ff4d4f;
    }
  }

  .status-text {
    font-size: 12px;
    color: var(--text-color-secondary);
  }
}

.connection-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 13px;

  .info-label {
    color: var(--text-color-secondary);
  }

  .info-value {
    color: var(--text-color);
    font-weight: 500;
  }
}

.connection-actions {
  display: flex;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-color-secondary);

  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.3;
  }

  .empty-text {
    h3 {
      margin: 0 0 8px 0;
      color: var(--text-color);
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.connection-detail {
  .detail-section {
    margin-bottom: 24px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 8px;
    }
  }

  .detail-grid {
    display: grid;
    gap: 12px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color-light);

    .detail-label {
      color: var(--text-color-secondary);
      font-size: 14px;
    }

    .detail-value {
      color: var(--text-color);
      font-weight: 500;
      font-size: 14px;

      &.status {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;

        &.status-connected {
          background: rgba(82, 196, 26, 0.1);
          color: #52c41a;
        }

        &.status-connecting {
          background: rgba(250, 173, 20, 0.1);
          color: #faad14;
        }

        &.status-disconnected {
          background: rgba(217, 217, 217, 0.1);
          color: #8c8c8c;
        }

        &.status-error {
          background: rgba(255, 77, 79, 0.1);
          color: #ff4d4f;
        }
      }
    }
  }

  .config-display {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    overflow-x: auto;

    pre {
      margin: 0;
      color: var(--text-color);
    }
  }
}

.settings-content {
  .settings-section {
    margin-bottom: 32px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 8px;
    }
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color-light);

    .setting-label {
      color: var(--text-color);
      font-size: 14px;
    }
  }
}
</style>
