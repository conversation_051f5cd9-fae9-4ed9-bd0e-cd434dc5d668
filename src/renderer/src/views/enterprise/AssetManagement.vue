<template>
  <div class="asset-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回企业资源管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Package class="title-icon" />
            固定资产管理
          </h1>
          <p class="page-description">管理企业固定资产、设备清单和资产状态跟踪</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          :disabled="loading"
          @click="refreshAssets"
        >
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新
        </button>
        <button
          class="btn btn-secondary"
          @click="exportAssets"
        >
          <Download class="btn-icon" />
          导出
        </button>
        <button
          class="btn btn-secondary"
          @click="showImportDialog = true"
        >
          <Upload class="btn-icon" />
          导入
        </button>
        <button
          class="btn btn-secondary"
          @click="showReportDialog = true"
        >
          <BarChart3 class="btn-icon" />
          报表
        </button>
        <button
          v-if="selectedAssets.length > 0"
          class="btn btn-danger"
          @click="batchDelete"
        >
          <Trash2 class="btn-icon" />
          批量删除 ({{ selectedAssets.length }})
        </button>
        <button
          class="btn btn-primary"
          @click="showAddAssetDialog = true"
        >
          <Plus class="btn-icon" />
          新增资产
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <Package class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ totalAssets }}</div>
          <div class="stat-label">总资产数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon active">
          <CheckCircle class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ activeAssets }}</div>
          <div class="stat-label">在用资产</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon maintenance">
          <Wrench class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ maintenanceAssets }}</div>
          <div class="stat-label">维护中</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon retired">
          <XCircle class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ retiredAssets }}</div>
          <div class="stat-label">已报废</div>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="search-box">
        <div class="search-input-wrapper">
          <Search class="search-icon" />
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            placeholder="搜索资产名称、编号或型号..."
            @input="handleSearch"
          />
          <button
            v-if="searchQuery"
            class="clear-button"
            @click="clearSearch"
          >
            <X class="clear-icon" />
          </button>
        </div>
      </div>
      <div class="filter-controls">
        <select
          v-model="selectedCategory"
          class="filter-select"
          @change="handleFilterChange"
        >
          <option value="">全部分类</option>
          <option value="computer">计算机设备</option>
          <option value="network">网络设备</option>
          <option value="server">服务器</option>
          <option value="storage">存储设备</option>
          <option value="printer">打印设备</option>
          <option value="furniture">办公家具</option>
          <option value="other">其他设备</option>
        </select>
        <select
          v-model="selectedStatus"
          class="filter-select"
          @change="handleFilterChange"
        >
          <option value="">全部状态</option>
          <option value="active">在用</option>
          <option value="maintenance">维护中</option>
          <option value="idle">闲置</option>
          <option value="retired">已报废</option>
        </select>
      </div>
    </div>

    <!-- 资产列表 -->
    <div class="asset-table-container">
      <table class="asset-table">
        <thead>
          <tr>
            <th class="col-checkbox">
              <input
                type="checkbox"
                :checked="selectedAssets.length === filteredAssets.length && filteredAssets.length > 0"
                :indeterminate="selectedAssets.length > 0 && selectedAssets.length < filteredAssets.length"
                @change="toggleSelectAll"
              />
            </th>
            <th class="col-asset-number">资产编号</th>
            <th class="col-asset-name">资产名称</th>
            <th class="col-category">分类</th>
            <th class="col-model">型号</th>
            <th class="col-status">状态</th>
            <th class="col-user">使用人</th>
            <th class="col-date">购买日期</th>
            <th class="col-actions">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="asset in filteredAssets"
            :key="asset.id"
            class="asset-row"
            :class="{ selected: selectedAssets.includes(asset.id) }"
          >
            <td class="col-checkbox">
              <input
                v-model="selectedAssets"
                type="checkbox"
                :value="asset.id"
              />
            </td>
            <td class="col-asset-number">
              <span class="asset-number">{{ asset.assetNumber }}</span>
            </td>
            <td class="col-asset-name">
              <span
                class="asset-name"
                :title="asset.name"
                >{{ asset.name }}</span
              >
            </td>
            <td class="col-category">
              <span
                class="category-tag"
                :class="asset.category"
              >
                {{ getCategoryLabel(asset.category) }}
              </span>
            </td>
            <td class="col-model">
              <span
                class="model-text"
                :title="asset.model"
                >{{ asset.model }}</span
              >
            </td>
            <td class="col-status">
              <span
                class="status-badge"
                :class="asset.status"
              >
                {{ getStatusLabel(asset.status) }}
              </span>
            </td>
            <td class="col-user">
              <span class="user-text">{{ asset.assignedTo || '-' }}</span>
            </td>
            <td class="col-date">
              <span class="date-text">{{ formatDate(asset.purchaseDate) }}</span>
            </td>
            <td class="col-actions">
              <div class="action-buttons">
                <button
                  class="action-btn edit-btn"
                  title="编辑"
                  @click.stop="editAsset(asset)"
                >
                  编辑
                </button>
                <button
                  class="action-btn view-btn"
                  title="详情"
                  @click.stop="viewAssetDetails(asset)"
                >
                  详情
                </button>
                <button
                  class="action-btn delete-btn"
                  title="删除"
                  @click.stop="deleteAsset(asset)"
                >
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 导入资产对话框 -->
    <teleport to="body">
      <div
        v-if="showImportDialog"
        class="modal-overlay"
        @click="closeImportDialog"
      >
        <div
          class="modal-content"
          @click.stop
        >
          <div class="modal-header">
            <h2 class="modal-title">导入资产数据</h2>
            <button
              class="modal-close"
              @click="closeImportDialog"
            >
              <X class="close-icon" />
            </button>
          </div>
          <div class="modal-body">
            <div class="form-group">
              <label class="form-label">选择文件</label>
              <input
                type="file"
                accept=".csv,.xlsx,.xls"
                class="form-input"
                @change="handleFileSelect"
              />
              <p class="text-sm text-gray-600 mt-2">
                支持 CSV、Excel 格式文件。请确保文件包含：资产编号、资产名称、分类、状态、使用人、购买价格、购买日期等字段。
              </p>
            </div>
            <div
              v-if="importPreview.length > 0"
              class="import-preview"
            >
              <h4>预览数据 (前5条)</h4>
              <div class="preview-table">
                <div class="preview-header">
                  <span>资产编号</span>
                  <span>资产名称</span>
                  <span>分类</span>
                  <span>状态</span>
                </div>
                <div
                  v-for="(item, index) in importPreview.slice(0, 5)"
                  :key="index"
                  class="preview-row"
                >
                  <span>{{ item.assetNumber }}</span>
                  <span>{{ item.name }}</span>
                  <span>{{ item.category }}</span>
                  <span>{{ item.status }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              @click="closeImportDialog"
              >取消</button
            >
            <button
              :disabled="importPreview.length === 0 || importing"
              class="btn btn-primary"
              @click="importAssets"
            >
              <Loader2
                v-if="importing"
                class="btn-icon animate-spin"
              />
              {{ importing ? '导入中...' : `导入 ${importPreview.length} 条数据` }}
            </button>
          </div>
        </div>
      </div>
    </teleport>

    <!-- 新增/编辑资产对话框 -->
    <teleport to="body">
      <div
        v-if="showAddAssetDialog || editingAsset"
        class="modal-overlay"
        @click="closeAssetDialog"
      >
        <div
          class="modal-content"
          @click.stop
        >
          <div class="modal-header">
            <h2 class="modal-title">
              {{ editingAsset ? '编辑资产' : '新增资产' }}
            </h2>
            <button
              class="modal-close"
              @click="closeAssetDialog"
            >
              <X class="close-icon" />
            </button>
          </div>

          <div class="modal-body">
            <form @submit.prevent="saveAsset">
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">资产编号</label>
                  <input
                    v-model="assetForm.assetNumber"
                    type="text"
                    class="form-input"
                    placeholder="请输入资产编号"
                    required
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">资产名称</label>
                  <input
                    v-model="assetForm.name"
                    type="text"
                    class="form-input"
                    placeholder="请输入资产名称"
                    required
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">分类</label>
                  <select
                    v-model="assetForm.category"
                    class="form-select"
                    required
                  >
                    <option value="">请选择分类</option>
                    <option value="computer">计算机设备</option>
                    <option value="network">网络设备</option>
                    <option value="server">服务器</option>
                    <option value="storage">存储设备</option>
                    <option value="printer">打印设备</option>
                    <option value="furniture">办公家具</option>
                    <option value="other">其他设备</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="form-label">型号</label>
                  <input
                    v-model="assetForm.model"
                    type="text"
                    class="form-input"
                    placeholder="请输入设备型号"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">状态</label>
                  <select
                    v-model="assetForm.status"
                    class="form-select"
                    required
                  >
                    <option value="active">在用</option>
                    <option value="maintenance">维护中</option>
                    <option value="idle">闲置</option>
                    <option value="retired">已报废</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="form-label">使用人</label>
                  <input
                    v-model="assetForm.assignedTo"
                    type="text"
                    class="form-input"
                    placeholder="请输入使用人姓名"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">购买日期</label>
                  <input
                    v-model="assetForm.purchaseDate"
                    type="date"
                    class="form-input"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">购买价格</label>
                  <input
                    v-model="assetForm.purchasePrice"
                    type="number"
                    class="form-input"
                    placeholder="请输入购买价格"
                    step="0.01"
                  />
                </div>
              </div>

              <!-- 硬件配置信息 - 仅在选择计算机设备或网络设备时显示 -->
              <div
                v-if="showHardwareConfig"
                class="hardware-config-section"
              >
                <h4 class="section-title">硬件配置信息</h4>

                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">CPU/处理器</label>
                    <input
                      v-model="assetForm.cpu"
                      type="text"
                      class="form-input"
                      placeholder="请输入CPU型号"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">内存</label>
                    <input
                      v-model="assetForm.memory"
                      type="text"
                      class="form-input"
                      placeholder="请输入内存大小"
                    />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">存储</label>
                    <input
                      v-model="assetForm.storage"
                      type="text"
                      class="form-input"
                      placeholder="请输入存储容量"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">网卡/网络接口</label>
                    <input
                      v-model="assetForm.networkInterface"
                      type="text"
                      class="form-input"
                      placeholder="请输入网络接口信息"
                    />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">操作系统</label>
                    <input
                      v-model="assetForm.operatingSystem"
                      type="text"
                      class="form-input"
                      placeholder="请输入操作系统"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">IP地址</label>
                    <input
                      v-model="assetForm.ipAddress"
                      type="text"
                      class="form-input"
                      placeholder="请输入IP地址"
                    />
                  </div>
                </div>

                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">MAC地址</label>
                    <input
                      v-model="assetForm.macAddress"
                      type="text"
                      class="form-input"
                      placeholder="请输入MAC地址"
                    />
                  </div>
                  <div class="form-group">
                    <label class="form-label">端口数量</label>
                    <input
                      v-model="assetForm.portCount"
                      type="number"
                      class="form-input"
                      placeholder="请输入端口数量"
                    />
                  </div>
                </div>
              </div>

              <div class="form-group full-width">
                <label class="form-label">备注</label>
                <textarea
                  v-model="assetForm.description"
                  class="form-textarea"
                  placeholder="请输入备注信息"
                  rows="3"
                ></textarea>
              </div>
            </form>
          </div>

          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              @click="closeAssetDialog"
            >
              取消
            </button>
            <button
              class="btn btn-primary"
              :disabled="saving"
              @click="saveAsset"
            >
              <Loader2
                v-if="saving"
                class="btn-icon animate-spin"
              />
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </div>
      </div>
    </teleport>

    <!-- 资产报表对话框 -->
    <teleport to="body">
      <div
        v-if="showReportDialog"
        class="modal-overlay"
        @click="closeReportDialog"
      >
        <div
          class="modal-content"
          @click.stop
        >
          <div class="modal-header">
            <h2 class="modal-title">资产报表</h2>
            <button
              class="modal-close"
              @click="closeReportDialog"
            >
              <X class="close-icon" />
            </button>
          </div>
          <div class="modal-body">
            <div class="report-section">
              <h3>资产统计概览</h3>
              <div class="report-stats">
                <div class="report-stat-item">
                  <span class="stat-name">总资产数量：</span>
                  <span class="stat-value">{{ totalAssets }}</span>
                </div>
                <div class="report-stat-item">
                  <span class="stat-name">在用资产：</span>
                  <span class="stat-value">{{ activeAssets }}</span>
                </div>
                <div class="report-stat-item">
                  <span class="stat-name">维护中资产：</span>
                  <span class="stat-value">{{ maintenanceAssets }}</span>
                </div>
                <div class="report-stat-item">
                  <span class="stat-name">已报废资产：</span>
                  <span class="stat-value">{{ retiredAssets }}</span>
                </div>
              </div>
            </div>

            <div class="report-section">
              <h3>分类统计</h3>
              <div class="category-stats">
                <div
                  v-for="(count, category) in categoryStats"
                  :key="category"
                  class="category-stat-item"
                >
                  <span class="category-name">{{ getCategoryLabel(category) }}：</span>
                  <span class="category-count">{{ count }}</span>
                </div>
              </div>
            </div>

            <div class="report-section">
              <h3>资产价值统计</h3>
              <div class="value-stats">
                <div class="value-stat-item">
                  <span class="stat-name">总资产价值：</span>
                  <span class="stat-value">¥{{ totalValue.toLocaleString() }}</span>
                </div>
                <div class="value-stat-item">
                  <span class="stat-name">在用资产价值：</span>
                  <span class="stat-value">¥{{ activeValue.toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              @click="exportReport"
            >
              <Download class="btn-icon" />
              导出报表
            </button>
            <button
              class="btn btn-primary"
              @click="closeReportDialog"
              >关闭</button
            >
          </div>
        </div>
      </div>
    </teleport>

    <!-- 资产详情对话框 -->
    <teleport to="body">
      <div
        v-if="showAssetDetailDialog && selectedAssetForView"
        class="modal-overlay"
        @click="closeAssetDetailDialog"
      >
        <div
          class="modal-content"
          @click.stop
        >
          <div class="modal-header">
            <h2 class="modal-title">资产详情</h2>
            <button
              class="modal-close"
              @click="closeAssetDetailDialog"
            >
              <X class="close-icon" />
            </button>
          </div>
          <div class="modal-body">
            <div class="detail-grid">
              <div class="detail-item">
                <label class="detail-label">资产编号</label>
                <span class="detail-value">{{ selectedAssetForView.assetNumber }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">资产名称</label>
                <span class="detail-value">{{ selectedAssetForView.name }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">分类</label>
                <span class="detail-value">{{ getCategoryLabel(selectedAssetForView.category) }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">型号</label>
                <span class="detail-value">{{ selectedAssetForView.model || '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">状态</label>
                <span class="detail-value">
                  <span :class="['status-badge', selectedAssetForView.status]">
                    {{ getStatusLabel(selectedAssetForView.status) }}
                  </span>
                </span>
              </div>
              <div class="detail-item">
                <label class="detail-label">使用人</label>
                <span class="detail-value">{{ selectedAssetForView.assignedTo || '-' }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">购买日期</label>
                <span class="detail-value">{{ formatDate(selectedAssetForView.purchaseDate) }}</span>
              </div>
              <div class="detail-item">
                <label class="detail-label">购买价格</label>
                <span class="detail-value">¥{{ selectedAssetForView.purchasePrice || 0 }}</span>
              </div>
              <div class="detail-item full-width">
                <label class="detail-label">备注</label>
                <span class="detail-value">{{ selectedAssetForView.description || '-' }}</span>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button
              class="btn btn-secondary"
              @click="closeAssetDetailDialog"
            >
              关闭
            </button>
            <button
              class="btn btn-primary"
              @click="editAssetAndClose(selectedAssetForView)"
            >
              编辑
            </button>
          </div>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAssetManagementStore, categoryLabels, statusLabels, type Asset } from '@/store/assetManagementStore'
import unifiedDataService, { type AssetResource } from '../../services/unifiedEnterpriseDataService'
import dataMigrationService from '../../services/dataMigrationService'
import eventBus from '../../utils/eventBus'
import {
  Package,
  RefreshCw,
  Plus,
  CheckCircle,
  Wrench,
  XCircle,
  Search,
  Edit,
  Eye,
  Trash2,
  X,
  Loader2,
  Download,
  Upload,
  BarChart3,
  ArrowLeft
} from 'lucide-vue-next'

// 路由实例
const router = useRouter()

// 使用资产管理store（保持兼容性）
const assetStore = useAssetManagementStore()

// 使用统一数据服务
const useUnifiedAssets = ref(true) // 控制是否使用统一服务

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedStatus = ref('')
const selectedAssets = ref([])
const showAddAssetDialog = ref(false)
const showImportDialog = ref(false)
const showReportDialog = ref(false)
const showAssetDetailDialog = ref(false)
const editingAsset = ref(null)
const selectedAssetForView = ref(null)
const importing = ref(false)
const importPreview = ref([])

// 资产表单数据
const assetForm = ref({
  assetNumber: '',
  name: '',
  category: '',
  model: '',
  status: 'active',
  assignedTo: '',
  purchaseDate: '',
  purchasePrice: '',
  description: '',
  // 硬件配置字段
  cpu: '',
  memory: '',
  storage: '',
  networkInterface: '',
  operatingSystem: '',
  ipAddress: '',
  macAddress: '',
  portCount: ''
})

// 使用store中的数据和计算属性（支持统一数据服务）
const assets = computed(() => {
  if (useUnifiedAssets.value) {
    return unifiedDataService.getAssets().map((asset) => ({
      id: parseInt(asset.id.toString()),
      assetNumber: asset.assetNumber,
      name: asset.name,
      category: asset.category,
      model: asset.model || '',
      status: mapUnifiedStatusToStore(asset.status),
      assignedTo: asset.assignedTo || '',
      purchaseDate: asset.purchaseDate || '',
      purchasePrice: asset.purchasePrice || 0,
      description: asset.description || '',
      cpu: asset.cpu,
      memory: asset.memory,
      storage: asset.storage,
      networkInterface: asset.networkInterface,
      operatingSystem: asset.operatingSystem,
      ipAddress: asset.ipAddress,
      macAddress: asset.macAddress,
      portCount: asset.portCount,
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt
    }))
  }
  return assetStore.assets
})

const totalAssets = computed(() => {
  if (useUnifiedAssets.value) {
    return unifiedDataService.getAssets().length
  }
  return assetStore.totalAssets
})

const activeAssets = computed(() => {
  if (useUnifiedAssets.value) {
    const stats = unifiedDataService.getAssetStats()
    return stats.active
  }
  return assetStore.assetsByStatus.active.length
})

const maintenanceAssets = computed(() => {
  if (useUnifiedAssets.value) {
    const stats = unifiedDataService.getAssetStats()
    return stats.maintenance
  }
  return assetStore.assetsByStatus.maintenance.length
})

const retiredAssets = computed(() => {
  if (useUnifiedAssets.value) {
    const stats = unifiedDataService.getAssetStats()
    return stats.retired
  }
  return assetStore.assetsByStatus.retired.length
})

// 状态映射函数
const mapUnifiedStatusToStore = (unifiedStatus: string): string => {
  const statusMap: Record<string, string> = {
    online: 'active',
    maintenance: 'maintenance',
    offline: 'idle',
    error: 'retired',
    normal: 'active'
  }
  return statusMap[unifiedStatus] || 'active'
}

const mapStoreStatusToUnified = (storeStatus: string): string => {
  const statusMap: Record<string, string> = {
    active: 'online',
    maintenance: 'maintenance',
    idle: 'offline',
    retired: 'error'
  }
  return statusMap[storeStatus] || 'normal'
}

const filteredAssets = computed(() => {
  let filtered = assets.value

  if (searchQuery.value) {
    filtered = assetStore.searchAssets(searchQuery.value)
  }

  if (selectedCategory.value || selectedStatus.value) {
    filtered = assetStore.filterAssets({
      category: selectedCategory.value || undefined,
      status: selectedStatus.value || undefined
    })
  }

  // 如果同时有搜索和筛选，需要组合处理
  if (searchQuery.value && (selectedCategory.value || selectedStatus.value)) {
    const searchResults = assetStore.searchAssets(searchQuery.value)
    filtered = searchResults.filter((asset) => {
      if (selectedCategory.value && asset.category !== selectedCategory.value) return false
      if (selectedStatus.value && asset.status !== selectedStatus.value) return false
      return true
    })
  }

  return filtered
})

// 是否显示硬件配置字段
const showHardwareConfig = computed(() => {
  return assetForm.value.category === 'computer' || assetForm.value.category === 'network'
})

// 方法
const refreshAssets = async () => {
  loading.value = true
  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 1000))
  loading.value = false
}

const getCategoryLabel = (category: string) => {
  return categoryLabels[category as keyof typeof categoryLabels] || category
}

const getStatusLabel = (status: string) => {
  return statusLabels[status as keyof typeof statusLabels] || status
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const editAsset = (asset) => {
  console.log('editAsset 被调用:', asset)
  editingAsset.value = asset
  assetForm.value = { ...asset }
  showAddAssetDialog.value = true
  console.log('编辑资产对话框状态:', showAddAssetDialog.value)
}

const editAssetAndClose = (asset) => {
  editAsset(asset)
  closeAssetDetailDialog()
}

const viewAssetDetails = (asset) => {
  console.log('viewAssetDetails 被调用:', asset)
  selectedAssetForView.value = asset
  showAssetDetailDialog.value = true
  console.log('查看资产详情对话框状态:', showAssetDetailDialog.value)
}

const deleteAsset = async (asset: Asset) => {
  console.log('deleteAsset 被调用:', asset)
  if (confirm(`确定要删除资产 "${asset.name}" 吗？`)) {
    const deletedAsset = assetStore.deleteAsset(asset.id)
    if (deletedAsset) {
      console.log('资产已删除:', asset.name)
    }
  }
}

const closeAssetDialog = () => {
  showAddAssetDialog.value = false
  editingAsset.value = null
  resetAssetForm()
}

const closeAssetDetailDialog = () => {
  showAssetDetailDialog.value = false
  selectedAssetForView.value = null
}

const resetAssetForm = () => {
  assetForm.value = {
    id: '',
    assetNumber: '',
    name: '',
    category: '',
    model: '',
    status: 'active',
    user: '',
    purchaseDate: '',
    purchasePrice: '',
    description: '',
    // 硬件配置字段
    cpu: '',
    memory: '',
    storage: '',
    networkInterface: '',
    operatingSystem: '',
    ipAddress: '',
    macAddress: '',
    portCount: ''
  }
}

const toggleSelectAll = () => {
  if (selectedAssets.value.length === filteredAssets.value.length) {
    selectedAssets.value = []
  } else {
    selectedAssets.value = filteredAssets.value.map((asset) => asset.id)
  }
}

const batchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedAssets.value.length} 个资产吗？`)) {
    assetStore.batchDeleteAssets(selectedAssets.value)
    selectedAssets.value = []
  }
}

const exportAssets = () => {
  // 实现导出功能
  const csvContent = generateCSV(filteredAssets.value)
  downloadCSV(csvContent, 'assets.csv')
}

const generateCSV = (data) => {
  const headers = ['资产编号', '资产名称', '分类', '型号', '状态', '使用人', '购买日期', '购买价格']
  const rows = data.map((asset) => [
    asset.assetNumber,
    asset.name,
    getCategoryLabel(asset.category),
    asset.model,
    getStatusLabel(asset.status),
    asset.assignedTo || '',
    formatDate(asset.purchaseDate),
    asset.purchasePrice
  ])

  return [headers, ...rows].map((row) => row.join(',')).join('\n')
}

const downloadCSV = (content, filename) => {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = filename
  link.click()
}

const closeImportDialog = () => {
  showImportDialog.value = false
  importPreview.value = []
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const text = e.target.result as string
      const lines = text.split('\n')
      const headers = lines[0].split(',')

      const preview = lines
        .slice(1, 6)
        .map((line) => {
          const values = line.split(',')
          return {
            assetNumber: values[0] || '',
            name: values[1] || '',
            category: values[2] || '',
            status: values[3] || ''
          }
        })
        .filter((item) => item.assetNumber)

      importPreview.value = preview
    } catch (error) {
      console.error('文件解析失败:', error)
    }
  }
  reader.readAsText(file)
}

const importAssets = async () => {
  importing.value = true

  try {
    // 模拟导入过程
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // 这里应该是实际的导入逻辑
    const newAssets = importPreview.value.map((item) => ({
      ...item,
      id: Date.now() + Math.random(),
      model: '',
      assignedTo: '',
      purchaseDate: '',
      purchasePrice: 0,
      description: ''
    }))

    assets.value.push(...newAssets)
    closeImportDialog()
  } catch (error) {
    console.error('导入失败:', error)
  } finally {
    importing.value = false
  }
}

const saveAsset = async () => {
  saving.value = true

  try {
    const assetData = {
      ...assetForm.value,
      purchasePrice: parseFloat(assetForm.value.purchasePrice) || 0
    }

    if (useUnifiedAssets.value) {
      // 使用统一数据服务
      if (editingAsset.value) {
        // 编辑现有资产
        const unifiedAssetData = {
          name: assetData.name,
          assetNumber: assetData.assetNumber,
          category: assetData.category,
          model: assetData.model,
          status: mapStoreStatusToUnified(assetData.status),
          assignedTo: assetData.assignedTo,
          purchaseDate: assetData.purchaseDate,
          purchasePrice: assetData.purchasePrice,
          description: assetData.description,
          cpu: assetData.cpu,
          memory: assetData.memory,
          storage: assetData.storage,
          networkInterface: assetData.networkInterface,
          operatingSystem: assetData.operatingSystem,
          ipAddress: assetData.ipAddress,
          macAddress: assetData.macAddress,
          portCount: assetData.portCount
        }
        unifiedDataService.updateAsset(editingAsset.value.id, unifiedAssetData)
      } else {
        // 新增资产
        const unifiedAssetData = {
          name: assetData.name,
          type: 'asset' as const,
          status: mapStoreStatusToUnified(assetData.status),
          assetNumber: assetData.assetNumber,
          category: assetData.category,
          model: assetData.model,
          assignedTo: assetData.assignedTo,
          purchaseDate: assetData.purchaseDate,
          purchasePrice: assetData.purchasePrice,
          description: assetData.description,
          cpu: assetData.cpu,
          memory: assetData.memory,
          storage: assetData.storage,
          networkInterface: assetData.networkInterface,
          operatingSystem: assetData.operatingSystem,
          ipAddress: assetData.ipAddress,
          macAddress: assetData.macAddress,
          portCount: assetData.portCount,
          tags: []
        }
        unifiedDataService.addAsset(unifiedAssetData)
      }
    } else {
      // 使用原有store
      if (editingAsset.value) {
        assetStore.updateAsset(editingAsset.value.id, assetData)
      } else {
        assetStore.addAsset(assetData)
      }
    }

    closeAssetDialog()
  } catch (error) {
    console.error('保存资产失败:', error)
  } finally {
    saving.value = false
  }
}

const closeReportDialog = () => {
  showReportDialog.value = false
}

const categoryStats = computed(() => {
  const stats: Record<string, number> = {}
  Object.entries(assetStore.assetsByCategory).forEach(([category, assets]) => {
    stats[category] = assets.length
  })
  return stats
})

const totalValue = computed(() => assetStore.totalValue)
const activeValue = computed(() => assetStore.activeValue)

const exportReport = () => {
  const reportData = {
    统计时间: new Date().toLocaleString('zh-CN'),
    总资产数量: totalAssets.value,
    在用资产: activeAssets.value,
    维护中资产: maintenanceAssets.value,
    已报废资产: retiredAssets.value,
    总资产价值: totalValue.value,
    在用资产价值: activeValue.value,
    分类统计: Object.entries(categoryStats.value).map(([category, count]) => ({
      分类: getCategoryLabel(category),
      数量: count
    }))
  }

  const jsonStr = JSON.stringify(reportData, null, 2)
  const blob = new Blob([jsonStr], { type: 'application/json' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `资产报表_${new Date().toISOString().split('T')[0]}.json`
  link.click()
}

// 搜索方法
const handleSearch = () => {
  // 触发响应式更新，确保搜索结果实时显示
  // 搜索逻辑在 filteredAssets 计算属性中处理
  console.log('搜索资产:', searchQuery.value)
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  console.log('清除搜索')
}

// 筛选器变化处理
const handleFilterChange = () => {
  // 触发响应式更新，确保筛选结果实时显示
  // 筛选逻辑在 filteredAssets 计算属性中处理
  console.log('筛选条件变化:', { category: selectedCategory.value, status: selectedStatus.value })
}

// 统一的按钮点击处理函数
const handleButtonClick = (event, asset, action) => {
  console.log('handleButtonClick 被调用:', { event, asset, action })

  // 阻止事件冒泡和默认行为
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  try {
    // 使用 nextTick 确保在Vue更新周期后执行
    setTimeout(() => {
      switch (action) {
        case 'edit':
          console.log('执行编辑操作')
          editAsset(asset)
          break
        case 'view':
          console.log('执行查看操作')
          viewAssetDetails(asset)
          break
        case 'delete':
          console.log('执行删除操作')
          deleteAsset(asset)
          break
        default:
          console.warn('未知的操作类型:', action)
      }
    }, 10) // 稍微增加延迟
  } catch (error) {
    console.error('处理按钮点击时出错:', error)
  }
}

// 返回企业资源管理页面
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

onMounted(async () => {
  // 初始化资产管理store
  assetStore.initialize()

  if (useUnifiedAssets.value) {
    // 检查是否需要迁移数据
    const hasUnifiedData = unifiedDataService.getAssets().length > 0
    const hasStoreData = localStorage.getItem('chaterm_asset_management')

    if (!hasUnifiedData && hasStoreData) {
      console.log('检测到需要迁移资产数据到统一服务...')
      try {
        const migrationResult = await dataMigrationService.migrateAssetManagementStoreData()
        if (migrationResult.success) {
          console.log(`✅ 资产数据迁移成功: ${migrationResult.migratedCount} 条数据`)
        } else {
          console.error('❌ 资产数据迁移失败:', migrationResult.errors)
          // 迁移失败时回退到原有store
          useUnifiedAssets.value = false
        }
      } catch (error) {
        console.error('迁移过程出错:', error)
        useUnifiedAssets.value = false
      }
    }
  }

  refreshAssets()

  // 添加原生事件监听器作为备用方案
  setTimeout(() => {
    setupNativeEventListeners()
    setupGlobalHandlers()
    forceEnableClicks()
  }, 100)
})

// 设置原生事件监听器
const setupNativeEventListeners = () => {
  console.log('设置原生事件监听器')

  // 使用事件委托监听所有按钮点击
  document.addEventListener(
    'click',
    (event) => {
      const target = event.target as HTMLElement

      // 检查是否是我们的操作按钮
      if (target && target.classList.contains('btn-icon-small')) {
        const assetId = target.getAttribute('data-asset-id')
        const action = target.getAttribute('data-action')

        if (assetId && action) {
          console.log('原生事件监听器捕获到点击:', { assetId, action })

          // 找到对应的资产
          const asset = assets.value.find((a) => a.id.toString() === assetId)
          if (asset) {
            handleButtonClick(event, asset, action)
          }
        }
      }
    },
    true
  ) // 使用捕获阶段
}

// 设置全局处理函数
const setupGlobalHandlers = () => {
  console.log('设置全局处理函数')

  // 在window对象上设置全局函数
  ;(window as any).handleAssetAction = (action: string, assetId: number) => {
    console.log('全局处理函数被调用:', { action, assetId })

    const asset = assets.value.find((a) => a.id === assetId)
    if (asset) {
      switch (action) {
        case 'edit':
          editAsset(asset)
          break
        case 'view':
          viewAssetDetails(asset)
          break
        case 'delete':
          deleteAsset(asset)
          break
      }
    }
  }
}

// 强制启用点击事件 - Electron修复
const forceEnableClicks = () => {
  console.log('强制启用点击事件')

  // 移除body的user-select限制
  document.body.style.userSelect = 'auto'
  ;(document.body.style as any).webkitUserSelect = 'auto'

  // 强制设置所有按钮的样式
  const buttons = document.querySelectorAll('button, .btn, .btn-icon-small')
  buttons.forEach((button) => {
    const element = button as HTMLElement
    element.style.pointerEvents = 'auto'
    ;(element.style as any).webkitAppRegion = 'no-drag'
    element.style.userSelect = 'auto'
    ;(element.style as any).webkitUserSelect = 'auto'
    element.style.cursor = 'pointer'
    element.style.zIndex = '999'
  })

  // 特别处理调试按钮
  const debugButtons = document.querySelectorAll('.debug-info button')
  debugButtons.forEach((button) => {
    const element = button as HTMLElement
    element.style.pointerEvents = 'auto'
    ;(element.style as any).webkitAppRegion = 'no-drag'
    element.style.userSelect = 'auto'
    ;(element.style as any).webkitUserSelect = 'auto'
    element.style.cursor = 'pointer'
    element.style.zIndex = '1000'

    // 添加额外的事件监听器
    element.addEventListener('mousedown', (e) => {
      console.log('mousedown事件被触发:', e)
      e.stopPropagation()
    })

    element.addEventListener('mouseup', (e) => {
      console.log('mouseup事件被触发:', e)
      e.stopPropagation()
    })
  })

  // 强制处理弹窗内的按钮
  setTimeout(() => {
    const modalButtons = document.querySelectorAll('.modal-overlay button, .modal-content button')
    modalButtons.forEach((button) => {
      const element = button as HTMLElement
      element.style.pointerEvents = 'auto'
      ;(element.style as any).webkitAppRegion = 'no-drag'
      element.style.cursor = 'pointer'
      element.style.zIndex = '1001'
    })
  }, 500)
}
</script>

<style scoped>
/* 基础布局 */
.asset-management {
  height: 100%;
  max-height: calc(100vh - 60px);
  background: #f8fafc;
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

/* 页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10;
  /* 启用拖拽 */
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: #3b82f6;
}

.page-description {
  color: #64748b;
  margin: 0;
  font-size: 14px;
}

/* 返回按钮 */
.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-back:hover {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-back:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}

.btn-back .btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.btn-back .btn-text {
  white-space: nowrap;
}

.header-actions {
  display: flex;
  gap: 12px;
  position: relative;
  z-index: 11;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
}

.stat-icon.active {
  background: #dcfce7;
}

.stat-icon.maintenance {
  background: #fef3c7;
}

.stat-icon.retired {
  background: #fee2e2;
}

.stat-icon .icon {
  width: 24px;
  height: 24px;
  color: #64748b;
}

.stat-icon.active .icon {
  color: #16a34a;
}

.stat-icon.maintenance .icon {
  color: #d97706;
}

.stat-icon.retired .icon {
  color: #dc2626;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
}

/* 筛选区域 */
.filter-section {
  padding: 0 24px 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #64748b;
  z-index: 1;
}

.clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.clear-button:hover {
  background-color: #f1f5f9;
}

.clear-icon {
  width: 14px;
  height: 14px;
  color: #64748b;
}

.search-input {
  width: 100%;
  padding: 8px 36px 8px 36px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  background: var(--bg-color-secondary, #ffffff);
  color: var(--text-color, #374151);
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: var(--input-focus-border, #3b82f6);
  box-shadow: var(--input-focus-shadow, 0 0 0 3px rgba(59, 130, 246, 0.1));
}

.search-input::placeholder {
  color: var(--text-color-tertiary, #9ca3af);
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

/* 资产表格容器 */
.asset-table-container {
  margin: 0 24px 24px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 表格基础样式 */
.asset-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

/* 列宽定义 */
.col-checkbox {
  width: 50px;
}
.col-asset-number {
  width: 120px;
}
.col-asset-name {
  width: 240px;
}
.col-category {
  width: 120px;
}
.col-model {
  width: 140px;
}
.col-status {
  width: 90px;
}
.col-user {
  width: 100px;
}
.col-date {
  width: 120px;
}
.col-actions {
  width: 100px;
}

/* 表头样式 */
.asset-table thead {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.asset-table th {
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 13px;
  border-right: 1px solid #e2e8f0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.asset-table th:last-child {
  border-right: none;
}

/* 表格主体样式 */
.asset-row {
  border-bottom: 1px solid #f1f5f9;
  transition: background-color 0.2s ease;
}

.asset-row:hover {
  background: #f8fafc;
}

.asset-row.selected {
  background: #eff6ff;
}

.asset-table td {
  padding: 10px 8px;
  font-size: 13px;
  color: #374151;
  border-right: 1px solid #f1f5f9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}

.asset-table td:last-child {
  border-right: none;
}

/* 内容样式优化 */
.asset-number {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #1f2937;
}

.asset-name {
  font-weight: 500;
  color: #111827;
}

.model-text {
  color: #6b7280;
  font-size: 12px;
}

.user-text {
  color: #374151;
}

.date-text {
  color: #6b7280;
  font-size: 12px;
}

.category-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  background: #f1f5f9;
  color: #64748b;
}

.category-tag.computer {
  background: #dbeafe;
  color: #1d4ed8;
}

.category-tag.network {
  background: #dcfce7;
  color: #16a34a;
}

.category-tag.server {
  background: #fef3c7;
  color: #d97706;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #dcfce7;
  color: #16a34a;
}

.status-badge.maintenance {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.idle {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge.retired {
  background: #fee2e2;
  color: #dc2626;
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  gap: 2px;
  justify-content: center;
  align-items: center;
  padding: 0;
}

/* 新的操作按钮样式 */
.action-btn {
  padding: 4px 6px;
  border: none;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 28px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.edit-btn {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.edit-btn:hover {
  background: #e0f2fe;
  border-color: #7dd3fc;
}

.view-btn {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.view-btn:hover {
  background: #dcfce7;
  border-color: #86efac;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.delete-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.btn-icon-small {
  min-width: 22px;
  height: 18px;
  padding: 1px 2px;
  border: none;
  background: #f8fafc;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  z-index: 1;
  pointer-events: auto;
  font-size: 8px;
  color: #374151;
  white-space: nowrap;
}

.btn-icon-small:hover {
  background: #e2e8f0;
}

.btn-icon-small.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

.btn-icon-small .icon {
  width: 14px;
  height: 14px;
}

/* 按钮样式 */
.btn {
  padding: 10px 18px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  border: none !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  transition: all 0.2s !important;
  position: relative !important;
  z-index: 100005 !important;
  pointer-events: auto !important;
  -webkit-app-region: no-drag !important;
}

.btn-primary {
  background: #3b82f6 !important;
  color: #ffffff !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3) !important;
}

.btn-primary:hover {
  background: #2563eb !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4) !important;
}

.btn-secondary {
  background: #f8fafc !important;
  color: #374151 !important;
  border: 1px solid #d1d5db !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.btn-secondary:hover {
  background: #f1f5f9 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
}

.btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.75) !important;
  backdrop-filter: blur(8px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10000 !important;
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
}

.modal-content {
  background: #ffffff !important;
  border-radius: 12px !important;
  width: 90% !important;
  max-width: 600px !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4) !important;
  border: 2px solid #e5e7eb !important;
  position: relative !important;
  z-index: 10001 !important;
  pointer-events: auto !important;
  margin: 20px !important;
}

.modal-header {
  padding: 20px 24px !important;
  border-bottom: 1px solid #e2e8f0 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background: #ffffff !important;
  position: relative !important;
  z-index: 1002 !important;
}

.modal-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1e293b !important;
  margin: 0 !important;
  z-index: 1003 !important;
  position: relative !important;
}

.modal-close {
  width: 32px !important;
  height: 32px !important;
  border: none !important;
  background: #f8fafc !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  z-index: 1003 !important;
  position: relative !important;
  transition: all 0.2s ease !important;
}

.modal-close:hover {
  background: #e2e8f0 !important;
}

.close-icon {
  width: 16px !important;
  height: 16px !important;
  color: #64748b !important;
}

.modal-body {
  padding: 24px !important;
  background: #ffffff !important;
  position: relative !important;
  z-index: 1002 !important;
}

.modal-footer {
  padding: 20px 24px !important;
  border-top: 2px solid #e2e8f0 !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
  background: #ffffff !important;
  position: relative !important;
  z-index: 1003 !important;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  padding: 10px 12px !important;
  border: 2px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  background: #ffffff !important;
  color: #1f2937 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  transition: all 0.2s ease !important;
  position: relative !important;
  z-index: 100003 !important;
  pointer-events: auto !important;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2) !important;
  z-index: 100004 !important;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 导入预览样式 */
.import-preview {
  margin-top: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
}

.import-preview h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.preview-table {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background: #f1f5f9;
  font-weight: 600;
  font-size: 12px;
  color: #374151;
}

.preview-header span,
.preview-row span {
  padding: 8px 12px;
  border-right: 1px solid #e2e8f0;
}

.preview-header span:last-child,
.preview-row span:last-child {
  border-right: none;
}

.preview-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background: white;
  font-size: 12px;
  color: #64748b;
  border-bottom: 1px solid #f1f5f9;
}

.preview-row:last-child {
  border-bottom: none;
}

.text-sm {
  font-size: 12px;
}

.text-gray-600 {
  color: #64748b;
}

.mt-2 {
  margin-top: 8px;
}

/* 报表样式 */
.report-section {
  margin-bottom: 24px;
}

.report-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.report-stats,
.category-stats,
.value-stats {
  display: grid;
  gap: 8px;
}

.report-stat-item,
.category-stat-item,
.value-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 4px;
}

.stat-name,
.category-name {
  font-size: 14px;
  color: #64748b;
}

.stat-value,
.category-count {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 详情弹窗样式 */
.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #374151;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* 强制确保所有交互元素可点击 - Electron修复 */
button,
input,
select,
textarea,
.btn,
.btn-icon-small,
.debug-info button,
[onclick],
.modal-overlay button,
.modal-content button,
.modal-content input,
.modal-content select,
.modal-content textarea {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  position: relative !important;
  z-index: 999 !important;
  cursor: pointer !important;
}

/* 调试信息区域 */
.debug-info {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  z-index: 1000 !important;
}

.debug-info * {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
}

/* 弹窗区域强制可交互 */
.modal-overlay,
.modal-content {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
}

.modal-overlay *,
.modal-content * {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
}

/* 硬件配置区域样式 */
.hardware-config-section {
  margin: 20px 0;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 8px;
}
</style>
