/**
 * SMB Public共享匿名访问测试脚本
 * 功能：测试public共享的匿名访问和文件夹查询
 * 依赖：@marsaud/smb2
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const { SMBAdapter } = require('./src/main/storage/multi_sync/adapters/SMBAdapter.ts')

// 测试配置：匿名访问和不同认证方式
const testConfigs = [
  {
    name: 'Public共享 - 匿名访问',
    config: {
      host: '************',
      share: 'public',
      username: '',
      password: '',
      domain: '',
      port: 445
    }
  },
  {
    name: 'Public共享 - Guest用户',
    config: {
      host: '************',
      share: 'public',
      username: 'guest',
      password: '',
      domain: '',
      port: 445
    }
  },
  {
    name: 'Public共享 - Anonymous用户',
    config: {
      host: '************',
      share: 'public',
      username: 'anonymous',
      password: '',
      domain: '',
      port: 445
    }
  },
  {
    name: 'Public共享 - 原始认证',
    config: {
      host: '************',
      share: 'public',
      username: '<EMAIL>',
      password: 'RSgz@lbq0925!',
      domain: 'brbiotech.com',
      port: 445
    }
  }
]

/**
 * 安全日志输出
 */
function safeLog(message, data = null) {
  if (data && typeof data === 'object') {
    const safeData = { ...data }
    if (safeData.password) safeData.password = safeData.password ? '***' : '(空)'
    console.log(message, safeData)
  } else {
    console.log(message, data)
  }
}

/**
 * 测试单个配置
 */
async function testSingleConfig(testConfig, index) {
  console.log(`\n${index + 1}️⃣ ${testConfig.name}`)
  console.log('='.repeat(50))

  safeLog('🔧 使用配置:', testConfig.config)

  let adapter = null
  try {
    adapter = new SMBAdapter()

    console.log('🔗 尝试连接...')
    const connectResult = await adapter.connect(testConfig.config)

    if (!connectResult) {
      console.log('❌ 连接失败')
      return { success: false, config: testConfig, error: '连接失败' }
    }

    console.log('✅ 连接成功！')

    // 测试文件夹列表
    console.log('📂 查询根目录...')
    const files = await adapter.listFiles('/')
    console.log(`✅ 成功获取 ${files.length} 个项目:`)

    files.forEach((file) => {
      const icon = file.isDirectory ? '📁' : '📄'
      const size = file.isDirectory ? '' : ` (${file.size} bytes)`
      console.log(`   ${icon} ${file.name}${size}`)
    })

    return { success: true, config: testConfig, files }
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`)
    return { success: false, config: testConfig, error: error.message }
  } finally {
    if (adapter) {
      try {
        await adapter.disconnect()
        console.log('🔌 连接已断开')
      } catch (e) {
        // 忽略断开连接的错误
      }
    }
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始SMB Public共享测试')
  console.log('目标服务器: ************')
  console.log('目标共享: public')
  console.log('============================================================')

  const results = []

  for (let i = 0; i < testConfigs.length; i++) {
    const result = await testSingleConfig(testConfigs[i], i)
    results.push(result)

    if (i < testConfigs.length - 1) {
      console.log('\n⏳ 等待2秒后继续下一个测试...')
      await new Promise((resolve) => setTimeout(resolve, 2000))
    }
  }

  // 测试总结
  console.log('\n============================================================')
  console.log('📊 SMB Public共享测试总结:')

  const successful = results.filter((r) => r.success)
  const failed = results.filter((r) => !r.success)

  console.log(`✅ 成功: ${successful.length}/${results.length}`)
  console.log(`❌ 失败: ${failed.length}/${results.length}`)

  if (successful.length > 0) {
    console.log('\n🎉 成功的配置:')
    successful.forEach((result) => {
      console.log(`   ✅ ${result.config.name}`)
    })
  }

  if (failed.length > 0) {
    console.log('\n❌ 失败的配置:')
    failed.forEach((result) => {
      console.log(`   ❌ ${result.config.name}: ${result.error}`)
    })
  }

  if (successful.length === 0) {
    console.log('\n🔧 建议:')
    console.log('   1. 检查服务器是否启用了public共享')
    console.log('   2. 确认public共享是否允许匿名访问')
    console.log('   3. 检查网络连接和防火墙设置')
    console.log('   4. 尝试使用macOS原生工具: smbutil view //************/public')
  }

  console.log('\n🏁 测试完成！')
}

// 运行测试
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { main, testConfigs }
