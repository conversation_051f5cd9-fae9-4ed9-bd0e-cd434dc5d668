/**
 * 企业资源同步系统 - 企业同步服务
 * 功能：整合所有企业级组件，提供统一的企业资源同步服务
 * 依赖：企业配置管理器、资源分离管理器、统一配置接口、权限服务
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import {
  EnterpriseResource,
  EnterpriseSyncResult,
  EnterpriseSyncProgress,
  SyncStatus,
  User,
  UserRole,
  ResourceType,
  Environment
} from '../types/EnterpriseTypes'
import { EnterpriseConfigManager } from '../manager/EnterpriseConfigManager'
import { ResourceSeparationManager } from '../manager/ResourceSeparationManager'
import { UnifiedConfigInterface } from '../core/UnifiedConfigInterface'
import { AuthenticationService } from '../security/AuthenticationService'
import { PermissionService, PermissionAction } from '../security/PermissionService'

/**
 * 同步任务接口
 */
interface SyncTask {
  id: string
  type: 'full' | 'incremental' | 'manual'
  user_id: string
  target_backends?: string[]
  resource_filter?: {
    resource_type?: ResourceType
    environment?: Environment
    tags?: string[]
  }
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: EnterpriseSyncProgress[]
  created_at: Date
  started_at?: Date
  completed_at?: Date
  error_message?: string
}

/**
 * 同步统计接口
 */
interface SyncStatistics {
  total_tasks: number
  completed_tasks: number
  failed_tasks: number
  total_resources_synced: number
  total_backends: number
  healthy_backends: number
  last_sync_time?: Date
  average_sync_duration_ms: number
}

/**
 * 企业资源同步服务
 * 提供企业级资源同步的核心服务
 */
export class EnterpriseSyncService extends EventEmitter {
  private static instance: EnterpriseSyncService | null = null
  private configManager: EnterpriseConfigManager
  private separationManager: ResourceSeparationManager
  private unifiedInterface: UnifiedConfigInterface
  private authService: AuthenticationService
  private permissionService: PermissionService

  private activeTasks: Map<string, SyncTask> = new Map()
  private taskHistory: SyncTask[] = []
  private syncStatistics: SyncStatistics
  private autoSyncInterval: NodeJS.Timeout | null = null
  private isAutoSyncEnabled: boolean = false

  /**
   * 获取服务单例实例
   * @returns 服务实例
   */
  static getInstance(): EnterpriseSyncService {
    if (!EnterpriseSyncService.instance) {
      EnterpriseSyncService.instance = new EnterpriseSyncService()
    }
    return EnterpriseSyncService.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()

    // 初始化依赖服务
    this.configManager = EnterpriseConfigManager.getInstance()
    this.separationManager = ResourceSeparationManager.getInstance()
    this.unifiedInterface = UnifiedConfigInterface.getInstance()
    this.authService = AuthenticationService.getInstance()
    this.permissionService = PermissionService.getInstance()

    // 初始化统计数据
    this.syncStatistics = {
      total_tasks: 0,
      completed_tasks: 0,
      failed_tasks: 0,
      total_resources_synced: 0,
      total_backends: 0,
      healthy_backends: 0,
      average_sync_duration_ms: 0
    }

    this.initializeEventListeners()
  }

  /**
   * 启动企业资源同步
   * @param token 用户令牌
   * @param options 同步选项
   * @returns 同步任务ID
   */
  async startSync(
    token: string,
    options?: {
      type?: 'full' | 'incremental' | 'manual'
      target_backends?: string[]
      resource_filter?: {
        resource_type?: ResourceType
        environment?: Environment
        tags?: string[]
      }
      force?: boolean
    }
  ): Promise<string> {
    // 验证用户权限
    const user = await this.authService.validateToken(token)
    if (!user) {
      throw new Error('无效的用户令牌')
    }

    // 检查同步权限
    const permissionCheck = await this.permissionService.checkPermission(token, 'enterprise_sync', PermissionAction.EXECUTE)

    if (!permissionCheck.allowed) {
      throw new Error(`权限不足: ${permissionCheck.reason}`)
    }

    // 检查是否有正在运行的任务（除非强制执行）
    if (!options?.force && this.hasRunningTasks()) {
      throw new Error('已有同步任务正在运行，请等待完成或使用force选项')
    }

    // 创建同步任务
    const task: SyncTask = {
      id: this.generateTaskId(),
      type: options?.type || 'manual',
      user_id: user.id,
      target_backends: options?.target_backends,
      resource_filter: options?.resource_filter,
      status: 'pending',
      progress: [],
      created_at: new Date()
    }

    // 添加到活跃任务列表
    this.activeTasks.set(task.id, task)
    this.syncStatistics.total_tasks++

    // 触发任务创建事件
    this.emit('taskCreated', task)

    // 异步执行同步任务
    this.executeTask(task).catch((error) => {
      console.error(`同步任务执行失败 [${task.id}]:`, error)
    })

    return task.id
  }

  /**
   * 取消同步任务
   * @param token 用户令牌
   * @param taskId 任务ID
   * @returns 是否成功
   */
  async cancelSync(token: string, taskId: string): Promise<boolean> {
    // 验证用户权限
    const user = await this.authService.validateToken(token)
    if (!user) {
      throw new Error('无效的用户令牌')
    }

    const task = this.activeTasks.get(taskId)
    if (!task) {
      return false
    }

    // 检查任务所有权或管理员权限
    if (task.user_id !== user.id && user.role_type !== UserRole.SYSTEM_ADMIN) {
      throw new Error('无权取消此任务')
    }

    // 只能取消待执行或正在运行的任务
    if (task.status !== 'pending' && task.status !== 'running') {
      return false
    }

    // 更新任务状态
    task.status = 'cancelled'
    task.completed_at = new Date()

    // 移动到历史记录
    this.moveTaskToHistory(task)

    // 触发任务取消事件
    this.emit('taskCancelled', task)

    return true
  }

  /**
   * 获取同步任务状态
   * @param token 用户令牌
   * @param taskId 任务ID
   * @returns 任务信息
   */
  async getTaskStatus(token: string, taskId: string): Promise<SyncTask | null> {
    // 验证用户权限
    const user = await this.authService.validateToken(token)
    if (!user) {
      throw new Error('无效的用户令牌')
    }

    // 先从活跃任务中查找
    let task = this.activeTasks.get(taskId)

    // 如果没找到，从历史记录中查找
    if (!task) {
      task = this.taskHistory.find((t) => t.id === taskId) || null
    }

    if (!task) {
      return null
    }

    // 检查任务可见性
    if (task.user_id !== user.id && user.role_type !== UserRole.SYSTEM_ADMIN) {
      throw new Error('无权查看此任务')
    }

    return task
  }

  /**
   * 获取用户的同步任务列表
   * @param token 用户令牌
   * @param filter 过滤条件
   * @returns 任务列表
   */
  async getUserTasks(
    token: string,
    filter?: {
      status?: SyncTask['status']
      type?: SyncTask['type']
      limit?: number
      offset?: number
    }
  ): Promise<{ tasks: SyncTask[]; total_count: number }> {
    // 验证用户权限
    const user = await this.authService.validateToken(token)
    if (!user) {
      throw new Error('无效的用户令牌')
    }

    // 合并活跃任务和历史任务
    let allTasks: SyncTask[] = [...Array.from(this.activeTasks.values()), ...this.taskHistory]

    // 过滤用户任务（系统管理员可以看到所有任务）
    if (user.role_type !== UserRole.SYSTEM_ADMIN) {
      allTasks = allTasks.filter((task) => task.user_id === user.id)
    }

    // 应用过滤条件
    if (filter?.status) {
      allTasks = allTasks.filter((task) => task.status === filter.status)
    }
    if (filter?.type) {
      allTasks = allTasks.filter((task) => task.type === filter.type)
    }

    // 按创建时间倒序排列
    allTasks.sort((a, b) => b.created_at.getTime() - a.created_at.getTime())

    const totalCount = allTasks.length

    // 应用分页
    if (filter?.offset || filter?.limit) {
      const offset = filter.offset || 0
      const limit = filter.limit || 50
      allTasks = allTasks.slice(offset, offset + limit)
    }

    return {
      tasks: allTasks,
      total_count: totalCount
    }
  }

  /**
   * 获取同步统计信息
   * @param token 用户令牌
   * @returns 统计信息
   */
  async getSyncStatistics(token: string): Promise<SyncStatistics> {
    // 验证用户权限
    const user = await this.authService.validateToken(token)
    if (!user) {
      throw new Error('无效的用户令牌')
    }

    // 更新实时统计
    await this.updateStatistics()

    return { ...this.syncStatistics }
  }

  /**
   * 启用自动同步
   * @param token 用户令牌
   * @param intervalMs 同步间隔（毫秒）
   * @returns 是否成功
   */
  async enableAutoSync(token: string, intervalMs: number = 30 * 60 * 1000): Promise<boolean> {
    // 验证用户权限
    const permissionCheck = await this.permissionService.checkPermission(token, 'enterprise_sync.auto', PermissionAction.ADMIN)

    if (!permissionCheck.allowed) {
      throw new Error(`权限不足: ${permissionCheck.reason}`)
    }

    // 停止现有的自动同步
    this.disableAutoSync()

    // 启动新的自动同步
    this.autoSyncInterval = setInterval(async () => {
      try {
        await this.startSync(token, { type: 'incremental' })
      } catch (error) {
        console.warn('自动同步失败:', error)
      }
    }, intervalMs)

    this.isAutoSyncEnabled = true
    this.emit('autoSyncEnabled', { interval_ms: intervalMs })

    return true
  }

  /**
   * 禁用自动同步
   * @returns 是否成功
   */
  disableAutoSync(): boolean {
    if (this.autoSyncInterval) {
      clearInterval(this.autoSyncInterval)
      this.autoSyncInterval = null
    }

    if (this.isAutoSyncEnabled) {
      this.isAutoSyncEnabled = false
      this.emit('autoSyncDisabled')
    }

    return true
  }

  /**
   * 检查是否启用了自动同步
   * @returns 是否启用
   */
  isAutoSyncActive(): boolean {
    return this.isAutoSyncEnabled
  }

  /**
   * 执行同步任务
   * @param task 同步任务
   */
  private async executeTask(task: SyncTask): Promise<void> {
    try {
      // 更新任务状态
      task.status = 'running'
      task.started_at = new Date()
      this.emit('taskStarted', task)

      // 获取目标后端
      const targetBackends = task.target_backends
        ? await this.getSpecificBackends(task.target_backends)
        : await this.unifiedInterface.getHealthyBackends()

      if (targetBackends.length === 0) {
        throw new Error('没有可用的健康后端')
      }

      // 初始化进度跟踪
      task.progress = targetBackends.map((backend) => ({
        backend_id: backend.id,
        status: SyncStatus.IDLE,
        progress: 0,
        total_resources: 0,
        processed_resources: 0
      }))

      const results: EnterpriseSyncResult[] = []

      // 并行同步所有后端
      const syncPromises = targetBackends.map(async (backend, index) => {
        const progress = task.progress[index]
        progress.status = SyncStatus.SYNCING
        progress.started_at = new Date()

        this.emit('taskProgress', task)

        try {
          // 执行后端同步
          const result = await this.syncBackend(backend, task.resource_filter)

          progress.status = SyncStatus.SUCCESS
          progress.progress = 100
          progress.processed_resources = result.resources_synced
          progress.message = '同步成功'

          results.push(result)
        } catch (error: any) {
          progress.status = SyncStatus.FAILED
          progress.error = error.message
          progress.message = `同步失败: ${error.message}`

          // 创建失败结果
          results.push({
            success: false,
            message: error.message,
            filesUploaded: 0,
            filesDownloaded: 0,
            filesDeleted: 0,
            errors: [error.message],
            conflicts: [],
            backend_id: backend.id,
            backend_name: backend.name,
            sync_type: task.type,
            duration_ms: 0,
            resources_synced: 0
          })
        }

        this.emit('taskProgress', task)
      })

      // 等待所有同步完成
      await Promise.allSettled(syncPromises)

      // 计算总体结果
      const successCount = results.filter((r) => r.success).length
      const totalResources = results.reduce((sum, r) => sum + r.resources_synced, 0)

      // 更新任务状态
      task.status = successCount > 0 ? 'completed' : 'failed'
      task.completed_at = new Date()

      if (successCount === 0) {
        task.error_message = '所有后端同步都失败了'
      }

      // 更新统计
      if (task.status === 'completed') {
        this.syncStatistics.completed_tasks++
        this.syncStatistics.total_resources_synced += totalResources
        this.syncStatistics.last_sync_time = new Date()
      } else {
        this.syncStatistics.failed_tasks++
      }

      // 移动到历史记录
      this.moveTaskToHistory(task)

      // 触发任务完成事件
      this.emit('taskCompleted', { task, results })
    } catch (error: any) {
      // 处理任务执行异常
      task.status = 'failed'
      task.completed_at = new Date()
      task.error_message = error.message

      this.syncStatistics.failed_tasks++
      this.moveTaskToHistory(task)

      this.emit('taskFailed', { task, error: error.message })
    }
  }

  /**
   * 同步单个后端
   * @param backend 后端配置
   * @param filter 资源过滤器
   * @returns 同步结果
   */
  private async syncBackend(backend: any, filter?: SyncTask['resource_filter']): Promise<EnterpriseSyncResult> {
    const startTime = Date.now()

    try {
      // TODO: 实现具体的后端同步逻辑
      // 这里需要根据后端类型调用相应的适配器

      // 模拟同步过程
      await new Promise((resolve) => setTimeout(resolve, 1000))

      return {
        success: true,
        message: '同步成功',
        filesUploaded: 5,
        filesDownloaded: 3,
        filesDeleted: 1,
        errors: [],
        conflicts: [],
        backend_id: backend.id,
        backend_name: backend.name,
        sync_type: 'manual',
        duration_ms: Date.now() - startTime,
        resources_synced: 9
      }
    } catch (error: any) {
      throw new Error(`后端同步失败 [${backend.name}]: ${error.message}`)
    }
  }

  /**
   * 获取指定的后端列表
   * @param backendIds 后端ID列表
   * @returns 后端配置列表
   */
  private async getSpecificBackends(backendIds: string[]): Promise<any[]> {
    const allBackends = await this.configManager.getAllBackends()
    return allBackends.filter((backend) => backendIds.includes(backend.id) && backend.enabled)
  }

  /**
   * 检查是否有正在运行的任务
   * @returns 是否有运行中的任务
   */
  private hasRunningTasks(): boolean {
    return Array.from(this.activeTasks.values()).some((task) => task.status === 'running' || task.status === 'pending')
  }

  /**
   * 将任务移动到历史记录
   * @param task 任务
   */
  private moveTaskToHistory(task: SyncTask): void {
    this.activeTasks.delete(task.id)
    this.taskHistory.unshift(task)

    // 限制历史记录数量
    if (this.taskHistory.length > 1000) {
      this.taskHistory = this.taskHistory.slice(0, 500)
    }
  }

  /**
   * 更新统计信息
   */
  private async updateStatistics(): Promise<void> {
    try {
      const allBackends = await this.configManager.getAllBackends()
      const healthyBackends = await this.unifiedInterface.getHealthyBackends()

      this.syncStatistics.total_backends = allBackends.length
      this.syncStatistics.healthy_backends = healthyBackends.length

      // 计算平均同步时长
      const completedTasks = this.taskHistory.filter((task) => task.status === 'completed' && task.started_at && task.completed_at)

      if (completedTasks.length > 0) {
        const totalDuration = completedTasks.reduce((sum, task) => {
          const duration = task.completed_at!.getTime() - task.started_at!.getTime()
          return sum + duration
        }, 0)

        this.syncStatistics.average_sync_duration_ms = totalDuration / completedTasks.length
      }
    } catch (error) {
      console.warn('更新统计信息失败:', error)
    }
  }

  /**
   * 生成任务ID
   * @returns 任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 监听配置管理器事件
    this.configManager.on('backendAdded', () => {
      this.updateStatistics()
    })

    this.configManager.on('backendRemoved', () => {
      this.updateStatistics()
    })

    // 监听统一接口事件
    this.unifiedInterface.on('backendRecovered', () => {
      this.updateStatistics()
    })

    this.unifiedInterface.on('backendFailedOver', () => {
      this.updateStatistics()
    })
  }

  /**
   * 销毁服务，清理资源
   */
  destroy(): void {
    this.disableAutoSync()
    this.activeTasks.clear()
    this.taskHistory.length = 0
    this.removeAllListeners()
  }
}
