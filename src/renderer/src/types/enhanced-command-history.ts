/**
 * 增强命令历史相关类型定义
 * 从主进程服务中导出的类型定义
 */

/**
 * 命令分类枚举
 */
export enum CommandCategory {
  SYSTEM = 'system',
  FILE = 'file',
  NETWORK = 'network',
  DEVELOPMENT = 'development',
  DATABASE = 'database',
  DOCKER = 'docker',
  GIT = 'git',
  PACKAGE_MANAGER = 'package_manager',
  TEXT_PROCESSING = 'text_processing',
  MONITORING = 'monitoring',
  SECURITY = 'security',
  OTHER = 'other'
}

/**
 * 增强命令历史项接口
 */
export interface EnhancedCommandHistoryItem {
  id?: number
  ip: string
  command: string
  category: CommandCategory
  usage_count: number
  last_used: string
  created_at: string
  tags: string[]
  description?: string
  success_rate?: number
  avg_execution_time?: number
  context?: {
    working_directory?: string
    user?: string
    session_id?: string
  }
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  ip: string
  query?: string
  category?: CommandCategory
  limit?: number
  offset?: number
  tags?: string[]
  minUsageCount?: number
  dateRange?: {
    start: string
    end: string
  }
}

/**
 * 添加命令参数接口
 */
export interface AddCommandParams {
  ip: string
  command: string
  context?: {
    working_directory?: string
    user?: string
    session_id?: string
  }
}

/**
 * 服务响应接口
 */
export interface ServiceResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}
