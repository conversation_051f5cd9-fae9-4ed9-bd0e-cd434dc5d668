# 功能迁移测试清单

## 测试目标
验证左侧菜单栏中的"资源"、"同步"、"监控"、"安全"功能已成功迁移到主机菜单的企业资源页面中。

## 测试步骤

### 1. 验证左侧菜单栏变更
- [ ] 打开应用，检查左侧菜单栏
- [ ] 确认"资源"菜单项已移除
- [ ] 确认"同步"菜单项已移除  
- [ ] 确认"监控"菜单项已移除
- [ ] 确认"安全"菜单项已移除
- [ ] 确认其他菜单项（主机、密钥链、文件、扩展、AI、用户、设置、通知）仍然存在

### 2. 验证主机菜单集成
- [ ] 点击左侧菜单栏的"主机"菜单
- [ ] 切换到"企业"工作空间（如果有多个工作空间选项）
- [ ] 查找并点击"企业资源管理"按钮
- [ ] 确认按钮提示文本显示为"企业资源管理 (含同步、监控、安全)"

### 3. 验证企业资源管理页面
- [ ] 确认企业资源管理页面成功打开
- [ ] 验证页面标题显示"企业资源管理"
- [ ] 验证页面描述包含"同步、监控、安全功能"
- [ ] 确认功能导航卡片区域存在

### 4. 验证功能导航卡片
- [ ] 确认"资源管理"卡片存在，点击可滚动到资源列表
- [ ] 确认"同步配置"卡片存在，点击可跳转到同步配置页面
- [ ] 确认"监控仪表板"卡片存在，点击可跳转到监控页面
- [ ] 确认"安全管理"卡片存在，点击可跳转到安全管理页面

### 5. 验证路由跳转
- [ ] 点击"同步配置"卡片，确认跳转到 `/sync/config` 页面
- [ ] 点击"监控仪表板"卡片，确认跳转到 `/sync/monitor` 页面
- [ ] 点击"安全管理"卡片，确认跳转到 `/security/permissions` 页面
- [ ] 使用浏览器后退按钮，确认可以正常返回企业资源管理页面

### 6. 验证样式和交互
- [ ] 确认功能导航卡片具有正确的样式（图标、标题、描述）
- [ ] 确认鼠标悬停时卡片有适当的视觉反馈
- [ ] 确认页面在不同屏幕尺寸下的响应式布局

## 预期结果
- 左侧菜单栏不再显示"资源"、"同步"、"监控"、"安全"菜单项
- 用户可以通过主机菜单中的企业资源管理页面访问所有迁移的功能
- 所有功能导航链接正常工作
- 页面样式美观，用户体验良好

## 测试状态
- [ ] 测试完成
- [ ] 发现问题需要修复
- [ ] 功能迁移成功

## 备注
如果在测试过程中发现任何问题，请记录具体的错误信息和重现步骤。
