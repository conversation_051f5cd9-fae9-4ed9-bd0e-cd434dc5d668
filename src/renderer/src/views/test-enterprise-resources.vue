<template>
  <div class="test-enterprise-resources">
    <h1>企业资源管理功能测试页面</h1>
    <p>这是一个测试页面，用于验证企业资源管理功能是否正常工作。</p>

    <div class="test-buttons">
      <button
        class="btn btn-primary"
        @click="navigateToEnterpriseMain"
      >
        打开企业资源管理主页面
      </button>
      <button
        class="btn btn-secondary"
        @click="navigateToResourcesPage"
      >
        直接访问资源管理功能页面
      </button>
      <button
        class="btn btn-secondary"
        @click="openInTab"
      >
        在标签页中打开企业资源管理
      </button>
    </div>

    <div class="test-info">
      <h2>测试说明</h2>
      <ul>
        <li><strong>企业资源管理主页面</strong>：包含功能模块导航的概览页面</li>
        <li><strong>资源管理功能页面</strong>：具体的资源管理界面，包含主机配置、密钥链、代码片段的管理</li>
        <li><strong>标签页模式</strong>：在主界面的标签页系统中打开企业资源管理</li>
      </ul>
    </div>

    <div class="test-results">
      <h2>测试结果</h2>
      <div class="result-item">
        <span class="result-label">路由配置：</span>
        <span class="result-value success">✅ 已配置</span>
      </div>
      <div class="result-item">
        <span class="result-label">页面组件：</span>
        <span class="result-value success">✅ 已创建</span>
      </div>
      <div class="result-item">
        <span class="result-label">导航逻辑：</span>
        <span class="result-value success">✅ 已修复</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'

const router = useRouter()

const navigateToEnterpriseMain = () => {
  router.push('/resources/enterprise')
}

const navigateToResourcesPage = () => {
  router.push('/enterprise/resources')
}

const openInTab = () => {
  router.push('/')
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}
</script>

<style lang="less" scoped>
.test-enterprise-resources {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #333333);
}

h1 {
  color: #1f2937;
  margin-bottom: 16px;
}

h2 {
  color: #374151;
  margin: 32px 0 16px 0;
  font-size: 20px;
}

p {
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.test-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.btn {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background: #f1f5f9;
  border-color: #9ca3af;
}

.test-info {
  background: #f8fafc;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 32px;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.test-results {
  background: #f0fdf4;
  padding: 24px;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
}

.result-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.result-label {
  font-weight: 500;
  color: #374151;
  min-width: 120px;
}

.result-value {
  font-weight: 600;
}

.result-value.success {
  color: #16a34a;
}

.result-value.error {
  color: #dc2626;
}
</style>
