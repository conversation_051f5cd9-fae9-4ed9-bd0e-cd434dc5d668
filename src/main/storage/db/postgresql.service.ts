/**
 * PostgreSQL数据库服务
 * 功能：连接PostgreSQL数据库，自动创建表和初始化数据
 * 作者：SOLO Coding
 * 修改时间：2025-01-10
 */

import { Pool, Client, PoolConfig } from 'pg'

export interface PostgreSQLConfig {
  host: string
  port: number
  database: string
  username: string
  password: string
  ssl?: boolean
  poolSize?: number
}

export class PostgreSQLService {
  private pool: Pool | null = null
  private config: PostgreSQLConfig | null = null
  private isConnected: boolean = false

  /**
   * 连接到PostgreSQL数据库
   */
  async connect(config: PostgreSQLConfig): Promise<boolean> {
    try {
      this.config = config

      const poolConfig: PoolConfig = {
        host: config.host,
        port: config.port,
        database: config.database,
        user: config.username,
        password: config.password,
        ssl: config.ssl ? { rejectUnauthorized: false } : false,
        max: config.poolSize || 10,
        min: 2,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 60000
      }

      console.log(`[PostgreSQL] 正在连接到数据库: ${config.host}:${config.port}/${config.database}`)

      this.pool = new Pool(poolConfig)

      // 测试连接
      const client = await this.pool.connect()
      const result = await client.query('SELECT NOW() as current_time')
      console.log(`[PostgreSQL] 连接成功，服务器时间: ${result.rows[0].current_time}`)
      client.release()

      this.isConnected = true

      // 自动创建表和初始化数据
      await this.initializeTables()

      return true
    } catch (error) {
      console.error('[PostgreSQL] 连接失败:', error)
      this.isConnected = false
      return false
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.pool) {
        await this.pool.end()
        this.pool = null
      }
      this.isConnected = false
      console.log('[PostgreSQL] 连接已断开')
    } catch (error) {
      console.error('[PostgreSQL] 断开连接时出错:', error)
    }
  }

  /**
   * 测试数据库连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.isConnected || !this.pool) {
      return false
    }

    try {
      const client = await this.pool.connect()
      await client.query('SELECT 1')
      client.release()
      return true
    } catch (error) {
      console.error('[PostgreSQL] 连接测试失败:', error)
      return false
    }
  }

  /**
   * 执行SQL查询
   */
  async query(sql: string, params: any[] = []): Promise<any> {
    if (!this.pool) {
      throw new Error('数据库未连接')
    }

    try {
      const result = await this.pool.query(sql, params)
      return result
    } catch (error) {
      console.error('[PostgreSQL] 查询失败:', error)
      throw error
    }
  }

  /**
   * 初始化数据库表
   */
  private async initializeTables(): Promise<void> {
    try {
      console.log('[PostgreSQL] 开始初始化数据库表...')

      // 创建资产表
      await this.createAssetsTable()

      // 创建组织表
      await this.createOrganizationsTable()

      // 创建组织资产表
      await this.createOrganizationAssetsTable()

      // 创建自定义文件夹表
      await this.createCustomFoldersTable()

      // 创建资产文件夹映射表
      await this.createAssetFolderMappingTable()

      // 创建密钥链表
      await this.createKeyChainTable()

      // 插入初始数据
      await this.insertInitialData()

      console.log('[PostgreSQL] 数据库表初始化完成')
    } catch (error) {
      console.error('[PostgreSQL] 初始化表失败:', error)
      throw error
    }
  }

  /**
   * 创建资产表
   */
  private async createAssetsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_assets (
        id SERIAL PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        label TEXT,
        asset_ip TEXT,
        group_name TEXT,
        uuid TEXT UNIQUE,
        auth_type TEXT,
        port INTEGER,
        username TEXT,
        password TEXT,
        key_chain_id INTEGER,
        favorite INTEGER DEFAULT 2,
        asset_type TEXT DEFAULT 'person',
        need_proxy INTEGER DEFAULT 0,
        proxy_name TEXT,
        version INTEGER DEFAULT 1
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 资产表创建成功')
  }

  /**
   * 创建组织表
   */
  private async createOrganizationsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_organizations (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 组织表创建成功')
  }

  /**
   * 创建组织资产表
   */
  private async createOrganizationAssetsTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_organization_assets (
        id SERIAL PRIMARY KEY,
        organization_uuid TEXT NOT NULL,
        hostname TEXT,
        host TEXT,
        uuid TEXT UNIQUE,
        jump_server_type TEXT DEFAULT 'manual',
        favorite INTEGER DEFAULT 2,
        comment TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 组织资产表创建成功')
  }

  /**
   * 创建自定义文件夹表
   */
  private async createCustomFoldersTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_custom_folders (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 自定义文件夹表创建成功')
  }

  /**
   * 创建资产文件夹映射表
   */
  private async createAssetFolderMappingTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_asset_folder_mapping (
        id SERIAL PRIMARY KEY,
        folder_uuid TEXT NOT NULL,
        organization_uuid TEXT NOT NULL,
        asset_host TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(folder_uuid, organization_uuid, asset_host)
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 资产文件夹映射表创建成功')
  }

  /**
   * 创建密钥链表
   */
  private async createKeyChainTable(): Promise<void> {
    const sql = `
      CREATE TABLE IF NOT EXISTS t_asset_chains (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT,
        private_key TEXT,
        passphrase TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    await this.query(sql)
    console.log('[PostgreSQL] 密钥链表创建成功')
  }

  /**
   * 插入初始数据
   */
  private async insertInitialData(): Promise<void> {
    try {
      // 检查是否已有数据
      const assetsResult = await this.query('SELECT COUNT(*) as count FROM t_assets')
      const assetsCount = parseInt(assetsResult.rows[0].count)

      if (assetsCount === 0) {
        console.log('[PostgreSQL] 插入初始资产数据...')

        // 插入企业资产管理组织
        const orgUuid = 'enterprise-asset-management'
        await this.query(
          `
          INSERT INTO t_assets (label, asset_ip, uuid, auth_type, port, username, password, asset_type, group_name)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          ON CONFLICT (uuid) DO NOTHING
        `,
          ['企业资产管理', '***********', orgUuid, 'password', 22, 'admin', '', 'organization', '企业资产管理']
        )

        // 插入组织资产
        const organizationAssets = [
          { hostname: '生产服务器-01', host: '***********00', comment: '主要生产环境服务器' },
          { hostname: '测试服务器-01', host: '*************', comment: '测试环境服务器' },
          { hostname: '开发服务器-01', host: '*************', comment: '开发环境服务器' },
          { hostname: '数据库服务器', host: '***********01', comment: 'MySQL主数据库服务器' },
          { hostname: '备份数据库', host: '*************', comment: '数据备份服务器' },
          { hostname: '网络设备-交换机', host: '***********', comment: '核心网络交换设备' }
        ]

        for (const asset of organizationAssets) {
          const assetUuid = `${orgUuid}-${asset.host}`
          await this.query(
            `
            INSERT INTO t_organization_assets (organization_uuid, hostname, host, uuid, comment)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (uuid) DO NOTHING
          `,
            [orgUuid, asset.hostname, asset.host, assetUuid, asset.comment]
          )
        }

        console.log('[PostgreSQL] 初始数据插入完成')
      } else {
        console.log('[PostgreSQL] 数据库已有数据，跳过初始化')
      }
    } catch (error) {
      console.error('[PostgreSQL] 插入初始数据失败:', error)
    }
  }

  /**
   * 获取连接状态
   */
  isConnectionActive(): boolean {
    return this.isConnected && this.pool !== null
  }

  /**
   * 获取数据库配置信息（隐藏敏感信息）
   */
  getConnectionInfo(): any {
    if (!this.config) return null

    return {
      host: this.config.host,
      port: this.config.port,
      database: this.config.database,
      username: this.config.username,
      ssl: this.config.ssl,
      connected: this.isConnected
    }
  }

  /**
   * 创建指定的表
   */
  public async createTable(tableName: string): Promise<boolean> {
    if (!this.client) {
      console.error('[PostgreSQL] 客户端未连接')
      return false
    }

    try {
      let sql = ''

      // 根据表名选择相应的创建语句
      switch (tableName) {
        case 'host_configurations':
          sql = `
            CREATE TABLE IF NOT EXISTS host_configurations (
              id SERIAL PRIMARY KEY,
              uuid TEXT UNIQUE,
              label TEXT,
              hostname TEXT,
              host TEXT NOT NULL,
              port INTEGER DEFAULT 22,
              username TEXT,
              password TEXT,
              auth_type TEXT,
              key_chain_id INTEGER,
              group_name TEXT,
              asset_type TEXT DEFAULT 'person',
              favorite BOOLEAN DEFAULT FALSE,
              need_proxy BOOLEAN DEFAULT FALSE,
              proxy_name TEXT,
              organization_id TEXT,
              description TEXT,
              tags TEXT[],
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            -- 创建索引
            CREATE INDEX IF NOT EXISTS idx_host_configurations_host ON host_configurations(host);
            CREATE INDEX IF NOT EXISTS idx_host_configurations_uuid ON host_configurations(uuid);
            CREATE INDEX IF NOT EXISTS idx_host_configurations_group ON host_configurations(group_name);
          `
          break

        default:
          // 如果是未知表名，创建一个通用的配置表
          sql = `
            CREATE TABLE IF NOT EXISTS ${tableName} (
              id SERIAL PRIMARY KEY,
              uuid TEXT UNIQUE,
              name TEXT,
              value TEXT,
              type TEXT,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
              updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
          `
          break
      }

      await this.client.query(sql)
      console.log(`[PostgreSQL] 表 ${tableName} 创建/验证成功`)
      return true
    } catch (error) {
      console.error(`[PostgreSQL] 创建表 ${tableName} 失败:`, error)
      return false
    }
  }

  /**
   * 同步单个资产到PostgreSQL
   */
  public async syncAsset(asset: any): Promise<boolean> {
    if (!this.client) {
      console.error('[PostgreSQL] 客户端未连接')
      return false
    }

    try {
      const sql = `
        INSERT INTO host_configurations (
          uuid, label, hostname, host, port, username, password,
          auth_type, key_chain_id, group_name, asset_type,
          favorite, need_proxy, proxy_name, organization_id,
          created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        ON CONFLICT (uuid) DO UPDATE SET
          label = EXCLUDED.label,
          hostname = EXCLUDED.hostname,
          host = EXCLUDED.host,
          port = EXCLUDED.port,
          username = EXCLUDED.username,
          password = EXCLUDED.password,
          auth_type = EXCLUDED.auth_type,
          key_chain_id = EXCLUDED.key_chain_id,
          group_name = EXCLUDED.group_name,
          asset_type = EXCLUDED.asset_type,
          favorite = EXCLUDED.favorite,
          need_proxy = EXCLUDED.need_proxy,
          proxy_name = EXCLUDED.proxy_name,
          organization_id = EXCLUDED.organization_id,
          updated_at = EXCLUDED.updated_at
      `

      const values = [
        asset.uuid || null,
        asset.label || asset.title || null,
        asset.hostname || asset.label || null,
        asset.ip || asset.host || null,
        asset.port || 22,
        asset.username || null,
        asset.password || null,
        asset.auth_type || 'password',
        asset.key_chain_id || null,
        asset.group_name || null,
        asset.asset_type || 'person',
        asset.favorite || false,
        asset.needProxy || false,
        asset.proxyName || null,
        asset.organizationId || null,
        new Date(),
        new Date()
      ]

      await this.client.query(sql, values)
      return true
    } catch (error) {
      console.error(`[PostgreSQL] 同步资产失败:`, error)
      return false
    }
  }
}
