<template>
  <div class="asset-search-box">
    <SearchBox
      v-model="searchValue"
      :placeholder="placeholder"
      :size="size"
      clearable
      :suggestions="filteredSuggestions"
      :show-suggestions="showSuggestions && filteredSuggestions.length > 0"
      class="asset-search-box__input"
      @search="handleSearch"
      @suggestion-select="handleSuggestionSelect"
      @input="handleInput"
      @clear="handleClear"
    >
      <template #suffix-icon>
        <SearchOutlined />
      </template>
      
      <template #suggestion="{ suggestion }">
        <div class="asset-suggestion">
          <div class="asset-suggestion__icon">
            <component :is="getAssetIcon(suggestion.type)" />
          </div>
          <div class="asset-suggestion__content">
            <div class="asset-suggestion__title">{{ suggestion.name }}</div>
            <div class="asset-suggestion__subtitle">
              {{ suggestion.host }}:{{ suggestion.port }}
            </div>
            <div v-if="suggestion.description" class="asset-suggestion__description">
              {{ suggestion.description }}
            </div>
          </div>
          <div v-if="suggestion.status" class="asset-suggestion__status">
            <span :class="['status-badge', `status-badge--${suggestion.status}`]">
              {{ getStatusText(suggestion.status) }}
            </span>
          </div>
        </div>
      </template>
    </SearchBox>
    
    <div v-if="showActionButtons" class="asset-search-box__actions">
      <a-button
        v-if="showNewButton"
        size="small"
        type="primary"
        class="action-button"
        @click="handleNewAsset"
      >
        <template #icon><PlusOutlined /></template>
        {{ newButtonText || t('personal.newHost') }}
      </a-button>
      
      <a-button
        v-if="showImportButton"
        size="small"
        class="action-button"
        @click="handleImport"
      >
        <template #icon><ImportOutlined /></template>
        {{ t('personal.import') }}
      </a-button>
      
      <a-button
        v-if="showExportButton"
        size="small"
        class="action-button"
        @click="handleExport"
      >
        <template #icon><ExportOutlined /></template>
        {{ t('personal.export') }}
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { SearchOutlined, PlusOutlined, ImportOutlined, ExportOutlined, DatabaseOutlined, CloudServerOutlined, DesktopOutlined } from '@ant-design/icons-vue'
import SearchBox from './SearchBox.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 资产类型定义
interface Asset {
  id?: string
  name: string
  host: string
  port: number
  type: 'server' | 'database' | 'network' | 'console' | 'rdp'
  description?: string
  status?: 'online' | 'offline' | 'unknown'
  organization?: string
  department?: string
  tags?: string[]
}

interface Props {
  modelValue?: string
  placeholder?: string
  size?: 'small' | 'default' | 'large'
  assets?: Asset[]
  showSuggestions?: boolean
  showActionButtons?: boolean
  showNewButton?: boolean
  showImportButton?: boolean
  showExportButton?: boolean
  newButtonText?: string
  maxSuggestions?: number
}

interface Emits {
  'update:modelValue': [value: string]
  'search': [value: string]
  'suggestion-select': [asset: Asset]
  'new-asset': []
  'import-assets': []
  'export-assets': []
  'clear': []
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '搜索资产...',
  size: 'default',
  assets: () => [],
  showSuggestions: true,
  showActionButtons: true,
  showNewButton: true,
  showImportButton: true,
  showExportButton: true,
  maxSuggestions: 8
})

const emit = defineEmits<Emits>()

// 响应式数据
const searchValue = ref(props.modelValue)

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

// 监听 searchValue 变化
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 过滤建议
const filteredSuggestions = computed(() => {
  if (!searchValue.value || !props.assets.length) {
    return []
  }
  
  const query = searchValue.value.toLowerCase()
  const filtered = props.assets.filter(asset => 
    asset.name.toLowerCase().includes(query) ||
    asset.host.toLowerCase().includes(query) ||
    asset.description?.toLowerCase().includes(query) ||
    asset.organization?.toLowerCase().includes(query) ||
    asset.department?.toLowerCase().includes(query) ||
    asset.tags?.some(tag => tag.toLowerCase().includes(query))
  )
  
  return filtered.slice(0, props.maxSuggestions)
})

// 获取资产图标
const getAssetIcon = (type: string) => {
  switch (type) {
    case 'server':
      return CloudServerOutlined
    case 'database':
      return DatabaseOutlined
    case 'network':
    case 'console':
    case 'rdp':
      return DesktopOutlined
    default:
      return CloudServerOutlined
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'online':
      return '在线'
    case 'offline':
      return '离线'
    case 'unknown':
    default:
      return '未知'
  }
}

// 事件处理
const handleSearch = (value: string) => {
  emit('search', value)
}

const handleInput = () => {
  // 输入时的处理逻辑
}

const handleClear = () => {
  emit('clear')
}

const handleSuggestionSelect = (suggestion: any) => {
  emit('suggestion-select', suggestion)
}

const handleNewAsset = () => {
  emit('new-asset')
}

const handleImport = () => {
  emit('import-assets')
}

const handleExport = () => {
  emit('export-assets')
}
</script>

<style lang="less" scoped>
.asset-search-box {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;

  &__input {
    flex: 1;
  }

  &__actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
  }
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

// 资产建议样式
.asset-suggestion {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  
  &__icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
  }
  
  &__content {
    flex: 1;
    min-width: 0;
  }
  
  &__title {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  &__subtitle {
    font-size: 12px;
    color: var(--text-color-secondary);
    margin-bottom: 2px;
  }
  
  &__description {
    font-size: 11px;
    color: var(--text-color-tertiary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  &__status {
    flex-shrink: 0;
  }
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  
  &--online {
    background: #d1fae5;
    color: #065f46;
  }
  
  &--offline {
    background: #fee2e2;
    color: #991b1b;
  }
  
  &--unknown {
    background: #f3f4f6;
    color: #374151;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .asset-search-box {
    flex-direction: column;
    gap: 8px;
    
    &__actions {
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }
  
  .action-button {
    flex: 1;
    min-width: 80px;
  }
}
</style>
