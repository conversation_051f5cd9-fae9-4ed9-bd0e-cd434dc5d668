/**
 * AI助手 - 智能错误诊断和命令优化
 * 提供基于AI的错误分析、命令建议和故障排除
 */

export interface ErrorContext {
  command: string
  output: string
  exitCode?: number
  workingDirectory: string
  environment: Record<string, string>
  timestamp: number
}

export interface DiagnosisResult {
  errorType: 'syntax' | 'permission' | 'network' | 'file' | 'dependency' | 'system' | 'unknown'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  possibleCauses: string[]
  solutions: Solution[]
  confidence: number
}

export interface Solution {
  title: string
  description: string
  commands?: string[]
  steps?: string[]
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedTime: string
  riskLevel: 'low' | 'medium' | 'high'
}

export interface CommandOptimization {
  originalCommand: string
  optimizedCommand: string
  improvements: string[]
  performanceGain: string
  explanation: string
}

export interface AIInsight {
  type: 'tip' | 'warning' | 'optimization' | 'learning'
  title: string
  content: string
  actionable: boolean
  commands?: string[]
}

export class AIAssistant {
  private errorPatterns: Map<RegExp, DiagnosisResult> = new Map()
  private commandOptimizations: Map<RegExp, CommandOptimization> = new Map()
  private learningData: ErrorContext[] = []
  private maxLearningData = 1000

  constructor() {
    this.initializeErrorPatterns()
    this.initializeOptimizations()
  }

  /**
   * 初始化错误模式识别
   */
  private initializeErrorPatterns(): void {
    // 权限错误
    this.errorPatterns.set(/permission denied|access denied|operation not permitted/i, {
      errorType: 'permission',
      severity: 'medium',
      description: '权限不足，无法执行操作',
      possibleCauses: ['当前用户没有足够权限', '文件或目录权限设置不正确', '需要管理员权限'],
      solutions: [
        {
          title: '使用sudo提升权限',
          description: '在命令前添加sudo获取管理员权限',
          commands: ['sudo [原命令]'],
          difficulty: 'easy',
          estimatedTime: '1分钟',
          riskLevel: 'medium'
        },
        {
          title: '修改文件权限',
          description: '使用chmod修改文件或目录权限',
          commands: ['chmod +x [文件名]', 'chmod 755 [目录名]'],
          difficulty: 'easy',
          estimatedTime: '2分钟',
          riskLevel: 'low'
        }
      ],
      confidence: 0.9
    })

    // 命令未找到
    this.errorPatterns.set(/command not found|not found|no such file or directory.*command/i, {
      errorType: 'dependency',
      severity: 'medium',
      description: '命令或程序未安装或不在PATH中',
      possibleCauses: ['程序未安装', 'PATH环境变量配置错误', '命令拼写错误'],
      solutions: [
        {
          title: '安装缺失的程序',
          description: '使用包管理器安装所需程序',
          commands: ['apt-get install [程序名]  # Ubuntu/Debian', 'yum install [程序名]      # CentOS/RHEL', 'brew install [程序名]     # macOS'],
          difficulty: 'easy',
          estimatedTime: '3-5分钟',
          riskLevel: 'low'
        },
        {
          title: '检查命令拼写',
          description: '确认命令名称拼写正确',
          steps: ['检查命令拼写', '使用tab键自动补全', '查看帮助文档'],
          difficulty: 'easy',
          estimatedTime: '1分钟',
          riskLevel: 'low'
        }
      ],
      confidence: 0.85
    })

    // 网络错误
    this.errorPatterns.set(/connection refused|network unreachable|timeout|dns resolution failed/i, {
      errorType: 'network',
      severity: 'medium',
      description: '网络连接问题',
      possibleCauses: ['目标服务器不可达', '网络配置错误', '防火墙阻止连接', 'DNS解析失败'],
      solutions: [
        {
          title: '检查网络连接',
          description: '测试基本网络连通性',
          commands: ['ping google.com', 'nslookup [域名]', 'curl -I [URL]'],
          difficulty: 'easy',
          estimatedTime: '2分钟',
          riskLevel: 'low'
        },
        {
          title: '检查防火墙设置',
          description: '确认防火墙没有阻止连接',
          commands: ['sudo ufw status', 'sudo iptables -L'],
          difficulty: 'medium',
          estimatedTime: '5分钟',
          riskLevel: 'medium'
        }
      ],
      confidence: 0.8
    })

    // 文件系统错误
    this.errorPatterns.set(/no space left on device|disk full|read-only file system/i, {
      errorType: 'file',
      severity: 'high',
      description: '文件系统问题',
      possibleCauses: ['磁盘空间不足', '文件系统只读', '磁盘故障'],
      solutions: [
        {
          title: '清理磁盘空间',
          description: '删除不必要的文件释放空间',
          commands: ['df -h', 'du -sh /*', 'sudo apt-get clean'],
          difficulty: 'medium',
          estimatedTime: '10分钟',
          riskLevel: 'medium'
        },
        {
          title: '检查文件系统状态',
          description: '检查文件系统是否正常',
          commands: ['mount | grep ro', 'sudo fsck /dev/[设备名]'],
          difficulty: 'hard',
          estimatedTime: '15分钟',
          riskLevel: 'high'
        }
      ],
      confidence: 0.9
    })
  }

  /**
   * 初始化命令优化规则
   */
  private initializeOptimizations(): void {
    // ls命令优化
    this.commandOptimizations.set(/^ls\s*$/, {
      originalCommand: 'ls',
      optimizedCommand: 'ls -la --color=auto',
      improvements: ['显示隐藏文件', '显示详细信息', '彩色输出'],
      performanceGain: '信息更全面',
      explanation: '添加-la参数显示所有文件的详细信息，--color参数提供彩色输出'
    })

    // grep命令优化
    this.commandOptimizations.set(/^grep\s+([^-].*)/, {
      originalCommand: 'grep $1',
      optimizedCommand: 'grep -n --color=auto $1',
      improvements: ['显示行号', '彩色高亮匹配'],
      performanceGain: '更易定位结果',
      explanation: '添加-n显示行号，--color高亮匹配内容'
    })

    // find命令优化
    this.commandOptimizations.set(/^find\s+\.\s+-name\s+(.+)/, {
      originalCommand: 'find . -name $1',
      optimizedCommand: 'find . -type f -name $1 2>/dev/null',
      improvements: ['只查找文件', '隐藏错误信息'],
      performanceGain: '减少无用输出，提升性能',
      explanation: '添加-type f只查找文件，2>/dev/null隐藏权限错误'
    })
  }

  /**
   * 诊断错误
   */
  diagnoseError(context: ErrorContext): DiagnosisResult | null {
    const output = context.output.toLowerCase()

    // 遍历错误模式
    for (const [pattern, diagnosis] of this.errorPatterns) {
      if (pattern.test(output)) {
        // 记录学习数据
        this.addLearningData(context)

        // 基于上下文调整诊断结果
        return this.enhanceDiagnosis(diagnosis, context)
      }
    }

    // 如果没有匹配的模式，尝试AI分析
    return this.aiAnalyzeError(context)
  }

  /**
   * 优化命令
   */
  optimizeCommand(command: string): CommandOptimization | null {
    for (const [pattern, optimization] of this.commandOptimizations) {
      const match = command.match(pattern)
      if (match) {
        // 替换占位符
        let optimizedCommand = optimization.optimizedCommand
        for (let i = 1; i < match.length; i++) {
          optimizedCommand = optimizedCommand.replace(`$${i}`, match[i])
        }

        return {
          ...optimization,
          originalCommand: command,
          optimizedCommand
        }
      }
    }

    return null
  }

  /**
   * 获取AI洞察
   */
  getAIInsights(context: { command?: string; output?: string; history?: string[] }): AIInsight[] {
    const insights: AIInsight[] = []

    // 基于命令历史的学习建议
    if (context.history && context.history.length > 0) {
      const frequentCommands = this.analyzeCommandFrequency(context.history)

      if (frequentCommands.length > 0) {
        insights.push({
          type: 'tip',
          title: '常用命令优化建议',
          content: `您经常使用 ${frequentCommands[0]}，建议创建别名以提高效率`,
          actionable: true,
          commands: [`alias ${frequentCommands[0].split(' ')[0]}='${frequentCommands[0]}'`]
        })
      }
    }

    // 基于当前命令的优化建议
    if (context.command) {
      const optimization = this.optimizeCommand(context.command)
      if (optimization) {
        insights.push({
          type: 'optimization',
          title: '命令优化建议',
          content: optimization.explanation,
          actionable: true,
          commands: [optimization.optimizedCommand]
        })
      }
    }

    // 安全提醒
    if (context.command && this.isRiskyCommand(context.command)) {
      insights.push({
        type: 'warning',
        title: '安全提醒',
        content: '检测到潜在风险命令，请确认操作安全性',
        actionable: false
      })
    }

    return insights
  }

  /**
   * 增强诊断结果
   */
  private enhanceDiagnosis(diagnosis: DiagnosisResult, context: ErrorContext): DiagnosisResult {
    // 基于上下文调整置信度
    let confidence = diagnosis.confidence

    // 如果是重复错误，提高置信度
    const similarErrors = this.learningData.filter(
      (data) => data.command === context.command && data.output.includes(context.output.substring(0, 50))
    )

    if (similarErrors.length > 0) {
      confidence = Math.min(0.95, confidence + 0.1)
    }

    return {
      ...diagnosis,
      confidence
    }
  }

  /**
   * AI分析错误（简化版本）
   */
  private aiAnalyzeError(context: ErrorContext): DiagnosisResult | null {
    // 这里可以集成真实的AI服务
    // 目前返回通用诊断
    return {
      errorType: 'unknown',
      severity: 'medium',
      description: '未知错误，需要进一步分析',
      possibleCauses: ['命令语法错误', '环境配置问题', '系统状态异常'],
      solutions: [
        {
          title: '检查命令语法',
          description: '确认命令语法正确',
          steps: ['查看命令帮助文档', '检查参数格式', '确认文件路径'],
          difficulty: 'easy',
          estimatedTime: '3分钟',
          riskLevel: 'low'
        }
      ],
      confidence: 0.3
    }
  }

  /**
   * 分析命令频率
   */
  private analyzeCommandFrequency(history: string[]): string[] {
    const frequency = new Map<string, number>()

    history.forEach((cmd) => {
      const count = frequency.get(cmd) || 0
      frequency.set(cmd, count + 1)
    })

    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([cmd]) => cmd)
  }

  /**
   * 检查是否为风险命令
   */
  private isRiskyCommand(command: string): boolean {
    const riskyPatterns = [/rm\s+-rf?\s+[\/~]/, /sudo\s+.*--force/, /chmod\s+777/, /dd\s+.*of=\/dev\//]

    return riskyPatterns.some((pattern) => pattern.test(command))
  }

  /**
   * 添加学习数据
   */
  private addLearningData(context: ErrorContext): void {
    this.learningData.push(context)

    // 保持数据量限制
    if (this.learningData.length > this.maxLearningData) {
      this.learningData.shift()
    }
  }

  /**
   * 获取学习统计
   */
  getLearningStats(): {
    totalErrors: number
    errorTypes: Record<string, number>
    commonCommands: string[]
    improvementSuggestions: string[]
  } {
    const errorTypes: Record<string, number> = {}
    const commandFreq = new Map<string, number>()

    this.learningData.forEach((data) => {
      // 统计错误类型（简化）
      if (data.output.includes('permission')) errorTypes.permission = (errorTypes.permission || 0) + 1
      if (data.output.includes('not found')) errorTypes.notFound = (errorTypes.notFound || 0) + 1
      if (data.output.includes('network')) errorTypes.network = (errorTypes.network || 0) + 1

      // 统计命令频率
      const cmd = data.command.split(' ')[0]
      commandFreq.set(cmd, (commandFreq.get(cmd) || 0) + 1)
    })

    const commonCommands = Array.from(commandFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([cmd]) => cmd)

    return {
      totalErrors: this.learningData.length,
      errorTypes,
      commonCommands,
      improvementSuggestions: ['考虑为常用命令创建别名', '学习使用命令行工具的高级功能', '定期检查系统状态和日志']
    }
  }
}

// 单例实例
export const aiAssistant = new AIAssistant()
