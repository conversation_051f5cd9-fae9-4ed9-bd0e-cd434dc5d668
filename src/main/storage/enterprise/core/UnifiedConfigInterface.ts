/**
 * 企业资源同步系统 - 统一配置接口
 * 功能：提供统一的配置访问接口，实现智能故障转移和负载均衡
 * 依赖：企业配置管理器、资源分离管理器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import {
  EnterpriseConfigBackend,
  EnterpriseResource,
  HealthCheckResult,
  FailoverConfig,
  EnterpriseSyncResult,
  SyncStatus,
  ResourceType,
  Environment
} from '../types/EnterpriseTypes'
import { EnterpriseConfigManager } from '../manager/EnterpriseConfigManager'
import { ResourceSeparationManager } from '../manager/ResourceSeparationManager'

/**
 * 故障转移状态
 */
interface FailoverState {
  backend_id: string
  failure_count: number
  last_failure: Date
  is_failed_over: boolean
  recovery_attempts: number
}

/**
 * 配置加载结果
 */
interface ConfigLoadResult {
  success: boolean
  data?: EnterpriseResource[]
  source_backend?: string
  message: string
  load_time_ms: number
}

/**
 * 配置保存结果
 */
interface ConfigSaveResult {
  success: boolean
  saved_backends: string[]
  failed_backends: string[]
  message: string
}

/**
 * 统一配置接口
 * 提供统一的企业资源配置访问接口，支持智能故障转移
 */
export class UnifiedConfigInterface extends EventEmitter {
  private static instance: UnifiedConfigInterface | null = null
  private configManager: EnterpriseConfigManager
  private separationManager: ResourceSeparationManager
  private failoverStates: Map<string, FailoverState> = new Map()
  private failoverConfig: FailoverConfig
  private healthCheckInterval: NodeJS.Timeout | null = null

  /**
   * 获取接口单例实例
   * @returns 接口实例
   */
  static getInstance(): UnifiedConfigInterface {
    if (!UnifiedConfigInterface.instance) {
      UnifiedConfigInterface.instance = new UnifiedConfigInterface()
    }
    return UnifiedConfigInterface.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()
    this.configManager = EnterpriseConfigManager.getInstance()
    this.separationManager = ResourceSeparationManager.getInstance()

    // 默认故障转移配置
    this.failoverConfig = {
      enabled: true,
      max_retries: 3,
      retry_delay_ms: 5000,
      health_check_interval_ms: 60000,
      failover_threshold: 3
    }

    this.initializeEventListeners()
    this.startHealthCheckMonitoring()
  }

  /**
   * 更新故障转移配置
   * @param config 故障转移配置
   */
  updateFailoverConfig(config: Partial<FailoverConfig>): void {
    this.failoverConfig = { ...this.failoverConfig, ...config }

    // 重启健康检查监控
    this.stopHealthCheckMonitoring()
    if (this.failoverConfig.enabled) {
      this.startHealthCheckMonitoring()
    }

    this.emit('failoverConfigUpdated', this.failoverConfig)
  }

  /**
   * 获取故障转移配置
   * @returns 故障转移配置
   */
  getFailoverConfig(): FailoverConfig {
    return { ...this.failoverConfig }
  }

  /**
   * 加载企业资源配置
   * @param filter 过滤条件
   * @returns 配置加载结果
   */
  async loadEnterpriseConfig(filter?: { resource_type?: ResourceType; environment?: Environment; tags?: string[] }): Promise<ConfigLoadResult> {
    const startTime = Date.now()
    const enabledBackends = await this.getHealthyBackends()

    if (enabledBackends.length === 0) {
      return {
        success: false,
        message: '没有可用的健康后端',
        load_time_ms: Date.now() - startTime
      }
    }

    // 按优先级尝试加载配置
    for (const backend of enabledBackends) {
      try {
        const result = await this.loadFromBackend(backend, filter)
        if (result.success) {
          // 重置故障状态
          this.resetFailoverState(backend.id)

          return {
            ...result,
            source_backend: backend.name,
            load_time_ms: Date.now() - startTime
          }
        }
      } catch (error: any) {
        console.warn(`从后端加载配置失败 [${backend.name}]:`, error)
        await this.handleBackendFailure(backend.id, error)
        continue
      }
    }

    return {
      success: false,
      message: '所有后端都加载失败',
      load_time_ms: Date.now() - startTime
    }
  }

  /**
   * 保存企业资源配置
   * @param resources 资源列表
   * @param options 保存选项
   * @returns 配置保存结果
   */
  async saveEnterpriseConfig(
    resources: EnterpriseResource[],
    options?: {
      target_backends?: string[]
      sync_mode?: 'all' | 'primary_only'
      validate_before_save?: boolean
    }
  ): Promise<ConfigSaveResult> {
    const savedBackends: string[] = []
    const failedBackends: string[] = []
    const errors: string[] = []

    // 确定目标后端
    let targetBackends: EnterpriseConfigBackend[]
    if (options?.target_backends) {
      const allBackends = await this.configManager.getAllBackends()
      targetBackends = allBackends.filter((b) => options.target_backends!.includes(b.id) && b.enabled)
    } else {
      targetBackends = await this.getHealthyBackends()
    }

    if (targetBackends.length === 0) {
      return {
        success: false,
        saved_backends: [],
        failed_backends: [],
        message: '没有可用的目标后端'
      }
    }

    // 验证资源（如果需要）
    if (options?.validate_before_save) {
      const validationErrors = this.validateResources(resources)
      if (validationErrors.length > 0) {
        return {
          success: false,
          saved_backends: [],
          failed_backends: [],
          message: `资源验证失败: ${validationErrors.join(', ')}`
        }
      }
    }

    // 根据同步模式决定保存策略
    if (options?.sync_mode === 'primary_only') {
      // 只保存到主后端（优先级最高的）
      const primaryBackend = targetBackends[0]
      try {
        await this.saveToBackend(primaryBackend, resources)
        savedBackends.push(primaryBackend.id)
        this.resetFailoverState(primaryBackend.id)
      } catch (error: any) {
        failedBackends.push(primaryBackend.id)
        errors.push(`${primaryBackend.name}: ${error.message}`)
        await this.handleBackendFailure(primaryBackend.id, error)
      }
    } else {
      // 保存到所有可用后端
      const savePromises = targetBackends.map(async (backend) => {
        try {
          await this.saveToBackend(backend, resources)
          savedBackends.push(backend.id)
          this.resetFailoverState(backend.id)
        } catch (error: any) {
          failedBackends.push(backend.id)
          errors.push(`${backend.name}: ${error.message}`)
          await this.handleBackendFailure(backend.id, error)
        }
      })

      await Promise.allSettled(savePromises)
    }

    const success = savedBackends.length > 0
    const message = success ? `成功保存到 ${savedBackends.length} 个后端` : `保存失败: ${errors.join(', ')}`

    const result: ConfigSaveResult = {
      success,
      saved_backends: savedBackends,
      failed_backends: failedBackends,
      message
    }

    this.emit('configSaved', result)
    return result
  }

  /**
   * 同步企业资源到所有后端
   * @param force 是否强制同步
   * @returns 同步结果
   */
  async syncToAllBackends(force: boolean = false): Promise<EnterpriseSyncResult[]> {
    const backends = force ? await this.configManager.getEnabledBackends() : await this.getHealthyBackends()

    const results: EnterpriseSyncResult[] = []

    for (const backend of backends) {
      const startTime = Date.now()
      try {
        // TODO: 实现具体的同步逻辑
        const syncResult = await this.syncBackend(backend)

        results.push({
          ...syncResult,
          backend_id: backend.id,
          backend_name: backend.name,
          sync_type: 'full',
          duration_ms: Date.now() - startTime,
          resources_synced: syncResult.filesUploaded + syncResult.filesDownloaded
        })

        this.resetFailoverState(backend.id)
      } catch (error: any) {
        results.push({
          success: false,
          message: `同步失败: ${error.message}`,
          filesUploaded: 0,
          filesDownloaded: 0,
          filesDeleted: 0,
          errors: [error.message],
          conflicts: [],
          backend_id: backend.id,
          backend_name: backend.name,
          sync_type: 'full',
          duration_ms: Date.now() - startTime,
          resources_synced: 0
        })

        await this.handleBackendFailure(backend.id, error)
      }
    }

    this.emit('allBackendsSynced', results)
    return results
  }

  /**
   * 获取健康的后端列表（按优先级排序）
   * @returns 健康的后端列表
   */
  async getHealthyBackends(): Promise<EnterpriseConfigBackend[]> {
    const enabledBackends = await this.configManager.getEnabledBackends()

    return enabledBackends.filter((backend) => {
      const failoverState = this.failoverStates.get(backend.id)

      // 如果没有故障状态记录，认为是健康的
      if (!failoverState) {
        return true
      }

      // 如果已经故障转移，检查是否可以恢复
      if (failoverState.is_failed_over) {
        const timeSinceFailure = Date.now() - failoverState.last_failure.getTime()
        const recoveryDelay = this.failoverConfig.retry_delay_ms * Math.pow(2, failoverState.recovery_attempts)
        return timeSinceFailure > recoveryDelay
      }

      // 检查故障次数是否超过阈值
      return failoverState.failure_count < this.failoverConfig.failover_threshold
    })
  }

  /**
   * 获取故障转移状态统计
   * @returns 故障转移状态统计
   */
  getFailoverStats(): {
    total_backends: number
    healthy_backends: number
    failed_backends: number
    recovering_backends: number
  } {
    const allBackends = Array.from(this.failoverStates.keys())
    const failedBackends = Array.from(this.failoverStates.values()).filter((s) => s.is_failed_over)
    const recoveringBackends = failedBackends.filter((s) => s.recovery_attempts > 0)

    return {
      total_backends: allBackends.length,
      healthy_backends: allBackends.length - failedBackends.length,
      failed_backends: failedBackends.length,
      recovering_backends: recoveringBackends.length
    }
  }

  /**
   * 手动触发后端恢复
   * @param backendId 后端ID
   * @returns 是否成功
   */
  async triggerBackendRecovery(backendId: string): Promise<boolean> {
    try {
      const result = await this.configManager.testBackendConnection(backendId)

      if (result.healthy) {
        this.resetFailoverState(backendId)
        this.emit('backendRecovered', { backend_id: backendId })
        return true
      } else {
        const state = this.failoverStates.get(backendId)
        if (state) {
          state.recovery_attempts++
        }
        return false
      }
    } catch (error) {
      console.warn(`后端恢复失败 [${backendId}]:`, error)
      return false
    }
  }

  /**
   * 从指定后端加载配置
   * @param backend 后端配置
   * @param filter 过滤条件
   * @returns 加载结果
   */
  private async loadFromBackend(backend: EnterpriseConfigBackend, filter?: any): Promise<{ success: boolean; data?: EnterpriseResource[] }> {
    // TODO: 实现具体的后端加载逻辑
    // 这里需要根据不同的后端类型调用相应的适配器

    // 模拟加载过程
    await new Promise((resolve) => setTimeout(resolve, 100))

    return {
      success: true,
      data: [] // 返回空数组作为示例
    }
  }

  /**
   * 保存配置到指定后端
   * @param backend 后端配置
   * @param resources 资源列表
   */
  private async saveToBackend(backend: EnterpriseConfigBackend, resources: EnterpriseResource[]): Promise<void> {
    // TODO: 实现具体的后端保存逻辑
    // 这里需要根据不同的后端类型调用相应的适配器

    // 模拟保存过程
    await new Promise((resolve) => setTimeout(resolve, 100))
  }

  /**
   * 同步指定后端
   * @param backend 后端配置
   * @returns 同步结果
   */
  private async syncBackend(backend: EnterpriseConfigBackend): Promise<any> {
    // TODO: 实现具体的后端同步逻辑

    // 模拟同步过程
    await new Promise((resolve) => setTimeout(resolve, 200))

    return {
      success: true,
      message: '同步成功',
      filesUploaded: 0,
      filesDownloaded: 0,
      filesDeleted: 0,
      errors: [],
      conflicts: []
    }
  }

  /**
   * 处理后端故障
   * @param backendId 后端ID
   * @param error 错误信息
   */
  private async handleBackendFailure(backendId: string, error: any): Promise<void> {
    let state = this.failoverStates.get(backendId)

    if (!state) {
      state = {
        backend_id: backendId,
        failure_count: 0,
        last_failure: new Date(),
        is_failed_over: false,
        recovery_attempts: 0
      }
      this.failoverStates.set(backendId, state)
    }

    state.failure_count++
    state.last_failure = new Date()

    // 检查是否需要故障转移
    if (state.failure_count >= this.failoverConfig.failover_threshold && !state.is_failed_over) {
      state.is_failed_over = true

      this.emit('backendFailedOver', {
        backend_id: backendId,
        failure_count: state.failure_count,
        error: error.message
      })
    }

    this.emit('backendFailure', {
      backend_id: backendId,
      error: error.message,
      failure_count: state.failure_count
    })
  }

  /**
   * 重置故障转移状态
   * @param backendId 后端ID
   */
  private resetFailoverState(backendId: string): void {
    const state = this.failoverStates.get(backendId)
    if (state && (state.failure_count > 0 || state.is_failed_over)) {
      this.failoverStates.delete(backendId)

      this.emit('backendRecovered', {
        backend_id: backendId,
        previous_failures: state.failure_count
      })
    }
  }

  /**
   * 验证资源列表
   * @param resources 资源列表
   * @returns 验证错误列表
   */
  private validateResources(resources: EnterpriseResource[]): string[] {
    const errors: string[] = []

    for (const resource of resources) {
      if (!resource.name || resource.name.trim() === '') {
        errors.push(`资源名称不能为空: ${resource.id}`)
      }

      if (!resource.config_data) {
        errors.push(`资源配置数据不能为空: ${resource.id}`)
      }

      if (!Object.values(ResourceType).includes(resource.resource_type)) {
        errors.push(`无效的资源类型: ${resource.resource_type}`)
      }
    }

    return errors
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 监听配置管理器事件
    this.configManager.on('backendAdded', (backend) => {
      this.emit('backendConfigChanged', { type: 'added', backend })
    })

    this.configManager.on('backendUpdated', (backend) => {
      this.emit('backendConfigChanged', { type: 'updated', backend })
    })

    this.configManager.on('backendRemoved', (backend) => {
      // 清理故障转移状态
      this.failoverStates.delete(backend.id)
      this.emit('backendConfigChanged', { type: 'removed', backend })
    })

    this.configManager.on('healthCheckCompleted', (result: HealthCheckResult) => {
      if (!result.healthy) {
        this.handleBackendFailure(result.backend_id, new Error(result.error_message || '健康检查失败'))
      } else {
        this.resetFailoverState(result.backend_id)
      }
    })
  }

  /**
   * 开始健康检查监控
   */
  private startHealthCheckMonitoring(): void {
    if (this.healthCheckInterval) {
      return
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        const backends = await this.configManager.getEnabledBackends()

        // 并行执行健康检查
        const healthCheckPromises = backends.map((backend) =>
          this.configManager.testBackendConnection(backend.id).catch((error) => {
            console.warn(`定时健康检查失败 [${backend.name}]:`, error)
            return null
          })
        )

        await Promise.allSettled(healthCheckPromises)
      } catch (error) {
        console.warn('健康检查监控异常:', error)
      }
    }, this.failoverConfig.health_check_interval_ms)
  }

  /**
   * 停止健康检查监控
   */
  private stopHealthCheckMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
      this.healthCheckInterval = null
    }
  }

  /**
   * 销毁接口，清理资源
   */
  destroy(): void {
    this.stopHealthCheckMonitoring()
    this.failoverStates.clear()
    this.removeAllListeners()
  }
}
