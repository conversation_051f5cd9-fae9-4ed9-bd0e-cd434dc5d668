<template>
  <div class="unified-resource-selector">
    <!-- 选择器头部 -->
    <div class="selector-header">
      <h3 class="selector-title">{{ title }}</h3>
      <div class="selector-filters">
        <!-- 资源类型筛选 -->
        <div class="filter-group">
          <label>资源类型:</label>
          <select
            v-model="selectedResourceType"
            @change="loadResources"
          >
            <option value="">全部</option>
            <option value="host">主机设备</option>
            <option value="network_device">网络设备</option>
            <option value="asset">固定资产</option>
            <option value="room">机房</option>
            <option value="monitor_target">监控目标</option>
          </select>
        </div>

        <!-- 状态筛选 -->
        <div class="filter-group">
          <label>状态:</label>
          <select
            v-model="selectedStatus"
            @change="loadResources"
          >
            <option value="">全部</option>
            <option value="online">在线</option>
            <option value="offline">离线</option>
            <option value="normal">正常</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
            <option value="maintenance">维护中</option>
          </select>
        </div>

        <!-- 环境筛选（仅主机） -->
        <div
          class="filter-group"
          v-if="selectedResourceType === 'host'"
        >
          <label>环境:</label>
          <select
            v-model="selectedEnvironment"
            @change="loadResources"
          >
            <option value="">全部</option>
            <option value="production">生产环境</option>
            <option value="staging">测试环境</option>
            <option value="development">开发环境</option>
          </select>
        </div>

        <!-- 搜索框 -->
        <div class="filter-group">
          <label>搜索:</label>
          <input
            type="text"
            v-model="searchQuery"
            @input="loadResources"
            placeholder="搜索名称、IP地址..."
            class="search-input"
          />
        </div>
      </div>
    </div>

    <!-- 资源列表 -->
    <div class="resource-list">
      <div class="list-header">
        <div class="list-stats"> 找到 {{ filteredResources.length }} 个资源 </div>
        <div class="list-actions">
          <button
            @click="refreshData"
            class="btn-refresh"
          >
            <RefreshCw class="icon" />
            刷新
          </button>
        </div>
      </div>

      <!-- 资源表格 -->
      <div class="resource-table">
        <table>
          <thead>
            <tr>
              <th v-if="multiple">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  @change="toggleSelectAll"
                />
              </th>
              <th>名称</th>
              <th>类型</th>
              <th>地址/位置</th>
              <th>状态</th>
              <th>描述</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="resource in paginatedResources"
              :key="resource.id"
              :class="{
                selected: isSelected(resource),
                clickable: !multiple
              }"
              @click="!multiple && selectResource(resource)"
            >
              <td v-if="multiple">
                <input
                  type="checkbox"
                  :checked="isSelected(resource)"
                  @change="toggleResource(resource)"
                />
              </td>
              <td class="resource-name">
                <div class="name-info">
                  <span class="name">{{ resource.name }}</span>
                  <span
                    class="type-badge"
                    :class="resource.type"
                  >
                    {{ getTypeLabel(resource.type) }}
                  </span>
                </div>
              </td>
              <td>{{ getTypeLabel(resource.type) }}</td>
              <td class="resource-address">
                <span v-if="'ip' in resource">{{ resource.ip }}</span>
                <span v-else-if="'location' in resource">{{ resource.location }}</span>
                <span v-else>-</span>
              </td>
              <td>
                <span
                  class="status-badge"
                  :class="resource.status"
                >
                  {{ getStatusLabel(resource.status) }}
                </span>
              </td>
              <td class="resource-description">
                {{ resource.description || '-' }}
              </td>
              <td class="resource-actions">
                <button
                  v-if="!multiple"
                  @click.stop="selectResource(resource)"
                  class="btn-select"
                >
                  选择
                </button>
                <button
                  @click.stop="viewResourceDetails(resource)"
                  class="btn-view"
                >
                  详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div
        class="pagination"
        v-if="totalPages > 1"
      >
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="btn-page"
        >
          上一页
        </button>
        <span class="page-info"> 第 {{ currentPage }} 页，共 {{ totalPages }} 页 </span>
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="btn-page"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 选择结果 -->
    <div
      class="selection-result"
      v-if="selectedResources.length > 0"
    >
      <h4>已选择的资源 ({{ selectedResources.length }})</h4>
      <div class="selected-items">
        <div
          v-for="resource in selectedResources"
          :key="resource.id"
          class="selected-item"
        >
          <span class="item-name">{{ resource.name }}</span>
          <span class="item-type">{{ getTypeLabel(resource.type) }}</span>
          <button
            @click="removeSelection(resource)"
            class="btn-remove"
          >
            ×
          </button>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="selector-actions">
      <button
        @click="cancel"
        class="btn-cancel"
      >
        取消
      </button>
      <button
        @click="confirm"
        :disabled="selectedResources.length === 0"
        class="btn-confirm"
      >
        确认选择 ({{ selectedResources.length }})
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { RefreshCw } from 'lucide-vue-next'
import unifiedDataService, { UnifiedResource } from '../../services/unifiedEnterpriseDataService'

// Props
interface Props {
  title?: string
  multiple?: boolean
  resourceTypes?: string[]
  filters?: any
  excludeIds?: (string | number)[]
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择资源',
  multiple: false,
  resourceTypes: () => ['host', 'network_device', 'asset', 'room', 'monitor_target'],
  filters: () => ({}),
  excludeIds: () => []
})

// Emits
const emit = defineEmits<{
  select: [resources: UnifiedResource[]]
  cancel: []
}>()

// 响应式数据
const allResources = ref<UnifiedResource[]>([])
const selectedResources = ref<UnifiedResource[]>([])
const selectedResourceType = ref('')
const selectedStatus = ref('')
const selectedEnvironment = ref('')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = 10

// 计算属性
const filteredResources = computed(() => {
  let resources = allResources.value

  // 按资源类型筛选
  if (selectedResourceType.value) {
    resources = resources.filter((r) => r.type === selectedResourceType.value)
  }

  // 按状态筛选
  if (selectedStatus.value) {
    resources = resources.filter((r) => r.status === selectedStatus.value)
  }

  // 按环境筛选（仅主机）
  if (selectedEnvironment.value && selectedResourceType.value === 'host') {
    resources = resources.filter((r) => 'environment' in r && r.environment === selectedEnvironment.value)
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    resources = resources.filter((r) => {
      const matchName = r.name.toLowerCase().includes(query)
      const matchIp = 'ip' in r && r.ip.includes(query)
      const matchLocation = 'location' in r && r.location?.toLowerCase().includes(query)
      return matchName || matchIp || matchLocation
    })
  }

  // 排除指定ID
  if (props.excludeIds.length > 0) {
    resources = resources.filter((r) => !props.excludeIds.includes(r.id))
  }

  return resources
})

const paginatedResources = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredResources.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredResources.value.length / pageSize)
})

const isAllSelected = computed(() => {
  return paginatedResources.value.length > 0 && paginatedResources.value.every((r) => isSelected(r))
})

// 方法
const loadResources = () => {
  allResources.value = []

  // 根据资源类型加载数据
  if (!selectedResourceType.value || selectedResourceType.value === 'host') {
    allResources.value.push(...unifiedDataService.getHosts(props.filters))
  }
  if (!selectedResourceType.value || selectedResourceType.value === 'network_device') {
    allResources.value.push(...unifiedDataService.getNetworkDevices(props.filters))
  }
  if (!selectedResourceType.value || selectedResourceType.value === 'asset') {
    allResources.value.push(...unifiedDataService.getAssets(props.filters))
  }
  if (!selectedResourceType.value || selectedResourceType.value === 'room') {
    allResources.value.push(...unifiedDataService.getRooms(props.filters))
  }
  if (!selectedResourceType.value || selectedResourceType.value === 'monitor_target') {
    allResources.value.push(...unifiedDataService.getMonitorTargets(props.filters))
  }

  // 重置分页
  currentPage.value = 1
}

const isSelected = (resource: UnifiedResource): boolean => {
  return selectedResources.value.some((r) => r.id === resource.id)
}

const selectResource = (resource: UnifiedResource) => {
  if (props.multiple) {
    toggleResource(resource)
  } else {
    selectedResources.value = [resource]
    confirm()
  }
}

const toggleResource = (resource: UnifiedResource) => {
  const index = selectedResources.value.findIndex((r) => r.id === resource.id)
  if (index > -1) {
    selectedResources.value.splice(index, 1)
  } else {
    selectedResources.value.push(resource)
  }
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 取消选择当前页所有项
    paginatedResources.value.forEach((resource) => {
      const index = selectedResources.value.findIndex((r) => r.id === resource.id)
      if (index > -1) {
        selectedResources.value.splice(index, 1)
      }
    })
  } else {
    // 选择当前页所有项
    paginatedResources.value.forEach((resource) => {
      if (!isSelected(resource)) {
        selectedResources.value.push(resource)
      }
    })
  }
}

const removeSelection = (resource: UnifiedResource) => {
  const index = selectedResources.value.findIndex((r) => r.id === resource.id)
  if (index > -1) {
    selectedResources.value.splice(index, 1)
  }
}

const refreshData = () => {
  loadResources()
}

const viewResourceDetails = (resource: UnifiedResource) => {
  // 显示资源详情（可以实现为弹窗或跳转）
  console.log('查看资源详情:', resource)
}

const confirm = () => {
  emit('select', selectedResources.value)
}

const cancel = () => {
  emit('cancel')
}

const getTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    host: '主机设备',
    network_device: '网络设备',
    asset: '固定资产',
    room: '机房',
    monitor_target: '监控目标'
  }
  return labels[type] || type
}

const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    normal: '正常',
    warning: '警告',
    error: '错误',
    critical: '严重',
    maintenance: '维护中'
  }
  return labels[status] || status
}

// 生命周期
onMounted(() => {
  loadResources()
})

// 监听资源类型变化
watch(
  () => props.resourceTypes,
  () => {
    loadResources()
  },
  { immediate: true }
)
</script>

<style scoped>
.unified-resource-selector {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-height: 80vh;
  overflow-y: auto;
}

.selector-header {
  margin-bottom: 20px;
}

.selector-title {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.selector-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-group label {
  font-size: 14px;
  color: #64748b;
  white-space: nowrap;
}

.filter-group select,
.search-input {
  padding: 6px 10px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 4px;
  font-size: 14px;
  background: var(--bg-color-secondary, #ffffff);
  color: var(--text-color, #374151);
}

.search-input {
  min-width: 200px;
}

.search-input:focus,
.filter-group select:focus {
  outline: none;
  border-color: var(--input-focus-border, #3b82f6);
  box-shadow: var(--input-focus-shadow, 0 0 0 3px rgba(59, 130, 246, 0.1));
}

.search-input::placeholder {
  color: var(--text-color-tertiary, #9ca3af);
}

.resource-list {
  margin-bottom: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.list-stats {
  color: #64748b;
  font-size: 14px;
}

.btn-refresh {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-refresh:hover {
  background: #e2e8f0;
}

.resource-table {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.resource-table table {
  width: 100%;
  border-collapse: collapse;
}

.resource-table th,
.resource-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.resource-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.resource-table tr.clickable:hover {
  background: #f9fafb;
  cursor: pointer;
}

.resource-table tr.selected {
  background: #eff6ff;
}

.name-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.type-badge {
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.host {
  background: #dbeafe;
  color: #1e40af;
}
.type-badge.network_device {
  background: #dcfce7;
  color: #166534;
}
.type-badge.asset {
  background: #fef3c7;
  color: #92400e;
}
.type-badge.room {
  background: #e0e7ff;
  color: #3730a3;
}
.type-badge.monitor_target {
  background: #fce7f3;
  color: #be185d;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.online,
.status-badge.normal {
  background: #dcfce7;
  color: #166534;
}
.status-badge.offline,
.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}
.status-badge.warning {
  background: #fef3c7;
  color: #d97706;
}
.status-badge.maintenance {
  background: #e0e7ff;
  color: #4338ca;
}

.resource-actions {
  display: flex;
  gap: 8px;
}

.btn-select,
.btn-view {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
}

.btn-select {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-select:hover {
  background: #2563eb;
}

.btn-view:hover {
  background: #f9fafb;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 15px;
}

.btn-page {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-page:not(:disabled):hover {
  background: #f9fafb;
}

.page-info {
  color: #64748b;
  font-size: 14px;
}

.selection-result {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 6px;
}

.selection-result h4 {
  margin: 0 0 10px 0;
  color: #1e293b;
  font-size: 14px;
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.item-type {
  color: #64748b;
  font-size: 12px;
}

.btn-remove {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-remove:hover {
  background: #fee2e2;
  border-radius: 50%;
}

.selector-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #e5e7eb;
}

.btn-cancel,
.btn-confirm {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-cancel:hover {
  background: #f9fafb;
}

.btn-confirm {
  background: #10b981;
  border: 1px solid #10b981;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #059669;
}

.btn-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
