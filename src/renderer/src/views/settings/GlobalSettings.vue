<template>
  <div class="global-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Settings class="title-icon" />
          全局设置
        </h1>
        <p class="page-description">配置系统级参数、功能开关和性能调优选项</p>
      </div>
      <div class="header-actions">
        <a-button @click="resetToDefaults">
          <RotateCcw class="btn-icon" />
          恢复默认
        </a-button>
        <a-button
          type="primary"
          :loading="saving"
          @click="saveSettings"
        >
          <Save class="btn-icon" />
          保存设置
        </a-button>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <a-row :gutter="24">
        <!-- 左侧设置菜单 -->
        <a-col :span="6">
          <a-card class="settings-menu">
            <a-menu
              v-model:selected-keys="selectedMenuKeys"
              mode="inline"
              @click="handleMenuClick"
            >
              <a-menu-item key="general">
                <Globe class="menu-icon" />
                常规设置
              </a-menu-item>
              <a-menu-item key="sync">
                <RefreshCw class="menu-icon" />
                同步配置
              </a-menu-item>
              <a-menu-item key="security">
                <Shield class="menu-icon" />
                安全设置
              </a-menu-item>
              <a-menu-item key="performance">
                <Zap class="menu-icon" />
                性能优化
              </a-menu-item>
              <a-menu-item key="notification">
                <Bell class="menu-icon" />
                通知设置
              </a-menu-item>
              <a-menu-item key="backup">
                <Database class="menu-icon" />
                备份恢复
              </a-menu-item>
              <a-menu-item key="advanced">
                <Cpu class="menu-icon" />
                高级设置
              </a-menu-item>
            </a-menu>
          </a-card>
        </a-col>

        <!-- 右侧设置面板 -->
        <a-col :span="18">
          <!-- 常规设置 -->
          <a-card
            v-show="activeTab === 'general'"
            title="常规设置"
            class="settings-panel"
          >
            <a-form
              :model="settings.general"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="系统语言">
                    <a-select v-model:value="settings.general.language">
                      <a-select-option value="zh-CN">简体中文</a-select-option>
                      <a-select-option value="en-US">English</a-select-option>
                      <a-select-option value="ja-JP">日本語</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="时区">
                    <a-select v-model:value="settings.general.timezone">
                      <a-select-option value="Asia/Shanghai">Asia/Shanghai (UTC+8)</a-select-option>
                      <a-select-option value="America/New_York">America/New_York (UTC-5)</a-select-option>
                      <a-select-option value="Europe/London">Europe/London (UTC+0)</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="主题模式">
                    <a-radio-group v-model:value="settings.general.theme">
                      <a-radio value="light">浅色模式</a-radio>
                      <a-radio value="dark">深色模式</a-radio>
                      <a-radio value="auto">跟随系统</a-radio>
                    </a-radio-group>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="启动时最小化">
                    <a-switch v-model:checked="settings.general.startMinimized" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="系统托盘">
                <a-checkbox v-model:checked="settings.general.enableSystemTray"> 启用系统托盘图标 </a-checkbox>
                <a-checkbox
                  v-model:checked="settings.general.minimizeToTray"
                  :disabled="!settings.general.enableSystemTray"
                >
                  最小化到系统托盘
                </a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 同步配置 -->
          <a-card
            v-show="activeTab === 'sync'"
            title="同步配置"
            class="settings-panel"
          >
            <a-form
              :model="settings.sync"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="自动同步间隔">
                    <a-select v-model:value="settings.sync.autoSyncInterval">
                      <a-select-option value="300">5分钟</a-select-option>
                      <a-select-option value="600">10分钟</a-select-option>
                      <a-select-option value="1800">30分钟</a-select-option>
                      <a-select-option value="3600">1小时</a-select-option>
                      <a-select-option value="0">禁用自动同步</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="同步重试次数">
                    <a-input-number
                      v-model:value="settings.sync.retryCount"
                      :min="0"
                      :max="10"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="冲突解决策略">
                    <a-select v-model:value="settings.sync.conflictResolution">
                      <a-select-option value="server_wins">服务器优先</a-select-option>
                      <a-select-option value="client_wins">客户端优先</a-select-option>
                      <a-select-option value="timestamp">时间戳优先</a-select-option>
                      <a-select-option value="manual">手动解决</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="网络超时时间">
                    <a-input-number
                      v-model:value="settings.sync.networkTimeout"
                      :min="5"
                      :max="300"
                      addon-after="秒"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="同步选项">
                <a-checkbox v-model:checked="settings.sync.enableCompression"> 启用数据压缩 </a-checkbox>
                <a-checkbox v-model:checked="settings.sync.enableEncryption"> 启用端到端加密 </a-checkbox>
                <a-checkbox v-model:checked="settings.sync.enableBatchSync"> 启用批量同步 </a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 安全设置 -->
          <a-card
            v-show="activeTab === 'security'"
            title="安全设置"
            class="settings-panel"
          >
            <a-form
              :model="settings.security"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="会话超时时间">
                    <a-select v-model:value="settings.security.sessionTimeout">
                      <a-select-option value="1800">30分钟</a-select-option>
                      <a-select-option value="3600">1小时</a-select-option>
                      <a-select-option value="7200">2小时</a-select-option>
                      <a-select-option value="28800">8小时</a-select-option>
                      <a-select-option value="0">永不超时</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="密码最小长度">
                    <a-input-number
                      v-model:value="settings.security.minPasswordLength"
                      :min="6"
                      :max="32"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="登录失败锁定">
                    <a-input-number
                      v-model:value="settings.security.maxLoginAttempts"
                      :min="3"
                      :max="10"
                      addon-after="次"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="锁定时间">
                    <a-input-number
                      v-model:value="settings.security.lockoutDuration"
                      :min="5"
                      :max="1440"
                      addon-after="分钟"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="安全选项">
                <a-checkbox v-model:checked="settings.security.enableTwoFactor"> 启用双因子认证 </a-checkbox>
                <a-checkbox v-model:checked="settings.security.enableAuditLog"> 启用审计日志 </a-checkbox>
                <a-checkbox v-model:checked="settings.security.enableIPWhitelist"> 启用IP白名单 </a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 性能优化 -->
          <a-card
            v-show="activeTab === 'performance'"
            title="性能优化"
            class="settings-panel"
          >
            <a-form
              :model="settings.performance"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="内存缓存大小">
                    <a-slider
                      v-model:value="settings.performance.cacheSize"
                      :min="64"
                      :max="1024"
                      :marks="{ 64: '64MB', 256: '256MB', 512: '512MB', 1024: '1GB' }"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="并发连接数">
                    <a-input-number
                      v-model:value="settings.performance.maxConnections"
                      :min="1"
                      :max="100"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="日志保留天数">
                    <a-input-number
                      v-model:value="settings.performance.logRetentionDays"
                      :min="7"
                      :max="365"
                      addon-after="天"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="数据库清理间隔">
                    <a-select v-model:value="settings.performance.dbCleanupInterval">
                      <a-select-option value="86400">每天</a-select-option>
                      <a-select-option value="604800">每周</a-select-option>
                      <a-select-option value="2592000">每月</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="性能选项">
                <a-checkbox v-model:checked="settings.performance.enableLazyLoading"> 启用懒加载 </a-checkbox>
                <a-checkbox v-model:checked="settings.performance.enableDataCompression"> 启用数据压缩 </a-checkbox>
                <a-checkbox v-model:checked="settings.performance.enableCaching"> 启用智能缓存 </a-checkbox>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 通知设置 -->
          <a-card
            v-show="activeTab === 'notification'"
            title="通知设置"
            class="settings-panel"
          >
            <a-form
              :model="settings.notification"
              layout="vertical"
            >
              <a-form-item label="通知类型">
                <a-checkbox v-model:checked="settings.notification.enableSyncNotifications"> 同步完成通知 </a-checkbox>
                <a-checkbox v-model:checked="settings.notification.enableErrorNotifications"> 错误警告通知 </a-checkbox>
                <a-checkbox v-model:checked="settings.notification.enableSecurityNotifications"> 安全事件通知 </a-checkbox>
                <a-checkbox v-model:checked="settings.notification.enableUpdateNotifications"> 系统更新通知 </a-checkbox>
              </a-form-item>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="通知方式">
                    <a-checkbox-group v-model:value="settings.notification.notificationMethods">
                      <a-checkbox value="desktop">桌面通知</a-checkbox>
                      <a-checkbox value="email">邮件通知</a-checkbox>
                      <a-checkbox value="webhook">Webhook</a-checkbox>
                    </a-checkbox-group>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="通知声音">
                    <a-switch v-model:checked="settings.notification.enableSound" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item
                v-if="settings.notification.notificationMethods.includes('email')"
                label="邮件配置"
              >
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-input
                      v-model:value="settings.notification.emailConfig.smtp"
                      placeholder="SMTP服务器"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-input
                      v-model:value="settings.notification.emailConfig.recipient"
                      placeholder="接收邮箱"
                    />
                  </a-col>
                </a-row>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 备份恢复 -->
          <a-card
            v-show="activeTab === 'backup'"
            title="备份恢复"
            class="settings-panel"
          >
            <a-form
              :model="settings.backup"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="自动备份">
                    <a-switch v-model:checked="settings.backup.enableAutoBackup" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="备份间隔">
                    <a-select
                      v-model:value="settings.backup.backupInterval"
                      :disabled="!settings.backup.enableAutoBackup"
                    >
                      <a-select-option value="86400">每天</a-select-option>
                      <a-select-option value="604800">每周</a-select-option>
                      <a-select-option value="2592000">每月</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="备份保留数量">
                    <a-input-number
                      v-model:value="settings.backup.maxBackups"
                      :min="1"
                      :max="50"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="备份路径">
                    <a-input
                      v-model:value="settings.backup.backupPath"
                      placeholder="选择备份目录"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="备份选项">
                <a-checkbox v-model:checked="settings.backup.includeUserData"> 包含用户数据 </a-checkbox>
                <a-checkbox v-model:checked="settings.backup.includeSystemConfig"> 包含系统配置 </a-checkbox>
                <a-checkbox v-model:checked="settings.backup.compressBackup"> 压缩备份文件 </a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-space>
                  <a-button
                    :loading="backupLoading"
                    @click="createBackup"
                  >
                    <Database class="btn-icon" />
                    立即备份
                  </a-button>
                  <a-button @click="restoreBackup">
                    <Upload class="btn-icon" />
                    恢复备份
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 高级设置 -->
          <a-card
            v-show="activeTab === 'advanced'"
            title="高级设置"
            class="settings-panel"
          >
            <a-form
              :model="settings.advanced"
              layout="vertical"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="调试模式">
                    <a-switch v-model:checked="settings.advanced.debugMode" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="开发者工具">
                    <a-switch v-model:checked="settings.advanced.enableDevTools" />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="日志级别">
                    <a-select v-model:value="settings.advanced.logLevel">
                      <a-select-option value="error">错误</a-select-option>
                      <a-select-option value="warn">警告</a-select-option>
                      <a-select-option value="info">信息</a-select-option>
                      <a-select-option value="debug">调试</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="API端点">
                    <a-input
                      v-model:value="settings.advanced.apiEndpoint"
                      placeholder="https://api.example.com"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="实验性功能">
                <a-checkbox v-model:checked="settings.advanced.enableExperimentalFeatures"> 启用实验性功能 </a-checkbox>
                <a-checkbox v-model:checked="settings.advanced.enableBetaUpdates"> 接收Beta版本更新 </a-checkbox>
                <a-checkbox v-model:checked="settings.advanced.enableTelemetry"> 发送匿名使用统计 </a-checkbox>
              </a-form-item>
              <a-form-item>
                <a-space>
                  <a-button
                    :loading="clearingCache"
                    @click="clearCache"
                  >
                    <Trash2 class="btn-icon" />
                    清除缓存
                  </a-button>
                  <a-button @click="exportSettings">
                    <Download class="btn-icon" />
                    导出设置
                  </a-button>
                  <a-button @click="importSettings">
                    <Upload class="btn-icon" />
                    导入设置
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message, notification } from 'ant-design-vue'
import { Settings, RotateCcw, Save, Globe, RefreshCw, Shield, Zap, Bell, Database, Cpu, Trash2, Download, Upload } from 'lucide-vue-next'

/**
 * 全局设置页面
 * 功能：配置系统级参数、功能开关和性能调优选项
 * 依赖：Ant Design Vue、Lucide Vue Next
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 响应式数据
const saving = ref(false)
const backupLoading = ref(false)
const clearingCache = ref(false)
const selectedMenuKeys = ref(['general'])
const activeTab = ref('general')

// 设置数据
const settings = reactive({
  general: {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: 'light',
    startMinimized: false,
    enableSystemTray: true,
    minimizeToTray: true
  },
  sync: {
    autoSyncInterval: 1800,
    retryCount: 3,
    conflictResolution: 'timestamp',
    networkTimeout: 30,
    enableCompression: true,
    enableEncryption: true,
    enableBatchSync: false
  },
  security: {
    sessionTimeout: 3600,
    minPasswordLength: 8,
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    enableTwoFactor: false,
    enableAuditLog: true,
    enableIPWhitelist: false
  },
  performance: {
    cacheSize: 256,
    maxConnections: 10,
    logRetentionDays: 30,
    dbCleanupInterval: 86400,
    enableLazyLoading: true,
    enableDataCompression: true,
    enableCaching: true
  },
  notification: {
    enableSyncNotifications: true,
    enableErrorNotifications: true,
    enableSecurityNotifications: true,
    enableUpdateNotifications: true,
    notificationMethods: ['desktop'],
    enableSound: true,
    emailConfig: {
      smtp: '',
      recipient: ''
    }
  },
  backup: {
    enableAutoBackup: true,
    backupInterval: 86400,
    maxBackups: 10,
    backupPath: '',
    includeUserData: true,
    includeSystemConfig: true,
    compressBackup: true
  },
  advanced: {
    debugMode: false,
    enableDevTools: false,
    logLevel: 'info',
    apiEndpoint: '',
    enableExperimentalFeatures: false,
    enableBetaUpdates: false,
    enableTelemetry: true
  }
})

// 默认设置（用于重置）
const defaultSettings = JSON.parse(JSON.stringify(settings))

// 方法
const handleMenuClick = ({ key }: { key: string }) => {
  activeTab.value = key
  selectedMenuKeys.value = [key]
}

const saveSettings = async () => {
  saving.value = true
  try {
    // 模拟保存设置
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 这里应该调用实际的保存API
    // await settingsAPI.save(settings)

    message.success('设置保存成功')

    // 如果语言或主题发生变化，可能需要重新加载应用
    if (settings.general.language !== defaultSettings.general.language || settings.general.theme !== defaultSettings.general.theme) {
      notification.info({
        message: '设置已保存',
        description: '部分设置需要重启应用后生效'
      })
    }
  } catch (error) {
    message.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

const resetToDefaults = () => {
  Object.assign(settings, JSON.parse(JSON.stringify(defaultSettings)))
  message.success('已恢复默认设置')
}

const createBackup = async () => {
  backupLoading.value = true
  try {
    // 模拟创建备份
    await new Promise((resolve) => setTimeout(resolve, 2000))
    message.success('备份创建成功')
  } catch (error) {
    message.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}

const restoreBackup = () => {
  // 实现恢复备份功能
  message.info('请选择备份文件进行恢复')
}

const clearCache = async () => {
  clearingCache.value = true
  try {
    // 模拟清除缓存
    await new Promise((resolve) => setTimeout(resolve, 1000))
    message.success('缓存清除成功')
  } catch (error) {
    message.error('缓存清除失败')
  } finally {
    clearingCache.value = false
  }
}

const exportSettings = () => {
  // 实现导出设置功能
  const settingsJson = JSON.stringify(settings, null, 2)
  const blob = new Blob([settingsJson], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chaterm-settings-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)
  message.success('设置导出成功')
}

const importSettings = () => {
  // 实现导入设置功能
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const importedSettings = JSON.parse(e.target?.result as string)
          Object.assign(settings, importedSettings)
          message.success('设置导入成功')
        } catch (error) {
          message.error('设置文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 生命周期
onMounted(() => {
  // 加载保存的设置
  // loadSettings()
})
</script>

<style scoped>
.global-settings {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.settings-content {
  margin-bottom: 24px;
}

.settings-menu {
  height: fit-content;
  position: sticky;
  top: 24px;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.settings-panel {
  min-height: 600px;
}

.settings-panel .ant-form-item {
  margin-bottom: 24px;
}

.settings-panel .ant-checkbox {
  display: block;
  margin-bottom: 8px;
}

.settings-panel .ant-checkbox:last-child {
  margin-bottom: 0;
}

.settings-panel .ant-checkbox-group .ant-checkbox {
  display: inline-block;
  margin-right: 16px;
  margin-bottom: 8px;
}
</style>
