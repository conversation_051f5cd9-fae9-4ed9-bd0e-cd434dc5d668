/**
 * SMB认证格式测试脚本
 * 测试不同的用户名和域配置组合
 */

const { SMBAdapter } = require('./src/main/storage/multi_sync/adapters/SMBAdapter')

// 基础配置
const baseConfig = {
  host: '************',
  share: 'it部',
  password: 'RSgz@lbq0925!',
  port: 445
}

// 不同的认证配置变体
const authVariants = [
  {
    name: '完整邮箱格式 + 域',
    username: '<EMAIL>',
    domain: 'brbiotech.com'
  },
  {
    name: '用户名 + 域',
    username: 'buqiu.li',
    domain: 'brbiotech.com'
  },
  {
    name: '域\\用户名格式',
    username: 'brbiotech\\buqiu.li',
    domain: ''
  },
  {
    name: '仅用户名',
    username: 'buqiu.li',
    domain: ''
  },
  {
    name: '完整邮箱格式，无域',
    username: '<EMAIL>',
    domain: ''
  },
  {
    name: '用户名 + WORKGROUP',
    username: 'buqiu.li',
    domain: 'WORKGROUP'
  }
]

/**
 * 安全日志输出（隐藏密码）
 */
function safeLog(message, config) {
  const safeConfig = { ...config }
  if (safeConfig.password) safeConfig.password = '***'
  console.log(message, safeConfig)
}

/**
 * 测试单个认证配置
 */
async function testAuthVariant(variant, index) {
  console.log(`\n${index + 1}️⃣ 测试: ${variant.name}`)
  console.log('='.repeat(40))

  const config = {
    ...baseConfig,
    username: variant.username,
    domain: variant.domain
  }

  safeLog('配置:', config)

  let adapter = null
  try {
    adapter = new SMBAdapter()

    // 尝试连接
    console.log('🔗 尝试建立连接...')
    const connectResult = await adapter.connect(config)

    if (connectResult) {
      console.log('✅ 连接成功！')

      // 测试基本操作
      console.log('🧪 测试基本操作...')
      const testResult = await adapter.testConnection()
      if (testResult) {
        console.log('✅ 连接测试通过')

        // 尝试列出根目录
        try {
          const files = await adapter.listFiles('/')
          console.log(`✅ 文件列表获取成功，找到 ${files.length} 个项目`)
          if (files.length > 0) {
            console.log('📁 前5个项目:')
            files.slice(0, 5).forEach((file) => {
              console.log(`   ${file.isDirectory ? '📁' : '📄'} ${file.name}`)
            })
          }
        } catch (error) {
          console.log('⚠️ 文件列表获取失败:', error.message)
        }

        return { success: true, variant }
      } else {
        console.log('❌ 连接测试失败')
      }
    } else {
      console.log('❌ 连接失败')
    }
  } catch (error) {
    console.log('❌ 连接异常:', error.message)

    // 分析错误类型
    if (error.message.includes('STATUS_LOGON_FAILURE')) {
      console.log('💡 分析: 认证失败，用户名或密码错误')
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 分析: 连接被拒绝，检查主机和端口')
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('💡 分析: 主机名解析失败')
    } else {
      console.log('💡 分析: 其他网络或协议错误')
    }
  } finally {
    if (adapter) {
      try {
        await adapter.disconnect()
      } catch (error) {
        // 忽略断开连接的错误
      }
    }
  }

  return { success: false, variant }
}

/**
 * 主测试函数
 */
async function runAuthTests() {
  console.log('🚀 开始SMB认证格式测试')
  console.log('测试目标: ************:445')
  console.log('共享名: it部')
  console.log('='.repeat(50))

  const results = []

  for (let i = 0; i < authVariants.length; i++) {
    const result = await testAuthVariant(authVariants[i], i)
    results.push(result)

    // 如果找到成功的配置，可以选择停止测试
    if (result.success) {
      console.log('\n🎉 找到有效的认证配置！')
      console.log('建议使用此配置进行后续开发。')
      break
    }

    // 在测试之间稍作延迟，避免过于频繁的连接尝试
    if (i < authVariants.length - 1) {
      console.log('\n⏳ 等待2秒后继续下一个测试...')
      await new Promise((resolve) => setTimeout(resolve, 2000))
    }
  }

  // 输出测试总结
  console.log('\n' + '='.repeat(50))
  console.log('📊 测试结果总结:')

  const successfulResults = results.filter((r) => r.success)
  const failedResults = results.filter((r) => !r.success)

  console.log(`✅ 成功: ${successfulResults.length}/${results.length}`)
  console.log(`❌ 失败: ${failedResults.length}/${results.length}`)

  if (successfulResults.length > 0) {
    console.log('\n🎯 成功的配置:')
    successfulResults.forEach((result) => {
      console.log(`   ✅ ${result.variant.name}`)
      console.log(`      用户名: ${result.variant.username}`)
      console.log(`      域: ${result.variant.domain || '(无)'}`)
    })
  } else {
    console.log('\n⚠️ 所有认证配置都失败了。')
    console.log('建议检查:')
    console.log('   1. 网络连接是否正常')
    console.log('   2. SMB服务器是否运行在445端口')
    console.log('   3. 用户名和密码是否正确')
    console.log('   4. 账户是否有SMB访问权限')
    console.log('   5. 防火墙是否阻止了连接')
  }
}

// 运行测试
if (require.main === module) {
  runAuthTests().catch((error) => {
    console.error('💥 测试运行失败:', error.message)
    process.exit(1)
  })
}

module.exports = { runAuthTests }
