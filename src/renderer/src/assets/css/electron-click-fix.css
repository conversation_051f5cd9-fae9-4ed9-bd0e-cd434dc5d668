/* Electron 桌面端点击事件修复 */

/* 强制启用所有交互元素的点击事件 */
button,
input,
select,
textarea,
a,
.btn,
.btn-icon-small,
.debug-info button,
[onclick],
[role="button"],
.clickable {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  position: relative !important;
  z-index: 999 !important;
  cursor: pointer !important;
}

/* 特别针对我们的资产管理页面 */
.asset-management button,
.asset-management input,
.asset-management select,
.asset-management .btn,
.asset-management .btn-icon-small {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  cursor: pointer !important;
  z-index: 1000 !important;
}

/* 调试信息区域强制可点击 */
.debug-info,
.debug-info * {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  z-index: 1001 !important;
}

/* 操作按钮区域强制可点击 */
.action-buttons,
.action-buttons * {
  -webkit-app-region: no-drag !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  z-index: 1002 !important;
}

/* 覆盖任何可能的全局禁用设置 */
body.electron-app button,
body.electron-app input,
body.electron-app select {
  user-select: auto !important;
  -webkit-user-select: auto !important;
  pointer-events: auto !important;
}

/* 确保没有透明覆盖层 */
.asset-management::before,
.asset-management::after {
  display: none !important;
}

/* 移除任何可能阻挡点击的伪元素 */
*::before,
*::after {
  pointer-events: none !important;
}

/* 但是按钮的伪元素应该可以点击 */
button::before,
button::after,
.btn::before,
.btn::after,
.btn-icon-small::before,
.btn-icon-small::after {
  pointer-events: auto !important;
}
