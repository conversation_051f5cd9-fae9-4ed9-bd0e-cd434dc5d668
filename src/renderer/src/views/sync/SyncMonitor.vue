<template>
  <div class="sync-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <BarChart3 class="title-icon" />
            监控仪表板
          </h1>
          <p class="page-description">实时监控同步状态、性能指标和系统健康状况</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button
          :loading="refreshing"
          @click="refreshData"
        >
          <RefreshCw class="btn-icon" />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="exportReport"
        >
          <Download class="btn-icon" />
          导出报告
        </a-button>
      </div>
    </div>

    <!-- 实时状态卡片 -->
    <div class="status-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="status-card success">
            <div class="status-content">
              <div class="status-icon">
                <CheckCircle />
              </div>
              <div class="status-info">
                <div class="status-value">{{ syncStats.successCount }}</div>
                <div class="status-label">成功同步</div>
              </div>
            </div>
            <div class="status-trend">
              <TrendingUp class="trend-icon" />
              <span class="trend-text">+12% 较昨日</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="status-card warning">
            <div class="status-content">
              <div class="status-icon">
                <Clock />
              </div>
              <div class="status-info">
                <div class="status-value">{{ syncStats.pendingCount }}</div>
                <div class="status-label">待同步</div>
              </div>
            </div>
            <div class="status-trend">
              <TrendingDown class="trend-icon" />
              <span class="trend-text">-5% 较昨日</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="status-card error">
            <div class="status-content">
              <div class="status-icon">
                <XCircle />
              </div>
              <div class="status-info">
                <div class="status-value">{{ syncStats.failedCount }}</div>
                <div class="status-label">同步失败</div>
              </div>
            </div>
            <div class="status-trend">
              <TrendingUp class="trend-icon" />
              <span class="trend-text">+3% 较昨日</span>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="status-card info">
            <div class="status-content">
              <div class="status-icon">
                <Activity />
              </div>
              <div class="status-info">
                <div class="status-value">{{ syncStats.avgLatency }}ms</div>
                <div class="status-label">平均延迟</div>
              </div>
            </div>
            <div class="status-trend">
              <TrendingDown class="trend-icon" />
              <span class="trend-text">-8% 较昨日</span>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <a-row :gutter="16">
        <!-- 同步趋势图 -->
        <a-col :span="12">
          <a-card
            title="同步趋势"
            class="chart-card"
          >
            <template #extra>
              <a-radio-group
                v-model:value="trendTimeRange"
                size="small"
              >
                <a-radio-button value="24h">24小时</a-radio-button>
                <a-radio-button value="7d">7天</a-radio-button>
                <a-radio-button value="30d">30天</a-radio-button>
              </a-radio-group>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                <BarChart3 class="placeholder-icon" />
                <p>同步趋势图表</p>
                <p class="placeholder-desc">显示指定时间范围内的同步成功率和失败率趋势</p>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 后端性能图 -->
        <a-col :span="12">
          <a-card
            title="后端性能"
            class="chart-card"
          >
            <template #extra>
              <a-select
                v-model:value="selectedBackend"
                size="small"
                style="width: 120px"
              >
                <a-select-option value="all">全部后端</a-select-option>
                <a-select-option value="database">数据库</a-select-option>
                <a-select-option value="smb">SMB</a-select-option>
                <a-select-option value="git">Git</a-select-option>
              </a-select>
            </template>
            <div class="chart-container">
              <div class="chart-placeholder">
                <Activity class="placeholder-icon" />
                <p>性能指标图表</p>
                <p class="placeholder-desc">显示各后端的响应时间、吞吐量等性能指标</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row
        :gutter="16"
        style="margin-top: 16px"
      >
        <!-- 错误分布图 -->
        <a-col :span="8">
          <a-card
            title="错误分布"
            class="chart-card"
          >
            <div class="chart-container small">
              <div class="chart-placeholder">
                <PieChart class="placeholder-icon" />
                <p>错误类型分布</p>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 资源使用率 -->
        <a-col :span="8">
          <a-card
            title="资源使用率"
            class="chart-card"
          >
            <div class="chart-container small">
              <div class="resource-metrics">
                <div class="metric-item">
                  <div class="metric-label">CPU使用率</div>
                  <a-progress
                    :percent="resourceUsage.cpu"
                    status="active"
                  />
                </div>
                <div class="metric-item">
                  <div class="metric-label">内存使用率</div>
                  <a-progress
                    :percent="resourceUsage.memory"
                    status="active"
                  />
                </div>
                <div class="metric-item">
                  <div class="metric-label">网络带宽</div>
                  <a-progress
                    :percent="resourceUsage.network"
                    status="active"
                  />
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 系统健康度 -->
        <a-col :span="8">
          <a-card
            title="系统健康度"
            class="chart-card"
          >
            <div class="chart-container small">
              <div class="health-score">
                <div class="score-circle">
                  <div class="score-value">{{ systemHealth.score }}</div>
                  <div class="score-label">健康度</div>
                </div>
                <div class="health-details">
                  <div class="health-item">
                    <a-badge
                      status="success"
                      text="后端连接"
                    />
                    <span class="health-value">{{ systemHealth.backendHealth }}/{{ systemHealth.totalBackends }}</span>
                  </div>
                  <div class="health-item">
                    <a-badge
                      status="processing"
                      text="同步服务"
                    />
                    <span class="health-value">正常</span>
                  </div>
                  <div class="health-item">
                    <a-badge
                      status="success"
                      text="数据完整性"
                    />
                    <span class="health-value">99.9%</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 实时日志和告警 -->
    <div class="logs-alerts-section">
      <a-row :gutter="16">
        <!-- 实时同步日志 -->
        <a-col :span="12">
          <a-card
            title="实时同步日志"
            class="logs-card"
          >
            <template #extra>
              <div class="log-controls">
                <a-switch
                  v-model:checked="autoRefreshLogs"
                  size="small"
                />
                <span class="control-label">自动刷新</span>
                <a-button
                  size="small"
                  @click="clearLogs"
                >
                  <Trash2 class="btn-icon" />
                  清空
                </a-button>
              </div>
            </template>
            <div class="logs-container">
              <div
                v-for="log in recentLogs"
                :key="log.id"
                class="log-item"
                :class="log.level"
              >
                <div class="log-time">{{ formatLogTime(log.timestamp) }}</div>
                <div class="log-level">
                  <a-tag :color="getLogLevelColor(log.level)">{{ log.level.toUpperCase() }}</a-tag>
                </div>
                <div class="log-message">{{ log.message }}</div>
                <div class="log-source">{{ log.source }}</div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 告警信息 -->
        <a-col :span="12">
          <a-card
            title="告警信息"
            class="alerts-card"
          >
            <template #extra>
              <a-badge
                :count="activeAlerts.length"
                :offset="[10, 0]"
              >
                <a-button
                  size="small"
                  @click="showAlertSettings"
                >
                  <Settings class="btn-icon" />
                  告警设置
                </a-button>
              </a-badge>
            </template>
            <div class="alerts-container">
              <div
                v-if="activeAlerts.length === 0"
                class="no-alerts"
              >
                <CheckCircle class="no-alerts-icon" />
                <p>暂无活跃告警</p>
              </div>
              <div
                v-for="alert in activeAlerts"
                :key="alert.id"
                class="alert-item"
                :class="alert.severity"
              >
                <div class="alert-header">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-time">{{ formatAlertTime(alert.timestamp) }}</div>
                </div>
                <div class="alert-message">{{ alert.message }}</div>
                <div class="alert-actions">
                  <a-button
                    size="small"
                    @click="acknowledgeAlert(alert)"
                  >
                    确认
                  </a-button>
                  <a-button
                    size="small"
                    @click="resolveAlert(alert)"
                  >
                    解决
                  </a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 后端状态表格 -->
    <div class="backend-status-section">
      <a-card
        title="后端状态详情"
        class="status-table-card"
      >
        <template #extra>
          <a-space>
            <a-button
              size="small"
              :loading="testingConnections"
              @click="testAllConnections"
            >
              <TestTube class="btn-icon" />
              测试所有连接
            </a-button>
            <a-button
              size="small"
              :loading="syncingAll"
              @click="syncAllBackends"
            >
              <RefreshCw class="btn-icon" />
              同步所有后端
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="backendColumns"
          :data-source="backendStatus"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="backend-name-cell">
                <div class="backend-icon">
                  <Database v-if="record.type === 'database'" />
                  <HardDrive v-else-if="record.type === 'smb'" />
                  <GitBranch v-else-if="record.type === 'git'" />
                  <Server v-else />
                </div>
                <div class="backend-info">
                  <div class="name">{{ record.name }}</div>
                  <div class="type">{{ getBackendTypeLabel(record.type) }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-badge
                :status="getStatusType(record.status)"
                :text="getStatusText(record.status)"
              />
            </template>
            <template v-else-if="column.key === 'latency'">
              <span :class="getLatencyClass(record.latency)">{{ record.latency }}ms</span>
            </template>
            <template v-else-if="column.key === 'last_sync'">
              {{ formatTime(record.last_sync) }}
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button
                  size="small"
                  :loading="record.testing"
                  @click="testConnection(record)"
                >
                  <TestTube class="action-icon" />
                </a-button>
                <a-button
                  size="small"
                  :loading="record.syncing"
                  @click="syncBackend(record)"
                >
                  <RefreshCw class="action-icon" />
                </a-button>
                <a-button
                  size="small"
                  @click="viewBackendDetails(record)"
                >
                  <Eye class="action-icon" />
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, notification } from 'ant-design-vue'
import eventBus from '@/utils/eventBus'
import {
  BarChart3,
  RefreshCw,
  Download,
  CheckCircle,
  Clock,
  XCircle,
  Activity,
  TrendingUp,
  TrendingDown,
  PieChart,
  Trash2,
  Settings,
  TestTube,
  Database,
  HardDrive,
  GitBranch,
  Server,
  Eye,
  ArrowLeft
} from 'lucide-vue-next'
import { formatDistanceToNow, format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 类型定义：日志、告警、后端状态与辅助联合类型
type LogLevel = 'info' | 'warning' | 'error' | 'success'
interface LogEntry {
  id: string
  timestamp: Date
  level: LogLevel
  message: string
  source: string
}

type AlertSeverity = 'error' | 'warning' | 'info' | 'success'
interface AlertItem {
  id: string
  title: string
  message: string
  severity: AlertSeverity
  timestamp: Date
}

type BackendKind = 'database' | 'smb' | 'git' | 'consul'
type HealthStatus = 'healthy' | 'unhealthy' | 'unknown'
interface BackendStatusItem {
  id: string
  name: string
  type: BackendKind
  status: HealthStatus
  latency: number
  last_sync: Date
  testing: boolean
  syncing: boolean
}

// 路由实例
const router = useRouter()

// 响应式数据
const refreshing = ref(false)
const testingConnections = ref(false)
const syncingAll = ref(false)
const autoRefreshLogs = ref(true)
const trendTimeRange = ref('24h')
const selectedBackend = ref('all')

// 同步统计数据
const syncStats = reactive({
  successCount: 1247,
  pendingCount: 23,
  failedCount: 8,
  avgLatency: 156
})

// 资源使用率
const resourceUsage = reactive({
  cpu: 45,
  memory: 67,
  network: 23
})

// 系统健康度
const systemHealth = reactive({
  score: 92,
  backendHealth: 3,
  totalBackends: 4
})

// 实时日志
const recentLogs = ref<LogEntry[]>([
  {
    id: '1',
    timestamp: new Date(),
    level: 'info',
    message: '数据库后端同步完成，同步了 15 个资源',
    source: 'primary-db'
  },
  {
    id: '2',
    timestamp: new Date(Date.now() - 30000),
    level: 'warning',
    message: 'SMB后端连接超时，正在重试',
    source: 'legacy-smb'
  },
  {
    id: '3',
    timestamp: new Date(Date.now() - 60000),
    level: 'error',
    message: 'Git后端认证失败，请检查访问令牌',
    source: 'git-backup'
  },
  {
    id: '4',
    timestamp: new Date(Date.now() - 120000),
    level: 'info',
    message: '开始执行定时同步任务',
    source: 'sync-scheduler'
  }
])

// 活跃告警
const activeAlerts = ref<AlertItem[]>([
  {
    id: '1',
    title: 'SMB后端连接异常',
    message: 'legacy-smb 后端连续 3 次连接失败，请检查网络连接和认证信息',
    severity: 'error',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: '2',
    title: '同步延迟过高',
    message: '平均同步延迟超过 200ms，可能影响用户体验',
    severity: 'warning',
    timestamp: new Date(Date.now() - 600000)
  }
])

// 后端状态
const backendStatus = ref<BackendStatusItem[]>([
  {
    id: '1',
    name: 'primary-db',
    type: 'database',
    status: 'healthy',
    latency: 45,
    last_sync: new Date(Date.now() - 120000),
    testing: false,
    syncing: false
  },
  {
    id: '2',
    name: 'legacy-smb',
    type: 'smb',
    status: 'unhealthy',
    latency: 0,
    last_sync: new Date(Date.now() - 1800000),
    testing: false,
    syncing: false
  },
  {
    id: '3',
    name: 'git-backup',
    type: 'git',
    status: 'healthy',
    latency: 234,
    last_sync: new Date(Date.now() - 300000),
    testing: false,
    syncing: false
  },
  {
    id: '4',
    name: 'consul-config',
    type: 'consul',
    status: 'healthy',
    latency: 89,
    last_sync: new Date(Date.now() - 180000),
    testing: false,
    syncing: false
  }
])

// 表格列配置
const backendColumns: { title: string; key: string; width: number; fixed?: 'left' | 'right' }[] = [
  {
    title: '后端名称',
    key: 'name',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '延迟',
    key: 'latency',
    width: 80
  },
  {
    title: '最后同步',
    key: 'last_sync',
    width: 150
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const formatTime = (date: Date) => {
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const formatLogTime = (date: Date) => {
  return format(date, 'HH:mm:ss', { locale: zhCN })
}

const formatAlertTime = (date: Date) => {
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const getLogLevelColor = (level: string) => {
  const colors = {
    info: 'blue',
    warning: 'orange',
    error: 'red',
    success: 'green'
  }
  return colors[level] || 'default'
}

const getBackendTypeLabel = (type: string) => {
  const labels = {
    database: '数据库',
    smb: 'SMB',
    git: 'Git',
    consul: 'Consul'
  }
  return labels[type] || type
}

const getStatusType = (status: string) => {
  const types = {
    healthy: 'success',
    unhealthy: 'error',
    unknown: 'default'
  }
  return types[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    healthy: '健康',
    unhealthy: '异常',
    unknown: '未知'
  }
  return texts[status] || status
}

const getLatencyClass = (latency: number) => {
  if (latency === 0) return 'latency-error'
  if (latency > 200) return 'latency-high'
  if (latency > 100) return 'latency-medium'
  return 'latency-low'
}

const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟刷新数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
    message.success('数据刷新成功')
  } finally {
    refreshing.value = false
  }
}

const exportReport = () => {
  // 实现导出报告功能
  message.success('报告导出成功')
}

const clearLogs = () => {
  recentLogs.value = []
  message.success('日志已清空')
}

const showAlertSettings = () => {
  // 显示告警设置模态框
  message.info('告警设置功能开发中')
}

const acknowledgeAlert = (alert: AlertItem) => {
  const index = activeAlerts.value.findIndex((a) => a.id === alert.id)
  if (index > -1) {
    activeAlerts.value.splice(index, 1)
    message.success('告警已确认')
  }
}

const resolveAlert = (alert: AlertItem) => {
  const index = activeAlerts.value.findIndex((a) => a.id === alert.id)
  if (index > -1) {
    activeAlerts.value.splice(index, 1)
    message.success('告警已解决')
  }
}

const testConnection = async (backend: BackendStatusItem) => {
  backend.testing = true
  try {
    await new Promise((resolve) => setTimeout(resolve, 2000))
    backend.status = 'healthy'
    backend.latency = Math.floor(Math.random() * 200) + 50
    message.success(`${backend.name} 连接测试成功`)
  } catch (error) {
    backend.status = 'unhealthy'
    backend.latency = 0
    message.error(`${backend.name} 连接测试失败`)
  } finally {
    backend.testing = false
  }
}

const syncBackend = async (backend: BackendStatusItem) => {
  backend.syncing = true
  try {
    await new Promise((resolve) => setTimeout(resolve, 3000))
    backend.last_sync = new Date()
    message.success(`${backend.name} 同步完成`)
  } catch (error) {
    message.error(`${backend.name} 同步失败`)
  } finally {
    backend.syncing = false
  }
}

const testAllConnections = async () => {
  testingConnections.value = true
  try {
    const promises = backendStatus.value.map((backend: BackendStatusItem) => testConnection(backend))
    await Promise.all(promises)
    message.success('所有连接测试完成')
  } finally {
    testingConnections.value = false
  }
}

const syncAllBackends = async () => {
  syncingAll.value = true
  try {
    const promises = backendStatus.value.map((backend: BackendStatusItem) => syncBackend(backend))
    await Promise.all(promises)
    message.success('所有后端同步完成')
  } finally {
    syncingAll.value = false
  }
}

const viewBackendDetails = (backend: any) => {
  // 查看后端详情
  message.info(`查看 ${backend.name} 详情`)
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  refreshTimer = setInterval(() => {
    if (autoRefreshLogs.value) {
      // 模拟新日志
      const levels: LogLevel[] = ['info', 'warning', 'error']
      const newLog: LogEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        level: levels[Math.floor(Math.random() * levels.length)],
        message: '模拟日志消息 ' + Date.now(),
        source: 'system'
      }
      recentLogs.value.unshift(newLog)
      if (recentLogs.value.length > 50) {
        recentLogs.value = recentLogs.value.slice(0, 50)
      }
    }
  }, 5000)
}

// 生命周期
onMounted(() => {
  startAutoRefresh()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.sync-monitor {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #6b7280;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  white-space: nowrap;
}

.back-button:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.back-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.status-overview {
  margin-bottom: 24px;
}

.status-card {
  border-radius: 8px;
  overflow: hidden;
}

.status-card.success {
  border-left: 4px solid #52c41a;
}

.status-card.warning {
  border-left: 4px solid #faad14;
}

.status-card.error {
  border-left: 4px solid #ff4d4f;
}

.status-card.info {
  border-left: 4px solid #1890ff;
}

.status-content {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.status-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 16px;
}

.status-card.success .status-icon {
  background: #f6ffed;
  color: #52c41a;
}

.status-card.warning .status-icon {
  background: #fffbe6;
  color: #faad14;
}

.status-card.error .status-icon {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-card.info .status-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.status-label {
  color: #6b7280;
  font-size: 14px;
  margin-top: 4px;
}

.status-trend {
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-icon {
  width: 16px;
  height: 16px;
  color: #52c41a;
}

.trend-text {
  font-size: 12px;
  color: #6b7280;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
}

.chart-card .chart-container.small {
  height: 300px;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #6b7280;
}

.placeholder-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  opacity: 0.5;
}

.placeholder-desc {
  font-size: 12px;
  margin-top: 8px;
}

.resource-metrics {
  padding: 20px;
}

.metric-item {
  margin-bottom: 20px;
}

.metric-label {
  margin-bottom: 8px;
  color: #1f2937;
  font-weight: 500;
}

.health-score {
  text-align: center;
  padding: 20px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border: 8px solid #52c41a;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.score-value {
  font-size: 32px;
  font-weight: 600;
  color: #52c41a;
}

.score-label {
  font-size: 12px;
  color: #6b7280;
}

.health-details {
  text-align: left;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.health-value {
  font-weight: 500;
  color: #1f2937;
}

.logs-alerts-section {
  margin-bottom: 24px;
}

.logs-card,
.alerts-card {
  height: 400px;
}

.log-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 12px;
  color: #6b7280;
}

.logs-container,
.alerts-container {
  height: 320px;
  overflow-y: auto;
}

.log-item {
  display: grid;
  grid-template-columns: 80px 80px 1fr 120px;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.log-time {
  color: #6b7280;
}

.log-message {
  color: #1f2937;
}

.log-source {
  color: #6b7280;
  text-align: right;
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
}

.no-alerts-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: #52c41a;
}

.alert-item {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.alert-item.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
}

.alert-item.warning {
  background: #fffbe6;
  border: 1px solid #ffe58f;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alert-title {
  font-weight: 600;
  color: #1f2937;
}

.alert-time {
  font-size: 12px;
  color: #6b7280;
}

.alert-message {
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 12px;
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.backend-status-section {
  margin-bottom: 24px;
}

.status-table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.backend-name-cell {
  display: flex;
  align-items: center;
}

.backend-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 6px;
  margin-right: 12px;
  color: #1890ff;
}

.backend-info .name {
  font-weight: 500;
  color: #1f2937;
}

.backend-info .type {
  font-size: 12px;
  color: #6b7280;
}

.latency-low {
  color: #52c41a;
}

.latency-medium {
  color: #faad14;
}

.latency-high {
  color: #ff4d4f;
}

.latency-error {
  color: #ff4d4f;
}

.action-icon {
  width: 14px;
  height: 14px;
}
</style>
