<template>
  <TerminalSearchBox
    :visible="true"
    :floating="true"
    :placeholder="t('term.searchPlaceholder')"
    :show-options="true"
    :search-addon="searchAddon"
    :terminal="terminal"
    @close="closeSearch"
    @search="handleSearch"
    @find-next="findNext"
    @find-previous="findPrevious"
  />
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import TerminalSearchBox from '@/components/common/SearchBox/TerminalSearchBox.vue'
import type { SearchAddon } from 'xterm-addon-search'
import type { Terminal } from 'xterm'

const { t } = useI18n()

interface Props {
  searchAddon: SearchAddon | null
  terminal: Terminal
}

interface Emits {
  'close-search': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 事件处理
const closeSearch = () => {
  emit('close-search')
}

const handleSearch = (term: string, options: any) => {
  if (!props.searchAddon || !term) return
  
  try {
    props.searchAddon.findNext(term, {
      caseSensitive: options.caseSensitive,
      wholeWord: options.wholeWord,
      regex: options.useRegex
    })
  } catch (error) {
    console.warn('Search failed:', error)
  }
}

const findNext = () => {
  // 这个逻辑已经在 TerminalSearchBox 内部处理
}

const findPrevious = () => {
  // 这个逻辑已经在 TerminalSearchBox 内部处理
}
</script>

<style lang="less" scoped>
// 样式已经在 TerminalSearchBox 组件中定义
</style>
