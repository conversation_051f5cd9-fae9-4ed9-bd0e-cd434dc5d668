# 统一搜索框组件系统

## 概述

本项目实现了一套完整的统一搜索框组件系统，旨在为 Chaterm 项目提供一致的搜索体验。该系统包含多个专用搜索框组件，支持不同的使用场景，并确保在整个应用中保持统一的设计语言和交互体验。

## 组件架构

### 核心组件

1. **SearchBox.vue** - 基础搜索框组件
   - 提供最基本的搜索功能
   - 支持多种尺寸和变体
   - 内置防抖搜索优化
   - 支持建议和自动完成

2. **AssetSearchBox.vue** - 资产搜索框
   - 专门用于资产管理场景
   - 集成操作按钮（新建、导入、导出）
   - 支持资产建议和元数据显示
   - 状态徽章和类型图标

3. **TerminalSearchBox.vue** - 终端搜索框
   - 浮动式搜索框，用于终端内容搜索
   - 支持高级搜索选项（大小写敏感、整词匹配、正则表达式）
   - 搜索结果导航控制
   - 与 xterm.js 搜索插件集成

4. **CommandHistorySearchBox.vue** - 命令历史搜索框
   - 专用于命令历史搜索和过滤
   - 显示命令元数据和执行状态
   - 支持复制和重新执行操作
   - 时间和类别过滤

### 支持文件

- **index.ts** - 组件导出和类型定义
- **register.ts** - 全局组件注册
- **SearchBox.less** - 统一样式系统
- **SearchBoxDesignSpec.md** - 设计规范文档

## 设计特性

### 视觉设计

- **统一的设计语言**：所有搜索框遵循相同的视觉规范
- **主题适配**：支持亮色和暗色主题自动切换
- **响应式设计**：适配桌面端和移动端
- **无障碍支持**：完整的 ARIA 标签和键盘导航

### 交互体验

- **一致的交互模式**：统一的键盘快捷键和鼠标操作
- **流畅的动画效果**：微妙的过渡动画提升用户体验
- **智能建议系统**：基于上下文的搜索建议
- **防抖优化**：避免频繁的搜索请求

### 技术实现

- **Vue 3 Composition API**：现代化的组件开发方式
- **TypeScript 支持**：完整的类型定义和检查
- **CSS 自定义属性**：灵活的主题系统
- **模块化架构**：易于维护和扩展

## 使用指南

### 基本用法

```vue
<template>
  <SearchBox
    v-model="searchValue"
    placeholder="搜索..."
    clearable
    @search="handleSearch"
  />
</template>

<script setup>
import { ref } from 'vue'
import { SearchBox } from '@/components/common/SearchBox'

const searchValue = ref('')

const handleSearch = (value) => {
  console.log('搜索:', value)
}
</script>
```

### 资产搜索

```vue
<template>
  <AssetSearchBox
    v-model="searchValue"
    :assets="assetList"
    :show-suggestions="true"
    @suggestion-select="handleAssetSelect"
    @new-asset="handleNewAsset"
  />
</template>
```

### 终端搜索

```vue
<template>
  <TerminalSearchBox
    :visible="showSearch"
    :floating="true"
    :search-addon="searchAddon"
    :terminal="terminal"
    @close="closeSearch"
  />
</template>
```

## 迁移指南

### 从旧搜索框迁移

1. **替换导入**

   ```javascript
   // 旧的
   import { Input } from 'ant-design-vue'

   // 新的
   import { AssetSearchBox } from '@/components/common/SearchBox'
   ```

2. **更新模板**

   ```vue
   <!-- 旧的 -->
   <a-input v-model:value="search" placeholder="搜索...">
     <template #suffix><SearchOutlined /></template>
   </a-input>

   <!-- 新的 -->
   <AssetSearchBox v-model="search" placeholder="搜索..." />
   ```

3. **调整事件处理**

   ```javascript
   // 旧的
   const handleInput = (e) => {
     searchValue.value = e.target.value
   }

   // 新的
   const handleSearch = (value) => {
     // 搜索逻辑
   }
   ```

## 已完成的迁移

### ✅ 已更新的组件

1. **AssetSearch.vue** (`src/renderer/src/views/components/LeftTab/components/AssetSearch.vue`)
   - 替换为 AssetSearchBox 组件
   - 保留原有功能和事件处理
   - 添加资产建议支持

2. **enterpriseAssetConfig.vue** (`src/renderer/src/views/components/LeftTab/enterpriseAssetConfig.vue`)
   - 集成 AssetSearchBox 组件
   - 支持企业资产搜索和管理
   - 保持原有的过滤和操作功能

3. **PermissionManagement.vue** (`src/renderer/src/views/security/PermissionManagement.vue`)
   - 用户管理搜索框替换为 SearchBox 组件
   - 保持小尺寸和清除功能
   - 统一权限管理页面的搜索体验

4. **NetworkDevicesMonitor.vue** (`src/renderer/src/views/enterprise/network/NetworkDevicesMonitor.vue`)
   - 设备列表搜索框替换为 SearchBox 组件
   - 支持设备名称和IP搜索
   - 保持网络设备监控的搜索功能

5. **NetworkAssetsManagement.vue** (`src/renderer/src/views/enterprise/network/NetworkAssetsManagement.vue`)
   - 网络资产清单搜索框替换为 SearchBox 组件
   - 支持设备名称、IP和序列号搜索
   - 统一网络资产管理的搜索体验

6. **AssetManagement.vue** (`src/renderer/src/views/enterprise/AssetManagement.vue`)
   - 企业资产管理搜索框替换为 SearchBox 组件
   - 支持资产名称、编号和型号搜索
   - 保持清除功能和搜索体验

### 🔄 待迁移的组件

1. **searchComp.vue** (`src/renderer/src/views/components/Term/searchComp.vue`)
   - 需要替换为 TerminalSearchBox
   - 已创建新版本 `searchCompNew.vue` 作为参考

2. **EnhancedCommandHistoryPanel.vue**
   - 需要集成 CommandHistorySearchBox
   - 保持命令历史功能

3. **Extensions/index.vue**
   - 需要使用统一的搜索框组件
   - 保持扩展搜索功能

## 测试建议

### 功能测试

- [ ] 基本搜索功能
- [ ] 建议和自动完成
- [ ] 清除和重置功能
- [ ] 键盘导航
- [ ] 事件触发

### 样式测试

- [ ] 亮色主题适配
- [ ] 暗色主题适配
- [ ] 响应式布局
- [ ] 不同尺寸显示
- [ ] 动画效果

### 兼容性测试

- [ ] 不同浏览器
- [ ] 移动端设备
- [ ] 键盘操作
- [ ] 屏幕阅读器

## 维护指南

### 添加新的搜索框变体

1. 创建新的组件文件
2. 继承基础 SearchBox 样式
3. 添加特定功能和样式
4. 更新 index.ts 导出
5. 添加使用文档

### 样式定制

所有样式变量定义在 `SearchBox.less` 中，通过 CSS 自定义属性实现主题适配：

```less
.search-box {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}
```

### 性能优化

- 使用防抖减少搜索频率
- 虚拟滚动处理大量建议
- 懒加载搜索结果
- 缓存常用搜索

## 贡献指南

1. 遵循现有的代码风格和命名规范
2. 添加适当的 TypeScript 类型定义
3. 编写单元测试和集成测试
4. 更新相关文档
5. 确保无障碍支持

## 版本历史

- **v1.0.0** - 初始版本，包含基础搜索框组件
- **v1.1.0** - 添加资产搜索框和终端搜索框
- **v1.2.0** - 添加命令历史搜索框和演示页面
- **v1.3.0** - 完成主要组件迁移和样式统一

## 联系方式

如有问题或建议，请联系开发团队或在项目仓库中提交 Issue。
