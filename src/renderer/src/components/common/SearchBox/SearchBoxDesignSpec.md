# 搜索框设计规范 (SearchBox Design Specification)

## 🎯 设计目标

创建一个统一、美观、易用的搜索框组件系统，确保在整个应用中的一致性和良好的用户体验。

## 🎨 视觉设计

### 基础样式
- **高度**: 32px (小尺寸), 40px (默认), 48px (大尺寸)
- **边框圆角**: 6px
- **边框宽度**: 1px
- **字体大小**: 14px (默认), 12px (小尺寸), 16px (大尺寸)
- **内边距**: 8px 12px (默认), 6px 10px (小尺寸), 10px 16px (大尺寸)

### 颜色系统

#### 亮色主题 (Light Theme)
- **背景色**: `var(--bg-color-secondary)` (#ffffff)
- **边框色**: `var(--border-color)` (#d1d5db)
- **文字色**: `var(--text-color)` (#1e293b)
- **占位符色**: `var(--text-color-tertiary)` (#94a3b8)
- **图标色**: `var(--text-color-tertiary)` (#94a3b8)
- **聚焦边框色**: `var(--input-focus-border)` (#3b82f6)
- **聚焦阴影**: `var(--input-focus-shadow)` (0 0 0 3px rgba(59, 130, 246, 0.1))

#### 暗色主题 (Dark Theme)
- **背景色**: `var(--bg-color-secondary)` (#1f1f1f)
- **边框色**: `var(--border-color)` (#4b5563)
- **文字色**: `var(--text-color)` (#e2e8f0)
- **占位符色**: `var(--text-color-tertiary)` (#666666)
- **图标色**: `var(--text-color-tertiary)` (#666666)
- **聚焦边框色**: `var(--input-focus-border)` (#60a5fa)
- **聚焦阴影**: `var(--input-focus-shadow)` (0 0 0 3px rgba(96, 165, 250, 0.2))

### 状态样式

#### 默认状态
- 使用基础样式
- 边框透明度 80%

#### 悬停状态 (Hover)
- 边框色加深 10%
- 背景色轻微变化
- 过渡动画 0.2s ease

#### 聚焦状态 (Focus)
- 聚焦边框色
- 聚焦阴影效果
- 移除默认 outline

#### 禁用状态 (Disabled)
- 背景色变灰
- 文字色变淡
- 鼠标指针变为 not-allowed

## 🔧 组件变体

### 1. 基础搜索框 (Basic)
- 只包含输入框和搜索图标
- 适用于简单的搜索场景

### 2. 带清除按钮 (Clearable)
- 输入内容时显示清除按钮
- 点击清除按钮清空输入

### 3. 带建议下拉 (Suggestions)
- 支持搜索建议/自动完成
- 键盘导航支持

### 4. 带过滤器 (Filterable)
- 集成过滤选项
- 支持多条件搜索

### 5. 紧凑型 (Compact)
- 适用于空间受限的场景
- 较小的尺寸和间距

## 🎭 图标系统

### 搜索图标
```svg
<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
  <circle cx="11" cy="11" r="8"></circle>
  <path d="m21 21-4.35-4.35"></path>
</svg>
```

### 清除图标
```svg
<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
  <line x1="18" y1="6" x2="6" y2="18"></line>
  <line x1="6" y1="6" x2="18" y2="18"></line>
</svg>
```

## 🎬 动画效果

### 过渡动画
- **属性**: background-color, border-color, box-shadow
- **持续时间**: 0.2s
- **缓动函数**: ease

### 聚焦动画
- 边框色渐变
- 阴影淡入效果

### 清除按钮动画
- 淡入淡出效果
- 轻微的缩放动画

## 📱 响应式设计

### 断点适配
- **移动端** (< 768px): 使用紧凑型样式
- **平板端** (768px - 1024px): 使用默认样式
- **桌面端** (> 1024px): 使用默认或大尺寸样式

### 触摸优化
- 增加触摸目标大小
- 优化移动端交互体验

## 🔤 文本规范

### 占位符文本
- 使用简洁明了的提示文字
- 支持国际化
- 常用占位符:
  - "搜索..." (通用)
  - "搜索资产..." (资产管理)
  - "搜索命令..." (命令历史)
  - "搜索扩展..." (扩展管理)

### 无障碍支持
- 提供 aria-label 属性
- 支持键盘导航
- 屏幕阅读器友好

## 🎯 使用场景

### 1. 资产管理搜索
- 支持按名称、IP、标签搜索
- 集成过滤器功能

### 2. 终端内搜索
- 实时搜索终端内容
- 高亮匹配结果

### 3. 命令历史搜索
- 搜索历史命令
- 支持模糊匹配

### 4. 扩展搜索
- 搜索可用扩展
- 按分类过滤

### 5. 企业资源搜索
- 多维度搜索企业资源
- 支持高级过滤

## 📋 实现清单

- [ ] 创建基础 SearchBox 组件
- [ ] 实现主题适配系统
- [ ] 添加尺寸变体支持
- [ ] 实现清除功能
- [ ] 添加建议下拉支持
- [ ] 集成过滤器功能
- [ ] 优化无障碍支持
- [ ] 添加动画效果
- [ ] 响应式适配
- [ ] 国际化支持
- [ ] 单元测试覆盖
- [ ] 文档和示例

## 🔄 迁移计划

1. **第一阶段**: 创建基础组件和核心功能
2. **第二阶段**: 替换现有的简单搜索框
3. **第三阶段**: 迁移复杂的搜索实现
4. **第四阶段**: 优化和完善细节
5. **第五阶段**: 全面测试和验证
