/**
 * SMB同步配置修复验证脚本
 * 功能：测试修复后的SMB同步功能
 * 依赖：@marsaud/smb2、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const { SMBAdapter } = require('./src/main/storage/multi_sync/adapters/SMBAdapter')
const fs = require('fs')
const path = require('path')

// 测试SMB配置
const testConfig = {
  host: '************',
  share: 'it部',
  username: '<EMAIL>',
  password: 'RSgz@lbq0925!',
  domain: 'brbiotech.com',
  port: 445,
  path: '/chaterm-hosts-test.json'
}

/**
 * 模拟同步数据
 */
const mockSyncData = {
  version: '1.0',
  timestamp: new Date().toISOString(),
  source: 'Chaterm Enterprise Test',
  hosts: [
    {
      id: 'test-uuid-1',
      name: '测试服务器1',
      hostname: '*************',
      port: 22,
      username: 'admin',
      description: '测试服务器描述',
      group: '测试组',
      asset_type: 'host',
      auth_type: 'password',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'test-uuid-2',
      name: '测试服务器2',
      hostname: '*************',
      port: 22,
      username: 'root',
      description: '另一个测试服务器',
      group: '测试组',
      asset_type: 'host',
      auth_type: 'key',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]
}

/**
 * 测试SMB同步功能
 */
async function testSMBSyncFix() {
  console.log('🚀 开始测试SMB同步配置修复')
  console.log('='.repeat(50))

  let smbAdapter = null

  try {
    // 1. 创建SMB适配器实例
    console.log('📦 创建SMB适配器实例...')
    smbAdapter = new SMBAdapter()

    // 2. 测试连接
    console.log('🔗 测试SMB连接...')
    const connected = await smbAdapter.connect(testConfig)

    if (!connected) {
      throw new Error('SMB连接失败')
    }

    console.log('✅ SMB连接成功')

    // 3. 准备测试文件
    console.log('📄 准备测试同步文件...')
    const tempFilePath = path.join(require('os').tmpdir(), 'chaterm-sync-test.json')
    fs.writeFileSync(tempFilePath, JSON.stringify(mockSyncData, null, 2))

    const fileSize = fs.statSync(tempFilePath).size
    console.log(`📊 测试文件大小: ${fileSize} 字节`)

    // 4. 测试文件上传
    console.log('⬆️ 测试文件上传...')
    await smbAdapter.uploadFile(tempFilePath, testConfig.path)
    console.log('✅ 文件上传成功')

    // 5. 测试文件列表
    console.log('📋 测试文件列表...')
    try {
      const files = await smbAdapter.listFiles('/')
      console.log(`📁 共享根目录包含 ${files.length} 个项目:`)
      files.slice(0, 5).forEach((file) => {
        const icon = file.isDirectory ? '📁' : '📄'
        console.log(`   ${icon} ${file.name}`)
      })
      if (files.length > 5) {
        console.log(`   ... 还有 ${files.length - 5} 个项目`)
      }
    } catch (listError) {
      console.warn('⚠️ 文件列表获取失败:', listError.message)
    }

    // 6. 清理测试文件
    console.log('🧹 清理临时文件...')
    try {
      fs.unlinkSync(tempFilePath)
      console.log('✅ 临时文件清理完成')
    } catch (cleanupError) {
      console.warn('⚠️ 临时文件清理失败:', cleanupError.message)
    }

    console.log('\n🎉 SMB同步配置修复测试成功！')
    console.log('✅ 所有功能正常工作:')
    console.log('   - SMB连接建立')
    console.log('   - 文件上传')
    console.log('   - 文件列表')
    console.log('   - 连接断开')

    return {
      success: true,
      message: 'SMB同步配置修复验证成功',
      uploadedFile: testConfig.path,
      fileSize: fileSize
    }
  } catch (error) {
    console.error('❌ SMB同步测试失败:', error.message)
    console.error('错误详情:', error)

    return {
      success: false,
      error: error.message
    }
  } finally {
    // 确保断开连接
    if (smbAdapter) {
      try {
        await smbAdapter.disconnect()
        console.log('🔌 SMB连接已断开')
      } catch (disconnectError) {
        console.warn('⚠️ 断开连接失败:', disconnectError.message)
      }
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const result = await testSMBSyncFix()

    console.log('\n' + '='.repeat(50))
    console.log('📊 测试结果总结:')

    if (result.success) {
      console.log('✅ 状态: 成功')
      console.log(`📄 上传文件: ${result.uploadedFile}`)
      console.log(`💾 文件大小: ${result.fileSize} 字节`)
      console.log('💡 建议: SMB同步配置修复完成，可以正常使用')
    } else {
      console.log('❌ 状态: 失败')
      console.log(`🔍 错误: ${result.error}`)
      console.log('💡 建议: 请检查网络连接、SMB服务器状态和认证配置')
    }

    console.log('\n🏁 测试完成！')
    process.exit(result.success ? 0 : 1)
  } catch (error) {
    console.error('💥 测试运行失败:', error.message)
    console.error('错误堆栈:', error.stack)
    process.exit(1)
  }
}

// 运行测试
if (require.main === module) {
  main()
}

module.exports = { testSMBSyncFix }
