/**
 * Console连接服务主入口
 * 提供统一的Console连接管理接口，整合新的架构设计
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { ConsoleManager } from './consoleManager'
import { initializeConsoleApi, cleanupConsoleApi } from './consoleApi'
import type { ConsoleConnectionConfig, ConsoleConnectionResult, ConsoleCommandResult, ConsoleManagerOptions } from './types'

/**
 * Console连接服务类
 * 单例模式，提供全局的Console连接管理
 */
class ConsoleService {
  private static instance: ConsoleService
  private manager: ConsoleManager
  private isInitialized: boolean = false

  private constructor(options?: ConsoleManagerOptions) {
    this.manager = new ConsoleManager(options)
  }

  /**
   * 获取ConsoleService单例实例
   */
  public static getInstance(options?: ConsoleManagerOptions): ConsoleService {
    if (!ConsoleService.instance) {
      ConsoleService.instance = new ConsoleService(options)
    }
    return ConsoleService.instance
  }

  /**
   * 初始化Console服务
   */
  public initialize(): void {
    if (this.isInitialized) {
      return
    }

    // 初始化Console API
    initializeConsoleApi()
    this.isInitialized = true
    console.log('[ConsoleService] Console服务已初始化')
  }

  /**
   * 清理Console服务
   */
  public async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    // 清理Console API
    await cleanupConsoleApi()

    // 关闭Console管理器
    await this.manager.shutdown()

    this.isInitialized = false
    console.log('[ConsoleService] Console服务已清理')
  }

  /**
   * 建立Console连接
   */
  public async connect(config: ConsoleConnectionConfig): Promise<ConsoleConnectionResult> {
    return this.manager.connect(config)
  }

  /**
   * 断开Console连接
   */
  public async disconnect(connectionId: string): Promise<void> {
    return this.manager.disconnect(connectionId)
  }

  /**
   * 执行命令
   */
  public async executeCommand(connectionId: string, command: string): Promise<ConsoleCommandResult> {
    return this.manager.executeCommand(connectionId, command)
  }

  /**
   * 获取连接状态
   */
  public async getStatus(connectionId: string) {
    return this.manager.getStatus(connectionId)
  }

  /**
   * 获取所有连接
   */
  public async listConnections() {
    return this.manager.listConnections()
  }
}

// 导出默认实例
export const consoleService = ConsoleService.getInstance()

// 导出类型
export type { ConsoleConnectionConfig, ConsoleConnectionResult, ConsoleCommandResult, ConsoleManagerOptions } from './types'

// 导出服务类
export { ConsoleService }
