<template>
  <div
    :class="[
      'search-box',
      `search-box--${size}`,
      `search-box--${variant}`,
      {
        'search-box--focused': isFocused,
        'search-box--disabled': disabled,
        'search-box--has-value': hasValue
      }
    ]"
  >
    <!-- 搜索图标 -->
    <div class="search-box__icon search-box__icon--search">
      <slot name="prefix-icon">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle
            cx="11"
            cy="11"
            r="8"
          ></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </slot>
    </div>

    <!-- 输入框 -->
    <input
      ref="inputRef"
      v-model="inputValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :maxlength="maxlength"
      :autocomplete="autocomplete"
      :aria-label="ariaLabel"
      class="search-box__input"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      @keyup="handleKeyup"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
    />

    <!-- 清除按钮 -->
    <button
      v-if="clearable && hasValue && !disabled"
      type="button"
      class="search-box__icon search-box__icon--clear"
      :aria-label="clearAriaLabel"
      @click="handleClear"
      @mousedown.prevent
    >
      <slot name="clear-icon">
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <line
            x1="18"
            y1="6"
            x2="6"
            y2="18"
          ></line>
          <line
            x1="6"
            y1="6"
            x2="18"
            y2="18"
          ></line>
        </svg>
      </slot>
    </button>

    <!-- 后缀图标 -->
    <div
      v-if="$slots['suffix-icon']"
      class="search-box__icon search-box__icon--suffix"
    >
      <slot name="suffix-icon"></slot>
    </div>

    <!-- 建议下拉 -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="search-box__suggestions"
      :style="suggestionsStyle"
    >
      <div
        v-for="(suggestion, index) in suggestions"
        :key="getSuggestionKey(suggestion, index)"
        :class="[
          'search-box__suggestion',
          {
            'search-box__suggestion--active': index === activeSuggestionIndex
          }
        ]"
        @click="handleSuggestionClick(suggestion, index)"
        @mouseenter="handleSuggestionHover(index)"
      >
        <slot
          name="suggestion"
          :suggestion="suggestion"
          :index="index"
        >
          <span class="search-box__suggestion-text">{{ getSuggestionText(suggestion) }}</span>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'

// 类型定义
interface Suggestion {
  text?: string
  value?: string
  label?: string
  [key: string]: any
}

interface Props {
  modelValue?: string
  type?: string
  size?: 'small' | 'default' | 'large'
  variant?: 'basic' | 'clearable' | 'suggestions' | 'compact'
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  maxlength?: number
  autocomplete?: string
  ariaLabel?: string
  clearAriaLabel?: string
  suggestions?: Suggestion[]
  showSuggestions?: boolean
  suggestionTextKey?: string
  suggestionValueKey?: string
  debounceDelay?: number
}

interface Emits {
  'update:modelValue': [value: string]
  input: [value: string, event: Event]
  change: [value: string, event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  search: [value: string]
  'suggestion-select': [suggestion: Suggestion, index: number]
  keydown: [event: KeyboardEvent]
  keyup: [event: KeyboardEvent]
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  type: 'text',
  size: 'default',
  variant: 'basic',
  placeholder: '搜索...',
  disabled: false,
  readonly: false,
  clearable: true,
  autocomplete: 'off',
  ariaLabel: '搜索输入框',
  clearAriaLabel: '清除搜索内容',
  suggestions: () => [],
  showSuggestions: false,
  suggestionTextKey: 'text',
  suggestionValueKey: 'value',
  debounceDelay: 300
})

// Emits 定义
const emit = defineEmits<Emits>()

// 响应式数据
const inputRef = ref<HTMLInputElement>()
const inputValue = ref(props.modelValue)
const isFocused = ref(false)
const isComposing = ref(false)
const activeSuggestionIndex = ref(-1)
const suggestionsStyle = ref({})

// 计算属性
const hasValue = computed(() => inputValue.value.length > 0)

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== inputValue.value) {
      inputValue.value = newValue
    }
  }
)

// 监听 inputValue 变化
watch(inputValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 防抖搜索
let searchTimeout: NodeJS.Timeout | null = null
const debouncedSearch = (value: string) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    emit('search', value)
  }, props.debounceDelay)
}

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (!isComposing.value) {
    inputValue.value = target.value
    emit('input', target.value, event)
    debouncedSearch(target.value)
  }
}

const handleChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('change', target.value, event)
}

const handleFocus = (event: FocusEvent) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  // 延迟失焦，允许点击建议项
  setTimeout(() => {
    isFocused.value = false
    activeSuggestionIndex.value = -1
  }, 150)
  emit('blur', event)
}

const handleClear = () => {
  inputValue.value = ''
  emit('clear')
  emit('search', '')
  inputRef.value?.focus()
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)

  if (props.showSuggestions && props.suggestions.length > 0) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        activeSuggestionIndex.value = Math.min(activeSuggestionIndex.value + 1, props.suggestions.length - 1)
        break
      case 'ArrowUp':
        event.preventDefault()
        activeSuggestionIndex.value = Math.max(activeSuggestionIndex.value - 1, -1)
        break
      case 'Enter':
        event.preventDefault()
        if (activeSuggestionIndex.value >= 0) {
          const suggestion = props.suggestions[activeSuggestionIndex.value]
          handleSuggestionClick(suggestion, activeSuggestionIndex.value)
        } else {
          emit('search', inputValue.value)
        }
        break
      case 'Escape':
        activeSuggestionIndex.value = -1
        inputRef.value?.blur()
        break
    }
  } else if (event.key === 'Enter') {
    emit('search', inputValue.value)
  }
}

const handleKeyup = (event: KeyboardEvent) => {
  emit('keyup', event)
}

const handleCompositionStart = () => {
  isComposing.value = true
}

const handleCompositionEnd = (event: CompositionEvent) => {
  isComposing.value = false
  const target = event.target as HTMLInputElement
  inputValue.value = target.value
  emit('input', target.value, event as any)
  debouncedSearch(target.value)
}

// 建议相关方法
const getSuggestionKey = (suggestion: Suggestion, index: number): string => {
  return suggestion[props.suggestionValueKey] || suggestion[props.suggestionTextKey] || index.toString()
}

const getSuggestionText = (suggestion: Suggestion): string => {
  return suggestion[props.suggestionTextKey] || suggestion[props.suggestionValueKey] || suggestion.toString()
}

const handleSuggestionClick = (suggestion: Suggestion, index: number) => {
  const value = suggestion[props.suggestionValueKey] || suggestion[props.suggestionTextKey] || suggestion.toString()
  inputValue.value = value
  emit('suggestion-select', suggestion, index)
  emit('search', value)
  activeSuggestionIndex.value = -1
  inputRef.value?.focus()
}

const handleSuggestionHover = (index: number) => {
  activeSuggestionIndex.value = index
}

// 公开方法
const focus = () => {
  inputRef.value?.focus()
}

const blur = () => {
  inputRef.value?.blur()
}

const select = () => {
  inputRef.value?.select()
}

// 暴露方法给父组件
defineExpose({
  focus,
  blur,
  select,
  inputRef
})

// 清理定时器
onBeforeUnmount(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
})
</script>

<style lang="less" scoped>
@import './SearchBox.less';
</style>
