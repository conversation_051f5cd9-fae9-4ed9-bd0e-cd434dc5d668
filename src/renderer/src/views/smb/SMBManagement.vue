<template>
  <div class="smb-management">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button
          class="toolbar-btn"
          @click="goBack"
        >
          <svg
            class="icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
          </svg>
        </button>
        <div class="toolbar-title">
          <svg
            class="title-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21V20A2,2 0 0,1 19,22H5A2,2 0 0,1 3,20V19H2A1,1 0 0,1 1,18V15A1,1 0 0,1 2,14H3A7,7 0 0,1 10,7H11V5.73C10.4,5.39 10,4.74 10,4A2,2 0 0,1 12,2M7.5,13A2.5,2.5 0 0,0 5,15.5A2.5,2.5 0 0,0 7.5,18A2.5,2.5 0 0,0 10,15.5A2.5,2.5 0 0,0 7.5,13M16.5,13A2.5,2.5 0 0,0 14,15.5A2.5,2.5 0 0,0 16.5,18A2.5,2.5 0 0,0 19,15.5A2.5,2.5 0 0,0 16.5,13Z"
            />
          </svg>
          <span>SMB 文件管理器</span>
        </div>
        <div class="connection-status">
          <div
            class="status-indicator"
            :class="connectionStatus.type"
          ></div>
          <span class="status-text">{{ connectionStatus.message }}</span>
        </div>
      </div>
      <div class="toolbar-center">
        <div class="breadcrumb">
          <span class="breadcrumb-item">{{ smbConfig.host || '未连接' }}</span>
          <svg
            class="breadcrumb-separator"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
          <span class="breadcrumb-item">{{ smbConfig.share || '共享' }}</span>
          <svg
            v-if="currentPath !== '/'"
            class="breadcrumb-separator"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z" />
          </svg>
          <span
            v-if="currentPath !== '/'"
            class="breadcrumb-item"
            >{{ currentPath }}</span
          >
        </div>
      </div>
      <div class="toolbar-right">
        <button
          :disabled="loading"
          class="toolbar-btn"
          @click="refreshFiles"
        >
          <svg
            class="icon"
            :class="{ 'animate-spin': loading }"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
            />
          </svg>
        </button>
        <button
          class="toolbar-btn"
          @click="toggleViewMode"
        >
          <svg
            v-if="viewMode === 'grid'"
            class="icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M3,5H9V11H3V5M5,7V9H7V7H5M11,7H21V9H11V7M11,15H21V17H11V15M5,20V18H7V20H5M3,17H9V23H3V17M11,11H21V13H11V11Z" />
          </svg>
          <svg
            v-else
            class="icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M3,11H11V3H3M3,21H11V13H3M13,21H21V13H13M13,3V11H21V3" />
          </svg>
        </button>
        <button
          class="toolbar-btn"
          @click="toggleSidebar"
        >
          <svg
            class="icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M3,9H17V7H3V9M3,13H17V11H3V13M3,17H17V15H3V17M19,17H21V15H19V17M19,7V9H21V7H19M19,13H21V11H19V13Z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-layout">
      <!-- 左侧边栏 -->
      <div
        v-if="showSidebar"
        class="sidebar"
      >
        <!-- 连接配置 -->
        <div class="sidebar-section">
          <div class="section-header">
            <h3 class="section-title">连接配置</h3>
            <button
              class="toggle-btn"
              @click="toggleConnectionConfig"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  v-if="showConnectionConfig"
                  d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"
                />
                <path
                  v-else
                  d="M8.59,16.58L13.17,12L8.59,7.41L10,6L16,12L10,18L8.59,16.58Z"
                />
              </svg>
            </button>
          </div>
          <div
            v-if="showConnectionConfig"
            class="connection-form"
          >
            <div class="form-group">
              <label class="form-label">服务器</label>
              <input
                v-model="smbConfig.host"
                type="text"
                placeholder="IP或域名"
                class="form-input"
              />
            </div>
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">端口</label>
                <input
                  v-model="smbConfig.port"
                  type="number"
                  placeholder="445"
                  class="form-input"
                />
              </div>
              <div class="form-group">
                <label class="form-label">域</label>
                <input
                  v-model="smbConfig.domain"
                  type="text"
                  placeholder="域名"
                  class="form-input"
                />
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">用户名</label>
              <input
                v-model="smbConfig.username"
                type="text"
                placeholder="用户名"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">密码</label>
              <input
                v-model="smbConfig.password"
                type="password"
                placeholder="密码"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">共享名</label>
              <input
                v-model="smbConfig.share"
                type="text"
                placeholder="共享文件夹"
                class="form-input"
              />
            </div>
            <button
              :disabled="connecting"
              class="connect-btn"
              @click="testConnection"
            >
              <svg
                v-if="connecting"
                class="icon animate-spin"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
              </svg>
              <svg
                v-else
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
              </svg>
              {{ connecting ? '连接中...' : '连接' }}
            </button>
          </div>
        </div>

        <!-- 连接状态 -->
        <div
          v-if="connected || connectionStatusSimple !== 'disconnected'"
          class="sidebar-section"
        >
          <div class="section-header">
            <h3 class="section-title">连接状态</h3>
          </div>
          <div class="connection-status">
            <div class="status-item">
              <span class="status-label">状态:</span>
              <span
                class="status-value"
                :style="{ color: getConnectionStatusColor() }"
              >
                {{ getConnectionStatusText() }}
              </span>
            </div>
            <div
              v-if="connected"
              class="status-item"
            >
              <span class="status-label">服务器:</span>
              <span class="status-value">{{ connectionConfig.server }}:{{ connectionConfig.port }}</span>
            </div>
            <div
              v-if="connected"
              class="status-item"
            >
              <span class="status-label">共享:</span>
              <span class="status-value">{{ connectionConfig.share }}</span>
            </div>
            <div
              v-if="connected"
              class="status-item"
            >
              <span class="status-label">用户:</span>
              <span class="status-value">{{ connectionConfig.username || '匿名' }}</span>
            </div>
          </div>

          <!-- 连接操作按钮 -->
          <div class="connection-actions">
            <button
              v-if="connected"
              class="action-btn"
              :disabled="connecting"
              @click="reconnectSMB"
            >
              🔄 重新连接
            </button>
            <button
              v-if="connected"
              class="action-btn danger"
              @click="disconnectSMB"
            >
              🔌 断开连接
            </button>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="sidebar-section">
          <div class="section-header">
            <h3 class="section-title">快速操作</h3>
          </div>
          <div class="quick-actions">
            <button
              class="action-btn"
              @click="uploadFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
              上传文件
            </button>
            <button
              class="action-btn"
              @click="createFolder"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
              </svg>
              新建文件夹
            </button>
            <button
              :disabled="selectedItems.length === 0"
              class="action-btn"
              @click="downloadSelected"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" />
              </svg>
              下载选中
            </button>
          </div>
        </div>

        <!-- 文件操作 -->
        <div
          v-if="selectedFiles.length > 0"
          class="sidebar-section"
        >
          <div class="section-header">
            <h3 class="section-title">文件操作</h3>
          </div>
          <div class="file-operations">
            <button
              class="action-btn"
              :disabled="selectedFiles.length === 0"
              @click="copyFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"
                />
              </svg>
              复制 ({{ selectedFiles.length }})
            </button>
            <button
              class="action-btn"
              :disabled="selectedFiles.length === 0"
              @click="cutFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M19,3L13,9L15,11L22,4V3M12,12.5A0.5,0.5 0 0,1 11.5,12A0.5,0.5 0 0,1 12,11.5A0.5,0.5 0 0,1 12.5,12A0.5,0.5 0 0,1 12,12.5M6,20A2,2 0 0,1 4,18C4,16.89 4.9,16 6,16A2,2 0 0,1 8,18C8,19.11 7.1,20 6,20M6,8A2,2 0 0,1 4,6C4,4.89 4.9,4 6,4A2,2 0 0,1 8,6C8,7.11 7.1,8 6,8M9.64,7.64C9.87,7.14 10,6.59 10,6A4,4 0 0,0 6,2A4,4 0 0,0 2,6A4,4 0 0,0 6,10C6.59,10 7.14,9.87 7.64,9.64L10,12L7.64,14.36C7.14,14.13 6.59,14 6,14A4,4 0 0,0 2,18A4,4 0 0,0 6,22A4,4 0 0,0 10,18C10,17.41 9.87,16.86 9.64,16.36L12,14L19,21H22V20L9.64,7.64Z"
                />
              </svg>
              剪切 ({{ selectedFiles.length }})
            </button>
            <button
              class="action-btn"
              :disabled="clipboard.length === 0"
              @click="pasteFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M19,20H5V4H7V7H17V4H19M12,2A1,1 0 0,1 13,3A1,1 0 0,1 12,4A1,1 0 0,1 11,3A1,1 0 0,1 12,2M19,2H14.82C14.4,0.84 13.3,0 12,0C10.7,0 9.6,0.84 9.18,2H5A2,2 0 0,0 3,4V20A2,2 0 0,0 5,22H19A2,2 0 0,0 21,20V4A2,2 0 0,0 19,2Z"
                />
              </svg>
              粘贴 ({{ clipboard.length }})
            </button>
            <button
              class="action-btn"
              :disabled="selectedFiles.length !== 1"
              @click="renameFile"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
                />
              </svg>
              重命名
            </button>
            <button
              class="action-btn danger"
              :disabled="selectedFiles.length === 0"
              @click="deleteFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
              </svg>
              删除 ({{ selectedFiles.length }})
            </button>
          </div>
        </div>

        <!-- 操作历史 -->
        <div class="sidebar-section">
          <div class="section-header">
            <h3 class="section-title">操作历史</h3>
            <button
              class="clear-btn"
              @click="clearLogs"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z" />
              </svg>
            </button>
          </div>
          <div class="operation-logs">
            <div
              v-if="operationLogs.length === 0"
              class="empty-logs"
            >
              <svg
                class="empty-icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3"
                />
              </svg>
              <span>暂无操作记录</span>
            </div>
            <div
              v-else
              class="log-list"
            >
              <div
                v-for="log in operationLogs.slice(0, 10)"
                :key="log.id"
                class="log-item"
                :class="log.type"
              >
                <div class="log-time">{{ formatTime(log.timestamp) }}</div>
                <div class="log-content">
                  <span class="log-title">[{{ log.title }}]</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要文件浏览区域 -->
      <div class="file-browser">
        <!-- 文件浏览器头部 -->
        <div class="browser-header">
          <div class="browser-nav">
            <button
              :disabled="currentPath === '/'"
              class="nav-btn"
              @click="navigateUp"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z" />
              </svg>
              上级目录
            </button>
            <button
              :disabled="loading"
              class="nav-btn"
              @click="refreshFiles"
            >
              <svg
                class="icon"
                :class="{ 'animate-spin': loading }"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
                />
              </svg>
              刷新
            </button>
          </div>
          <div class="browser-search">
            <div class="search-box">
              <svg
                class="search-icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"
                />
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索文件..."
                class="search-input"
              />
            </div>
          </div>
          <div class="browser-actions">
            <button
              class="action-btn"
              @click="selectAll"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
              </svg>
              全选
            </button>
            <button
              :disabled="selectedItems.length === 0"
              class="action-btn"
              @click="clearSelection"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
              </svg>
              取消选择
            </button>
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-content">
          <div
            v-if="loading"
            class="loading-state"
          >
            <svg
              class="loading-icon animate-spin"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
            </svg>
            <span>加载中...</span>
          </div>
          <div
            v-else-if="filteredFiles.length === 0"
            class="empty-state"
          >
            <svg
              class="empty-icon"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
            </svg>
            <p class="empty-text">{{ searchQuery ? '未找到匹配的文件' : '此目录为空' }}</p>
            <button
              v-if="!searchQuery"
              class="refresh-btn"
              @click="refreshFiles"
            >
              <svg
                class="icon"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path
                  d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"
                />
              </svg>
              刷新目录
            </button>
          </div>
          <div
            v-else
            class="file-list"
            :class="viewMode"
          >
            <div
              v-for="file in filteredFiles"
              :key="file.name"
              class="file-item"
              :class="{ selected: selectedItems.includes(file.name) }"
              @click="selectFile(file)"
              @dblclick="openFile(file)"
              @contextmenu="showContextMenu($event, file)"
            >
              <div class="file-icon">
                <svg
                  v-if="file.type === 'directory'"
                  class="folder-icon"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
                </svg>
                <svg
                  v-else
                  class="file-icon-svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
              </div>
              <div class="file-info">
                <div
                  class="file-name"
                  :title="file.name"
                  >{{ file.name }}</div
                >
                <div
                  v-if="viewMode === 'list'"
                  class="file-meta"
                >
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-date">{{ formatDate(file.modified) }}</span>
                  <span class="file-type">{{ file.type === 'directory' ? '文件夹' : getFileType(file.name) }}</span>
                </div>
              </div>
              <div
                v-if="selectedItems.includes(file.name)"
                class="selection-indicator"
              >
                <svg
                  class="check-icon"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="item-count">{{ filteredFiles.length }} 项</span>
        <span
          v-if="selectedItems.length > 0"
          class="selected-count"
          >已选择 {{ selectedItems.length }} 项</span
        >
      </div>
      <div class="status-right">
        <span class="connection-info">{{ smbConfig.host ? `${smbConfig.host}/${smbConfig.share}` : '未连接' }}</span>
      </div>
    </div>
  </div>

  <!-- 右键菜单 -->
  <div
    v-show="contextMenu.show"
    class="context-menu"
    :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
  >
    <div
      class="context-menu-item"
      @click="copyToLocal"
    >
      <svg
        class="menu-icon"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
      </svg>
      拷贝到本地
    </div>
    <div
      class="context-menu-item"
      @click="showShareModal = true"
    >
      <svg
        class="menu-icon"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path
          d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z"
        />
      </svg>
      共享文件
    </div>
  </div>

  <!-- 拷贝模态框 -->
  <div
    v-if="showCopyModal"
    class="modal-overlay"
    @click="closeCopyModal"
  >
    <div
      class="modal-content"
      @click.stop
    >
      <div class="modal-header">
        <div class="modal-title">
          <svg
            class="modal-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
          <h3>拷贝文件到本地</h3>
        </div>
        <button
          class="modal-close-btn"
          @click="closeCopyModal"
        >
          <svg
            class="close-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <div class="copy-info">
          <div class="info-item">
            <label class="info-label">选中文件</label>
            <div
              v-if="selectedItem"
              class="selected-item"
            >
              <div class="item-icon">
                <svg
                  v-if="selectedItem.type === 'file'"
                  class="icon"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                </svg>
                <svg
                  v-else
                  class="icon"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
                </svg>
              </div>
              <div class="item-details">
                <div class="item-name">{{ selectedItem.name }}</div>
                <div class="item-path">{{ selectedItem.path }}</div>
                <div
                  v-if="selectedItem.type === 'file'"
                  class="item-size"
                  >{{ formatFileSize(selectedItem.size || 0) }}</div
                >
              </div>
            </div>
          </div>
          <div class="info-item">
            <label class="info-label">本地保存路径</label>
            <div class="input-with-button">
              <input
                v-model="copyConfig.localPath"
                type="text"
                placeholder="选择本地保存路径"
                class="form-input"
                readonly
              />
              <button
                class="btn btn-outline input-button"
                @click="selectLocalPath"
              >
                <svg
                  class="btn-icon"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6H12L10,4Z" />
                </svg>
                选择
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          class="btn btn-outline"
          @click="closeCopyModal"
        >
          取消
        </button>
        <button
          :disabled="copying || !copyConfig.localPath"
          class="btn btn-primary"
          @click="startRemoteCopy"
        >
          <svg
            v-if="copying"
            class="btn-icon animate-spin"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z" />
          </svg>
          <svg
            v-else
            class="btn-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
          {{ copying ? '拷贝中...' : '开始拷贝' }}
        </button>
      </div>
    </div>
  </div>

  <!-- 共享模态框 -->
  <div
    v-if="showShareModal"
    class="modal-overlay"
    @click="closeShareModal"
  >
    <div
      class="modal-content"
      @click.stop
    >
      <div class="modal-header">
        <div class="modal-title">
          <svg
            class="modal-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path
              d="M18,16.08C17.24,16.08 16.56,16.38 16.04,16.85L8.91,12.7C8.96,12.47 9,12.24 9,12C9,11.76 8.96,11.53 8.91,11.3L15.96,7.19C16.5,7.69 17.21,8 18,8A3,3 0 0,0 21,5A3,3 0 0,0 18,2A3,3 0 0,0 15,5C15,5.24 15.04,5.47 15.09,5.7L8.04,9.81C7.5,9.31 6.79,9 6,9A3,3 0 0,0 3,12A3,3 0 0,0 6,15C6.79,15 7.5,14.69 8.04,14.19L15.16,18.34C15.11,18.55 15.08,18.77 15.08,19C15.08,20.61 16.39,21.91 18,21.91C19.61,21.91 20.92,20.61 20.92,19A2.92,2.92 0 0,0 18,16.08Z"
            />
          </svg>
          <h3>文件共享</h3>
        </div>
        <button
          class="modal-close-btn"
          @click="closeShareModal"
        >
          <svg
            class="close-icon"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
          </svg>
        </button>
      </div>
      <div class="modal-body">
        <div class="share-info">
          <div class="info-item">
            <label class="info-label">本地IP地址</label>
            <div class="access-url">{{ localIP }}</div>
          </div>
          <div class="info-item">
            <label class="info-label">共享状态</label>
            <div class="info-value">{{ shareStatus.message }}</div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          class="btn btn-outline"
          @click="closeShareModal"
        >
          关闭
        </button>
        <button
          v-if="shareStatus.type !== 'idle'"
          class="btn btn-danger"
          @click="stopShare"
        >
          停止共享
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
// 暂时不导入图标，使用 emoji 代替

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const connecting = ref(false)
const scanning = ref(false)
const preparing = ref(false)
const copying = ref(false)
const connected = ref(false)
const localIP = ref('')
const showConnectionConfig = ref(false)
const showShareModal = ref(false)
const showCopyModal = ref(false)
const showSidebar = ref(true)
const viewMode = ref('list') // 'list' or 'grid'
const currentPath = ref('/')
const searchQuery = ref('')
const selectedItems = ref<string[]>([])
const selectedFiles = ref([])
const files = ref<any[]>([])
const filteredFiles = ref<any[]>([])
const fileList = ref([])
const sortBy = ref('name') // 'name', 'size', 'date', 'type'
const sortOrder = ref('asc') // 'asc' or 'desc'
const breadcrumbs = ref([{ name: '根目录', path: '/' }])
const clipboard = ref([])
const clipboardOperation = ref('') // 'copy' or 'cut'

// 右键菜单状态
const contextMenu = reactive({
  show: false,
  x: 0,
  y: 0
})

// 选中的文件项目
const selectedItem = ref<{
  name: string
  path: string
  size?: number
  fileCount?: number
  type: 'file' | 'directory'
} | null>(null)

// SMB配置
const smbConfig = reactive({
  host: '************',
  port: 445,
  username: '',
  password: '',
  domain: 'brbiotech',
  share: 'public'
})

// 连接配置
const connectionConfig = ref({
  server: '',
  port: 445,
  username: '',
  password: '',
  domain: '',
  share: ''
})

// 扫描配置
const scanConfig = reactive({
  directory: '',
  extensions: '.txt,.pdf,.docx,.json'
})

// 拷贝配置
const copyConfig = reactive({
  localPath: '',
  remotePath: ''
})

// 连接状态
const connectionStatus = reactive({
  type: 'idle' as 'success' | 'error' | 'idle',
  message: '未连接'
})

// 连接状态简化版
const connectionStatusSimple = ref('disconnected') // 'disconnected', 'connecting', 'connected', 'error'

// 共享状态
const shareStatus = reactive({
  type: 'idle',
  message: '未准备'
})

// 扫描结果
const scanResults = reactive({
  files: [] as Array<{
    name: string
    path: string
    size: number
    type: 'file'
  }>,
  directories: [] as Array<{
    name: string
    path: string
    fileCount: number
    type: 'directory'
  }>,
  // 合并的文件和文件夹列表，用于统一显示
  items: [] as Array<{
    name: string
    path: string
    size?: number
    fileCount?: number
    type: 'file' | 'directory'
  }>,
  totalFiles: 0,
  totalDirs: 0
})

// 操作日志
const operationLogs = ref<
  Array<{
    id: string
    timestamp: number
    title: string
    message: string
    type: 'success' | 'error' | 'info'
  }>
>([])

const addLog = (type: 'success' | 'error' | 'info', title: string, message: string) => {
  operationLogs.value.unshift({
    id: `${Date.now()}-${Math.random()}`,
    timestamp: Date.now(),
    title,
    message,
    type
  })
}

const formatTime = (timestamp: number) => {
  const date = new Date(timestamp)
  return `${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`
  return `${(size / (1024 * 1024)).toFixed(2)} MB`
}

const formatDate = (date: string | Date) => {
  const d = new Date(date)
  return d.toLocaleDateString()
}

const getFileType = (filename: string) => {
  const ext = filename.split('.').pop()?.toLowerCase()
  return ext || '文件'
}

const clearLogs = () => {
  operationLogs.value = []
  addLog('info', '系统操作', '已清空操作历史')
}

const getLocalIP = async () => {
  localIP.value = '***********'
}

const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const testClick = async () => {
  if (!smbConfig.host || !smbConfig.share) {
    addLog('error', '连接测试', '请填写服务器地址和共享名')
    return
  }

  connecting.value = true
  connectionStatusSimple.value = 'connecting'

  try {
    addLog('info', '连接测试', '正在测试SMB连接...')

    // 模拟连接测试
    await new Promise((resolve) => setTimeout(resolve, 2000))

    connected.value = true
    connectionStatusSimple.value = 'connected'
    addLog('success', '连接测试', '连接成功！')

    // 复制配置到新的连接配置
    connectionConfig.value = {
      server: smbConfig.host,
      port: smbConfig.port,
      username: smbConfig.username,
      password: smbConfig.password,
      domain: smbConfig.domain,
      share: smbConfig.share
    }

    // 自动加载文件列表
    await loadFiles('/')

    // 隐藏连接配置面板
    showConnectionConfig.value = false
  } catch (error) {
    connected.value = false
    connectionStatusSimple.value = 'error'
    addLog('error', '连接测试', `连接失败: ${error}`)
  } finally {
    connecting.value = false
  }
}

// 断开连接
const disconnectSMB = () => {
  connected.value = false
  connectionStatusSimple.value = 'disconnected'
  files.value = []
  fileList.value = []
  filteredFiles.value = []
  selectedFiles.value = []
  selectedItems.value = []
  currentPath.value = '/'
  breadcrumbs.value = [{ name: '根目录', path: '/' }]

  addLog('info', '连接管理', '已断开SMB连接')
}

// 重新连接
const reconnectSMB = async () => {
  if (connectionConfig.value.server && connectionConfig.value.share) {
    // 使用保存的配置重新连接
    smbConfig.host = connectionConfig.value.server
    smbConfig.port = connectionConfig.value.port
    smbConfig.username = connectionConfig.value.username
    smbConfig.password = connectionConfig.value.password
    smbConfig.domain = connectionConfig.value.domain
    smbConfig.share = connectionConfig.value.share

    await testClick()
  } else {
    addLog('error', '连接管理', '没有保存的连接配置')
  }
}

// 获取连接状态文本
const getConnectionStatusText = () => {
  switch (connectionStatusSimple.value) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中...'
    case 'error':
      return '连接失败'
    default:
      return '未连接'
  }
}

// 获取连接状态颜色
const getConnectionStatusColor = () => {
  switch (connectionStatusSimple.value) {
    case 'connected':
      return '#10b981'
    case 'connecting':
      return '#f59e0b'
    case 'error':
      return '#ef4444'
    default:
      return '#6b7280'
  }
}

const toggleConnectionConfig = () => {
  showConnectionConfig.value = !showConnectionConfig.value
}

const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'list' ? 'grid' : 'list'
}

const navigateUp = () => {
  if (currentPath.value !== '/') {
    const parts = currentPath.value.split('/').filter(Boolean)
    parts.pop()
    currentPath.value = '/' + parts.join('/')
    refreshFiles()
  }
}

const refreshFiles = async () => {
  loading.value = true
  setTimeout(() => {
    files.value = [
      { name: 'folder1', type: 'directory', size: 0, modified: new Date() },
      { name: 'file1.txt', type: 'file', size: 1024, modified: new Date() },
      { name: 'file2.pdf', type: 'file', size: 2048, modified: new Date() }
    ]
    filteredFiles.value = filterFilesBySearch(files.value)
    loading.value = false
    addLog('info', '文件刷新', '文件列表已刷新')
  }, 1000)
}

const filterFilesBySearch = (fileList: any[]) => {
  if (!searchQuery.value.trim()) {
    return fileList
  }
  const query = searchQuery.value.toLowerCase()
  return fileList.filter((file) => file.name.toLowerCase().includes(query))
}

const updateFilteredFiles = () => {
  filteredFiles.value = filterFilesBySearch(files.value)
}

const selectFile = (file: any) => {
  const index = selectedItems.value.indexOf(file.name)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(file.name)
  }
}

const openFile = (file: any) => {
  if (file.type === 'directory') {
    currentPath.value = currentPath.value === '/' ? `/${file.name}` : `${currentPath.value}/${file.name}`
    refreshFiles()
  }
  addLog('info', '文件操作', `打开 ${file.name}`)
}

const selectAll = () => {
  selectedItems.value = files.value.map((f) => f.name)
}

const clearSelection = () => {
  selectedItems.value = []
}

const uploadFiles = () => {
  addLog('info', '文件上传', '开始上传文件')
}

const createFolder = () => {
  addLog('info', '文件夹创建', '创建新文件夹')
}

const downloadSelected = () => {
  addLog('info', '文件下载', `下载 ${selectedItems.value.length} 个文件`)
}

const refreshStatus = async () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    addLog('info', '刷新状态', '状态已刷新')
  }, 2000)
}

const testConnection = async () => {
  connecting.value = true
  setTimeout(() => {
    connecting.value = false
    connectionStatus.type = 'success'
    connectionStatus.message = '连接成功'
    addLog('success', '连接测试', '连接成功')
  }, 2000)
}

const startFileScan = async () => {
  scanning.value = true
  setTimeout(() => {
    scanning.value = false
    scanResults.items = [
      {
        name: '文件1.txt',
        path: '/public/文件1.txt',
        size: 1024,
        type: 'file'
      },
      {
        name: '文件夹1',
        path: '/public/文件夹1',
        fileCount: 5,
        type: 'directory'
      }
    ]
    scanResults.totalFiles = 1
    scanResults.totalDirs = 1
    addLog('success', '文件扫描', '扫描完成')
  }, 3000)
}

const showContextMenu = (event: MouseEvent, item: any) => {
  event.preventDefault()
  contextMenu.show = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  selectedItem.value = item

  const hideMenu = () => {
    contextMenu.show = false
    document.removeEventListener('click', hideMenu)
  }

  setTimeout(() => {
    document.addEventListener('click', hideMenu)
  }, 0)

  addLog('info', '右键菜单', `显示 ${item.name} 的操作菜单`)
}

const selectItem = (item: any) => {
  selectedItem.value = item
  addLog('info', '文件选择', `选中文件: ${item.name}`)
}

const copyToLocal = () => {
  contextMenu.show = false
  if (selectedItem.value) {
    copyConfig.remotePath = selectedItem.value.path
    showCopyModal.value = true
    addLog('info', '拷贝操作', `准备拷贝 ${selectedItem.value.name} 到本地`)
  }
}

const closeCopyModal = () => {
  showCopyModal.value = false
  addLog('info', '拷贝操作', '取消拷贝操作')
}

const selectLocalPath = async () => {
  // 这里应该调用系统文件选择对话框
  copyConfig.localPath = '/Users/<USER>'
  addLog('info', '路径选择', '已选择本地保存路径')
}

const startRemoteCopy = async () => {
  copying.value = true
  addLog('info', '拷贝操作', '开始拷贝文件到本地')

  setTimeout(() => {
    copying.value = false
    showCopyModal.value = false
    addLog('success', '拷贝操作', '文件拷贝完成')
  }, 3000)
}

const closeShareModal = () => {
  showShareModal.value = false
}

const stopShare = () => {
  shareStatus.type = 'idle'
  shareStatus.message = '未共享'
  showShareModal.value = false
  addLog('info', '文件共享', '已停止文件共享')
}

// 监听搜索查询变化
watch(searchQuery, () => {
  filteredFiles.value = filterFilesBySearch(files.value)
})

// 监听文件列表变化
watch(
  files,
  () => {
    filteredFiles.value = filterFilesBySearch(files.value)
  },
  { deep: true }
)

// 组件挂载时初始化
onMounted(() => {
  getLocalIP()
  refreshFiles()
  addLog('info', '系统启动', 'SMB管理工具已启动')

  // 添加键盘快捷键监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + A 全选
  if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
    event.preventDefault()
    selectAllFiles()
  }

  // F5 刷新
  if (event.key === 'F5') {
    event.preventDefault()
    refreshFiles()
  }

  // Escape 清除选择
  if (event.key === 'Escape') {
    selectedFiles.value = []
    selectedItems.value = []
  }

  // Backspace 返回上级目录
  if (event.key === 'Backspace' && !(event.target as HTMLElement)?.tagName.match(/INPUT|TEXTAREA/)) {
    event.preventDefault()
    if (currentPath.value !== '/') {
      navigateUp()
    }
  }
}

// 文件操作方法
const copyFiles = () => {
  if (selectedFiles.value.length === 0) return
  clipboard.value = [...selectedFiles.value]
  clipboardOperation.value = 'copy'
  addLog('info', '文件操作', `已复制 ${selectedFiles.value.length} 个文件到剪贴板`)
}

const cutFiles = () => {
  if (selectedFiles.value.length === 0) return
  clipboard.value = [...selectedFiles.value]
  clipboardOperation.value = 'cut'
  addLog('info', '文件操作', `已剪切 ${selectedFiles.value.length} 个文件到剪贴板`)
}

const pasteFiles = async () => {
  if (clipboard.value.length === 0) return

  try {
    const operation = clipboardOperation.value
    const targetPath = currentPath.value

    for (const file of clipboard.value) {
      if (operation === 'copy') {
        // 模拟复制操作
        addLog('info', '文件操作', `正在复制文件: ${file.name}`)
        // 这里应该调用实际的SMB复制API
      } else if (operation === 'cut') {
        // 模拟移动操作
        addLog('info', '文件操作', `正在移动文件: ${file.name}`)
        // 这里应该调用实际的SMB移动API
      }
    }

    addLog('success', '文件操作', `${operation === 'copy' ? '复制' : '移动'}操作完成`)

    // 清空剪贴板（如果是剪切操作）
    if (operation === 'cut') {
      clipboard.value = []
      clipboardOperation.value = ''
    }

    // 刷新文件列表
    await loadFiles(currentPath.value)
  } catch (error) {
    addLog('error', '文件操作', `粘贴操作失败: ${error.message}`)
  }
}

const renameFile = () => {
  if (selectedFiles.value.length !== 1) return

  const file = selectedFiles.value[0]
  const newName = prompt('请输入新的文件名:', file.name)

  if (newName && newName !== file.name) {
    try {
      // 这里应该调用实际的SMB重命名API
      addLog('info', '文件操作', `正在重命名文件: ${file.name} -> ${newName}`)
      addLog('success', '文件操作', '文件重命名成功')

      // 刷新文件列表
      loadFiles(currentPath.value)
    } catch (error) {
      addLog('error', '文件操作', `重命名失败: ${error.message}`)
    }
  }
}

const deleteFiles = async () => {
  if (selectedFiles.value.length === 0) return

  const fileNames = selectedFiles.value.map((f) => f.name).join(', ')
  const confirmed = confirm(`确定要删除以下文件吗？\n\n${fileNames}`)

  if (confirmed) {
    try {
      for (const file of selectedFiles.value) {
        // 这里应该调用实际的SMB删除API
        addLog('info', '文件操作', `正在删除文件: ${file.name}`)
      }

      addLog('success', '文件操作', `成功删除 ${selectedFiles.value.length} 个文件`)
      selectedFiles.value = []

      // 刷新文件列表
      await loadFiles(currentPath.value)
    } catch (error) {
      addLog('error', '文件操作', `删除操作失败: ${error.message}`)
    }
  }
}

// 导航相关方法（移除重复声明）

const navigateToPath = async (path: string) => {
  currentPath.value = path
  selectedFiles.value = []
  selectedItems.value = []
  await loadFiles(path)
}

const loadFiles = async (path: string = currentPath.value) => {
  if (!connected.value) return

  scanning.value = true

  try {
    addLog('info', '文件浏览', `正在加载目录: ${path}`)

    // 模拟文件加载
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟文件数据
    const mockFiles = [
      {
        name: '文档',
        type: 'folder',
        size: 0,
        modified: new Date('2024-01-15'),
        path: path + '/文档'
      },
      {
        name: '图片',
        type: 'folder',
        size: 0,
        modified: new Date('2024-01-14'),
        path: path + '/图片'
      },
      {
        name: 'report.pdf',
        type: 'file',
        size: 2048576,
        modified: new Date('2024-01-13'),
        path: path + '/report.pdf'
      },
      {
        name: 'data.xlsx',
        type: 'file',
        size: 1024000,
        modified: new Date('2024-01-12'),
        path: path + '/data.xlsx'
      }
    ]

    files.value = mockFiles
    fileList.value = mockFiles
    filteredFiles.value = filterFilesBySearch(mockFiles)
    updateBreadcrumbs(path)

    addLog('success', '文件浏览', `成功加载 ${mockFiles.length} 个项目`)
  } catch (error) {
    addLog('error', '文件浏览', `加载失败: ${error}`)
  } finally {
    scanning.value = false
  }
}

const updateBreadcrumbs = (path: string) => {
  const parts = path.split('/').filter((part) => part)
  breadcrumbs.value = [{ name: '根目录', path: '/' }]

  let currentPath = ''
  parts.forEach((part) => {
    currentPath += '/' + part
    breadcrumbs.value.push({
      name: part,
      path: currentPath
    })
  })
}

const selectAllFiles = () => {
  if (selectedFiles.value.length === filteredFiles.value.length) {
    selectedFiles.value = []
    selectedItems.value = []
  } else {
    selectedFiles.value = [...filteredFiles.value]
    selectedItems.value = filteredFiles.value.map((f) => f.name)
  }
}
</script>

<style scoped>
/* 全屏布局 */
.smb-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  overflow: hidden;
}

/* 顶部工具栏 */
.toolbar {
  height: 60px;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc2626;
}

.status-indicator.success {
  background: #059669;
}

.status-indicator.error {
  background: #dc2626;
}

.status-indicator.idle {
  background: #6b7280;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.breadcrumb-item {
  color: rgba(255, 255, 255, 0.9);
}

.breadcrumb-separator {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

.icon {
  width: 16px;
  height: 16px;
}

/* 主要内容区域 */
.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
  width: 320px;
  background: white;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  z-index: 50;
}

.sidebar-section {
  border-bottom: 1px solid #e5e7eb;
}

.section-header {
  padding: 16px 20px;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.toggle-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.toggle-btn:hover {
  background: #e5e7eb;
}

.connection-form {
  padding: 16px 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 8px;
}

.form-row .form-group {
  flex: 1;
}

.form-label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.connect-btn {
  width: 100%;
  background: #4f46e5;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.connect-btn:hover:not(:disabled) {
  background: #4338ca;
}

.connect-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.quick-actions {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  background: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: left;
}

.action-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.operation-logs {
  padding: 16px 20px;
  max-height: 300px;
  overflow-y: auto;
}

.empty-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  color: #9ca3af;
  text-align: center;
}

.empty-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.log-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 4px;
}

.log-content {
  font-size: 12px;
  line-height: 1.4;
}

.log-title {
  font-weight: 500;
  color: #4f46e5;
}

.log-message {
  color: #6b7280;
  margin-left: 4px;
}

.log-item.success .log-title {
  color: #059669;
}

.log-item.error .log-title {
  color: #dc2626;
}

.clear-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: #6b7280;
}

.clear-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

/* 文件浏览器主区域 */
.file-browser {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.browser-header {
  height: 56px;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.browser-nav {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  background: transparent;
  border: 1px solid #d1d5db;
  color: #6b7280;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.nav-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #374151;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.browser-search {
  display: flex;
  align-items: center;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  color: #9ca3af;
  pointer-events: none;
}

.search-input {
  width: 240px;
  padding: 8px 12px 8px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #4f46e5;
}

.browser-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 文件列表 */
.file-content {
  flex: 1;
  overflow-y: auto;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6b7280;
  font-size: 14px;
  gap: 8px;
}

.loading-icon {
  width: 20px;
  height: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #9ca3af;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin: 16px 0 8px 0;
  color: #6b7280;
}

.refresh-btn {
  background: #4f46e5;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 16px;
}

.refresh-btn:hover {
  background: #4338ca;
}

.file-list {
  padding: 0;
}

.file-list.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 16px;
}

.file-list.list .file-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-list.grid .file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.file-item:hover {
  background: #f8fafc;
}

.file-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.file-icon {
  flex-shrink: 0;
  margin-right: 12px;
}

.file-list.grid .file-icon {
  margin-right: 0;
  margin-bottom: 8px;
}

.folder-icon {
  width: 20px;
  height: 20px;
  color: #f59e0b;
}

.file-icon-svg {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
  display: flex;
  gap: 12px;
}

.file-size {
  color: #6b7280;
}

.file-date {
  color: #6b7280;
}

.file-type {
  color: #6b7280;
}

.selection-indicator {
  margin-left: 12px;
  flex-shrink: 0;
}

.check-icon {
  width: 16px;
  height: 16px;
  color: #3b82f6;
}

/* 底部状态栏 */
.status-bar {
  height: 32px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 12px;
  color: #6b7280;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.item-count {
  color: #6b7280;
}

.selected-count {
  color: #3b82f6;
  font-weight: 500;
}

.connection-info {
  color: #6b7280;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 14px;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.context-menu-item:hover {
  background: #f3f4f6;
}

.menu-icon {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-title h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.modal-icon {
  width: 20px;
  height: 20px;
  color: #6366f1;
}

.modal-close-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.close-icon {
  width: 18px;
  height: 18px;
}

.modal-body {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 表单样式 */
.copy-info,
.share-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-item {
  margin-bottom: 20px;
}

.info-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.item-icon .icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 2px;
}

.item-path {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 2px;
}

.item-size {
  font-size: 12px;
  color: #9ca3af;
}

.input-with-button {
  display: flex;
  gap: 8px;
}

.input-with-button .form-input {
  flex: 1;
}

.input-button {
  flex-shrink: 0;
}

.access-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #4f46e5;
  border: 1px solid #4f46e5;
}

.btn-outline:hover:not(:disabled) {
  background: #4f46e5;
  color: white;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    padding: 0 16px;
  }

  .toolbar-title {
    font-size: 16px;
  }

  .sidebar {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 200;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .browser-header {
    padding: 0 16px;
  }

  .search-input {
    width: 180px;
  }

  .file-item {
    padding: 10px 16px;
  }

  .status-bar {
    padding: 0 16px;
  }
}

/* 连接状态样式 */
.connection-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  font-size: 13px;
}

.status-label {
  color: #64748b;
  font-weight: 500;
}

.status-value {
  font-weight: 600;
  text-align: right;
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.connection-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn.danger {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.action-btn.danger:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}
</style>
