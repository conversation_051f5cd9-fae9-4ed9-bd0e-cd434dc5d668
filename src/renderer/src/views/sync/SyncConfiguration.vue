<template>
  <div class="sync-configuration">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Settings class="title-icon" />
            同步配置
          </h1>
          <p class="page-description">配置数据同步后端、策略和故障转移规则</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="scrollToSyncStrategy">
          <Settings class="btn-icon" />
          同步策略
        </a-button>
        <a-button
          type="primary"
          @click="showAddBackendModal"
        >
          <Plus class="btn-icon" />
          添加后端
        </a-button>
      </div>
    </div>

    <!-- 配置概览 -->
    <div class="config-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="配置后端总数"
              :value="backends.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Database class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="启用的后端"
              :value="enabledBackends.length"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircle class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="健康后端"
              :value="healthyBackends.length"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <Heart class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="最后同步"
              :value="lastSyncTime"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <Clock class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 后端配置列表 -->
    <div class="backend-list">
      <div class="section-header">
        <h2 class="section-title">配置后端</h2>
        <div class="section-actions">
          <a-button
            :loading="testingAll"
            @click="testAllBackends"
          >
            <TestTube class="btn-icon" />
            测试所有连接
          </a-button>
          <a-button
            :loading="syncingAll"
            @click="syncAllBackends"
          >
            <RefreshCw class="btn-icon" />
            同步所有后端
          </a-button>
        </div>
      </div>

      <div class="backend-cards">
        <div
          v-for="backend in backends"
          :key="backend.id"
          class="backend-card"
          :class="{ disabled: !backend.enabled }"
        >
          <div class="card-header">
            <div class="backend-info">
              <div class="backend-icon">
                <Database v-if="backend.type === 'database'" />
                <HardDrive v-else-if="backend.type === 'smb'" />
                <GitBranch v-else-if="backend.type === 'git'" />
                <Server v-else-if="backend.type === 'consul'" />
                <Cloud v-else />
              </div>
              <div class="backend-details">
                <h3 class="backend-name">{{ backend.name }}</h3>
                <p class="backend-type">{{ getBackendTypeLabel(backend.type) }}</p>
              </div>
            </div>
            <div class="backend-status">
              <a-badge
                :status="getHealthStatusType(backend.health_status)"
                :text="getHealthStatusText(backend.health_status)"
              />
              <a-switch
                v-model:checked="backend.enabled"
                size="small"
                @change="toggleBackend(backend)"
              />
            </div>
          </div>

          <div class="card-content">
            <div class="backend-meta">
              <div class="meta-item">
                <span class="meta-label">优先级:</span>
                <a-tag color="blue">{{ backend.priority }}</a-tag>
              </div>
              <div class="meta-item">
                <span class="meta-label">最后检查:</span>
                <span class="meta-value">{{ formatTime(backend.last_health_check) }}</span>
              </div>
            </div>
            <div class="backend-config">
              <a-descriptions
                size="small"
                :column="1"
              >
                <a-descriptions-item
                  v-for="(value, key) in getDisplayConfig(backend.config)"
                  :key="key"
                  :label="key"
                >
                  {{ value }}
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </div>

          <div class="card-actions">
            <a-button
              size="small"
              :loading="backend.testing"
              @click="testBackend(backend)"
            >
              <TestTube class="action-icon" />
              测试连接
            </a-button>
            <a-button
              size="small"
              :loading="backend.syncing"
              @click="syncBackend(backend)"
            >
              <RefreshCw class="action-icon" />
              立即同步
            </a-button>
            <a-button
              size="small"
              @click="editBackend(backend)"
            >
              <Edit class="action-icon" />
              编辑
            </a-button>
            <a-button
              size="small"
              danger
              @click="deleteBackend(backend)"
            >
              <Trash2 class="action-icon" />
              删除
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步策略配置 -->
    <div
      id="sync-strategy-section"
      class="sync-strategy"
    >
      <div class="section-header">
        <h2 class="section-title">同步策略</h2>
        <a-tag color="blue">配置同步行为</a-tag>
      </div>

      <a-card>
        <a-form
          :model="syncStrategy"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="同步间隔">
                <a-select v-model:value="syncStrategy.interval">
                  <a-select-option value="300">5分钟</a-select-option>
                  <a-select-option value="600">10分钟</a-select-option>
                  <a-select-option value="1800">30分钟</a-select-option>
                  <a-select-option value="3600">1小时</a-select-option>
                  <a-select-option value="21600">6小时</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="冲突解决策略">
                <a-select v-model:value="syncStrategy.conflictResolution">
                  <a-select-option value="server_wins">服务器优先</a-select-option>
                  <a-select-option value="client_wins">客户端优先</a-select-option>
                  <a-select-option value="timestamp">时间戳优先</a-select-option>
                  <a-select-option value="manual">手动解决</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="重试次数">
                <a-input-number
                  v-model:value="syncStrategy.retryCount"
                  :min="0"
                  :max="10"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item>
                <a-checkbox v-model:checked="syncStrategy.autoSync"> 启用自动同步 </a-checkbox>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <a-checkbox v-model:checked="syncStrategy.enableFailover"> 启用故障转移 </a-checkbox>
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item>
            <a-button
              type="primary"
              @click="saveSyncStrategy"
            >
              保存策略配置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 故障转移配置 -->
    <div class="failover-config">
      <div class="section-header">
        <h2 class="section-title">故障转移配置</h2>
        <a-tag color="orange">高可用性设置</a-tag>
      </div>

      <a-card>
        <a-form
          :model="failoverConfig"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="主后端">
                <a-select
                  v-model:value="failoverConfig.primaryBackend"
                  placeholder="选择主后端"
                >
                  <a-select-option
                    v-for="backend in enabledBackends"
                    :key="backend.id"
                    :value="backend.id"
                  >
                    {{ backend.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="备用后端">
                <a-select
                  v-model:value="failoverConfig.backupBackends"
                  mode="multiple"
                  placeholder="选择备用后端"
                >
                  <a-select-option
                    v-for="backend in enabledBackends"
                    :key="backend.id"
                    :value="backend.id"
                  >
                    {{ backend.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="健康检查间隔">
                <a-select v-model:value="failoverConfig.healthCheckInterval">
                  <a-select-option value="30">30秒</a-select-option>
                  <a-select-option value="60">1分钟</a-select-option>
                  <a-select-option value="300">5分钟</a-select-option>
                  <a-select-option value="600">10分钟</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="故障检测阈值">
                <a-input-number
                  v-model:value="failoverConfig.failureThreshold"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                  placeholder="连续失败次数"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="恢复检测阈值">
                <a-input-number
                  v-model:value="failoverConfig.recoveryThreshold"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                  placeholder="连续成功次数"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-form-item>
            <a-checkbox v-model:checked="failoverConfig.autoFailback"> 启用自动故障恢复 </a-checkbox>
          </a-form-item>
          <a-form-item>
            <a-button
              type="primary"
              @click="saveFailoverConfig"
            >
              保存故障转移配置
            </a-button>
            <a-button
              style="margin-left: 8px"
              @click="testFailover"
            >
              测试故障转移
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 同步状态监控 -->
    <div class="sync-monitoring">
      <div class="section-header">
        <h2 class="section-title">同步状态监控</h2>
        <div class="section-actions">
          <a-button @click="refreshSyncStatus">
            <RefreshCw class="btn-icon" />
            刷新状态
          </a-button>
          <a-button @click="forceSyncAll">
            <Zap class="btn-icon" />
            强制同步
          </a-button>
        </div>
      </div>

      <a-row :gutter="16">
        <a-col
          v-for="backend in backends"
          :key="backend.id"
          :span="8"
        >
          <a-card class="sync-status-card">
            <div class="status-header">
              <div class="backend-info">
                <h4>{{ backend.name }}</h4>
                <a-tag :color="getBackendStatusColor(backend.status)">
                  {{ getBackendStatusText(backend.status) }}
                </a-tag>
              </div>
              <div class="status-actions">
                <a-button
                  size="small"
                  @click="syncBackendWithProgress(backend)"
                >
                  同步
                </a-button>
              </div>
            </div>
            <div class="status-metrics">
              <div class="metric">
                <span class="metric-label">最后同步</span>
                <span class="metric-value">{{ formatTime(backend.lastSync) }}</span>
              </div>
              <div class="metric">
                <span class="metric-label">同步耗时</span>
                <span class="metric-value">{{ backend.syncDuration }}ms</span>
              </div>
              <div class="metric">
                <span class="metric-label">数据量</span>
                <span class="metric-value">{{ backend.dataSize }}</span>
              </div>
            </div>
            <div class="sync-progress">
              <a-progress
                :percent="backend.syncProgress"
                :status="backend.syncProgress === 100 ? 'success' : 'active'"
                size="small"
              />
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 高级设置 -->
    <div class="advanced-settings">
      <div class="section-header">
        <h2 class="section-title">高级设置</h2>
        <a-tag color="purple">专家选项</a-tag>
      </div>

      <a-card>
        <a-collapse>
          <a-collapse-panel
            key="performance"
            header="性能优化"
          >
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="并发连接数">
                    <a-input-number
                      v-model:value="advancedSettings.maxConcurrentConnections"
                      :min="1"
                      :max="50"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="连接超时(秒)">
                    <a-input-number
                      v-model:value="advancedSettings.connectionTimeout"
                      :min="5"
                      :max="300"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="批量大小">
                    <a-input-number
                      v-model:value="advancedSettings.batchSize"
                      :min="10"
                      :max="1000"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-collapse-panel>

          <a-collapse-panel
            key="security"
            header="安全设置"
          >
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item>
                    <a-checkbox v-model:checked="advancedSettings.enableEncryption"> 启用传输加密 </a-checkbox>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item>
                    <a-checkbox v-model:checked="advancedSettings.enableCompression"> 启用数据压缩 </a-checkbox>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-form-item label="SSL证书路径">
                <a-input
                  v-model:value="advancedSettings.sslCertPath"
                  placeholder="/path/to/certificate.pem"
                />
              </a-form-item>
            </a-form>
          </a-collapse-panel>

          <a-collapse-panel
            key="logging"
            header="日志设置"
          >
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="日志级别">
                    <a-select v-model:value="advancedSettings.logLevel">
                      <a-select-option value="debug">调试</a-select-option>
                      <a-select-option value="info">信息</a-select-option>
                      <a-select-option value="warning">警告</a-select-option>
                      <a-select-option value="error">错误</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="日志保留天数">
                    <a-input-number
                      v-model:value="advancedSettings.logRetentionDays"
                      :min="1"
                      :max="365"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="最大日志文件大小(MB)">
                    <a-input-number
                      v-model:value="advancedSettings.maxLogFileSize"
                      :min="1"
                      :max="1000"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-collapse-panel>
        </a-collapse>

        <div style="margin-top: 16px">
          <a-button
            type="primary"
            @click="saveAdvancedSettings"
          >
            保存高级设置
          </a-button>
          <a-button
            style="margin-left: 8px"
            @click="resetAdvancedSettings"
          >
            重置为默认值
          </a-button>
        </div>
      </a-card>
    </div>

    <!-- 添加/编辑后端模态框 -->
    <a-modal
      v-model:open="showBackendModal"
      :title="editingBackend ? '编辑后端' : '添加后端'"
      width="800px"
      @ok="handleSaveBackend"
      @cancel="handleCancelBackend"
    >
      <a-form
        ref="backendForm"
        :model="backendFormData"
        :rules="backendFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="后端名称"
              name="name"
            >
              <a-input
                v-model:value="backendFormData.name"
                placeholder="请输入后端名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="后端类型"
              name="type"
            >
              <a-select
                v-model:value="backendFormData.type"
                placeholder="请选择后端类型"
                @change="handleTypeChange"
              >
                <a-select-option value="database">数据库</a-select-option>
                <a-select-option value="smb">SMB共享</a-select-option>
                <a-select-option value="git">Git仓库</a-select-option>
                <a-select-option value="consul">Consul</a-select-option>
                <a-select-option value="s3">对象存储</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="优先级"
              name="priority"
            >
              <a-input-number
                v-model:value="backendFormData.priority"
                :min="1"
                :max="100"
                style="width: 100%"
                placeholder="数字越小优先级越高"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="backendFormData.enabled"> 启用此后端 </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 动态配置表单 -->
        <div
          v-if="backendFormData.type"
          class="config-form"
        >
          <h4>{{ getBackendTypeLabel(backendFormData.type) }}配置</h4>

          <!-- 数据库配置 -->
          <template v-if="backendFormData.type === 'database'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="数据库类型">
                  <a-select v-model:value="backendFormData.config.database_type">
                    <a-select-option value="postgresql">PostgreSQL</a-select-option>
                    <a-select-option value="mysql">MySQL</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="表名">
                  <a-input
                    v-model:value="backendFormData.config.table_name"
                    placeholder="host_configurations"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="backendFormData.config.host"
                    placeholder="***************"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="端口">
                  <a-input-number
                    v-model:value="backendFormData.config.port"
                    placeholder="5432"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="数据库名">
                  <a-input
                    v-model:value="backendFormData.config.database"
                    placeholder="postgres"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input
                    v-model:value="backendFormData.config.username"
                    placeholder="postgres"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="密码">
                  <a-input-password
                    v-model:value="backendFormData.config.password"
                    placeholder="请输入数据库密码"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="SSL模式">
                  <a-select v-model:value="backendFormData.config.ssl_mode">
                    <a-select-option value="disable">禁用</a-select-option>
                    <a-select-option value="prefer">首选</a-select-option>
                    <a-select-option value="require">必需</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 数据库初始化选项 -->
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item>
                  <a-checkbox v-model:checked="backendFormData.config.initialize_database"> 初始化数据库 </a-checkbox>
                  <div class="text-gray-500 text-sm mt-1"> 适用于新部署的数据库，将创建所有必要的表结构和初始数据 </div>
                </a-form-item>
              </a-col>
            </a-row>
          </template>

          <!-- SMB配置 -->
          <template v-if="backendFormData.type === 'smb'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="backendFormData.config.host"
                    placeholder="fileserver.company.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="共享名">
                  <a-input
                    v-model:value="backendFormData.config.share"
                    placeholder="it_resources"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input
                    v-model:value="backendFormData.config.username"
                    placeholder="service_account"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密码">
                  <a-input-password
                    v-model:value="backendFormData.config.password"
                    placeholder="请输入密码"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="域">
                  <a-input
                    v-model:value="backendFormData.config.domain"
                    placeholder="COMPANY"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="端口">
                  <a-input-number
                    v-model:value="backendFormData.config.port"
                    placeholder="445"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="文件路径">
              <a-input
                v-model:value="backendFormData.config.path"
                placeholder="/configs/hosts.yaml"
              />
            </a-form-item>

            <!-- SMB测试连接按钮 -->
            <a-form-item>
              <a-space>
                <a-button
                  type="primary"
                  :loading="backendFormData.testing"
                  @click="testSmbConnection"
                >
                  <template #icon>
                    <CheckCircle :size="16" />
                  </template>
                  测试连接
                </a-button>
                <span
                  v-if="backendFormData.testResult"
                  :class="backendFormData.testResult.success ? 'text-green-600' : 'text-red-600'"
                >
                  {{ backendFormData.testResult.message }}
                </span>
              </a-space>
            </a-form-item>
          </template>

          <!-- Git配置 -->
          <template v-if="backendFormData.type === 'git'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="仓库地址">
                  <a-input
                    v-model:value="backendFormData.config.repository"
                    placeholder="**************:company/config.git"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="分支">
                  <a-input
                    v-model:value="backendFormData.config.branch"
                    placeholder="main"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="文件路径">
              <a-input
                v-model:value="backendFormData.config.file_path"
                placeholder="hosts.yaml"
              />
            </a-form-item>

            <!-- Git认证配置 -->
            <a-divider orientation="left">认证配置</a-divider>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="认证方式">
                  <a-select
                    v-model:value="backendFormData.config.auth_type"
                    placeholder="请选择认证方式"
                    @change="handleGitAuthTypeChange"
                  >
                    <a-select-option value="token">访问令牌</a-select-option>
                    <a-select-option value="username_password">用户名密码</a-select-option>
                    <a-select-option value="ssh_key">SSH密钥</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 访问令牌认证 -->
            <template v-if="backendFormData.config.auth_type === 'token'">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="访问令牌">
                    <a-input-password
                      v-model:value="backendFormData.config.token"
                      placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                    />
                    <div class="text-gray-500 text-sm mt-1"> GitHub Personal Access Token 或其他Git服务的访问令牌 </div>
                  </a-form-item>
                </a-col>
              </a-row>
            </template>

            <!-- 用户名密码认证 -->
            <template v-if="backendFormData.config.auth_type === 'username_password'">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="用户名">
                    <a-input
                      v-model:value="backendFormData.config.username"
                      placeholder="请输入Git用户名"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="密码">
                    <a-input-password
                      v-model:value="backendFormData.config.password"
                      placeholder="请输入Git密码"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </template>

            <!-- SSH密钥认证 -->
            <template v-if="backendFormData.config.auth_type === 'ssh_key'">
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="SSH私钥路径">
                    <a-input
                      v-model:value="backendFormData.config.ssh_key_path"
                      placeholder="~/.ssh/id_rsa"
                    />
                    <div class="text-gray-500 text-sm mt-1"> SSH私钥文件的完整路径 </div>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="SSH密钥密码（可选）">
                    <a-input-password
                      v-model:value="backendFormData.config.ssh_passphrase"
                      placeholder="如果SSH密钥有密码保护，请输入"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </template>
          </template>

          <!-- Consul配置 -->
          <template v-if="backendFormData.type === 'consul'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="backendFormData.config.host"
                    placeholder="consul.company.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="端口">
                  <a-input-number
                    v-model:value="backendFormData.config.port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                    placeholder="8500"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-form-item label="键前缀">
              <a-input
                v-model:value="backendFormData.config.key_prefix"
                placeholder="chaterm/hosts/"
              />
            </a-form-item>
          </template>

          <!-- S3配置 -->
          <template v-if="backendFormData.type === 's3'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="端点">
                  <a-input
                    v-model:value="backendFormData.config.endpoint"
                    placeholder="s3.amazonaws.com"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="存储桶">
                  <a-input
                    v-model:value="backendFormData.config.bucket"
                    placeholder="config-bucket"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="访问密钥">
                  <a-input
                    v-model:value="backendFormData.config.access_key"
                    placeholder="AKIAIOSFODNN7EXAMPLE"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="密钥">
                  <a-input-password
                    v-model:value="backendFormData.config.secret_key"
                    placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </template>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, notification } from 'ant-design-vue'
import eventBus from '@/utils/eventBus'
import {
  Settings,
  Plus,
  Database,
  CheckCircle,
  Heart,
  Clock,
  TestTube,
  RefreshCw,
  HardDrive,
  GitBranch,
  Server,
  Cloud,
  Edit,
  Trash2,
  ArrowLeft
} from 'lucide-vue-next'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 同步配置页面
 * 功能：配置数据同步后端、策略和故障转移规则
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 路由实例
const router = useRouter()

// 响应式数据
const testingAll = ref(false)
const syncingAll = ref(false)
const showBackendModal = ref(false)
const editingBackend = ref(null)

// 后端列表数据
const backends = ref([
  {
    id: '1',
    name: 'primary-db',
    type: 'database',
    priority: 1,
    enabled: true,
    health_status: 'healthy',
    last_health_check: new Date('2025-01-15T10:30:00Z'),
    config: {
      database_type: 'postgresql',
      host: '***************',
      port: 5432,
      database: 'postgres',
      username: 'postgres',
      password: 'E5z`+wk%YjtO:@zE^YI<',
      ssl_mode: 'disable',
      table_name: 'host_configurations'
    },
    testing: false,
    syncing: false,
    syncProgress: 100,
    lastSync: new Date('2025-01-15T10:30:00Z'),
    syncDuration: 250,
    dataSize: '45MB'
  },
  {
    id: '2',
    name: 'legacy-smb',
    type: 'smb',
    priority: 2,
    enabled: true,
    health_status: 'unhealthy',
    last_health_check: new Date('2025-01-15T09:15:00Z'),
    config: {
      host: 'fileserver.company.com',
      share: 'it_resources',
      path: '/configs/hosts.yaml',
      username: 'service_account',
      domain: 'COMPANY'
    },
    testing: false,
    syncing: false,
    syncProgress: 0,
    lastSync: new Date('2025-01-15T08:45:00Z'),
    syncDuration: 1200,
    dataSize: '12MB'
  }
])

// 同步策略配置
const syncStrategy = reactive({
  interval: 1800,
  conflictResolution: 'timestamp',
  retryCount: 3,
  autoSync: true,
  enableFailover: true
})

// 故障转移配置
const failoverConfig = reactive({
  primaryBackend: '',
  backupBackends: [],
  healthCheckInterval: 60,
  failureThreshold: 3,
  recoveryThreshold: 2,
  autoFailback: true
})

// 高级设置
const advancedSettings = reactive({
  maxConcurrentConnections: 10,
  connectionTimeout: 30,
  batchSize: 100,
  enableEncryption: true,
  enableCompression: false,
  sslCertPath: '',
  logLevel: 'info',
  logRetentionDays: 30,
  maxLogFileSize: 100
})

// 表单数据
const backendFormData = reactive({
  name: '',
  type: '',
  priority: 1,
  enabled: true,
  config: {},
  testing: false,
  testResult: null
})

// 表单验证规则
const backendFormRules = {
  name: [{ required: true, message: '请输入后端名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择后端类型', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
}

// 计算属性
const enabledBackends = computed(() => {
  return backends.value.filter((backend) => backend.enabled)
})

const healthyBackends = computed(() => {
  return backends.value.filter((backend) => backend.health_status === 'healthy')
})

const lastSyncTime = computed(() => {
  const times = backends.value.map((b) => b.last_health_check).filter(Boolean)
  if (times.length === 0) return '从未同步'
  const latest = new Date(Math.max(...times.map((t) => t.getTime())))
  return formatDistanceToNow(latest, { addSuffix: true, locale: zhCN })
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const formatTime = (date: Date) => {
  if (!date) return '从未检查'
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const getBackendTypeLabel = (type: string) => {
  const labels = {
    database: '数据库',
    smb: 'SMB共享',
    git: 'Git仓库',
    consul: 'Consul',
    s3: '对象存储'
  }
  return labels[type] || type
}

const getHealthStatusType = (status: string) => {
  const types = {
    healthy: 'success',
    unhealthy: 'error',
    unknown: 'default'
  }
  return types[status] || 'default'
}

const getHealthStatusText = (status: string) => {
  const texts = {
    healthy: '健康',
    unhealthy: '异常',
    unknown: '未知'
  }
  return texts[status] || status
}

const getDisplayConfig = (config: any) => {
  const display = {}
  for (const [key, value] of Object.entries(config)) {
    if (key.includes('password') || key.includes('secret') || key.includes('key')) {
      display[key] = '***'
    } else {
      display[key] = String(value)
    }
  }
  return display
}

const showAddBackendModal = () => {
  editingBackend.value = null
  resetBackendForm()
  showBackendModal.value = true
}

const editBackend = (backend: any) => {
  editingBackend.value = backend
  Object.assign(backendFormData, backend)
  showBackendModal.value = true
}

const deleteBackend = (backend: any) => {
  const index = backends.value.findIndex((b) => b.id === backend.id)
  if (index > -1) {
    backends.value.splice(index, 1)
    message.success('后端删除成功')
  }
}

const toggleBackend = (backend: any) => {
  message.success(`后端已${backend.enabled ? '启用' : '禁用'}`)
}

const testBackend = async (backend: any) => {
  backend.testing = true
  try {
    if (backend.type === 'postgresql') {
      // 调用主进程的PostgreSQL连接测试
      const result = await (window as any).electron.ipcRenderer.invoke('test-postgresql-connection', backend.config)

      if (result.success) {
        backend.health_status = 'healthy'
        backend.last_health_check = new Date()
        message.success(`${backend.name} 连接测试成功`)
        console.log('连接信息:', result.connectionInfo)
      } else {
        backend.health_status = 'unhealthy'
        message.error(`${backend.name} 连接测试失败: ${result.error}`)
      }
    } else if (backend.type === 'smb') {
      // 调用主进程的SMB连接测试
      const result = await (window as any).electron.ipcRenderer.invoke('test-smb-connection', backend.config)

      if (result.success) {
        backend.health_status = 'healthy'
        backend.last_health_check = new Date()
        message.success(`${backend.name} 连接测试成功`)
        console.log('SMB连接信息:', result.connectionInfo)
      } else {
        backend.health_status = 'unhealthy'
        message.error(`${backend.name} 连接测试失败: ${result.error}`)
      }
    } else {
      // 其他类型的后端使用模拟测试
      await new Promise((resolve) => setTimeout(resolve, 2000))
      backend.health_status = 'healthy'
      backend.last_health_check = new Date()
      message.success(`${backend.name} 连接测试成功`)
    }
  } catch (error) {
    backend.health_status = 'unhealthy'
    message.error(`${backend.name} 连接测试失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
  } finally {
    backend.testing = false
  }
}

// SMB测试连接方法（用于表单中的测试按钮）
const testSmbConnection = async () => {
  console.log('[前端] 开始SMB连接测试')
  console.log('[前端] SMB配置:', backendFormData.config)

  const config = backendFormData.config as any
  if (!config.host || !config.share) {
    message.error('请填写主机地址和共享名')
    return
  }

  backendFormData.testing = true
  backendFormData.testResult = null

  try {
    console.log('[前端] 开始SMB连接测试')
    console.log('[前端] 配置参数:', {
      host: backendFormData.config.host,
      share: backendFormData.config.share,
      username: backendFormData.config.username ? '***' : undefined,
      domain: backendFormData.config.domain,
      port: backendFormData.config.port
    })

    console.log('[前端] 调用IPC: test-smb-connection')
    const result = await (window as any).electron.ipcRenderer.invoke('test-smb-connection', backendFormData.config)
    console.log('[前端] IPC返回结果:', JSON.stringify(result, null, 2))

    if (result && result.success) {
      backendFormData.testResult = {
        success: true,
        message: result.message || 'SMB连接测试成功'
      }
      message.success(result.message || 'SMB连接测试成功')

      if (result.connectionInfo) {
        console.log('[前端] SMB连接信息:', result.connectionInfo)
        notification.success({
          message: 'SMB连接成功',
          description: `已成功连接到 ${result.connectionInfo.host}:${result.connectionInfo.port}/${result.connectionInfo.share}`,
          duration: 3
        })
      }
    } else {
      const errorMessage = result?.error || '未知错误'
      backendFormData.testResult = {
        success: false,
        message: `连接失败: ${errorMessage}`
      }
      message.error(`SMB连接测试失败: ${errorMessage}`)
      console.error('[前端] SMB连接测试失败:', errorMessage)
    }
  } catch (error) {
    console.error('[前端] SMB连接测试异常:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'

    backendFormData.testResult = {
      success: false,
      message: `连接异常: ${errorMessage}`
    }

    // 显示详细的错误信息
    if (errorMessage.includes('could not be cloned')) {
      message.error('SMB连接测试失败: 数据序列化错误，请检查配置参数')
      notification.error({
        message: 'SMB连接错误',
        description: '检测到数据序列化问题，请确保所有配置参数格式正确',
        duration: 5
      })
    } else {
      message.error(`SMB连接测试失败: ${errorMessage}`)
    }
  } finally {
    backendFormData.testing = false
  }
}

const syncBackend = async (backend: any) => {
  backend.syncing = true
  try {
    if (backend.type === 'postgresql') {
      // 调用主进程的PostgreSQL同步功能
      const result = await (window as any).electron.ipcRenderer.invoke('sync-postgresql-data', backend.config)

      if (result.success) {
        const syncMessage = result.syncedCount ? `${backend.name} 同步完成，共同步 ${result.syncedCount} 个资产` : `${backend.name} 同步完成`

        message.success(syncMessage)
        console.log('同步结果:', result)

        // 显示详细的同步通知
        if (result.syncedCount > 0) {
          notification.success({
            message: '数据同步成功',
            description: `已将企业资产管理中的 ${result.syncedCount} 个设备信息同步到 ${backend.name} 数据库`,
            duration: 5
          })
        }

        // 更新后端状态
        backend.health_status = 'healthy'
        backend.last_health_check = new Date()
      } else {
        message.error(`${backend.name} 同步失败: ${result.error}`)
        backend.health_status = 'unhealthy'
      }
    } else if (backend.type === 'smb') {
      // 调用主进程的SMB同步功能
      console.log('[前端] 开始SMB数据同步:', {
        name: backend.name,
        host: backend.config.host,
        share: backend.config.share
      })

      const result = await (window as any).electron.ipcRenderer.invoke('sync-smb-data', backend.config)
      console.log('[前端] SMB同步结果:', JSON.stringify(result, null, 2))

      if (result && result.success) {
        const syncMessage = result.syncedCount ? `${backend.name} 同步完成，共同步 ${result.syncedCount} 个资产` : `${backend.name} 同步完成`

        message.success(syncMessage)
        console.log('[前端] SMB同步成功:', result)

        // 显示详细的同步通知
        if (result.syncedCount > 0) {
          notification.success({
            message: 'SMB同步成功',
            description: `已将企业资产管理中的 ${result.syncedCount} 个设备信息同步到 ${backend.name} SMB共享 (${backend.config.host}/${backend.config.share})`,
            duration: 5
          })
        } else {
          notification.info({
            message: 'SMB同步完成',
            description: `${backend.name} 同步完成，但没有找到可同步的资产数据`,
            duration: 3
          })
        }

        // 更新后端状态
        backend.health_status = 'healthy'
        backend.last_health_check = new Date()
      } else {
        const errorMessage = result?.error || '未知同步错误'
        console.error('[前端] SMB同步失败:', errorMessage)
        message.error(`${backend.name} 同步失败: ${errorMessage}`)

        // 显示详细的错误通知
        notification.error({
          message: 'SMB同步失败',
          description: `${backend.name} (${backend.config.host}/${backend.config.share}) 同步失败: ${errorMessage}`,
          duration: 5
        })

        backend.health_status = 'unhealthy'
      }
    } else {
      // 其他类型的后端使用模拟同步
      await new Promise((resolve) => setTimeout(resolve, 3000))
      message.success(`${backend.name} 同步完成`)
      backend.health_status = 'healthy'
      backend.last_health_check = new Date()
    }
  } catch (error) {
    message.error(`${backend.name} 同步失败: ${error instanceof Error ? error.message : 'Unknown error'}`)
    backend.health_status = 'unhealthy'
  } finally {
    backend.syncing = false
  }
}

const testAllBackends = async () => {
  testingAll.value = true
  try {
    const promises = enabledBackends.value.map((backend) => testBackend(backend))
    await Promise.all(promises)
    message.success('所有后端连接测试完成')
  } finally {
    testingAll.value = false
  }
}

const syncAllBackends = async () => {
  syncingAll.value = true
  try {
    const promises = enabledBackends.value.map((backend) => syncBackend(backend))
    await Promise.all(promises)
    message.success('所有后端同步完成')
  } finally {
    syncingAll.value = false
  }
}

const handleTypeChange = () => {
  // 根据类型初始化配置对象
  if (backendFormData.type === 'database') {
    backendFormData.config = {
      database_type: 'postgresql',
      host: '',
      port: 5432,
      database: '',
      username: '',
      password: '',
      ssl_mode: 'disable',
      table_name: 'host_configurations',
      initialize_database: false
    }
  } else if (backendFormData.type === 'smb') {
    backendFormData.config = {
      host: '',
      share: '',
      path: '',
      username: '',
      password: '',
      domain: '',
      port: 445
    }
  } else if (backendFormData.type === 'git') {
    backendFormData.config = {
      repository: '',
      branch: 'main',
      file_path: '',
      auth_type: 'token',
      token: '',
      username: '',
      password: '',
      ssh_key_path: '',
      ssh_passphrase: ''
    }
  } else if (backendFormData.type === 'consul') {
    backendFormData.config = {
      host: '',
      port: 8500,
      key_prefix: '',
      token: '',
      datacenter: '',
      scheme: 'http'
    }
  } else if (backendFormData.type === 's3') {
    backendFormData.config = {
      bucket: '',
      region: '',
      endpoint: '',
      access_key: '',
      secret_key: ''
    }
  } else {
    backendFormData.config = {}
  }
}

const handleGitAuthTypeChange = () => {
  // 根据认证类型清理不相关的字段
  const config = backendFormData.config as any
  if (config.auth_type === 'token') {
    config.username = ''
    config.password = ''
    config.ssh_key_path = ''
    config.ssh_passphrase = ''
  } else if (config.auth_type === 'username_password') {
    config.token = ''
    config.ssh_key_path = ''
    config.ssh_passphrase = ''
  } else if (config.auth_type === 'ssh_key') {
    config.token = ''
    config.username = ''
    config.password = ''
  }
}

const resetBackendForm = () => {
  Object.assign(backendFormData, {
    name: '',
    type: '',
    priority: 1,
    enabled: true,
    config: {}
  })
}

const handleSaveBackend = () => {
  // 验证必填字段
  if (!backendFormData.name || !backendFormData.type) {
    message.error('请填写完整的后端配置信息')
    return
  }

  // 确保配置对象不为空
  if (Object.keys(backendFormData.config).length === 0) {
    handleTypeChange() // 初始化配置对象
  }

  if (editingBackend.value) {
    // 更新现有后端
    Object.assign(editingBackend.value, backendFormData)
    message.success('后端配置更新成功')
  } else {
    // 添加新后端
    const newBackend = {
      ...backendFormData,
      id: Date.now().toString(),
      health_status: 'unknown',
      last_health_check: null,
      testing: false,
      syncing: false
    }
    backends.value.push(newBackend)
    message.success('后端配置添加成功')
  }
  showBackendModal.value = false
}

const handleCancelBackend = () => {
  showBackendModal.value = false
  resetBackendForm()
}

const saveSyncStrategy = () => {
  // 保存同步策略配置
  message.success('同步策略配置保存成功')
}

// 故障转移相关方法
const saveFailoverConfig = () => {
  message.success('故障转移配置保存成功')
}

const testFailover = () => {
  message.info('故障转移测试功能开发中')
}

// 同步状态监控相关方法
const refreshSyncStatus = () => {
  backends.value.forEach((backend) => {
    backend.syncProgress = Math.floor(Math.random() * 100)
    backend.lastSync = new Date()
    backend.syncDuration = Math.floor(Math.random() * 1000) + 100
    backend.dataSize = `${Math.floor(Math.random() * 100)}MB`
  })
  message.success('同步状态已刷新')
}

const forceSyncAll = () => {
  message.info('强制同步所有后端')
  backends.value.forEach((backend) => {
    if (backend.enabled) {
      syncBackendWithProgress(backend)
    }
  })
}

const syncBackendWithProgress = (backend: any) => {
  backend.syncing = true
  backend.syncProgress = 0

  const interval = setInterval(() => {
    backend.syncProgress += Math.floor(Math.random() * 20) + 5
    if (backend.syncProgress >= 100) {
      backend.syncProgress = 100
      backend.syncing = false
      backend.lastSync = new Date()
      clearInterval(interval)
      message.success(`${backend.name} 同步完成`)
    }
  }, 200)
}

const getBackendStatusColor = (status: string) => {
  const colors = {
    healthy: 'green',
    unhealthy: 'red',
    syncing: 'blue',
    unknown: 'default'
  }
  return colors[status] || 'default'
}

const getBackendStatusText = (status: string) => {
  const texts = {
    healthy: '健康',
    unhealthy: '异常',
    syncing: '同步中',
    unknown: '未知'
  }
  return texts[status] || status
}

// 高级设置相关方法
const saveAdvancedSettings = () => {
  message.success('高级设置保存成功')
}

const resetAdvancedSettings = () => {
  Object.assign(advancedSettings, {
    maxConcurrentConnections: 10,
    connectionTimeout: 30,
    batchSize: 100,
    enableEncryption: true,
    enableCompression: false,
    sslCertPath: '',
    logLevel: 'info',
    logRetentionDays: 30,
    maxLogFileSize: 100
  })
  message.info('高级设置已重置为默认值')
}

const scrollToSyncStrategy = () => {
  const element = document.getElementById('sync-strategy-section')
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.sync-configuration {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.back-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #6b7280;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  white-space: nowrap;
}

.back-button:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.back-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.config-overview {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.backend-list {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.backend-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  min-height: 200px;
}

/* 自定义滚动条样式 */
.backend-cards::-webkit-scrollbar {
  width: 6px;
}

.backend-cards::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.backend-cards::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.backend-cards::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sync-configuration {
    padding: 16px;
  }

  .backend-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .sync-configuration {
    padding: 12px;
  }

  .backend-cards {
    gap: 8px;
  }
}

.backend-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s;
}

.backend-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.backend-card.disabled {
  opacity: 0.6;
  background: #f9f9f9;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.backend-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.backend-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 8px;
  margin-right: 12px;
  color: #1890ff;
}

.backend-details {
  flex: 1;
}

.backend-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.backend-type {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.backend-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.card-content {
  margin-bottom: 16px;
}

.backend-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-label {
  color: #6b7280;
  font-size: 12px;
}

.meta-value {
  color: #1f2937;
  font-size: 12px;
}

.backend-config {
  background: #f9fafb;
  border-radius: 6px;
  padding: 12px;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.sync-strategy {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-top: 24px;
}

.config-form {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.config-form h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}
</style>
