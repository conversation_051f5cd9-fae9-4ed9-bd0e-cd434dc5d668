/**
 * 企业资源同步系统 - 企业配置后端管理器
 * 功能：管理企业级存储后端配置，支持多种后端类型的统一管理
 * 依赖：企业类型定义、存储适配器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import {
  EnterpriseConfigBackend,
  EnterpriseStorageType,
  EnterpriseStorageConfig,
  DatabaseConfig,
  ConsulConfig,
  IEnterpriseConfigManager,
  HealthCheckResult,
  ConfigValidationResult,
  SyncLog
} from '../types/EnterpriseTypes'
import { StorageAdapterFactory } from '../../multi_sync/factory/StorageAdapterFactory'
import { IStorageAdapter } from '../../multi_sync/types/StorageTypes'

/**
 * 企业配置后端管理器
 * 负责管理企业级存储后端的配置、连接和健康检查
 */
export class EnterpriseConfigManager extends EventEmitter implements IEnterpriseConfigManager {
  private static instance: EnterpriseConfigManager | null = null
  private backends: Map<string, EnterpriseConfigBackend> = new Map()
  private adapters: Map<string, IStorageAdapter> = new Map()
  private healthCheckTimers: Map<string, NodeJS.Timeout> = new Map()
  private adapterFactory: StorageAdapterFactory

  /**
   * 获取管理器单例实例
   * @returns 管理器实例
   */
  static getInstance(): EnterpriseConfigManager {
    if (!EnterpriseConfigManager.instance) {
      EnterpriseConfigManager.instance = new EnterpriseConfigManager()
    }
    return EnterpriseConfigManager.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()
    this.adapterFactory = StorageAdapterFactory.getInstance()
    this.loadBackendsFromStorage()
  }

  /**
   * 添加配置后端
   * @param config 后端配置
   * @returns 配置ID
   */
  async addBackend(config: Omit<EnterpriseConfigBackend, 'id' | 'created_at' | 'updated_at'>): Promise<string> {
    // 验证配置
    const validation = this.validateBackendConfig(config.type, config.config)
    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
    }

    // 检查名称是否重复
    const existingBackend = Array.from(this.backends.values()).find((b) => b.name === config.name)
    if (existingBackend) {
      throw new Error(`后端名称已存在: ${config.name}`)
    }

    const backendId = this.generateBackendId()
    const now = new Date()

    const backend: EnterpriseConfigBackend = {
      ...config,
      id: backendId,
      health_status: 'unknown',
      created_at: now,
      updated_at: now
    }

    // 保存配置
    this.backends.set(backendId, backend)
    await this.saveBackendsToStorage()

    // 如果启用，立即进行健康检查
    if (backend.enabled) {
      this.startHealthCheck(backendId)
      // 异步进行初始健康检查
      this.testBackendConnection(backendId).catch((error) => {
        console.warn(`初始健康检查失败 [${backend.name}]:`, error)
      })
    }

    // 触发事件
    this.emit('backendAdded', backend)

    return backendId
  }

  /**
   * 更新配置后端
   * @param id 配置ID
   * @param updates 更新内容
   * @returns 是否成功
   */
  async updateBackend(id: string, updates: Partial<EnterpriseConfigBackend>): Promise<boolean> {
    const existingBackend = this.backends.get(id)
    if (!existingBackend) {
      throw new Error(`配置不存在: ${id}`)
    }

    // 如果更新了配置内容，需要验证
    if (updates.config) {
      const validation = this.validateBackendConfig(existingBackend.type, updates.config)
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
      }
    }

    // 检查名称冲突（如果更新了名称）
    if (updates.name && updates.name !== existingBackend.name) {
      const nameConflict = Array.from(this.backends.values()).find((b) => b.id !== id && b.name === updates.name)
      if (nameConflict) {
        throw new Error(`后端名称已存在: ${updates.name}`)
      }
    }

    // 更新配置
    const updatedBackend: EnterpriseConfigBackend = {
      ...existingBackend,
      ...updates,
      id, // 确保ID不被更改
      created_at: existingBackend.created_at, // 确保创建时间不被更改
      updated_at: new Date()
    }

    this.backends.set(id, updatedBackend)
    await this.saveBackendsToStorage()

    // 处理启用状态变化
    if (updates.enabled !== undefined) {
      if (updates.enabled && !existingBackend.enabled) {
        // 启用后端
        this.startHealthCheck(id)
      } else if (!updates.enabled && existingBackend.enabled) {
        // 禁用后端
        this.stopHealthCheck(id)
        await this.disconnectAdapter(id)
      }
    }

    // 如果更新了配置内容且后端已连接，需要重新连接
    if (updates.config && this.adapters.has(id)) {
      await this.disconnectAdapter(id)
      if (updatedBackend.enabled) {
        this.testBackendConnection(id).catch((error) => {
          console.warn(`重新连接失败 [${updatedBackend.name}]:`, error)
        })
      }
    }

    // 触发事件
    this.emit('backendUpdated', updatedBackend, existingBackend)

    return true
  }

  /**
   * 删除配置后端
   * @param id 配置ID
   * @returns 是否成功
   */
  async removeBackend(id: string): Promise<boolean> {
    const backend = this.backends.get(id)
    if (!backend) {
      return false
    }

    // 停止健康检查
    this.stopHealthCheck(id)

    // 断开连接
    await this.disconnectAdapter(id)

    // 删除配置
    this.backends.delete(id)
    await this.saveBackendsToStorage()

    // 触发事件
    this.emit('backendRemoved', backend)

    return true
  }

  /**
   * 获取所有配置后端
   * @returns 配置后端列表
   */
  async getAllBackends(): Promise<EnterpriseConfigBackend[]> {
    return Array.from(this.backends.values())
  }

  /**
   * 获取启用的配置后端（按优先级排序）
   * @returns 启用的配置后端列表
   */
  async getEnabledBackends(): Promise<EnterpriseConfigBackend[]> {
    return Array.from(this.backends.values())
      .filter((backend) => backend.enabled)
      .sort((a, b) => a.priority - b.priority)
  }

  /**
   * 测试后端连接
   * @param id 配置ID
   * @returns 健康检查结果
   */
  async testBackendConnection(id: string): Promise<HealthCheckResult> {
    const backend = this.backends.get(id)
    if (!backend) {
      throw new Error(`配置不存在: ${id}`)
    }

    const startTime = Date.now()
    const result: HealthCheckResult = {
      backend_id: id,
      healthy: false,
      checked_at: new Date()
    }

    try {
      // 获取或创建适配器
      let adapter = this.adapters.get(id)
      if (!adapter) {
        adapter = this.createAdapter(backend)
        if (!adapter) {
          throw new Error(`不支持的后端类型: ${backend.type}`)
        }
      }

      // 测试连接
      const connected = await adapter.testConnection()
      result.healthy = connected
      result.latency_ms = Date.now() - startTime

      if (connected) {
        this.adapters.set(id, adapter)
      }

      // 更新后端健康状态
      backend.health_status = connected ? 'healthy' : 'unhealthy'
      backend.last_health_check = result.checked_at
      await this.saveBackendsToStorage()
    } catch (error: any) {
      result.healthy = false
      result.error_message = error.message
      result.latency_ms = Date.now() - startTime

      // 更新后端健康状态
      backend.health_status = 'unhealthy'
      backend.last_health_check = result.checked_at
      await this.saveBackendsToStorage()
    }

    // 记录同步日志
    await this.logSyncOperation(id, 'health_check', result.healthy ? 'success' : 'failed', {
      latency_ms: result.latency_ms,
      error_message: result.error_message
    })

    // 触发事件
    this.emit('healthCheckCompleted', result)

    return result
  }

  /**
   * 验证后端配置
   * @param type 后端类型
   * @param config 配置内容
   * @returns 验证结果
   */
  validateBackendConfig(type: EnterpriseStorageType, config: EnterpriseStorageConfig): ConfigValidationResult {
    const result: ConfigValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    }

    try {
      switch (type) {
        case EnterpriseStorageType.DATABASE:
          this.validateDatabaseConfig(config as DatabaseConfig, result)
          break
        case EnterpriseStorageType.CONSUL:
          this.validateConsulConfig(config as ConsulConfig, result)
          break
        case EnterpriseStorageType.SMB:
        case EnterpriseStorageType.SFTP:
        case EnterpriseStorageType.GITHUB:
        case EnterpriseStorageType.MINIO:
        case EnterpriseStorageType.ONEDRIVE:
          // 使用现有的适配器工厂验证
          const validation = this.adapterFactory.validateConfig(type as any, config)
          result.valid = validation.valid
          result.errors = validation.errors
          break
        default:
          result.valid = false
          result.errors.push(`不支持的后端类型: ${type}`)
      }
    } catch (error: any) {
      result.valid = false
      result.errors.push(`配置验证异常: ${error.message}`)
    }

    return result
  }

  /**
   * 验证数据库配置
   * @param config 数据库配置
   * @param result 验证结果
   */
  private validateDatabaseConfig(config: DatabaseConfig, result: ConfigValidationResult): void {
    if (!config.database_type) {
      result.errors.push('数据库类型不能为空')
    } else if (!['postgresql', 'mysql'].includes(config.database_type)) {
      result.errors.push('数据库类型必须是 postgresql 或 mysql')
    }

    if (!config.connection_string) {
      result.errors.push('连接字符串不能为空')
    } else {
      // 简单的连接字符串格式验证
      if (!config.connection_string.includes('host=') || !config.connection_string.includes('dbname=')) {
        result.errors.push('连接字符串格式不正确，必须包含 host 和 dbname')
      }
    }

    if (!config.table_name) {
      result.errors.push('表名不能为空')
    }

    if (config.ssl_mode && !['require', 'prefer', 'disable'].includes(config.ssl_mode)) {
      result.errors.push('SSL模式必须是 require、prefer 或 disable')
    }

    if (config.pool_size && (config.pool_size < 1 || config.pool_size > 100)) {
      result.warnings?.push('连接池大小建议在 1-100 之间')
    }

    result.valid = result.errors.length === 0
  }

  /**
   * 验证Consul配置
   * @param config Consul配置
   * @param result 验证结果
   */
  private validateConsulConfig(config: ConsulConfig, result: ConfigValidationResult): void {
    if (!config.host) {
      result.errors.push('Consul主机地址不能为空')
    }

    if (config.port && (config.port < 1 || config.port > 65535)) {
      result.errors.push('端口号必须在 1-65535 之间')
    }

    if (!config.key_prefix) {
      result.errors.push('键前缀不能为空')
    }

    if (config.scheme && !['http', 'https'].includes(config.scheme)) {
      result.errors.push('协议必须是 http 或 https')
    }

    result.valid = result.errors.length === 0
  }

  /**
   * 创建存储适配器
   * @param backend 后端配置
   * @returns 存储适配器
   */
  private createAdapter(backend: EnterpriseConfigBackend): IStorageAdapter | null {
    try {
      // 对于企业特有的类型，需要创建专门的适配器
      if (backend.type === EnterpriseStorageType.DATABASE || backend.type === EnterpriseStorageType.CONSUL) {
        // TODO: 实现企业专用适配器
        console.warn(`企业适配器 ${backend.type} 尚未实现`)
        return null
      }

      // 使用现有的适配器工厂
      return this.adapterFactory.createAdapter(backend.type as any)
    } catch (error) {
      console.error(`创建适配器失败 [${backend.name}]:`, error)
      return null
    }
  }

  /**
   * 断开适配器连接
   * @param id 配置ID
   */
  private async disconnectAdapter(id: string): Promise<void> {
    const adapter = this.adapters.get(id)
    if (adapter) {
      try {
        await adapter.disconnect()
      } catch (error) {
        console.warn(`断开适配器连接失败 [${id}]:`, error)
      }
      this.adapters.delete(id)
    }
  }

  /**
   * 开始健康检查
   * @param id 配置ID
   */
  private startHealthCheck(id: string): void {
    this.stopHealthCheck(id) // 先停止现有的定时器

    const timer = setInterval(async () => {
      try {
        await this.testBackendConnection(id)
      } catch (error) {
        console.warn(`定时健康检查失败 [${id}]:`, error)
      }
    }, 60000) // 每分钟检查一次

    this.healthCheckTimers.set(id, timer)
  }

  /**
   * 停止健康检查
   * @param id 配置ID
   */
  private stopHealthCheck(id: string): void {
    const timer = this.healthCheckTimers.get(id)
    if (timer) {
      clearInterval(timer)
      this.healthCheckTimers.delete(id)
    }
  }

  /**
   * 生成后端ID
   * @returns 唯一ID
   */
  private generateBackendId(): string {
    return `backend_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 从存储加载后端配置
   */
  private async loadBackendsFromStorage(): Promise<void> {
    try {
      // TODO: 从持久化存储加载配置
      // 这里可以从文件、数据库或其他存储中加载
      console.log('从存储加载企业后端配置...')
    } catch (error) {
      console.warn('加载企业后端配置失败:', error)
    }
  }

  /**
   * 保存后端配置到存储
   */
  private async saveBackendsToStorage(): Promise<void> {
    try {
      // TODO: 保存配置到持久化存储
      console.log('保存企业后端配置到存储...')
    } catch (error) {
      console.warn('保存企业后端配置失败:', error)
    }
  }

  /**
   * 记录同步操作日志
   * @param backendId 后端ID
   * @param operationType 操作类型
   * @param status 状态
   * @param resultData 结果数据
   */
  private async logSyncOperation(
    backendId: string,
    operationType: 'sync' | 'health_check' | 'failover',
    status: 'success' | 'failed' | 'partial',
    resultData?: any
  ): Promise<void> {
    const log: SyncLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      backend_id: backendId,
      operation_type: operationType,
      status,
      result_data: resultData,
      created_at: new Date()
    }

    // TODO: 保存日志到持久化存储
    console.log('同步日志:', log)
  }

  /**
   * 销毁管理器，清理资源
   */
  async destroy(): Promise<void> {
    // 停止所有健康检查
    for (const id of this.healthCheckTimers.keys()) {
      this.stopHealthCheck(id)
    }

    // 断开所有适配器连接
    for (const id of this.adapters.keys()) {
      await this.disconnectAdapter(id)
    }

    // 清理数据
    this.backends.clear()
    this.adapters.clear()
    this.healthCheckTimers.clear()

    // 移除所有事件监听器
    this.removeAllListeners()
  }
}
