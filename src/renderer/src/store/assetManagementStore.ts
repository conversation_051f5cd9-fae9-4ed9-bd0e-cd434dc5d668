import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 固定资产类型定义
export interface Asset {
  id: number
  assetNumber: string
  name: string
  category: 'computer' | 'network' | 'server' | 'storage' | 'printer'
  model: string
  status: 'active' | 'maintenance' | 'idle' | 'retired'
  assignedTo: string
  purchaseDate: string
  purchasePrice: number
  description: string
  // 硬件配置字段
  cpu?: string
  memory?: string
  storage?: string
  networkInterface?: string
  operatingSystem?: string
  ipAddress?: string
  macAddress?: string
  portCount?: string
  // 元数据
  createdAt?: string
  updatedAt?: string
}

// 分类标签映射
export const categoryLabels = {
  computer: '计算机设备',
  network: '网络设备',
  server: '服务器设备',
  storage: '存储设备',
  printer: '打印设备'
}

// 状态标签映射
export const statusLabels = {
  active: '在用',
  maintenance: '维护中',
  idle: '闲置',
  retired: '已报废'
}

export const useAssetManagementStore = defineStore('assetManagement', () => {
  // 状态
  const assets = ref<Asset[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const lastSyncTime = ref<string | null>(null)

  // 初始化默认数据
  const initializeDefaultAssets = () => {
    const defaultAssets: Asset[] = [
      {
        id: 1,
        assetNumber: 'IT-001',
        name: 'Dell OptiPlex 7090',
        category: 'computer',
        model: 'OptiPlex 7090',
        status: 'active',
        assignedTo: '张三',
        purchaseDate: '2023-01-15',
        purchasePrice: 5999.0,
        description: '办公用台式机',
        cpu: 'Intel i7-11700',
        memory: '16GB DDR4',
        storage: '512GB SSD',
        operatingSystem: 'Windows 11 Pro',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 2,
        assetNumber: 'NET-001',
        name: 'Cisco 交换机',
        category: 'network',
        model: 'Catalyst 2960',
        status: 'active',
        assignedTo: '',
        purchaseDate: '2023-02-20',
        purchasePrice: 3500.0,
        description: '24口千兆交换机',
        portCount: '24',
        networkInterface: '24x 1Gbps',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 3,
        assetNumber: 'SRV-001',
        name: 'HP ProLiant 服务器',
        category: 'server',
        model: 'DL380 Gen10',
        status: 'maintenance',
        assignedTo: '',
        purchaseDate: '2022-12-10',
        purchasePrice: 25000.0,
        description: '数据库服务器',
        cpu: 'Intel Xeon Silver 4214',
        memory: '64GB DDR4',
        storage: '2TB SSD RAID1',
        operatingSystem: 'Ubuntu Server 22.04',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]
    
    if (assets.value.length === 0) {
      assets.value = defaultAssets
      saveToStorage()
    }
  }

  // 计算属性
  const totalAssets = computed(() => assets.value.length)
  
  const assetsByStatus = computed(() => {
    const grouped: Record<string, Asset[]> = {
      active: [],
      maintenance: [],
      idle: [],
      retired: []
    }
    
    assets.value.forEach(asset => {
      if (grouped[asset.status]) {
        grouped[asset.status].push(asset)
      }
    })
    
    return grouped
  })

  const assetsByCategory = computed(() => {
    const grouped: Record<string, Asset[]> = {
      computer: [],
      network: [],
      server: [],
      storage: [],
      printer: []
    }
    
    assets.value.forEach(asset => {
      if (grouped[asset.category]) {
        grouped[asset.category].push(asset)
      }
    })
    
    return grouped
  })

  const totalValue = computed(() => {
    return assets.value.reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0)
  })

  const activeValue = computed(() => {
    return assets.value
      .filter(asset => asset.status === 'active')
      .reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0)
  })

  // 存储相关方法
  const STORAGE_KEY = 'chaterm_asset_management'

  const saveToStorage = () => {
    try {
      const dataToSave = {
        assets: assets.value,
        lastSyncTime: new Date().toISOString(),
        version: '1.0'
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave))
      lastSyncTime.value = dataToSave.lastSyncTime
      console.log('资产数据已保存到本地存储')
    } catch (err) {
      console.error('保存资产数据失败:', err)
      error.value = '保存数据失败'
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        if (data.assets && Array.isArray(data.assets)) {
          assets.value = data.assets
          lastSyncTime.value = data.lastSyncTime
          console.log('从本地存储加载资产数据成功')
          return true
        }
      }
    } catch (err) {
      console.error('加载资产数据失败:', err)
      error.value = '加载数据失败'
    }
    return false
  }

  // 资产操作方法
  const addAsset = (assetData: Omit<Asset, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newAsset: Asset = {
      ...assetData,
      id: Date.now(), // 简单的ID生成策略
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    assets.value.push(newAsset)
    saveToStorage()
    return newAsset
  }

  const updateAsset = (id: number, assetData: Partial<Asset>) => {
    const index = assets.value.findIndex(asset => asset.id === id)
    if (index !== -1) {
      assets.value[index] = {
        ...assets.value[index],
        ...assetData,
        updatedAt: new Date().toISOString()
      }
      saveToStorage()
      return assets.value[index]
    }
    return null
  }

  const deleteAsset = (id: number) => {
    const index = assets.value.findIndex(asset => asset.id === id)
    if (index !== -1) {
      const deletedAsset = assets.value.splice(index, 1)[0]
      saveToStorage()
      return deletedAsset
    }
    return null
  }

  const batchDeleteAssets = (ids: number[]) => {
    const deletedAssets = assets.value.filter(asset => ids.includes(asset.id))
    assets.value = assets.value.filter(asset => !ids.includes(asset.id))
    saveToStorage()
    return deletedAssets
  }

  const getAssetById = (id: number) => {
    return assets.value.find(asset => asset.id === id) || null
  }

  const searchAssets = (query: string) => {
    if (!query.trim()) return assets.value
    
    const searchTerm = query.toLowerCase()
    return assets.value.filter(asset => 
      asset.name.toLowerCase().includes(searchTerm) ||
      asset.assetNumber.toLowerCase().includes(searchTerm) ||
      asset.model.toLowerCase().includes(searchTerm) ||
      asset.assignedTo.toLowerCase().includes(searchTerm)
    )
  }

  const filterAssets = (filters: {
    category?: string
    status?: string
    assignedTo?: string
  }) => {
    return assets.value.filter(asset => {
      if (filters.category && asset.category !== filters.category) return false
      if (filters.status && asset.status !== filters.status) return false
      if (filters.assignedTo && asset.assignedTo !== filters.assignedTo) return false
      return true
    })
  }

  // 初始化方法
  const initialize = () => {
    loading.value = true
    error.value = null
    
    try {
      const loaded = loadFromStorage()
      if (!loaded) {
        initializeDefaultAssets()
      }
    } catch (err) {
      console.error('初始化资产管理失败:', err)
      error.value = '初始化失败'
      initializeDefaultAssets()
    } finally {
      loading.value = false
    }
  }

  // 清空数据方法（用于测试或重置）
  const clearAllAssets = () => {
    assets.value = []
    saveToStorage()
  }

  return {
    // 状态
    assets,
    loading,
    error,
    lastSyncTime,
    
    // 计算属性
    totalAssets,
    assetsByStatus,
    assetsByCategory,
    totalValue,
    activeValue,
    
    // 方法
    initialize,
    addAsset,
    updateAsset,
    deleteAsset,
    batchDeleteAssets,
    getAssetById,
    searchAssets,
    filterAssets,
    saveToStorage,
    loadFromStorage,
    clearAllAssets
  }
}, {
  persist: {
    key: 'chaterm_asset_management_store',
    storage: localStorage,
    paths: ['assets', 'lastSyncTime']
  }
})
