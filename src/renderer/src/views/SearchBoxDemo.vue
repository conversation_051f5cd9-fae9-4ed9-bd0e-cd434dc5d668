<template>
  <div class="search-box-demo">
    <div class="demo-header">
      <h1>统一搜索框组件系统演示</h1>
      <p>展示项目中统一的搜索框组件及其各种变体</p>
    </div>

    <div class="demo-sections">
      <!-- 基础搜索框 -->
      <section class="demo-section">
        <h2>基础搜索框 (SearchBox)</h2>
        <p>最基本的搜索框组件，支持输入、清除、建议等功能</p>

        <div class="demo-examples">
          <div class="example">
            <h3>默认尺寸</h3>
            <SearchBox
              v-model="basicSearch"
              placeholder="搜索..."
              clearable
              @search="handleBasicSearch"
              @clear="handleClear"
            />
          </div>

          <div class="example">
            <h3>不同尺寸</h3>
            <div class="size-examples">
              <SearchBox
                v-model="sizeSearch"
                size="small"
                placeholder="小尺寸搜索框"
                clearable
              />
              <SearchBox
                v-model="sizeSearch"
                size="default"
                placeholder="默认尺寸搜索框"
                clearable
              />
              <SearchBox
                v-model="sizeSearch"
                size="large"
                placeholder="大尺寸搜索框"
                clearable
              />
            </div>
          </div>

          <div class="example">
            <h3>带建议的搜索框</h3>
            <SearchBox
              v-model="suggestionSearch"
              placeholder="输入以查看建议..."
              clearable
              :suggestions="suggestions"
              :show-suggestions="true"
              @search="handleSuggestionSearch"
              @suggestion-select="handleSuggestionSelect"
            />
          </div>
        </div>
      </section>

      <!-- 资产搜索框 -->
      <section class="demo-section">
        <h2>资产搜索框 (AssetSearchBox)</h2>
        <p>专门用于资产管理的搜索框，集成了操作按钮和资产建议</p>

        <div class="demo-examples">
          <div class="example">
            <AssetSearchBox
              v-model="assetSearch"
              placeholder="搜索资产..."
              :assets="mockAssets"
              :show-suggestions="true"
              @search="handleAssetSearch"
              @suggestion-select="handleAssetSelect"
              @new-asset="handleNewAsset"
              @import-assets="handleImportAssets"
              @export-assets="handleExportAssets"
            />
          </div>
        </div>
      </section>

      <!-- 终端搜索框 -->
      <section class="demo-section">
        <h2>终端搜索框 (TerminalSearchBox)</h2>
        <p>用于终端内容搜索的浮动搜索框，支持高级搜索选项</p>

        <div class="demo-examples">
          <div class="example">
            <div class="terminal-demo">
              <div class="terminal-content">
                <p>模拟终端内容...</p>
                <p>$ ls -la</p>
                <p>total 24</p>
                <p>drwxr-xr-x 5 <USER> <GROUP> 160 Dec 1 10:00 .</p>
                <p>drwxr-xr-x 3 <USER> <GROUP> 96 Dec 1 09:30 ..</p>
                <p>-rw-r--r-- 1 <USER> <GROUP> 1024 Dec 1 10:00 file.txt</p>
              </div>

              <TerminalSearchBox
                :visible="showTerminalSearch"
                :floating="true"
                placeholder="在终端中搜索..."
                :show-options="true"
                @close="closeTerminalSearch"
                @search="handleTerminalSearch"
              />

              <button
                class="show-search-btn"
                @click="showTerminalSearch = true"
              >
                显示终端搜索框 (Ctrl+F)
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 命令历史搜索框 -->
      <section class="demo-section">
        <h2>命令历史搜索框 (CommandHistorySearchBox)</h2>
        <p>用于搜索命令历史的专用搜索框，支持过滤和操作</p>

        <div class="demo-examples">
          <div class="example">
            <CommandHistorySearchBox
              v-model="commandSearch"
              placeholder="搜索命令、服务器或描述..."
              :commands="mockCommands"
              :show-suggestions="true"
              :show-filters="true"
              @search="handleCommandSearch"
              @suggestion-select="handleCommandSelect"
              @copy-command="handleCopyCommand"
              @execute-command="handleExecuteCommand"
            />
          </div>
        </div>
      </section>

      <!-- 主题演示 -->
      <section class="demo-section">
        <h2>主题适配演示</h2>
        <p>搜索框在不同主题下的表现</p>

        <div class="demo-examples">
          <div class="theme-controls">
            <button
              :class="['theme-btn', { active: currentTheme === 'light' }]"
              @click="setTheme('light')"
            >
              亮色主题
            </button>
            <button
              :class="['theme-btn', { active: currentTheme === 'dark' }]"
              @click="setTheme('dark')"
            >
              暗色主题
            </button>
          </div>

          <div class="theme-demo">
            <SearchBox
              v-model="themeSearch"
              placeholder="主题适配演示..."
              clearable
              :suggestions="themeSuggestions"
              :show-suggestions="true"
            />
          </div>
        </div>
      </section>
    </div>

    <!-- 使用说明 -->
    <section class="demo-section">
      <h2>使用说明</h2>
      <div class="usage-guide">
        <h3>组件导入</h3>
        <pre><code>import { SearchBox, AssetSearchBox, TerminalSearchBox, CommandHistorySearchBox } from '@/components/common/SearchBox'</code></pre>

        <h3>基本用法</h3>
        <pre><code>&lt;SearchBox v-model="searchValue" placeholder="搜索..." @search="handleSearch" /&gt;</code></pre>

        <h3>主要特性</h3>
        <ul>
          <li>统一的设计语言和交互体验</li>
          <li>支持亮色和暗色主题自动适配</li>
          <li>响应式设计，适配移动端</li>
          <li>丰富的配置选项和事件支持</li>
          <li>内置防抖搜索优化</li>
          <li>无障碍支持</li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchBox from '@/components/common/SearchBox/SearchBox.vue'
import AssetSearchBox from '@/components/common/SearchBox/AssetSearchBox.vue'
import TerminalSearchBox from '@/components/common/SearchBox/TerminalSearchBox.vue'
import CommandHistorySearchBox from '@/components/common/SearchBox/CommandHistorySearchBox.vue'

// 响应式数据
const basicSearch = ref('')
const sizeSearch = ref('')
const suggestionSearch = ref('')
const assetSearch = ref('')
const commandSearch = ref('')
const themeSearch = ref('')
const showTerminalSearch = ref(false)
const currentTheme = ref('light')

// 模拟数据
const suggestions = computed(() => [
  { text: '用户管理', value: 'user-management' },
  { text: '系统设置', value: 'system-settings' },
  { text: '数据分析', value: 'data-analysis' },
  { text: '报表生成', value: 'report-generation' }
])

const themeSuggestions = computed(() => [
  { text: '亮色主题', value: 'light-theme' },
  { text: '暗色主题', value: 'dark-theme' },
  { text: '自动主题', value: 'auto-theme' }
])

const mockAssets = ref([
  {
    id: '1',
    name: 'Web服务器',
    host: '*************',
    port: 22,
    type: 'server',
    status: 'online',
    description: '生产环境Web服务器'
  },
  {
    id: '2',
    name: '数据库服务器',
    host: '*************',
    port: 3306,
    type: 'database',
    status: 'online',
    description: 'MySQL主数据库'
  }
])

const mockCommands = ref([
  {
    id: '1',
    command: 'ls -la',
    server: '*************',
    timestamp: Date.now() - 3600000,
    category: 'file',
    exitCode: 0,
    description: '列出目录详细信息'
  },
  {
    id: '2',
    command: 'docker ps',
    server: '*************',
    timestamp: Date.now() - 7200000,
    category: 'system',
    exitCode: 0,
    description: '查看运行中的容器'
  }
])

// 事件处理
const handleBasicSearch = (value: string) => {
  console.log('基础搜索:', value)
}

const handleClear = () => {
  console.log('清除搜索')
}

const handleSuggestionSearch = (value: string) => {
  console.log('建议搜索:', value)
}

const handleSuggestionSelect = (suggestion: any) => {
  console.log('选择建议:', suggestion)
}

const handleAssetSearch = (value: string) => {
  console.log('资产搜索:', value)
}

const handleAssetSelect = (asset: any) => {
  console.log('选择资产:', asset)
}

const handleNewAsset = () => {
  console.log('新建资产')
}

const handleImportAssets = () => {
  console.log('导入资产')
}

const handleExportAssets = () => {
  console.log('导出资产')
}

const closeTerminalSearch = () => {
  showTerminalSearch.value = false
}

const handleTerminalSearch = (value: string) => {
  console.log('终端搜索:', value)
}

const handleCommandSearch = (value: string) => {
  console.log('命令搜索:', value)
}

const handleCommandSelect = (command: any) => {
  console.log('选择命令:', command)
}

const handleCopyCommand = (command: string) => {
  console.log('复制命令:', command)
}

const handleExecuteCommand = (command: string) => {
  console.log('执行命令:', command)
}

const setTheme = (theme: string) => {
  currentTheme.value = theme
  document.documentElement.className = `theme-${theme}`
}
</script>

<style lang="less" scoped>
.search-box-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--bg-color);
  color: var(--text-color);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;

  h1 {
    font-size: 32px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 12px;
  }

  p {
    font-size: 16px;
    color: var(--text-color-secondary);
    margin: 0;
  }
}

.demo-sections {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.demo-section {
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 32px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  > p {
    font-size: 14px;
    color: var(--text-color-secondary);
    margin-bottom: 24px;
  }
}

.demo-examples {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.example {
  background: var(--bg-color-tertiary);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  padding: 20px;

  h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 12px;
  }
}

.size-examples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.terminal-demo {
  position: relative;
  background: #1a1a1a;
  border-radius: 8px;
  padding: 16px;
  min-height: 200px;

  .terminal-content {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #e2e8f0;
    line-height: 1.5;

    p {
      margin: 4px 0;
    }
  }

  .show-search-btn {
    position: absolute;
    bottom: 16px;
    right: 16px;
    padding: 8px 16px;
    background: var(--button-bg-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      background: var(--button-hover-bg);
    }
  }
}

.theme-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;

  .theme-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--bg-color);
    color: var(--text-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--hover-bg-color);
    }

    &.active {
      background: var(--button-bg-color);
      color: white;
      border-color: var(--button-bg-color);
    }
  }
}

.theme-demo {
  padding: 16px;
  background: var(--bg-color-quaternary);
  border-radius: 8px;
}

.usage-guide {
  h3 {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin: 24px 0 12px 0;

    &:first-child {
      margin-top: 0;
    }
  }

  pre {
    background: var(--bg-color-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;

    code {
      color: var(--text-color);
    }
  }

  ul {
    margin: 0;
    padding-left: 20px;

    li {
      margin: 8px 0;
      color: var(--text-color-secondary);
      line-height: 1.5;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .search-box-demo {
    padding: 16px;
  }

  .demo-header {
    margin-bottom: 32px;

    h1 {
      font-size: 24px;
    }
  }

  .demo-sections {
    gap: 32px;
  }

  .demo-section {
    padding: 20px;
  }

  .size-examples {
    gap: 8px;
  }

  .theme-controls {
    flex-direction: column;

    .theme-btn {
      text-align: center;
    }
  }
}
</style>
