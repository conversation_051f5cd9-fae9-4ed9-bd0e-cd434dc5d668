// 测试创建收藏夹的脚本
// 在浏览器控制台中运行

async function testCreateFolder() {
  try {
    console.log('开始创建测试收藏夹...')

    // 创建"燃石"收藏夹
    const result = await window.api.createCustomFolder({
      name: '燃石',
      description: '燃石医学相关主机'
    })

    console.log('创建结果:', result)

    if (result && result.data && result.data.message === 'success') {
      console.log('✅ 燃石收藏夹创建成功!')

      // 获取所有收藏夹
      const folders = await window.api.getCustomFolders()
      console.log('所有收藏夹:', folders)
    } else {
      console.log('❌ 创建失败:', result)
    }
  } catch (error) {
    console.error('❌ 创建过程中出错:', error)
  }
}

// 测试移动资产到收藏夹
async function testMoveAsset() {
  try {
    console.log('开始测试移动资产到收藏夹...')

    // 首先获取收藏夹列表
    const foldersResult = await window.api.getCustomFolders()
    console.log('收藏夹列表:', foldersResult)

    if (foldersResult && foldersResult.data && foldersResult.data.folders.length > 0) {
      const folder = foldersResult.data.folders[0]
      console.log('使用收藏夹:', folder)

      // 测试移动一个模拟的企业资产
      const moveResult = await window.api.moveAssetToFolder({
        folderUuid: folder.uuid,
        organizationUuid: 'enterprise-demo-org-uuid',
        assetHost: '*************'
      })

      console.log('移动结果:', moveResult)

      if (moveResult && moveResult.data && moveResult.data.message === 'success') {
        console.log('✅ 资产移动成功!')
      } else {
        console.log('❌ 移动失败:', moveResult)
      }
    }
  } catch (error) {
    console.error('❌ 移动过程中出错:', error)
  }
}

// 运行测试
console.log('=== 收藏夹功能测试 ===')
console.log('请在控制台中运行以下命令:')
console.log('1. testCreateFolder() - 创建测试收藏夹')
console.log('2. testMoveAsset() - 测试移动资产到收藏夹')
