/**
 * Console连接API路由
 * 提供Console连接服务的HTTP接口
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { ipcMain } from 'electron'
import { ConsoleManager } from './consoleManager'
import { ConsoleConnectionConfig, ConsoleConnectionType, ConsoleManagerOptions } from './types'

/**
 * Console API控制器类
 * 处理来自渲染进程的Console相关请求
 */
export class ConsoleApiController {
  private consoleManager: ConsoleManager
  private isInitialized: boolean = false

  /**
   * 构造函数
   * @param options Console管理器选项
   */
  constructor(options?: ConsoleManagerOptions) {
    this.consoleManager = new ConsoleManager(options)
    this.setupEventHandlers()
  }

  /**
   * 初始化API路由
   */
  public initialize(): void {
    if (this.isInitialized) {
      return
    }

    this.registerIpcHandlers()
    this.isInitialized = true
    console.log('[ConsoleApi] Console API已初始化')
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    // 移除IPC处理器
    this.removeIpcHandlers()

    // 关闭Console管理器
    await this.consoleManager.shutdown()

    this.isInitialized = false
    console.log('[ConsoleApi] Console API已清理')
  }

  /**
   * 注册IPC处理器
   */
  private registerIpcHandlers(): void {
    // 建立Console连接
    ipcMain.handle('console:connect', async (event, config: ConsoleConnectionConfig) => {
      void event
      try {
        this.validateConnectionConfig(config)
        const result = await this.consoleManager.connect(config)
        return { success: true, data: result }
      } catch (error) {
        console.error('[ConsoleApi] 连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '连接失败',
            code: 'CONNECTION_FAILED'
          }
        }
      }
    })

    // 断开Console连接
    ipcMain.handle('console:disconnect', async (event, connectionId: string) => {
      void event
      try {
        if (!connectionId) {
          throw new Error('连接ID不能为空')
        }

        await this.consoleManager.disconnect(connectionId)
        return { success: true }
      } catch (error) {
        console.error('[ConsoleApi] 断开连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '断开连接失败',
            code: 'DISCONNECT_FAILED'
          }
        }
      }
    })

    // 执行Console命令
    ipcMain.handle(
      'console:execute',
      async (
        event,
        params: {
          connectionId: string
          command: string
          timeout?: number
        }
      ) => {
        void event
        try {
          const { connectionId, command, timeout } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          if (!command) {
            throw new Error('命令不能为空')
          }

          const result = await this.consoleManager.executeCommand(connectionId, command, timeout)
          return { success: true, data: result }
        } catch (error) {
          console.error('[ConsoleApi] 命令执行失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '命令执行失败',
              code: 'COMMAND_FAILED'
            }
          }
        }
      }
    )

    // 获取连接状态
    ipcMain.handle('console:status', async (event, connectionId: string) => {
      void event
      try {
        if (!connectionId) {
          throw new Error('连接ID不能为空')
        }

        const status = await this.consoleManager.getStatus(connectionId)
        return { success: true, data: status }
      } catch (error) {
        console.error('[ConsoleApi] 获取状态失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '获取状态失败',
            code: 'STATUS_FAILED'
          }
        }
      }
    })

    // 获取所有连接列表
    ipcMain.handle('console:list', async (event) => {
      void event
      try {
        const connections = await this.consoleManager.listConnections()
        return { success: true, data: connections }
      } catch (error) {
        console.error('[ConsoleApi] 获取连接列表失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '获取连接列表失败',
            code: 'LIST_FAILED'
          }
        }
      }
    })

    // 发送数据到连接
    ipcMain.handle(
      'console:send',
      async (
        event,
        params: {
          connectionId: string
          data: string | Buffer
        }
      ) => {
        void event
        try {
          const { connectionId, data } = params

          if (!connectionId) {
            throw new Error('连接ID不能为空')
          }

          if (!data) {
            throw new Error('数据不能为空')
          }

          const connection = this.consoleManager.getConnection(connectionId)
          if (!connection) {
            throw new Error('连接不存在')
          }

          await connection.send(data)
          return { success: true }
        } catch (error) {
          console.error('[ConsoleApi] 发送数据失败:', error)
          return {
            success: false,
            error: {
              message: error instanceof Error ? error.message : '发送数据失败',
              code: 'SEND_FAILED'
            }
          }
        }
      }
    )

    // 清理无效连接
    ipcMain.handle('console:cleanup', async (event) => {
      void event
      try {
        await this.consoleManager.cleanup()
        return { success: true }
      } catch (error) {
        console.error('[ConsoleApi] 清理连接失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '清理连接失败',
            code: 'CLEANUP_FAILED'
          }
        }
      }
    })

    // 获取可用串口列表
    ipcMain.handle('console:serial-ports', async (event) => {
      void event
      try {
        const { SerialPort } = await import('serialport')
        const ports = await SerialPort.list()
        return {
          success: true,
          data: ports.map((port) => ({
            path: port.path,
            manufacturer: port.manufacturer,
            serialNumber: port.serialNumber,
            pnpId: port.pnpId,
            locationId: port.locationId,
            productId: port.productId,
            vendorId: port.vendorId
          }))
        }
      } catch (error) {
        console.error('[ConsoleApi] 获取串口列表失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '获取串口列表失败',
            code: 'SERIAL_PORTS_FAILED'
          }
        }
      }
    })

    // 测试连接
    ipcMain.handle('console:test', async (event, config: ConsoleConnectionConfig) => {
      void event
      try {
        this.validateConnectionConfig(config)

        // 创建临时连接进行测试
        const testConfig = {
          ...config,
          id: `test-${Date.now()}`,
          autoReconnect: false
        }

        const result = await this.consoleManager.connect(testConfig)

        // 测试完成后立即断开
        setTimeout(async () => {
          try {
            await this.consoleManager.disconnect(testConfig.id)
          } catch (error) {
            console.warn('[ConsoleApi] 测试连接断开失败:', error)
          }
        }, 1000)

        return { success: true, data: result }
      } catch (error) {
        console.error('[ConsoleApi] 连接测试失败:', error)
        return {
          success: false,
          error: {
            message: error instanceof Error ? error.message : '连接测试失败',
            code: 'TEST_FAILED'
          }
        }
      }
    })
  }

  /**
   * 移除IPC处理器
   */
  private removeIpcHandlers(): void {
    const handlers = [
      'console:connect',
      'console:disconnect',
      'console:execute',
      'console:status',
      'console:list',
      'console:send',
      'console:cleanup',
      'console:serial-ports',
      'console:test'
    ]

    handlers.forEach((handler) => {
      ipcMain.removeAllListeners(handler)
    })
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 监听连接事件并转发给渲染进程
    this.consoleManager.on('connectionEstablished', (result) => {
      this.broadcastToRenderers('console:connection-established', result)
    })

    this.consoleManager.on('connectionConnect', (connectionId) => {
      this.broadcastToRenderers('console:connection-connect', { connectionId })
    })

    this.consoleManager.on('connectionDisconnect', (connectionId, reason) => {
      this.broadcastToRenderers('console:connection-disconnect', { connectionId, reason })
    })

    this.consoleManager.on('connectionData', (connectionId, data) => {
      this.broadcastToRenderers('console:connection-data', { connectionId, data })
    })

    this.consoleManager.on('connectionError', (connectionId, error) => {
      this.broadcastToRenderers('console:connection-error', {
        connectionId,
        error: {
          message: error.message,
          stack: error.stack
        }
      })
    })

    this.consoleManager.on('connectionStatusChange', (connectionId, status) => {
      this.broadcastToRenderers('console:connection-status-change', { connectionId, status })
    })

    this.consoleManager.on('connectionReconnect', (connectionId, attempt) => {
      this.broadcastToRenderers('console:connection-reconnect', { connectionId, attempt })
    })
  }

  /**
   * 向所有渲染进程广播事件
   * @param channel 频道名称
   * @param data 数据
   */
  private broadcastToRenderers(channel: string, data: any): void {
    const { webContents } = require('electron')

    webContents.getAllWebContents().forEach((contents) => {
      if (!contents.isDestroyed()) {
        contents.send(channel, data)
      }
    })
  }

  /**
   * 验证连接配置
   * @param config 连接配置
   */
  private validateConnectionConfig(config: ConsoleConnectionConfig): void {
    if (!config) {
      throw new Error('连接配置不能为空')
    }

    if (!config.id) {
      throw new Error('连接ID不能为空')
    }

    if (!config.assetId) {
      throw new Error('资产ID不能为空')
    }

    if (!config.name) {
      throw new Error('连接名称不能为空')
    }

    if (!Object.values(ConsoleConnectionType).includes(config.type)) {
      throw new Error(`不支持的连接类型: ${config.type}`)
    }

    if (!config.config) {
      throw new Error('连接配置参数不能为空')
    }

    // 根据连接类型验证特定配置
    switch (config.type) {
      case ConsoleConnectionType.SERIAL:
        this.validateSerialConfig(config.config as any)
        break
      case ConsoleConnectionType.TELNET:
        this.validateTelnetConfig(config.config as any)
        break
      case ConsoleConnectionType.SSH:
        this.validateSSHConfig(config.config as any)
        break
    }
  }

  /**
   * 验证串口配置
   * @param config 串口配置
   */
  private validateSerialConfig(config: any): void {
    if (!config.path) {
      throw new Error('串口路径不能为空')
    }
    if (!config.baudRate || config.baudRate <= 0) {
      throw new Error('波特率必须大于0')
    }
  }

  /**
   * 验证Telnet配置
   * @param config Telnet配置
   */
  private validateTelnetConfig(config: any): void {
    if (!config.host) {
      throw new Error('Telnet主机地址不能为空')
    }
    if (!config.port || config.port <= 0 || config.port > 65535) {
      throw new Error('Telnet端口必须在1-65535之间')
    }
  }

  /**
   * 验证SSH配置
   * @param config SSH配置
   */
  private validateSSHConfig(config: any): void {
    if (!config.host) {
      throw new Error('SSH主机地址不能为空')
    }
    if (!config.port || config.port <= 0 || config.port > 65535) {
      throw new Error('SSH端口必须在1-65535之间')
    }
    if (!config.username) {
      throw new Error('SSH用户名不能为空')
    }
  }
}

// 导出单例实例
let consoleApiInstance: ConsoleApiController | null = null

/**
 * 获取Console API控制器实例
 * @param options 管理器选项
 * @returns API控制器实例
 */
export function getConsoleApiController(options?: ConsoleManagerOptions): ConsoleApiController {
  if (!consoleApiInstance) {
    consoleApiInstance = new ConsoleApiController(options)
  }
  return consoleApiInstance
}

/**
 * 初始化Console API
 * @param options 管理器选项
 */
export function initializeConsoleApi(options?: ConsoleManagerOptions): void {
  const controller = getConsoleApiController(options)
  controller.initialize()
}

/**
 * 清理Console API
 */
export async function cleanupConsoleApi(): Promise<void> {
  if (consoleApiInstance) {
    await consoleApiInstance.cleanup()
    consoleApiInstance = null
  }
}
