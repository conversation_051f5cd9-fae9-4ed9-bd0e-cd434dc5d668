<template>
  <div
    class="network-security-monitor"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Shield class="title-icon" />
            网络安全监控
          </h1>
          <p class="page-description">监控网络安全事件、威胁检测和流量分析</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button @click="addSecurityMonitor">
          <Plus class="btn-icon" />
          添加监控对象
        </a-button>
        <a-button
          type="primary"
          @click="showSecurityReport"
        >
          <FileText class="btn-icon" />
          安全报告
        </a-button>
      </div>
    </div>

    <!-- 安全概览 -->
    <div class="security-overview">
      <div class="overview-grid">
        <div class="security-card threat-level">
          <div class="card-header">
            <div class="card-title">
              <AlertTriangle class="card-icon" />
              威胁等级
            </div>
            <div
              class="threat-indicator"
              :class="securityData.threatLevel"
            >
              {{ getThreatLevelLabel(securityData.threatLevel) }}
            </div>
          </div>
          <div class="card-content">
            <div class="threat-details">
              <span>今日检测到 {{ securityData.todayThreats }} 个威胁</span>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Eye class="card-icon" />
              实时监控
            </div>
            <div class="card-value">{{ securityData.activeConnections }}</div>
          </div>
          <div class="card-content">
            <div class="monitoring-stats">
              <div class="stat-item">
                <span class="stat-label">活动连接</span>
                <span class="stat-value">{{ securityData.activeConnections }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">可疑连接</span>
                <span class="stat-value suspicious">{{ securityData.suspiciousConnections }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Ban class="card-icon" />
              阻断统计
            </div>
            <div class="card-value">{{ securityData.blockedAttempts }}</div>
          </div>
          <div class="card-content">
            <div class="block-stats">
              <div class="stat-item">
                <span class="stat-label">今日阻断</span>
                <span class="stat-value">{{ securityData.todayBlocked }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">成功率</span>
                <span class="stat-value">{{ securityData.blockSuccessRate }}%</span>
              </div>
            </div>
          </div>
        </div>

        <div class="security-card">
          <div class="card-header">
            <div class="card-title">
              <Activity class="card-icon" />
              流量分析
            </div>
            <div class="card-value">{{ securityData.trafficVolume }}GB</div>
          </div>
          <div class="card-content">
            <div class="traffic-stats">
              <div class="stat-item">
                <span class="stat-label">正常流量</span>
                <span class="stat-value">{{ securityData.normalTraffic }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">异常流量</span>
                <span class="stat-value anomaly">{{ securityData.anomalousTraffic }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全事件列表 -->
    <div class="security-events">
      <div class="section-header">
        <h2 class="section-title">安全事件</h2>
        <div class="section-filters">
          <a-select
            v-model:value="selectedSeverity"
            style="width: 120px"
          >
            <a-select-option value="all">全部级别</a-select-option>
            <a-select-option value="critical">严重</a-select-option>
            <a-select-option value="high">高</a-select-option>
            <a-select-option value="medium">中</a-select-option>
            <a-select-option value="low">低</a-select-option>
          </a-select>
          <a-select
            v-model:value="selectedType"
            style="width: 150px"
          >
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="intrusion">入侵检测</a-select-option>
            <a-select-option value="malware">恶意软件</a-select-option>
            <a-select-option value="ddos">DDoS攻击</a-select-option>
            <a-select-option value="bruteforce">暴力破解</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="events-list">
        <div
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-item"
          :class="event.severity"
        >
          <div class="event-icon">
            <component
              :is="getEventIcon(event.type)"
              class="icon"
            />
          </div>
          <div class="event-content">
            <div class="event-header">
              <h4 class="event-title">{{ event.title }}</h4>
              <div
                class="event-severity"
                :class="event.severity"
              >
                {{ getSeverityLabel(event.severity) }}
              </div>
            </div>
            <p class="event-description">{{ event.description }}</p>
            <div class="event-details">
              <span class="event-source">来源: {{ event.sourceIP }}</span>
              <span class="event-target">目标: {{ event.targetIP }}</span>
              <span class="event-time">{{ event.timestamp }}</span>
            </div>
          </div>
          <div class="event-actions">
            <a-button
              size="small"
              @click="investigateEvent(event)"
            >
              <Search class="btn-icon" />
              调查
            </a-button>
            <a-button
              size="small"
              :disabled="event.blocked"
              @click="blockSource(event)"
            >
              <Ban class="btn-icon" />
              {{ event.blocked ? '已阻断' : '阻断' }}
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 威胁情报 -->
    <div class="threat-intelligence">
      <div class="section-header">
        <h2 class="section-title">威胁情报</h2>
        <div class="intelligence-summary">
          <span class="intel-item">恶意IP: {{ threatIntel.maliciousIPs }}</span>
          <span class="intel-item">恶意域名: {{ threatIntel.maliciousDomains }}</span>
          <span class="intel-item">已知威胁: {{ threatIntel.knownThreats }}</span>
        </div>
      </div>

      <div class="intelligence-grid">
        <div class="intel-card">
          <div class="intel-header">
            <h3>恶意IP地址</h3>
            <span class="intel-count">{{ threatIntel.maliciousIPs }}</span>
          </div>
          <div class="intel-list">
            <div
              v-for="ip in maliciousIPs"
              :key="ip.address"
              class="intel-item"
            >
              <span class="ip-address">{{ ip.address }}</span>
              <span class="ip-country">{{ ip.country }}</span>
              <span class="ip-threat-type">{{ ip.threatType }}</span>
            </div>
          </div>
        </div>

        <div class="intel-card">
          <div class="intel-header">
            <h3>攻击类型分布</h3>
          </div>
          <div class="attack-distribution">
            <div
              v-for="attack in attackTypes"
              :key="attack.type"
              class="attack-item"
            >
              <div class="attack-info">
                <span class="attack-name">{{ attack.name }}</span>
                <span class="attack-count">{{ attack.count }}</span>
              </div>
              <div class="attack-bar">
                <div
                  class="attack-fill"
                  :style="{ width: attack.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="intel-card">
          <div class="intel-header">
            <h3>地理位置分析</h3>
          </div>
          <div class="geo-analysis">
            <div
              v-for="location in geoData"
              :key="location.country"
              class="geo-item"
            >
              <span class="geo-country">{{ location.country }}</span>
              <span class="geo-attacks">{{ location.attacks }} 次攻击</span>
              <div
                class="geo-risk"
                :class="location.riskLevel"
              >
                {{ location.riskLevel }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加安全监控对象对话框 -->
    <a-modal
      v-model:open="showAddMonitorModal"
      title="添加安全监控对象"
      width="700px"
      :footer="null"
    >
      <a-form
        :model="monitorForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveSecurityMonitor"
      >
        <a-form-item
          label="监控对象名称"
          name="name"
          :rules="[{ required: true, message: '请输入监控对象名称' }]"
        >
          <a-input
            v-model:value="monitorForm.name"
            placeholder="请输入监控对象名称"
          />
        </a-form-item>

        <a-form-item
          label="监控类型"
          name="type"
          :rules="[{ required: true, message: '请选择监控类型' }]"
        >
          <a-select
            v-model:value="monitorForm.type"
            placeholder="请选择监控类型"
            @change="onMonitorTypeChange"
          >
            <a-select-option value="huawei-firewall">华为防火墙日志</a-select-option>
            <a-select-option value="syslog">Syslog日志</a-select-option>
            <a-select-option value="ids">入侵检测系统</a-select-option>
            <a-select-option value="waf">Web应用防火墙</a-select-option>
            <a-select-option value="antivirus">防病毒系统</a-select-option>
            <a-select-option value="network-flow">网络流量分析</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="设备地址"
          name="address"
          :rules="[{ required: true, message: '请输入设备地址' }]"
        >
          <a-input
            v-model:value="monitorForm.address"
            :placeholder="getAddressPlaceholder()"
          />
        </a-form-item>

        <a-form-item
          label="端口"
          name="port"
        >
          <a-input-number
            v-model:value="monitorForm.port"
            :min="1"
            :max="65535"
            placeholder="端口号"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="协议"
          name="protocol"
        >
          <a-select
            v-model:value="monitorForm.protocol"
            placeholder="请选择协议"
          >
            <a-select-option value="syslog">Syslog</a-select-option>
            <a-select-option value="snmp">SNMP</a-select-option>
            <a-select-option value="http">HTTP</a-select-option>
            <a-select-option value="https">HTTPS</a-select-option>
            <a-select-option value="tcp">TCP</a-select-option>
            <a-select-option value="udp">UDP</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 华为防火墙特定配置 -->
        <div v-if="monitorForm.type === 'huawei-firewall'">
          <a-form-item
            label="设备型号"
            name="deviceModel"
          >
            <a-select
              v-model:value="monitorForm.deviceModel"
              placeholder="请选择华为防火墙型号"
            >
              <a-select-option value="USG6000">USG6000系列</a-select-option>
              <a-select-option value="USG9000">USG9000系列</a-select-option>
              <a-select-option value="HiSecEngine">HiSecEngine系列</a-select-option>
              <a-select-option value="CloudEngine">CloudEngine系列</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item
            label="日志类型"
            name="logTypes"
          >
            <a-checkbox-group v-model:value="monitorForm.logTypes">
              <a-checkbox value="attack">攻击日志</a-checkbox>
              <a-checkbox value="traffic">流量日志</a-checkbox>
              <a-checkbox value="system">系统日志</a-checkbox>
              <a-checkbox value="operation">操作日志</a-checkbox>
              <a-checkbox value="alarm">告警日志</a-checkbox>
            </a-checkbox-group>
          </a-form-item>

          <a-form-item
            label="SNMP Community"
            name="snmpCommunity"
          >
            <a-input
              v-model:value="monitorForm.snmpCommunity"
              placeholder="SNMP团体名"
            />
          </a-form-item>
        </div>

        <!-- Syslog特定配置 -->
        <div v-if="monitorForm.type === 'syslog'">
          <a-form-item
            label="Syslog设施"
            name="syslogFacility"
          >
            <a-select
              v-model:value="monitorForm.syslogFacility"
              placeholder="请选择Syslog设施"
            >
              <a-select-option value="local0">Local0</a-select-option>
              <a-select-option value="local1">Local1</a-select-option>
              <a-select-option value="local2">Local2</a-select-option>
              <a-select-option value="local3">Local3</a-select-option>
              <a-select-option value="local4">Local4</a-select-option>
              <a-select-option value="local5">Local5</a-select-option>
              <a-select-option value="local6">Local6</a-select-option>
              <a-select-option value="local7">Local7</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item
            label="日志级别"
            name="logLevel"
          >
            <a-select
              v-model:value="monitorForm.logLevel"
              placeholder="请选择日志级别"
            >
              <a-select-option value="emergency">Emergency (0)</a-select-option>
              <a-select-option value="alert">Alert (1)</a-select-option>
              <a-select-option value="critical">Critical (2)</a-select-option>
              <a-select-option value="error">Error (3)</a-select-option>
              <a-select-option value="warning">Warning (4)</a-select-option>
              <a-select-option value="notice">Notice (5)</a-select-option>
              <a-select-option value="info">Info (6)</a-select-option>
              <a-select-option value="debug">Debug (7)</a-select-option>
            </a-select>
          </a-form-item>
        </div>

        <a-form-item
          label="认证信息"
          name="credentials"
        >
          <a-input-group compact>
            <a-input
              v-model:value="monitorForm.username"
              placeholder="用户名"
              style="width: 50%"
            />
            <a-input-password
              v-model:value="monitorForm.password"
              placeholder="密码"
              style="width: 50%"
            />
          </a-input-group>
        </a-form-item>

        <a-form-item
          label="监控间隔"
          name="interval"
        >
          <a-select
            v-model:value="monitorForm.interval"
            placeholder="请选择监控间隔"
          >
            <a-select-option value="30">30秒</a-select-option>
            <a-select-option value="60">1分钟</a-select-option>
            <a-select-option value="300">5分钟</a-select-option>
            <a-select-option value="600">10分钟</a-select-option>
            <a-select-option value="1800">30分钟</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="告警阈值"
          name="alertThreshold"
        >
          <a-input-number
            v-model:value="monitorForm.alertThreshold"
            :min="1"
            :max="1000"
            placeholder="每分钟事件数量阈值"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="启用状态"
          name="enabled"
        >
          <a-switch
            v-model:checked="monitorForm.enabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="monitorForm.description"
            :rows="3"
            placeholder="监控对象描述信息"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeAddMonitorModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, Shield, RefreshCw, FileText, Plus, AlertTriangle, Eye, Ban, Activity, Search, Zap, Bug, Skull } from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const selectedSeverity = ref('all')
const selectedType = ref('all')
const showAddMonitorModal = ref(false)
const saving = ref(false)

// 安全监控对象表单数据
const monitorForm = ref({
  name: '',
  type: '',
  address: '',
  port: 514,
  protocol: 'syslog',
  deviceModel: '',
  logTypes: ['attack', 'traffic'],
  snmpCommunity: 'public',
  syslogFacility: 'local0',
  logLevel: 'info',
  username: '',
  password: '',
  interval: '60',
  alertThreshold: 100,
  enabled: true,
  description: ''
})

// 安全监控对象列表
const securityMonitors = ref([
  {
    id: 1,
    name: '华为防火墙-主',
    type: 'huawei-firewall',
    address: '*************',
    status: 'active',
    lastUpdate: '2024-01-15 14:30:25',
    eventsCount: 156
  },
  {
    id: 2,
    name: 'Syslog服务器',
    type: 'syslog',
    address: '*************',
    status: 'active',
    lastUpdate: '2024-01-15 14:30:20',
    eventsCount: 89
  }
])

// 安全数据
const securityData = reactive({
  threatLevel: 'medium',
  todayThreats: 23,
  activeConnections: 1247,
  suspiciousConnections: 8,
  blockedAttempts: 156,
  todayBlocked: 45,
  blockSuccessRate: 98.5,
  trafficVolume: 2.4,
  normalTraffic: 94.2,
  anomalousTraffic: 5.8
})

// 安全事件数据
const securityEvents = ref([
  {
    id: 1,
    title: 'DDoS攻击检测',
    description: '检测到来自多个IP地址的大量请求，疑似DDoS攻击',
    type: 'ddos',
    severity: 'critical',
    sourceIP: '*************',
    targetIP: '********',
    timestamp: '2024-01-15 14:30:25',
    blocked: false
  },
  {
    id: 2,
    title: '暴力破解尝试',
    description: 'SSH服务检测到多次登录失败，疑似暴力破解攻击',
    type: 'bruteforce',
    severity: 'high',
    sourceIP: '************',
    targetIP: '********',
    timestamp: '2024-01-15 14:25:18',
    blocked: true
  },
  {
    id: 3,
    title: '恶意软件通信',
    description: '检测到主机与已知恶意域名的通信',
    type: 'malware',
    severity: 'medium',
    sourceIP: '********5',
    targetIP: 'malicious.example.com',
    timestamp: '2024-01-15 14:20:12',
    blocked: true
  }
])

// 威胁情报数据
const threatIntel = reactive({
  maliciousIPs: 1247,
  maliciousDomains: 856,
  knownThreats: 2103
})

const maliciousIPs = ref([
  { address: '************', country: '美国', threatType: '暴力破解' },
  { address: '*************', country: '俄罗斯', threatType: 'DDoS' },
  { address: '**********', country: '中国', threatType: '恶意软件' }
])

const attackTypes = ref([
  { type: 'ddos', name: 'DDoS攻击', count: 45, percentage: 35 },
  { type: 'bruteforce', name: '暴力破解', count: 32, percentage: 25 },
  { type: 'malware', name: '恶意软件', count: 28, percentage: 22 },
  { type: 'intrusion', name: '入侵检测', count: 23, percentage: 18 }
])

const geoData = ref([
  { country: '美国', attacks: 156, riskLevel: 'high' },
  { country: '俄罗斯', attacks: 89, riskLevel: 'critical' },
  { country: '中国', attacks: 67, riskLevel: 'medium' },
  { country: '德国', attacks: 34, riskLevel: 'low' }
])

// 过滤后的事件列表
const filteredEvents = computed(() => {
  let filtered = securityEvents.value

  if (selectedSeverity.value !== 'all') {
    filtered = filtered.filter((event) => event.severity === selectedSeverity.value)
  }

  if (selectedType.value !== 'all') {
    filtered = filtered.filter((event) => event.type === selectedType.value)
  }

  return filtered
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const showSecurityReport = () => {
  console.log('显示安全报告')
}

const addSecurityMonitor = () => {
  showAddMonitorModal.value = true
  resetMonitorForm()
}

const closeAddMonitorModal = () => {
  showAddMonitorModal.value = false
  resetMonitorForm()
}

const resetMonitorForm = () => {
  monitorForm.value = {
    name: '',
    type: '',
    address: '',
    port: 514,
    protocol: 'syslog',
    deviceModel: '',
    logTypes: ['attack', 'traffic'],
    snmpCommunity: 'public',
    syslogFacility: 'local0',
    logLevel: 'info',
    username: '',
    password: '',
    interval: '60',
    alertThreshold: 100,
    enabled: true,
    description: ''
  }
}

const onMonitorTypeChange = (type: string) => {
  // 根据监控类型设置默认值
  const typeDefaults: Record<string, any> = {
    'huawei-firewall': {
      port: 161,
      protocol: 'snmp',
      logTypes: ['attack', 'traffic', 'system'],
      alertThreshold: 50
    },
    syslog: {
      port: 514,
      protocol: 'syslog',
      syslogFacility: 'local0',
      logLevel: 'info',
      alertThreshold: 100
    },
    ids: {
      port: 443,
      protocol: 'https',
      alertThreshold: 20
    },
    waf: {
      port: 443,
      protocol: 'https',
      alertThreshold: 30
    },
    antivirus: {
      port: 8080,
      protocol: 'http',
      alertThreshold: 10
    },
    'network-flow': {
      port: 2055,
      protocol: 'udp',
      alertThreshold: 200
    }
  }

  if (typeDefaults[type]) {
    Object.assign(monitorForm.value, typeDefaults[type])
  }
}

const getAddressPlaceholder = () => {
  const placeholders: Record<string, string> = {
    'huawei-firewall': '华为防火墙IP地址 (如: *************)',
    syslog: 'Syslog服务器IP地址 (如: *************)',
    ids: 'IDS设备IP地址',
    waf: 'WAF设备IP地址',
    antivirus: '防病毒服务器IP地址',
    'network-flow': '网络流量分析设备IP地址'
  }

  return placeholders[monitorForm.value.type] || '请输入设备IP地址'
}

const saveSecurityMonitor = async () => {
  saving.value = true

  try {
    // 模拟保存安全监控对象
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建新安全监控对象
    const newMonitor = {
      id: Date.now(),
      name: monitorForm.value.name,
      type: monitorForm.value.type,
      address: monitorForm.value.address,
      port: monitorForm.value.port,
      protocol: monitorForm.value.protocol,
      deviceModel: monitorForm.value.deviceModel,
      logTypes: monitorForm.value.logTypes,
      snmpCommunity: monitorForm.value.snmpCommunity,
      syslogFacility: monitorForm.value.syslogFacility,
      logLevel: monitorForm.value.logLevel,
      username: monitorForm.value.username,
      password: monitorForm.value.password,
      interval: parseInt(monitorForm.value.interval),
      alertThreshold: monitorForm.value.alertThreshold,
      enabled: monitorForm.value.enabled,
      description: monitorForm.value.description,
      status: 'active',
      lastUpdate: new Date().toLocaleString('zh-CN'),
      eventsCount: 0
    }

    // 添加到安全监控对象列表
    securityMonitors.value.push(newMonitor)

    // 关闭对话框
    closeAddMonitorModal()

    // 显示成功消息
    console.log('安全监控对象添加成功:', newMonitor)
  } catch (error) {
    console.error('保存安全监控对象失败:', error)
  } finally {
    saving.value = false
  }
}

const investigateEvent = (event: any) => {
  console.log('调查事件:', event)
}

const blockSource = (event: any) => {
  console.log('阻断来源:', event)
  event.blocked = true
}

const getThreatLevelLabel = (level: string) => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[level] || level
}

const getSeverityLabel = (severity: string) => {
  const labels: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[severity] || severity
}

const getEventIcon = (type: string) => {
  const icons: Record<string, any> = {
    ddos: Zap,
    bruteforce: Ban,
    malware: Bug,
    intrusion: Skull
  }
  return icons[type] || AlertTriangle
}

onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.network-security-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.security-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.security-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.security-card.threat-level {
  border-left: 4px solid #faad14;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
}

.threat-indicator {
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.threat-indicator.low {
  background: #f6ffed;
  color: #52c41a;
}

.threat-indicator.medium {
  background: #fffbe6;
  color: #faad14;
}

.threat-indicator.high {
  background: #fff2e8;
  color: #fa8c16;
}

.threat-indicator.critical {
  background: #fff2f0;
  color: #ff4d4f;
}

.card-content {
  margin-top: 12px;
}

.threat-details {
  font-size: 14px;
  color: #666;
}

.monitoring-stats,
.block-stats,
.traffic-stats {
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.stat-value.suspicious {
  color: #ff4d4f;
}

.stat-value.anomaly {
  color: #fa8c16;
}

.security-events,
.threat-intelligence {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-filters {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.event-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.event-item.critical {
  border-left: 4px solid #ff4d4f;
  background: #fff2f0;
}

.event-item.high {
  border-left: 4px solid #fa8c16;
  background: #fff2e8;
}

.event-item.medium {
  border-left: 4px solid #faad14;
  background: #fffbe6;
}

.event-item.low {
  border-left: 4px solid #52c41a;
  background: #f6ffed;
}

.event-icon {
  margin-top: 2px;
  color: #666;
}

.event-content {
  flex: 1;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.event-severity {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.event-severity.critical {
  background: #ff4d4f;
  color: white;
}

.event-severity.high {
  background: #fa8c16;
  color: white;
}

.event-severity.medium {
  background: #faad14;
  color: white;
}

.event-severity.low {
  background: #52c41a;
  color: white;
}

.event-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.event-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #999;
}

.event-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 2px;
}

.intelligence-summary {
  display: flex;
  gap: 24px;
  font-size: 14px;
  color: #666;
}

.intel-item {
  font-weight: 500;
}

.intelligence-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.intel-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.intel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.intel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.intel-count {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.intel-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.intel-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.ip-address {
  font-family: monospace;
  font-weight: 600;
  color: #1a1a1a;
}

.ip-country {
  color: #666;
}

.ip-threat-type {
  color: #ff4d4f;
  font-weight: 500;
}

.attack-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attack-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attack-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.attack-name {
  color: #1a1a1a;
  font-weight: 500;
}

.attack-count {
  color: #666;
}

.attack-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.attack-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.geo-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.geo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.geo-country {
  font-weight: 600;
  color: #1a1a1a;
}

.geo-attacks {
  color: #666;
}

.geo-risk {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.geo-risk.low {
  background: #f6ffed;
  color: #52c41a;
}

.geo-risk.medium {
  background: #fffbe6;
  color: #faad14;
}

.geo-risk.high {
  background: #fff2e8;
  color: #fa8c16;
}

.geo-risk.critical {
  background: #fff2f0;
  color: #ff4d4f;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 16px;
  height: 16px;
}

.icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
