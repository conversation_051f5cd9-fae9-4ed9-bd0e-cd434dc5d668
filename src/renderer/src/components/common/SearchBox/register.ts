import type { App } from 'vue'
import SearchBox from './SearchBox.vue'
import AssetSearchBox from './AssetSearchBox.vue'
import TerminalSearchBox from './TerminalSearchBox.vue'
import CommandHistorySearchBox from './CommandHistorySearchBox.vue'

// 搜索框组件集合
const searchBoxComponents = {
  SearchBox,
  AssetSearchBox,
  TerminalSearchBox,
  CommandHistorySearchBox
}

// 全局注册搜索框组件
export function registerSearchBoxComponents(app: App) {
  // 注册基础搜索框
  app.component('SearchBox', SearchBox)
  app.component('CSearchBox', SearchBox) // 别名
  
  // 注册专用搜索框
  app.component('AssetSearchBox', AssetSearchBox)
  app.component('TerminalSearchBox', TerminalSearchBox)
  app.component('CommandHistorySearchBox', CommandHistorySearchBox)
  
  // 注册简化别名
  app.component('CAssetSearch', AssetSearchBox)
  app.component('CTerminalSearch', TerminalSearchBox)
  app.component('CCommandHistorySearch', CommandHistorySearchBox)
}

// 按需导入支持
export {
  SearchBox,
  AssetSearchBox,
  TerminalSearchBox,
  CommandHistorySearchBox
}

// 默认导出
export default {
  install: registerSearchBoxComponents,
  ...searchBoxComponents
}

// 搜索框使用指南
export const SearchBoxUsageGuide = {
  // 基础搜索框 - 适用于简单的搜索场景
  basic: {
    component: 'SearchBox',
    description: '基础搜索框，支持输入、清除、建议等基本功能',
    usage: `
<SearchBox 
  v-model="searchValue" 
  placeholder="搜索..." 
  @search="handleSearch"
/>`,
    props: [
      'modelValue', 'placeholder', 'size', 'variant', 'clearable', 
      'suggestions', 'showSuggestions', 'disabled', 'readonly'
    ],
    events: [
      'update:modelValue', 'search', 'input', 'change', 'focus', 'blur', 
      'clear', 'suggestion-select'
    ]
  },

  // 资产搜索框 - 适用于资产管理页面
  asset: {
    component: 'AssetSearchBox',
    description: '资产搜索框，集成了资产特定的搜索功能和操作按钮',
    usage: `
<AssetSearchBox 
  v-model="searchValue" 
  :assets="assetList"
  @search="handleSearch"
  @new-asset="handleNewAsset"
/>`,
    props: [
      'modelValue', 'placeholder', 'assets', 'showSuggestions', 
      'showActionButtons', 'showNewButton', 'showImportButton', 'showExportButton'
    ],
    events: [
      'update:modelValue', 'search', 'suggestion-select', 
      'new-asset', 'import-assets', 'export-assets', 'clear'
    ]
  },

  // 终端搜索框 - 适用于终端内搜索
  terminal: {
    component: 'TerminalSearchBox',
    description: '终端搜索框，支持终端内容搜索和高级搜索选项',
    usage: `
<TerminalSearchBox 
  :visible="showSearch"
  :search-addon="searchAddon"
  :terminal="terminal"
  @close="closeSearch"
/>`,
    props: [
      'visible', 'floating', 'placeholder', 'showOptions', 
      'searchAddon', 'terminal'
    ],
    events: [
      'close', 'search', 'find-next', 'find-previous'
    ]
  },

  // 命令历史搜索框 - 适用于命令历史页面
  commandHistory: {
    component: 'CommandHistorySearchBox',
    description: '命令历史搜索框，支持命令搜索、过滤和操作',
    usage: `
<CommandHistorySearchBox 
  v-model="searchValue"
  :commands="commandHistory"
  @search="handleSearch"
  @execute-command="executeCommand"
/>`,
    props: [
      'modelValue', 'placeholder', 'commands', 'showSuggestions', 
      'showFilters', 'maxSuggestions'
    ],
    events: [
      'update:modelValue', 'search', 'suggestion-select', 
      'copy-command', 'execute-command', 'clear'
    ]
  }
}

// 迁移指南
export const MigrationGuide = {
  // 从 Ant Design Input 迁移
  fromAntInput: {
    before: `
<a-input
  v-model:value="searchValue"
  placeholder="搜索..."
  @input="handleInput"
>
  <template #suffix>
    <SearchOutlined />
  </template>
</a-input>`,
    after: `
<SearchBox
  v-model="searchValue"
  placeholder="搜索..."
  @search="handleSearch"
/>`,
    notes: [
      '将 @input 改为 @search 以获得防抖效果',
      '移除手动添加的图标，组件内置搜索图标',
      '自动支持清除功能，无需手动实现'
    ]
  },

  // 从原生 input 迁移
  fromNativeInput: {
    before: `
<div class="search-container">
  <input
    v-model="searchValue"
    type="text"
    placeholder="搜索..."
    @input="handleInput"
  />
  <button @click="clearSearch">清除</button>
</div>`,
    after: `
<SearchBox
  v-model="searchValue"
  placeholder="搜索..."
  clearable
  @search="handleSearch"
  @clear="handleClear"
/>`,
    notes: [
      '移除手动的容器和清除按钮',
      '使用 clearable 属性启用内置清除功能',
      '获得更好的样式和交互体验'
    ]
  },

  // 从现有的 AssetSearch 组件迁移
  fromAssetSearch: {
    before: `
<AssetSearch
  v-model="searchValue"
  @search="handleSearch"
  @new-asset="handleNewAsset"
/>`,
    after: `
<AssetSearchBox
  v-model="searchValue"
  :assets="assetList"
  @search="handleSearch"
  @new-asset="handleNewAsset"
/>`,
    notes: [
      '添加 :assets 属性以启用智能建议',
      '保持现有的事件处理逻辑',
      '获得更丰富的搜索建议功能'
    ]
  }
}

// 主题定制指南
export const ThemeCustomization = {
  cssVariables: [
    '--bg-color-secondary: 搜索框背景色',
    '--border-color: 搜索框边框色',
    '--text-color: 搜索框文字色',
    '--text-color-tertiary: 占位符和图标色',
    '--input-focus-border: 聚焦时边框色',
    '--input-focus-shadow: 聚焦时阴影效果',
    '--hover-bg-color: 悬停时背景色',
    '--active-bg-color: 激活时背景色'
  ],
  
  customization: `
// 自定义搜索框主题
.custom-search-theme {
  .search-box {
    --bg-color-secondary: #f8f9fa;
    --border-color: #dee2e6;
    --input-focus-border: #0d6efd;
    
    &--focused {
      box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }
  }
}`,
  
  darkMode: `
// 暗色主题适配
.theme-dark {
  .search-box {
    --bg-color-secondary: #1a1a1a;
    --border-color: #404040;
    --text-color: #e2e8f0;
    --text-color-tertiary: #666666;
  }
}`
}
