## 1.Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[React Frontend Application]
    B --> C[Electron Main Process]
    C --> D[Console Service Module]
    C --> E[RDP Service Module]
    C --> F[Connection Manager]
    D --> G[Serial Port Service]
    D --> H[Telnet Service]
    D --> I[SSH Service]
    E --> J[RDP Client Service]
    F --> K[Supabase Database]
    F --> L[Local SQLite]
    
    subgraph "Frontend Layer"
        B
    end
    
    subgraph "Main Process Layer"
        C
        D
        E
        F
    end
    
    subgraph "Service Layer"
        G
        H
        I
        J
    end
    
    subgraph "Data Layer"
        K
        L
    end
```

## 2.Technology Description

* Frontend: React\@18 + TypeScript + Ant Design + Vite

* Backend: Electron Main Process + Node.js

* Console Services: serialport\@12.0.0 + telnet-client\@2.0.0 + node-ssh\@13.1.0

* RDP Services: node-rdp\@1.0.0 + freerdp-wrapper

* Database: Supabase (PostgreSQL) + SQLite (本地缓存)

* State Management: Zustand + React Query

## 3.Route definitions

| Route                 | Purpose                        |
| --------------------- | ------------------------------ |
| /assets/personal      | 个人资产管理页面，支持Console和RDP资产的添加和管理 |
| /assets/enterprise    | 企业资产管理页面，支持批量管理和权限控制           |
| /connections/active   | 活动连接管理页面，显示当前所有连接状态            |
| /connections/history  | 连接历史记录页面，查看连接日志和统计             |
| /settings/connections | 连接配置页面，管理Console和RDP的全局设置      |
| /console/:id          | Console连接界面，提供终端交互             |
| /rdp/:id              | RDP连接界面，提供远程桌面显示               |

## 4.API definitions

### 4.1 Core API

#### Console连接相关API

```
POST /api/console/connect
```

Request:

| Param Name | Param Type | isRequired | Description            |
| ---------- | ---------- | ---------- | ---------------------- |
| assetId    | string     | true       | 资产ID                   |
| type       | string     | true       | 连接类型：serial/telnet/ssh |
| config     | object     | true       | 连接配置参数                 |

Response:

| Param Name   | Param Type | Description |
| ------------ | ---------- | ----------- |
| connectionId | string     | 连接会话ID      |
| status       | string     | 连接状态        |
| message      | string     | 状态消息        |

Example:

```json
{
  "assetId": "asset-123",
  "type": "telnet",
  "config": {
    "host": "***********",
    "port": 23,
    "timeout": 5000
  }
}
```

#### RDP连接相关API

```
POST /api/rdp/connect
```

Request:

| Param Name      | Param Type | isRequired | Description |
| --------------- | ---------- | ---------- | ----------- |
| assetId         | string     | true       | 资产ID        |
| config          | object     | true       | RDP连接配置     |
| displaySettings | object     | false      | 显示设置        |

Response:

| Param Name   | Param Type | Description |
| ------------ | ---------- | ----------- |
| connectionId | string     | RDP会话ID     |
| status       | string     | 连接状态        |
| sessionInfo  | object     | 会话信息        |

Example:

```json
{
  "assetId": "asset-456",
  "config": {
    "host": "*************",
    "port": 3389,
    "username": "administrator",
    "password": "encrypted_password",
    "domain": "WORKGROUP"
  },
  "displaySettings": {
    "width": 1920,
    "height": 1080,
    "colorDepth": 32
  }
}
```

#### 连接管理API

```
GET /api/connections/list
```

Response:

| Param Name  | Param Type | Description |
| ----------- | ---------- | ----------- |
| connections | array      | 活动连接列表      |
| total       | number     | 连接总数        |

```
POST /api/connections/:id/disconnect
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| force      | boolean    | false      | 是否强制断开      |

## 5.Server architecture diagram

```mermaid
graph TD
    A[Electron Renderer] --> B[IPC Communication]
    B --> C[Main Process Controller]
    C --> D[Connection Service Layer]
    D --> E[Console Manager]
    D --> F[RDP Manager]
    D --> G[Connection Pool]
    E --> H[Serial Service]
    E --> I[Telnet Service]
    E --> J[SSH Service]
    F --> K[RDP Client]
    G --> L[Session Storage]
    L --> M[Database Layer]
    
    subgraph "Main Process"
        C
        D
        E
        F
        G
    end
    
    subgraph "Service Implementations"
        H
        I
        J
        K
    end
    
    subgraph "Data Persistence"
        L
        M
    end
```

## 6.Data model

### 6.1 Data model definition

```mermaid
erDiagram
    ASSETS ||--o{ CONNECTIONS : has
    ASSETS ||--o{ CONSOLE_CONFIGS : configures
    ASSETS ||--o{ RDP_CONFIGS : configures
    CONNECTIONS ||--o{ CONNECTION_LOGS : generates
    USERS ||--o{ ASSETS : owns
    ORGANIZATIONS ||--o{ ASSETS : manages
    
    ASSETS {
        uuid id PK
        string name
        string type
        string asset_type
        string owner_id
        string organization_id
        timestamp created_at
        timestamp updated_at
    }
    
    CONSOLE_CONFIGS {
        uuid id PK
        uuid asset_id FK
        string connection_type
        string host
        int port
        json serial_config
        json auth_config
        timestamp created_at
    }
    
    RDP_CONFIGS {
        uuid id PK
        uuid asset_id FK
        string host
        int port
        string username
        string password_encrypted
        string domain
        json display_settings
        json security_settings
        timestamp created_at
    }
    
    CONNECTIONS {
        uuid id PK
        uuid asset_id FK
        string connection_type
        string status
        timestamp started_at
        timestamp ended_at
        json session_info
    }
    
    CONNECTION_LOGS {
        uuid id PK
        uuid connection_id FK
        string log_type
        text content
        timestamp created_at
    }
    
    USERS {
        uuid id PK
        string email
        string name
        timestamp created_at
    }
    
    ORGANIZATIONS {
        uuid id PK
        string name
        json settings
        timestamp created_at
    }
```

### 6.2 Data Definition Language

#### Console配置表 (console\_configs)

```sql
-- 创建Console配置表
CREATE TABLE console_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    connection_type VARCHAR(20) NOT NULL CHECK (connection_type IN ('serial', 'telnet', 'ssh')),
    host VARCHAR(255),
    port INTEGER DEFAULT 23,
    serial_config JSONB DEFAULT '{}',
    auth_config JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_console_configs_asset_id ON console_configs(asset_id);
CREATE INDEX idx_console_configs_type ON console_configs(connection_type);
```

#### RDP配置表 (rdp\_configs)

```sql
-- 创建RDP配置表
CREATE TABLE rdp_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    host VARCHAR(255) NOT NULL,
    port INTEGER DEFAULT 3389,
    username VARCHAR(100),
    password_encrypted TEXT,
    domain VARCHAR(100) DEFAULT 'WORKGROUP',
    display_settings JSONB DEFAULT '{"width": 1920, "height": 1080, "colorDepth": 32}',
    security_settings JSONB DEFAULT '{"encryption": true, "authentication": "ntlm"}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_rdp_configs_asset_id ON rdp_configs(asset_id);
CREATE INDEX idx_rdp_configs_host ON rdp_configs(host);
```

#### 连接会话表 (connections)

```sql
-- 创建连接会话表
CREATE TABLE connections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    asset_id UUID NOT NULL REFERENCES assets(id),
    connection_type VARCHAR(20) NOT NULL CHECK (connection_type IN ('console', 'rdp')),
    status VARCHAR(20) DEFAULT 'connecting' CHECK (status IN ('connecting', 'connected', 'disconnected', 'error')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    session_info JSONB DEFAULT '{}',
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_connections_asset_id ON connections(asset_id);
CREATE INDEX idx_connections_status ON connections(status);
CREATE INDEX idx_connections_started_at ON connections(started_at DESC);
```

#### 连接日志表 (connection\_logs)

```sql
-- 创建连接日志表
CREATE TABLE connection_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    connection_id UUID NOT NULL REFERENCES connections(id) ON DELETE CASCADE,
    log_type VARCHAR(20) DEFAULT 'info' CHECK (log_type IN ('info', 'warning', 'error', 'debug')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_connection_logs_connection_id ON connection_logs(connection_id);
CREATE INDEX idx_connection_logs_created_at ON connection_logs(created_at DESC);
CREATE INDEX idx_connection_logs_type ON connection_logs(log_type);
```

#### 扩展现有assets表

```sql
-- 为assets表添加新的资产类型支持
ALTER TABLE assets ADD COLUMN IF NOT EXISTS connection_type VARCHAR(20);
ALTER TABLE assets ADD CONSTRAINT check_connection_type 
    CHECK (connection_type IS NULL OR connection_type IN ('ssh', 'console', 'rdp'));

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_assets_connection_type ON assets(connection_type);
```

#### 初始化数据

```sql
-- 插入示例Console配置
INSERT INTO console_configs (asset_id, connection_type, host, port, serial_config, auth_config)
VALUES 
    ('example-asset-1', 'telnet', '***********', 23, '{}', '{"username": "admin"}'),
    ('example-asset-2', 'serial', '/dev/ttyUSB0', NULL, '{"baudRate": 9600, "dataBits": 8, "stopBits": 1, "parity": "none"}', '{}');

-- 插入示例RDP配置
INSERT INTO rdp_configs (asset_id, host, port, username, domain, display_settings)
VALUES 
    ('example-asset-3', '*************', 3389, 'administrator', 'WORKGROUP', '{"width": 1920, "height": 1080, "colorDepth": 32}');
```

