/**
 * 企业资源同步系统 - 权限控制服务
 * 功能：实现基于角色的访问控制(RBAC)，管理用户权限和资源访问
 * 依赖：企业类型定义、认证服务
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import {
  User,
  UserRole,
  Permission,
  RolePermissions,
  ResourceType,
  Environment,
  EnterpriseResource,
  EnterpriseConfigBackend
} from '../types/EnterpriseTypes'
import { AuthenticationService } from './AuthenticationService'

/**
 * 权限操作枚举
 */
export enum PermissionAction {
  READ = 'read',
  WRITE = 'write',
  DELETE = 'delete',
  EXECUTE = 'execute',
  ADMIN = 'admin'
}

/**
 * 资源权限检查结果
 */
interface PermissionCheckResult {
  allowed: boolean
  reason?: string
  conditions?: Record<string, any>
}

/**
 * 权限上下文
 */
interface PermissionContext {
  user: User
  resource_type: string
  resource_id?: string
  action: PermissionAction
  environment?: Environment
  additional_data?: Record<string, any>
}

/**
 * 权限规则
 */
interface PermissionRule {
  id: string
  role: UserRole
  resource_pattern: string
  actions: PermissionAction[]
  conditions?: {
    environment?: Environment[]
    tags?: string[]
    custom?: (context: PermissionContext) => boolean
  }
  priority: number
  enabled: boolean
}

/**
 * 权限控制服务
 * 负责管理用户权限、角色权限和资源访问控制
 */
export class PermissionService extends EventEmitter {
  private static instance: PermissionService | null = null
  private authService: AuthenticationService
  private permissionRules: Map<string, PermissionRule> = new Map()
  private rolePermissions: Map<UserRole, RolePermissions> = new Map()

  /**
   * 获取服务单例实例
   * @returns 服务实例
   */
  static getInstance(): PermissionService {
    if (!PermissionService.instance) {
      PermissionService.instance = new PermissionService()
    }
    return PermissionService.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()
    this.authService = AuthenticationService.getInstance()
    this.initializeDefaultPermissions()
  }

  /**
   * 检查用户权限
   * @param token 用户令牌
   * @param resource 资源类型
   * @param action 操作类型
   * @param resourceId 资源ID（可选）
   * @param additionalData 额外数据（可选）
   * @returns 权限检查结果
   */
  async checkPermission(
    token: string,
    resource: string,
    action: PermissionAction,
    resourceId?: string,
    additionalData?: Record<string, any>
  ): Promise<PermissionCheckResult> {
    try {
      // 验证用户令牌
      const user = await this.authService.validateToken(token)
      if (!user) {
        return {
          allowed: false,
          reason: '无效的用户令牌'
        }
      }

      // 检查用户是否激活
      if (!user.is_active) {
        return {
          allowed: false,
          reason: '用户账户已被禁用'
        }
      }

      // 构建权限上下文
      const context: PermissionContext = {
        user,
        resource_type: resource,
        resource_id: resourceId,
        action,
        additional_data: additionalData
      }

      // 系统管理员拥有所有权限
      if (user.role_type === UserRole.SYSTEM_ADMIN) {
        return {
          allowed: true,
          reason: '系统管理员权限'
        }
      }

      // 检查角色权限
      const rolePermission = this.checkRolePermission(user.role_type, context)
      if (rolePermission.allowed) {
        return rolePermission
      }

      // 检查自定义权限规则
      const rulePermission = this.checkPermissionRules(context)
      if (rulePermission.allowed) {
        return rulePermission
      }

      return {
        allowed: false,
        reason: '权限不足'
      }
    } catch (error: any) {
      return {
        allowed: false,
        reason: `权限检查失败: ${error.message}`
      }
    }
  }

  /**
   * 检查企业资源访问权限
   * @param token 用户令牌
   * @param resource 企业资源
   * @param action 操作类型
   * @returns 权限检查结果
   */
  async checkEnterpriseResourcePermission(token: string, resource: EnterpriseResource, action: PermissionAction): Promise<PermissionCheckResult> {
    const additionalData = {
      environment: resource.environment,
      tags: resource.tags,
      backend_id: resource.backend_id
    }

    return this.checkPermission(token, `enterprise_resource.${resource.resource_type}`, action, resource.id, additionalData)
  }

  /**
   * 检查配置后端访问权限
   * @param token 用户令牌
   * @param backend 配置后端
   * @param action 操作类型
   * @returns 权限检查结果
   */
  async checkBackendPermission(token: string, backend: EnterpriseConfigBackend, action: PermissionAction): Promise<PermissionCheckResult> {
    const additionalData = {
      backend_type: backend.type,
      backend_priority: backend.priority
    }

    return this.checkPermission(token, 'config_backend', action, backend.id, additionalData)
  }

  /**
   * 获取用户可访问的资源列表
   * @param token 用户令牌
   * @param resourceType 资源类型
   * @param action 操作类型
   * @returns 可访问的资源ID列表
   */
  async getAccessibleResources(token: string, resourceType: string, action: PermissionAction): Promise<string[]> {
    const user = await this.authService.validateToken(token)
    if (!user) {
      return []
    }

    // 系统管理员可以访问所有资源
    if (user.role_type === UserRole.SYSTEM_ADMIN) {
      return ['*'] // 表示所有资源
    }

    // TODO: 根据用户角色和权限规则返回可访问的资源列表
    // 这里需要查询数据库或缓存来获取具体的资源列表

    return []
  }

  /**
   * 添加权限规则
   * @param rule 权限规则
   * @returns 规则ID
   */
  addPermissionRule(rule: Omit<PermissionRule, 'id'>): string {
    const ruleId = this.generateRuleId()
    const fullRule: PermissionRule = {
      ...rule,
      id: ruleId
    }

    this.permissionRules.set(ruleId, fullRule)
    this.emit('permissionRuleAdded', fullRule)

    return ruleId
  }

  /**
   * 更新权限规则
   * @param ruleId 规则ID
   * @param updates 更新内容
   * @returns 是否成功
   */
  updatePermissionRule(ruleId: string, updates: Partial<PermissionRule>): boolean {
    const existingRule = this.permissionRules.get(ruleId)
    if (!existingRule) {
      return false
    }

    const updatedRule: PermissionRule = {
      ...existingRule,
      ...updates,
      id: ruleId // 确保ID不被更改
    }

    this.permissionRules.set(ruleId, updatedRule)
    this.emit('permissionRuleUpdated', updatedRule)

    return true
  }

  /**
   * 删除权限规则
   * @param ruleId 规则ID
   * @returns 是否成功
   */
  removePermissionRule(ruleId: string): boolean {
    const rule = this.permissionRules.get(ruleId)
    if (!rule) {
      return false
    }

    this.permissionRules.delete(ruleId)
    this.emit('permissionRuleRemoved', rule)

    return true
  }

  /**
   * 获取所有权限规则
   * @returns 权限规则列表
   */
  getAllPermissionRules(): PermissionRule[] {
    return Array.from(this.permissionRules.values()).sort((a, b) => b.priority - a.priority) // 按优先级降序排列
  }

  /**
   * 获取角色权限
   * @param role 用户角色
   * @returns 角色权限
   */
  getRolePermissions(role: UserRole): RolePermissions | undefined {
    return this.rolePermissions.get(role)
  }

  /**
   * 更新角色权限
   * @param role 用户角色
   * @param permissions 权限列表
   */
  updateRolePermissions(role: UserRole, permissions: Permission[]): void {
    const rolePermissions: RolePermissions = {
      role,
      permissions
    }

    this.rolePermissions.set(role, rolePermissions)
    this.emit('rolePermissionsUpdated', rolePermissions)
  }

  /**
   * 检查角色权限
   * @param role 用户角色
   * @param context 权限上下文
   * @returns 权限检查结果
   */
  private checkRolePermission(role: UserRole, context: PermissionContext): PermissionCheckResult {
    const rolePermissions = this.rolePermissions.get(role)
    if (!rolePermissions) {
      return {
        allowed: false,
        reason: '未找到角色权限配置'
      }
    }

    for (const permission of rolePermissions.permissions) {
      if (this.matchesPermission(permission, context)) {
        return {
          allowed: permission.allowed,
          reason: permission.allowed ? '角色权限允许' : '角色权限拒绝',
          conditions: permission.conditions
        }
      }
    }

    return {
      allowed: false,
      reason: '未找到匹配的角色权限'
    }
  }

  /**
   * 检查权限规则
   * @param context 权限上下文
   * @returns 权限检查结果
   */
  private checkPermissionRules(context: PermissionContext): PermissionCheckResult {
    const applicableRules = Array.from(this.permissionRules.values())
      .filter((rule) => rule.enabled && rule.role === context.user.role_type)
      .sort((a, b) => b.priority - a.priority) // 按优先级降序排列

    for (const rule of applicableRules) {
      if (this.matchesRule(rule, context)) {
        const allowed = rule.actions.includes(context.action)
        return {
          allowed,
          reason: allowed ? '权限规则允许' : '权限规则拒绝'
        }
      }
    }

    return {
      allowed: false,
      reason: '未找到匹配的权限规则'
    }
  }

  /**
   * 检查权限是否匹配
   * @param permission 权限
   * @param context 权限上下文
   * @returns 是否匹配
   */
  private matchesPermission(permission: Permission, context: PermissionContext): boolean {
    // 检查资源匹配
    if (permission.resource !== context.resource_type && permission.resource !== '*') {
      return false
    }

    // 检查操作匹配
    if (permission.action !== context.action && permission.action !== '*') {
      return false
    }

    // 检查条件匹配
    if (permission.conditions) {
      return this.evaluateConditions(permission.conditions, context)
    }

    return true
  }

  /**
   * 检查规则是否匹配
   * @param rule 权限规则
   * @param context 权限上下文
   * @returns 是否匹配
   */
  private matchesRule(rule: PermissionRule, context: PermissionContext): boolean {
    // 检查资源模式匹配
    if (!this.matchesPattern(rule.resource_pattern, context.resource_type)) {
      return false
    }

    // 检查条件匹配
    if (rule.conditions) {
      // 检查环境条件
      if (rule.conditions.environment && context.additional_data?.environment) {
        if (!rule.conditions.environment.includes(context.additional_data.environment)) {
          return false
        }
      }

      // 检查标签条件
      if (rule.conditions.tags && context.additional_data?.tags) {
        const resourceTags = context.additional_data.tags as string[]
        const hasRequiredTag = rule.conditions.tags.some((tag) => resourceTags.includes(tag))
        if (!hasRequiredTag) {
          return false
        }
      }

      // 检查自定义条件
      if (rule.conditions.custom) {
        if (!rule.conditions.custom(context)) {
          return false
        }
      }
    }

    return true
  }

  /**
   * 检查模式匹配
   * @param pattern 模式
   * @param value 值
   * @returns 是否匹配
   */
  private matchesPattern(pattern: string, value: string): boolean {
    if (pattern === '*') {
      return true
    }

    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'))
      return regex.test(value)
    }

    return pattern === value
  }

  /**
   * 评估条件
   * @param conditions 条件
   * @param context 权限上下文
   * @returns 是否满足条件
   */
  private evaluateConditions(conditions: Record<string, any>, context: PermissionContext): boolean {
    // TODO: 实现更复杂的条件评估逻辑
    return true
  }

  /**
   * 生成规则ID
   * @returns 规则ID
   */
  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 初始化默认权限
   */
  private initializeDefaultPermissions(): void {
    // 个人用户权限
    this.rolePermissions.set(UserRole.PERSONAL, {
      role: UserRole.PERSONAL,
      permissions: [
        {
          resource: 'personal_resource.*',
          action: '*',
          allowed: true
        },
        {
          resource: 'enterprise_resource.*',
          action: 'read',
          allowed: true
        },
        {
          resource: 'config_backend',
          action: '*',
          allowed: false
        }
      ]
    })

    // 企业管理员权限
    this.rolePermissions.set(UserRole.ENTERPRISE_ADMIN, {
      role: UserRole.ENTERPRISE_ADMIN,
      permissions: [
        {
          resource: 'personal_resource.*',
          action: '*',
          allowed: true
        },
        {
          resource: 'enterprise_resource.*',
          action: '*',
          allowed: true
        },
        {
          resource: 'config_backend',
          action: '*',
          allowed: true
        },
        {
          resource: 'user_management',
          action: 'read',
          allowed: true
        }
      ]
    })

    // 系统管理员权限
    this.rolePermissions.set(UserRole.SYSTEM_ADMIN, {
      role: UserRole.SYSTEM_ADMIN,
      permissions: [
        {
          resource: '*',
          action: '*',
          allowed: true
        }
      ]
    })

    // 添加默认权限规则
    this.addPermissionRule({
      role: UserRole.PERSONAL,
      resource_pattern: 'enterprise_resource.*',
      actions: [PermissionAction.READ],
      conditions: {
        environment: [Environment.DEVELOPMENT, Environment.STAGING]
      },
      priority: 100,
      enabled: true
    })

    this.addPermissionRule({
      role: UserRole.ENTERPRISE_ADMIN,
      resource_pattern: 'enterprise_resource.*',
      actions: [PermissionAction.READ, PermissionAction.WRITE, PermissionAction.DELETE],
      priority: 200,
      enabled: true
    })
  }

  /**
   * 销毁服务，清理资源
   */
  destroy(): void {
    this.permissionRules.clear()
    this.rolePermissions.clear()
    this.removeAllListeners()
  }
}
