<template>
  <div class="test-enterprise">
    <h1>企业资源管理测试页面</h1>
    <p>这是一个测试页面，用于验证企业资源管理组件是否正常工作。</p>

    <div class="test-buttons">
      <button
        class="btn btn-primary"
        @click="navigateToEnterprise"
      >
        直接访问企业资源管理页面
      </button>
      <button
        class="btn btn-secondary"
        @click="openInTab"
      >
        在标签页中打开
      </button>
    </div>

    <div class="component-test">
      <h2>组件直接渲染测试</h2>
      <EnterpriseResourceManagement />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import EnterpriseResourceManagement from '@/views/enterprise/EnterpriseResourceManagement.vue'

const router = useRouter()

const navigateToEnterprise = () => {
  router.push('/resources/enterprise')
}

const openInTab = () => {
  // 这里可以触发打开标签页的事件
  console.log('Opening in tab...')
}
</script>

<style scoped>
.test-enterprise {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-buttons {
  display: flex;
  gap: 16px;
  margin: 20px 0;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #d9d9d9;
}

.component-test {
  margin-top: 40px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
}

h1,
h2 {
  color: #333;
  margin-bottom: 16px;
}

p {
  color: #666;
  margin-bottom: 20px;
}
</style>
