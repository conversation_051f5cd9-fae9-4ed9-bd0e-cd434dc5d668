<template>
  <div class="enhanced-command-history-panel">
    <!-- 重新设计的头部区域 -->
    <header class="command-history-header">
      <!-- 搜索区域 - 独占一行 -->
      <div class="search-section">
        <div class="search-wrapper">
          <div class="search-container">
            <svg
              class="search-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <circle
                cx="11"
                cy="11"
                r="8"
              ></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索命令、服务器或描述..."
              class="search-input"
              @input="handleSearchChange"
            />
            <button
              v-if="searchQuery"
              class="search-clear"
              aria-label="清除搜索"
              @click="clearSearch"
            >
              <svg
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <line
                  x1="18"
                  y1="6"
                  x2="6"
                  y2="18"
                ></line>
                <line
                  x1="6"
                  y1="6"
                  x2="18"
                  y2="18"
                ></line>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 操作按钮区域 - 第二行 -->
      <div class="actions-section">
        <div class="action-group">
          <button
            :class="['action-btn', 'compact-btn', { active: onlyFavorites }]"
            title="仅显示收藏的命令"
            @click="onlyFavorites = !onlyFavorites"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>
          </button>

          <button
            :class="['action-btn', 'compact-btn', { active: showAdvancedFilters }]"
            title="显示高级过滤选项"
            @click="showAdvancedFilters = !showAdvancedFilters"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"></polygon>
            </svg>
          </button>

          <button
            :disabled="loading"
            :class="['action-btn', 'compact-btn', { loading: loading }]"
            title="刷新命令历史"
            @click="refreshHistory"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              :class="{ spinning: loading }"
            >
              <polyline points="23,4 23,10 17,10"></polyline>
              <polyline points="1,20 1,14 7,14"></polyline>
              <path d="M20.49,9A9,9,0,0,0,5.64,5.64L1,10m22,4a9,9,0,0,1-14.85,3.36L23,14"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <!-- 高级过滤器（可折叠） -->
    <div
      v-if="showAdvancedFilters"
      class="advanced-filters"
    >
      <div class="filter-grid">
        <select
          v-model="selectedCategory"
          class="filter-select"
          @change="handleCategoryChange"
        >
          <option value="">全部分类</option>
          <option
            v-for="category in categories"
            :key="category"
            :value="category"
          >
            {{ getCategoryDisplayName(category) }}
          </option>
        </select>

        <select
          v-model="selectedServer"
          class="filter-select"
          @change="handleServerChange"
        >
          <option value="">全部服务器</option>
          <option
            v-for="server in servers"
            :key="server"
            :value="server"
          >
            {{ server }}
          </option>
        </select>

        <select
          v-model="timeRange"
          class="filter-select"
          @change="handleTimeRangeChange"
        >
          <option value="">全部时间</option>
          <option value="today">今天</option>
          <option value="week">本周</option>
          <option value="month">本月</option>
        </select>

        <button
          class="clear-filters-btn"
          @click="clearAllFilters"
        >
          清除过滤
        </button>
        <!-- 标题和统计 -->
        <div class="title-stats">
          <h1 class="page-title">命令历史</h1>
          <span class="stats-count">({{ filteredHistory.length }})</span>
        </div>
      </div>
    </div>

    <!-- 命令列表区域 -->
    <div class="commands-container">
      <!-- 简化的结果信息 -->
      <div
        v-if="filteredHistory.length > 0"
        class="result-bar"
      >
        <span class="result-text">{{ filteredHistory.length }} 条命令</span>
        <div class="view-toggle">
          <button
            :class="['view-btn', { active: viewMode === 'compact' }]"
            title="紧凑视图"
            @click="viewMode = 'compact'"
          >
            ☰
          </button>
          <button
            :class="['view-btn', { active: viewMode === 'detailed' }]"
            title="详细视图"
            @click="viewMode = 'detailed'"
          >
            📋
          </button>
        </div>
      </div>

      <!-- 优化的命令列表 -->
      <div
        ref="commandsListRef"
        class="commands-list"
        :class="viewMode"
      >
        <div
          v-for="item in filteredHistory"
          :key="item.id"
          class="command-item"
          :class="{ selected: selectedItem?.id === item.id, favorite: item.isFavorite }"
          @click="selectCommand(item)"
        >
          <!-- 紧凑模式 -->
          <template v-if="viewMode === 'compact'">
            <div class="compact-content">
              <div class="command-line">
                <code class="command-text">{{ truncateCommand(item.command, 80) }}</code>
                <div class="item-actions">
                  <button
                    :class="['action-btn', { active: item.isFavorite }]"
                    @click.stop="toggleFavorite(item)"
                  >
                    {{ item.isFavorite ? '⭐' : '☆' }}
                  </button>
                  <button
                    class="action-btn"
                    @click.stop="copyCommand(item.command)"
                  >
                    📋
                  </button>
                </div>
              </div>
              <div class="compact-meta">
                <span
                  class="category-badge"
                  :style="{ backgroundColor: getCategoryColor(item.category) }"
                >
                  {{ getCategoryDisplayName(item.category) }}
                </span>
                <span class="server-info">{{ item.serverIp }}</span>
                <span class="time-info">{{ formatTime(item.executedAt) }}</span>
              </div>
            </div>
          </template>

          <!-- 详细模式 -->
          <template v-else>
            <div class="detailed-content">
              <div class="command-header">
                <code class="command-text">{{ item.command }}</code>
                <div class="header-actions">
                  <span
                    class="category-tag"
                    :style="{ backgroundColor: getCategoryColor(item.category) }"
                  >
                    {{ getCategoryDisplayName(item.category) }}
                  </span>
                  <div class="action-buttons">
                    <button
                      :class="['action-btn', { active: item.isFavorite }]"
                      @click.stop="toggleFavorite(item)"
                    >
                      {{ item.isFavorite ? '⭐' : '☆' }}
                    </button>
                    <button
                      class="action-btn"
                      @click.stop="copyCommand(item.command)"
                    >
                      📋
                    </button>
                    <button
                      class="action-btn"
                      @click.stop="editCommand(item)"
                    >
                      ✏️
                    </button>
                  </div>
                </div>
              </div>

              <div class="command-details">
                <div class="meta-info">
                  <span class="meta-item">
                    <span class="meta-label">服务器:</span>
                    <span class="meta-value">{{ item.serverIp }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">用户:</span>
                    <span class="meta-value">{{ item.username }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">时间:</span>
                    <span class="meta-value">{{ formatTime(item.executedAt) }}</span>
                  </span>
                  <span
                    v-if="item.frequency > 1"
                    class="meta-item"
                  >
                    <span class="meta-label">使用:</span>
                    <span class="meta-value">{{ item.frequency }} 次</span>
                  </span>
                </div>

                <div
                  v-if="item.tags && item.tags.length > 0"
                  class="command-tags"
                >
                  <span
                    v-for="tag in item.tags"
                    :key="tag"
                    class="tag"
                    @click.stop="filterByTag(tag)"
                  >
                    #{{ tag }}
                  </span>
                </div>

                <div
                  v-if="item.description"
                  class="command-description"
                >
                  {{ item.description }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 加载更多 -->
      <div
        v-if="hasMore"
        class="load-more-section"
      >
        <button
          :disabled="loadingMore"
          class="load-more-btn"
          @click="loadMore"
        >
          {{ loadingMore ? '加载中...' : '加载更多' }}
        </button>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!loading && filteredHistory.length === 0"
        class="empty-state"
      >
        <div class="empty-content">
          <div class="empty-icon">📝</div>
          <div class="empty-text">
            {{ searchQuery ? '未找到匹配的命令' : '暂无命令历史' }}
          </div>
          <button
            v-if="searchQuery"
            class="empty-action"
            @click="clearAllFilters"
          >
            清除过滤条件
          </button>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <button
        class="action-btn"
        @click="exportHistory"
      >
        📤 导出
      </button>
      <button
        class="action-btn"
        @click="importHistory"
      >
        📥 导入
      </button>
    </div>

    <!-- 编辑命令模态框 -->
    <div
      v-if="editingItem"
      class="modal-overlay"
      @click="closeEditModal"
    >
      <div
        class="edit-modal"
        @click.stop
      >
        <div class="modal-header">
          <h3 class="modal-title">编辑命令</h3>
          <button
            class="modal-close"
            @click="closeEditModal"
            >✕</button
          >
        </div>

        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">命令</label>
            <div class="command-display">
              <code>{{ editingItem.command }}</code>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea
              v-model="editingDescription"
              placeholder="为这个命令添加描述..."
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <label class="form-label">标签</label>
            <div class="tags-input">
              <span
                v-for="tag in editingTags"
                :key="tag"
                class="tag editable"
              >
                {{ tag }}
                <button
                  class="tag-remove"
                  @click="removeTag(tag)"
                  >✕</button
                >
              </span>
              <input
                v-model="newTag"
                placeholder="添加标签..."
                class="tag-input"
                @keydown.enter="addTag"
                @keydown.space="addTag"
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button
            class="btn secondary"
            @click="closeEditModal"
            >取消</button
          >
          <button
            class="btn primary"
            @click="saveEdit"
            >保存</button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 类型定义
interface EnhancedCommandHistoryItem {
  id: string
  command: string
  serverIp: string
  username: string
  currentDirectory: string
  sessionId: string
  category: string
  tags: string[]
  executedAt: string
  frequency: number
  lastUsed: string
  isFavorite?: boolean
  description?: string
}

interface HistoryStats {
  totalCommands: number
  todayCommands: number
  categoryCounts: Record<string, number>
  tagCounts: Record<string, number>
}

interface QuickFilter {
  key: string
  label: string
  icon: string
  filter: (item: EnhancedCommandHistoryItem) => boolean
}

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const hasMore = ref(true)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedServer = ref('')
const timeRange = ref('')
const onlyFavorites = ref(false)
const showAdvancedFilters = ref(false)
const viewMode = ref<'compact' | 'detailed'>('compact')
const selectedItem = ref<EnhancedCommandHistoryItem | null>(null)
const editingItem = ref<EnhancedCommandHistoryItem | null>(null)
const editingDescription = ref('')
const editingTags = ref<string[]>([])
const newTag = ref('')

const historyItems = ref<EnhancedCommandHistoryItem[]>([])
const stats = ref<HistoryStats>({
  totalCommands: 0,
  todayCommands: 0,
  categoryCounts: {},
  tagCounts: {}
})

const categories = ref<string[]>(['general', 'system', 'file', 'network', 'development', 'database'])
const servers = ref<string[]>([])

// 快速过滤器配置
const quickFilters = ref<QuickFilter[]>([
  {
    key: 'favorites',
    label: '收藏',
    icon: '⭐',
    filter: (item) => !!item.isFavorite
  },
  {
    key: 'recent',
    label: '最近',
    icon: '🕐',
    filter: (item) => dayjs().diff(dayjs(item.executedAt), 'hour') < 24
  },
  {
    key: 'frequent',
    label: '常用',
    icon: '🔥',
    filter: (item) => item.frequency > 5
  },
  {
    key: 'system',
    label: '系统',
    icon: '⚙️',
    filter: (item) => item.category === 'system'
  }
])

// 计算属性
const filteredHistory = computed(() => {
  let filtered = historyItems.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(
      (item) =>
        item.command.toLowerCase().includes(query) ||
        item.currentDirectory.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.tags.some((tag) => tag.toLowerCase().includes(query))
    )
  }

  // 分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter((item) => item.category === selectedCategory.value)
  }

  // 服务器过滤
  if (selectedServer.value) {
    filtered = filtered.filter((item) => item.serverIp === selectedServer.value)
  }

  // 时间范围过滤
  if (timeRange.value) {
    const now = dayjs()
    filtered = filtered.filter((item) => {
      const itemDate = dayjs(item.executedAt)
      switch (timeRange.value) {
        case 'today':
          return itemDate.isSame(now, 'day')
        case 'week':
          return itemDate.isAfter(now.subtract(7, 'day'))
        case 'month':
          return itemDate.isAfter(now.subtract(30, 'day'))
        default:
          return true
      }
    })
  }

  // 收藏过滤
  if (onlyFavorites.value) {
    filtered = filtered.filter((item) => item.isFavorite)
  }

  return filtered.sort((a, b) => dayjs(b.executedAt).valueOf() - dayjs(a.executedAt).valueOf())
})

// 方法实现
const refreshHistory = async () => {
  loading.value = true
  try {
    await loadHistoryData()
    await loadStats()
    message.success('历史记录已刷新')
  } catch (error) {
    console.error('刷新历史记录失败:', error)
    message.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const loadHistoryData = async (append = false) => {
  try {
    const result = await (window as any).api?.enhancedCommandHistorySearch?.({
      query: searchQuery.value,
      category: selectedCategory.value,
      serverIp: selectedServer.value,
      limit: 50,
      offset: append ? historyItems.value.length : 0
    })

    if (result?.success) {
      const newItems = result.data || []
      if (append) {
        historyItems.value.push(...newItems)
      } else {
        historyItems.value = newItems
      }
      hasMore.value = newItems.length === 50
      updateFilterOptions()
    } else {
      // 模拟数据用于演示
      const mockData = generateMockData()
      historyItems.value = append ? [...historyItems.value, ...mockData] : mockData
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载历史数据失败:', error)
    // 使用模拟数据
    const mockData = generateMockData()
    historyItems.value = append ? [...historyItems.value, ...mockData] : mockData
    hasMore.value = false
  }
}

const loadStats = async () => {
  try {
    const result = await (window as any).api?.enhancedCommandHistoryGetStats?.({})
    if (result?.success) {
      stats.value = result.data
    } else {
      // 计算模拟统计数据
      stats.value = {
        totalCommands: historyItems.value.length,
        todayCommands: historyItems.value.filter((item) => dayjs(item.executedAt).isSame(dayjs(), 'day')).length,
        categoryCounts: {},
        tagCounts: {}
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const generateMockData = (): EnhancedCommandHistoryItem[] => {
  return [
    {
      id: '1',
      command: 'ls -la',
      serverIp: '*************',
      username: 'admin',
      currentDirectory: '/home/<USER>',
      sessionId: 'session1',
      category: 'file',
      tags: ['list', 'directory'],
      executedAt: dayjs().subtract(1, 'hour').toISOString(),
      frequency: 15,
      lastUsed: dayjs().subtract(1, 'hour').toISOString(),
      isFavorite: true,
      description: '列出当前目录的详细信息'
    },
    {
      id: '2',
      command: 'docker ps -a',
      serverIp: '*************',
      username: 'root',
      currentDirectory: '/root',
      sessionId: 'session2',
      category: 'development',
      tags: ['docker', 'container'],
      executedAt: dayjs().subtract(2, 'hour').toISOString(),
      frequency: 8,
      lastUsed: dayjs().subtract(2, 'hour').toISOString(),
      isFavorite: false,
      description: '查看所有Docker容器状态'
    },
    {
      id: '3',
      command: 'systemctl status nginx',
      serverIp: '*************',
      username: 'admin',
      currentDirectory: '/etc/nginx',
      sessionId: 'session3',
      category: 'system',
      tags: ['systemctl', 'nginx', 'status'],
      executedAt: dayjs().subtract(3, 'hour').toISOString(),
      frequency: 12,
      lastUsed: dayjs().subtract(3, 'hour').toISOString(),
      isFavorite: true,
      description: '检查Nginx服务状态'
    }
  ]
}

const updateFilterOptions = () => {
  const serverSet = new Set(historyItems.value.map((item) => item.serverIp))
  servers.value = Array.from(serverSet)
}

const handleSearch = () => {
  loadHistoryData()
}

const handleSearchChange = () => {
  clearTimeout((handleSearchChange as any).timer)
  ;(handleSearchChange as any).timer = setTimeout(() => {
    loadHistoryData()
  }, 300)
}

const clearSearch = () => {
  searchQuery.value = ''
  loadHistoryData()
}

const handleServerChange = () => {
  loadHistoryData()
}

const handleTimeRangeChange = () => {
  loadHistoryData()
}

const handleCategoryChange = () => {
  loadHistoryData()
}

const clearAllFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedServer.value = ''
  timeRange.value = ''
  onlyFavorites.value = false
  loadHistoryData()
}

const loadMore = async () => {
  loadingMore.value = true
  try {
    await loadHistoryData(true)
  } finally {
    loadingMore.value = false
  }
}

const selectCommand = (item: EnhancedCommandHistoryItem) => {
  selectedItem.value = item
}

const toggleFavorite = async (item: EnhancedCommandHistoryItem) => {
  try {
    const result = await (window as any).api?.enhancedCommandHistoryToggleFavorite?.({
      id: item.id,
      isFavorite: !item.isFavorite
    })

    if (result?.success || !result) {
      item.isFavorite = !item.isFavorite
      message.success(item.isFavorite ? '已添加到收藏' : '已取消收藏')
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error)
    // 本地更新用于演示
    item.isFavorite = !item.isFavorite
    message.success(item.isFavorite ? '已添加到收藏' : '已取消收藏')
  }
}

const copyCommand = async (command: string) => {
  try {
    await navigator.clipboard.writeText(command)
    message.success('命令已复制到剪贴板')
  } catch (error) {
    message.error('复制失败')
  }
}

const editCommand = (item: EnhancedCommandHistoryItem) => {
  editingItem.value = item
  editingDescription.value = item.description || ''
  editingTags.value = [...item.tags]
}

const closeEditModal = () => {
  editingItem.value = null
  editingDescription.value = ''
  editingTags.value = []
  newTag.value = ''
}

const saveEdit = async () => {
  if (!editingItem.value) return

  try {
    const result = await (window as any).api?.enhancedCommandHistoryUpdate?.({
      id: editingItem.value.id,
      description: editingDescription.value,
      tags: editingTags.value
    })

    if (result?.success || !result) {
      editingItem.value.description = editingDescription.value
      editingItem.value.tags = [...editingTags.value]
      message.success('命令信息已更新')
      closeEditModal()
    }
  } catch (error) {
    console.error('更新命令失败:', error)
    // 本地更新用于演示
    editingItem.value.description = editingDescription.value
    editingItem.value.tags = [...editingTags.value]
    message.success('命令信息已更新')
    closeEditModal()
  }
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !editingTags.value.includes(tag)) {
    editingTags.value.push(tag)
    newTag.value = ''
  }
}

const removeTag = (tag: string) => {
  const index = editingTags.value.indexOf(tag)
  if (index > -1) {
    editingTags.value.splice(index, 1)
  }
}

const filterByTag = (tag: string) => {
  searchQuery.value = `#${tag}`
  loadHistoryData()
}

const exportHistory = async () => {
  try {
    const dataStr = JSON.stringify(historyItems.value, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `command-history-${dayjs().format('YYYY-MM-DD')}.json`
    a.click()
    URL.revokeObjectURL(url)
    message.success('历史记录已导出')
  } catch (error) {
    message.error('导出失败')
  }
}

const importHistory = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      try {
        const text = await file.text()
        const data = JSON.parse(text)
        // 这里应该调用API导入数据
        message.success('历史记录已导入')
      } catch (error) {
        message.error('导入失败，文件格式错误')
      }
    }
  }
  input.click()
}

// 工具方法
const truncateCommand = (command: string, maxLength = 60) => {
  return command.length > maxLength ? command.substring(0, maxLength) + '...' : command
}

const truncatePath = (path: string, maxLength = 30) => {
  return path.length > maxLength ? '...' + path.substring(path.length - maxLength) : path
}

const getCategoryDisplayName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    general: '常规',
    system: '系统',
    file: '文件',
    network: '网络',
    development: '开发',
    database: '数据库',
    monitoring: '监控'
  }
  return categoryNames[category] || category
}

const getCategoryColor = (category: string): string => {
  const categoryColors: Record<string, string> = {
    general: '#108ee9',
    system: '#f50',
    file: '#87d068',
    network: '#fa8c16',
    development: '#722ed1',
    database: '#eb2f96',
    monitoring: '#13c2c2'
  }
  return categoryColors[category] || '#108ee9'
}

const formatTime = (time: string): string => {
  const now = dayjs()
  const itemTime = dayjs(time)

  if (now.diff(itemTime, 'day') === 0) {
    return itemTime.format('HH:mm')
  } else if (now.diff(itemTime, 'day') < 7) {
    return itemTime.format('MM-DD HH:mm')
  } else {
    return itemTime.format('YYYY-MM-DD')
  }
}

// 生命周期
onMounted(async () => {
  await refreshHistory()
})
</script>

<style scoped>
.enhanced-command-history-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

/* 简化的头部区域 */
.command-history-header {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 2px 6px;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.search-section {
  width: 100%;
}

.actions-section {
  display: flex;
  justify-content: flex-start;
}

/* 标题和统计 */
.title-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.stats-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 操作区域 */
.actions-section {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-start;
}

/* 搜索区域 */
.search-wrapper {
  flex: 1;
  max-width: 400px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  height: 16px;
}

.search-icon {
  position: absolute;
  left: 3px;
  color: #64748b;
  z-index: 2;
  width: 10px;
  height: 10px;
}

.search-input {
  width: 100%;
  height: 16px;
  padding: 0px 2px 0px 16px;
  border: 1px solid #d1d5db;
  border-radius: 2px;
  font-size: 10px;
  background: white;
  color: #374151;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-clear {
  position: absolute;
  right: 3px;
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0px;
  border-radius: 1px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 操作按钮组 */
.action-group {
  display: flex;
  align-items: center;
  gap: 3px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 1px;
  padding: 2px 3px;
  border: 1px solid #d1d5db;
  border-radius: 2px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 9px;
  font-weight: 500;
}

.action-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn svg {
  width: 14px;
  height: 14px;
}

/* 紧凑按钮样式 */
.compact-btn {
  padding: 1px;
  min-width: 18px;
  justify-content: center;
}

.compact-btn svg {
  width: 10px;
  height: 10px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.divider {
  width: 1px;
  height: 16px;
  background: #e2e8f0;
  margin: 0 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .command-history-header {
    padding: 1px 4px;
    gap: 1px;
  }

  .search-wrapper {
    max-width: none;
  }

  .action-group {
    justify-content: center;
    gap: 1px;
  }

  .search-container {
    height: 14px;
  }

  .search-input {
    height: 14px;
    padding: 0px 2px 0px 14px;
    font-size: 9px;
  }

  .compact-btn {
    padding: 1px;
    min-width: 16px;
  }

  .compact-btn svg {
    width: 8px;
    height: 8px;
  }
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* 智能搜索区域 */
.search-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.search-container {
  position: relative;
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #667eea;
  font-size: 16px;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #64748b;
}

/* 快速过滤器 */
.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.filter-chip {
  padding: 6px 12px;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-chip:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.filter-chip.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

/* 高级过滤器（可折叠） */
.advanced-filters {
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.filter-select {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.clear-filters-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.clear-filters-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.filter-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 12px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
  color: #667eea;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.filter-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

/* 命令列表区域 */
.commands-container {
  flex: 1;
  overflow: hidden;
  background: white;
  display: flex;
  flex-direction: column;
}

/* 简化的结果信息 */
.result-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.result-text {
  color: #6b7280;
}

.view-toggle {
  display: flex;
  gap: 2px;
}

.view-btn {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.view-btn:first-child {
  border-radius: 4px 0 0 4px;
}

.view-btn:last-child {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.view-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.view-btn:hover:not(.active) {
  background: #f3f4f6;
}

.commands-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.commands-list.compact .command-item {
  margin-bottom: 4px;
}

.commands-list.detailed .command-item {
  margin-bottom: 8px;
}

.command-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.command-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.command-item.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.command-item.favorite {
  border-left: 3px solid #fbbf24;
}

/* 紧凑模式样式 */
.compact-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.command-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.compact-content .command-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  color: #374151;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.command-item:hover .item-actions {
  opacity: 1;
}

.item-actions .action-btn {
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-actions .action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.item-actions .action-btn.active {
  color: #fbbf24;
}

.compact-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
}

.category-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
}

.server-info,
.time-info {
  font-size: 11px;
}

/* 详细模式样式 */
.detailed-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.detailed-content .command-text {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 14px;
  color: #374151;
  background: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #3b82f6;
  flex: 1;
  min-width: 0;
  word-break: break-all;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.category-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .action-btn {
  padding: 4px 6px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 12px;
}

.action-buttons .action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-buttons .action-btn.active {
  background: #fef3c7;
  border-color: #fbbf24;
  color: #92400e;
}

.command-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-label {
  color: #6b7280;
  font-weight: 500;
}

.meta-value {
  color: #374151;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.command-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  background: #e5e7eb;
  color: #374151;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.tag:hover {
  background: #d1d5db;
}

.command-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  padding: 8px;
  background: #f9fafb;
  border-radius: 4px;
  border-left: 3px solid #e5e7eb;
}

/* 加载更多按钮 */
.load-more-section {
  padding: 16px;
  text-align: center;
}

.load-more-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.load-more-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.load-more-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  padding: 32px;
}

.empty-content {
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 16px;
}

.empty-action {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
}

.empty-action:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

/* 底部操作栏 */
.bottom-actions {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
}

.fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.fab:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.fab.secondary {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
  font-size: 16px;
}

/* 编辑模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.edit-modal {
  background: white;
  border-radius: 16px;
  padding: 24px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #94a3b8;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: rgba(148, 163, 184, 0.1);
  color: #64748b;
}

.modal-body {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.command-display {
  background: #f3f4f6;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.command-display code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 14px;
  color: #374151;
}

.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  resize: vertical;
  min-height: 80px;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  padding: 8px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  min-height: 40px;
  align-items: center;
}

.tag.editable {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #3b82f6;
  color: white;
  border-radius: 12px;
  font-size: 12px;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  font-size: 14px;
  line-height: 1;
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  font-size: 14px;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn.primary {
  background: #3b82f6;
  color: white;
}

.btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn.secondary {
  background: #e5e7eb;
  color: #374151;
}

.btn.secondary:hover {
  background: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-stats {
    justify-content: space-around;
  }

  .advanced-filters {
    grid-template-columns: 1fr;
  }

  .quick-filters {
    justify-content: center;
  }

  .floating-actions {
    bottom: 16px;
    right: 16px;
  }

  .modal-content {
    margin: 16px;
    width: calc(100% - 32px);
  }
}

/* 滚动条样式 */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.history-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}
</style>
