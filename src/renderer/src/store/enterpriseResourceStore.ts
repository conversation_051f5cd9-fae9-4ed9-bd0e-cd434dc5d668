import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import eventBus from '@/utils/eventBus'

// 企业资源类型定义
export interface EnterpriseResource {
  id: string
  backend_id: string
  resource_type: 'host' | 'keychain' | 'snippet'
  name: string
  config_data: any
  environment: 'production' | 'staging' | 'development'
  tags: string[]
  version: number
  checksum?: string
  created_at: string
  updated_at: string
  shared_with?: string[] // 共享给哪些用户/组
  permissions?: {
    read: boolean
    write: boolean
    execute: boolean
    share: boolean
  }
}

// 机房环境数据类型
export interface RoomEnvironmentData {
  roomId: string
  temperature: number
  humidity: number
  powerConsumption: number
  timestamp: string
  alerts?: Array<{
    type: 'temperature' | 'humidity' | 'power'
    severity: 'low' | 'medium' | 'high'
    message: string
  }>
}

// 设备状态数据类型
export interface DeviceStatus {
  deviceId: string
  roomId: string
  name: string
  status: 'online' | 'offline' | 'warning' | 'error'
  lastSeen: string
  metrics?: {
    cpu?: number
    memory?: number
    disk?: number
    network?: number
  }
}

export const useEnterpriseResourceStore = defineStore('enterpriseResource', () => {
  // 状态
  const resources = ref<EnterpriseResource[]>([])
  const selectedResource = ref<EnterpriseResource | null>(null)
  const currentEnvironment = ref<'production' | 'staging' | 'development'>('production')
  const syncStatus = ref<'idle' | 'syncing' | 'completed' | 'failed'>('idle')
  const syncProgress = ref(0)
  
  // 机房管理相关状态
  const roomEnvironments = ref<Map<string, RoomEnvironmentData>>(new Map())
  const deviceStatuses = ref<Map<string, DeviceStatus>>(new Map())
  const roomAlerts = ref<Array<{
    id: string
    roomId: string
    type: 'temperature' | 'humidity' | 'power' | 'device'
    severity: 'low' | 'medium' | 'high'
    message: string
    timestamp: string
    acknowledged: boolean
  }>>([])

  // 计算属性
  const resourcesByType = computed(() => {
    const grouped: Record<string, EnterpriseResource[]> = {
      host: [],
      keychain: [],
      snippet: []
    }
    
    resources.value.forEach(resource => {
      if (grouped[resource.resource_type]) {
        grouped[resource.resource_type].push(resource)
      }
    })
    
    return grouped
  })

  const resourcesByEnvironment = computed(() => {
    return resources.value.filter(resource => resource.environment === currentEnvironment.value)
  })

  const activeAlerts = computed(() => {
    return roomAlerts.value.filter(alert => !alert.acknowledged)
  })

  const criticalAlerts = computed(() => {
    return activeAlerts.value.filter(alert => alert.severity === 'high')
  })

  // Actions
  const addResource = (resource: EnterpriseResource) => {
    resources.value.push(resource)
    eventBus.emit('enterpriseResourceUpdated', { type: resource.resource_type, data: resource })
  }

  const updateResource = (id: string, updates: Partial<EnterpriseResource>) => {
    const index = resources.value.findIndex(r => r.id === id)
    if (index !== -1) {
      resources.value[index] = { ...resources.value[index], ...updates }
      eventBus.emit('enterpriseResourceUpdated', { 
        type: resources.value[index].resource_type, 
        data: resources.value[index] 
      })
    }
  }

  const deleteResource = (id: string) => {
    const resource = resources.value.find(r => r.id === id)
    if (resource) {
      resources.value = resources.value.filter(r => r.id !== id)
      eventBus.emit('enterpriseResourceDeleted', { type: resource.resource_type, id })
    }
  }

  const selectResource = (resource: EnterpriseResource | null) => {
    selectedResource.value = resource
    if (resource) {
      eventBus.emit('enterpriseResourceSelected', { type: resource.resource_type, data: resource })
    }
  }

  const switchEnvironment = (environment: 'production' | 'staging' | 'development') => {
    currentEnvironment.value = environment
    eventBus.emit('enterpriseEnvironmentChanged', { environment })
  }

  const updateSyncStatus = (status: 'idle' | 'syncing' | 'completed' | 'failed', progress = 0) => {
    syncStatus.value = status
    syncProgress.value = progress
    eventBus.emit('enterpriseSyncStatusChanged', { status, progress })
  }

  // 机房管理相关方法
  const updateRoomEnvironment = (data: RoomEnvironmentData) => {
    roomEnvironments.value.set(data.roomId, data)
    eventBus.emit('roomEnvironmentUpdated', { roomId: data.roomId, data: {
      temperature: data.temperature,
      humidity: data.humidity,
      powerConsumption: data.powerConsumption
    }})
    
    // 检查是否需要触发告警
    checkEnvironmentAlerts(data)
  }

  const updateDeviceStatus = (device: DeviceStatus) => {
    deviceStatuses.value.set(device.deviceId, device)
    eventBus.emit('roomDeviceStatusChanged', {
      roomId: device.roomId,
      deviceId: device.deviceId,
      status: device.status
    })
  }

  const addRoomAlert = (alert: {
    roomId: string
    type: 'temperature' | 'humidity' | 'power' | 'device'
    severity: 'low' | 'medium' | 'high'
    message: string
  }) => {
    const newAlert = {
      id: Date.now().toString(),
      ...alert,
      timestamp: new Date().toISOString(),
      acknowledged: false
    }
    
    roomAlerts.value.push(newAlert)
    eventBus.emit('roomAlertTriggered', alert)
    eventBus.emit('enterpriseAlertTriggered', {
      type: alert.severity === 'high' ? 'error' : 'warning',
      message: alert.message,
      module: 'room-management'
    })
  }

  const acknowledgeAlert = (alertId: string) => {
    const alert = roomAlerts.value.find(a => a.id === alertId)
    if (alert) {
      alert.acknowledged = true
    }
  }

  const checkEnvironmentAlerts = (data: RoomEnvironmentData) => {
    // 温度告警检查
    if (data.temperature > 30) {
      addRoomAlert({
        roomId: data.roomId,
        type: 'temperature',
        severity: data.temperature > 35 ? 'high' : 'medium',
        message: `机房温度过高: ${data.temperature}°C`
      })
    }
    
    // 湿度告警检查
    if (data.humidity > 70 || data.humidity < 30) {
      addRoomAlert({
        roomId: data.roomId,
        type: 'humidity',
        severity: data.humidity > 80 || data.humidity < 20 ? 'high' : 'medium',
        message: `机房湿度异常: ${data.humidity}%`
      })
    }
    
    // 功耗告警检查
    if (data.powerConsumption > 85) {
      addRoomAlert({
        roomId: data.roomId,
        type: 'power',
        severity: data.powerConsumption > 95 ? 'high' : 'medium',
        message: `机房功耗过高: ${data.powerConsumption}kW`
      })
    }
  }

  // 模块间数据请求处理
  const handleDataRequest = (request: { module: string; dataType: string; params?: any }) => {
    let responseData: any = null
    
    switch (request.dataType) {
      case 'resources':
        responseData = request.params?.type 
          ? resourcesByType.value[request.params.type] 
          : resources.value
        break
      case 'roomEnvironments':
        responseData = request.params?.roomId 
          ? roomEnvironments.value.get(request.params.roomId)
          : Array.from(roomEnvironments.value.values())
        break
      case 'deviceStatuses':
        responseData = request.params?.roomId
          ? Array.from(deviceStatuses.value.values()).filter(d => d.roomId === request.params.roomId)
          : Array.from(deviceStatuses.value.values())
        break
      case 'alerts':
        responseData = request.params?.roomId
          ? roomAlerts.value.filter(a => a.roomId === request.params.roomId)
          : roomAlerts.value
        break
    }
    
    eventBus.emit('enterpriseModuleDataResponse', {
      module: request.module,
      dataType: request.dataType,
      data: responseData
    })
  }

  // 初始化事件监听
  const initializeEventListeners = () => {
    eventBus.on('enterpriseModuleDataRequest', handleDataRequest)
  }

  return {
    // 状态
    resources,
    selectedResource,
    currentEnvironment,
    syncStatus,
    syncProgress,
    roomEnvironments,
    deviceStatuses,
    roomAlerts,
    
    // 计算属性
    resourcesByType,
    resourcesByEnvironment,
    activeAlerts,
    criticalAlerts,
    
    // 方法
    addResource,
    updateResource,
    deleteResource,
    selectResource,
    switchEnvironment,
    updateSyncStatus,
    updateRoomEnvironment,
    updateDeviceStatus,
    addRoomAlert,
    acknowledgeAlert,
    initializeEventListeners
  }
})
