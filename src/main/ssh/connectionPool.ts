/**
 * SSH连接池管理器
 * 实现连接复用、自动清理和性能优化
 */

import { EventEmitter } from 'events'

export interface ConnectionInfo {
  id: string
  host: string
  port: number
  username: string
  lastUsed: number
  isActive: boolean
  connectionCount: number
  connection: any // SSH连接对象
}

export interface PoolConfig {
  maxConnections: number
  maxIdleTime: number // 毫秒
  cleanupInterval: number // 毫秒
  maxConnectionsPerHost: number
}

export interface PoolMetrics {
  totalConnections: number
  activeConnections: number
  idleConnections: number
  totalRequests: number
  cacheHits: number
  cacheMisses: number
}

export class SSHConnectionPool extends EventEmitter {
  private connections = new Map<string, ConnectionInfo>()
  private hostConnections = new Map<string, Set<string>>()
  private config: PoolConfig
  private metrics: PoolMetrics
  private cleanupTimer: NodeJS.Timeout | null = null

  constructor(config: Partial<PoolConfig> = {}) {
    super()

    this.config = {
      maxConnections: 50,
      maxIdleTime: 30 * 60 * 1000, // 30分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      maxConnectionsPerHost: 10,
      ...config
    }

    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0
    }

    this.startCleanupTimer()
  }

  /**
   * 获取或创建连接
   */
  async getConnection(connectionKey: string, createFn: () => Promise<any>): Promise<any> {
    this.metrics.totalRequests++

    // 检查是否已存在连接
    const existingConnection = this.connections.get(connectionKey)
    if (existingConnection && existingConnection.isActive) {
      existingConnection.lastUsed = Date.now()
      existingConnection.connectionCount++
      this.metrics.cacheHits++

      console.log(`[ConnectionPool] 复用连接: ${connectionKey}`)
      return existingConnection.connection
    }

    this.metrics.cacheMisses++

    // 检查连接数限制
    if (this.connections.size >= this.config.maxConnections) {
      await this.cleanupOldestConnections(1)
    }

    // 检查单主机连接数限制
    const hostKey = this.extractHostKey(connectionKey)
    const hostConnections = this.hostConnections.get(hostKey) || new Set()
    if (hostConnections.size >= this.config.maxConnectionsPerHost) {
      await this.cleanupHostConnections(hostKey, 1)
    }

    // 创建新连接
    try {
      console.log(`[ConnectionPool] 创建新连接: ${connectionKey}`)
      const connection = await createFn()

      const connectionInfo: ConnectionInfo = {
        id: connectionKey,
        host: hostKey,
        port: this.extractPort(connectionKey),
        username: this.extractUsername(connectionKey),
        lastUsed: Date.now(),
        isActive: true,
        connectionCount: 1,
        connection
      }

      this.connections.set(connectionKey, connectionInfo)

      // 更新主机连接映射
      if (!this.hostConnections.has(hostKey)) {
        this.hostConnections.set(hostKey, new Set())
      }
      this.hostConnections.get(hostKey)!.add(connectionKey)

      this.updateMetrics()
      this.emit('connectionCreated', connectionInfo)

      return connection
    } catch (error) {
      console.error(`[ConnectionPool] 创建连接失败: ${connectionKey}`, error)
      throw error
    }
  }

  /**
   * 释放连接（标记为空闲）
   */
  releaseConnection(connectionKey: string): void {
    const connection = this.connections.get(connectionKey)
    if (connection) {
      connection.lastUsed = Date.now()
      console.log(`[ConnectionPool] 释放连接: ${connectionKey}`)
      this.updateMetrics()
      this.emit('connectionReleased', connection)
    }
  }

  /**
   * 关闭连接
   */
  async closeConnection(connectionKey: string): Promise<void> {
    const connectionInfo = this.connections.get(connectionKey)
    if (!connectionInfo) return

    try {
      // 关闭SSH连接
      if (connectionInfo.connection && typeof connectionInfo.connection.end === 'function') {
        connectionInfo.connection.end()
      }

      // 从池中移除
      this.connections.delete(connectionKey)

      // 更新主机连接映射
      const hostConnections = this.hostConnections.get(connectionInfo.host)
      if (hostConnections) {
        hostConnections.delete(connectionKey)
        if (hostConnections.size === 0) {
          this.hostConnections.delete(connectionInfo.host)
        }
      }

      this.updateMetrics()
      this.emit('connectionClosed', connectionInfo)

      console.log(`[ConnectionPool] 关闭连接: ${connectionKey}`)
    } catch (error) {
      console.error(`[ConnectionPool] 关闭连接失败: ${connectionKey}`, error)
    }
  }

  /**
   * 清理空闲连接
   */
  async cleanupIdleConnections(): Promise<number> {
    const now = Date.now()
    const connectionsToClose: string[] = []

    for (const [key, connection] of this.connections) {
      if (now - connection.lastUsed > this.config.maxIdleTime) {
        connectionsToClose.push(key)
      }
    }

    console.log(`[ConnectionPool] 清理 ${connectionsToClose.length} 个空闲连接`)

    for (const key of connectionsToClose) {
      await this.closeConnection(key)
    }

    return connectionsToClose.length
  }

  /**
   * 清理最旧的连接
   */
  private async cleanupOldestConnections(count: number): Promise<void> {
    const connections = Array.from(this.connections.entries())
      .sort(([, a], [, b]) => a.lastUsed - b.lastUsed)
      .slice(0, count)

    for (const [key] of connections) {
      await this.closeConnection(key)
    }
  }

  /**
   * 清理指定主机的连接
   */
  private async cleanupHostConnections(host: string, count: number): Promise<void> {
    const hostConnections = this.hostConnections.get(host)
    if (!hostConnections) return

    const connectionsToClose = Array.from(hostConnections)
      .map((key) => ({ key, connection: this.connections.get(key)! }))
      .sort((a, b) => a.connection.lastUsed - b.connection.lastUsed)
      .slice(0, count)

    for (const { key } of connectionsToClose) {
      await this.closeConnection(key)
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(): void {
    this.metrics.totalConnections = this.connections.size
    this.metrics.activeConnections = Array.from(this.connections.values()).filter((conn) => conn.isActive).length
    this.metrics.idleConnections = this.metrics.totalConnections - this.metrics.activeConnections
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PoolMetrics {
    this.updateMetrics()
    return { ...this.metrics }
  }

  /**
   * 获取连接信息
   */
  getConnectionInfo(): ConnectionInfo[] {
    return Array.from(this.connections.values())
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(async () => {
      try {
        await this.cleanupIdleConnections()
      } catch (error) {
        console.error('[ConnectionPool] 清理定时器错误:', error)
      }
    }, this.config.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  /**
   * 关闭连接池
   */
  async close(): Promise<void> {
    this.stopCleanupTimer()

    const connectionKeys = Array.from(this.connections.keys())
    for (const key of connectionKeys) {
      await this.closeConnection(key)
    }

    this.emit('poolClosed')
    console.log('[ConnectionPool] 连接池已关闭')
  }

  /**
   * 提取主机键
   */
  private extractHostKey(connectionKey: string): string {
    // 假设connectionKey格式为: username@host:port
    const match = connectionKey.match(/^(.+)@(.+):(\d+)$/)
    return match ? match[2] : connectionKey
  }

  /**
   * 提取端口
   */
  private extractPort(connectionKey: string): number {
    const match = connectionKey.match(/:(\d+)$/)
    return match ? parseInt(match[1]) : 22
  }

  /**
   * 提取用户名
   */
  private extractUsername(connectionKey: string): string {
    const match = connectionKey.match(/^(.+)@/)
    return match ? match[1] : 'unknown'
  }
}

// 单例连接池实例
export const sshConnectionPool = new SSHConnectionPool()
