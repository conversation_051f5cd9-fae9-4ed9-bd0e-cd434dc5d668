<template>
  <div class="migration-panel">
    <div class="panel-header">
      <h3>数据迁移管理</h3>
      <p class="description">将现有模块数据迁移到统一存储方案</p>
    </div>

    <!-- 迁移状态概览 -->
    <div class="migration-overview">
      <div class="status-card">
        <div class="status-icon" :class="{ success: migrationStats.totalMigrated > 0 }">
          <CheckCircle v-if="migrationStats.totalMigrated > 0" />
          <AlertCircle v-else />
        </div>
        <div class="status-info">
          <h4>迁移状态</h4>
          <p>已迁移: {{ migrationStats.totalMigrated }} 条数据</p>
          <p v-if="migrationStats.totalErrors > 0" class="error">
            错误: {{ migrationStats.totalErrors }} 个
          </p>
        </div>
      </div>
    </div>

    <!-- 模块迁移列表 -->
    <div class="migration-modules">
      <h4>模块迁移</h4>
      
      <!-- 固定资产管理 -->
      <div class="module-card">
        <div class="module-info">
          <h5>固定资产管理</h5>
          <p>迁移 assetManagementStore 数据到统一服务</p>
          <div class="module-status">
            <span v-if="assetMigrationStatus === 'completed'" class="status success">
              ✅ 已完成 ({{ assetMigrationResult?.migratedCount || 0 }} 条)
            </span>
            <span v-else-if="assetMigrationStatus === 'running'" class="status running">
              🔄 迁移中... ({{ assetProgress?.percentage || 0 }}%)
            </span>
            <span v-else-if="assetMigrationStatus === 'failed'" class="status error">
              ❌ 迁移失败
            </span>
            <span v-else class="status pending">⏳ 待迁移</span>
          </div>
        </div>
        <div class="module-actions">
          <button 
            @click="migrateAssetData" 
            :disabled="assetMigrationStatus === 'running'"
            class="btn-migrate"
          >
            {{ assetMigrationStatus === 'running' ? '迁移中...' : '开始迁移' }}
          </button>
        </div>
      </div>

      <!-- 网络设备管理 -->
      <div class="module-card">
        <div class="module-info">
          <h5>网络设备管理</h5>
          <p>迁移网络设备数据到统一服务</p>
          <div class="module-status">
            <span v-if="networkMigrationStatus === 'completed'" class="status success">
              ✅ 已完成 ({{ networkMigrationResult?.migratedCount || 0 }} 条)
            </span>
            <span v-else-if="networkMigrationStatus === 'running'" class="status running">
              🔄 迁移中... ({{ networkProgress?.percentage || 0 }}%)
            </span>
            <span v-else-if="networkMigrationStatus === 'failed'" class="status error">
              ❌ 迁移失败
            </span>
            <span v-else class="status pending">⏳ 待迁移</span>
          </div>
        </div>
        <div class="module-actions">
          <button 
            @click="migrateNetworkData" 
            :disabled="networkMigrationStatus === 'running'"
            class="btn-migrate"
          >
            {{ networkMigrationStatus === 'running' ? '迁移中...' : '开始迁移' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 批量操作 -->
    <div class="batch-actions">
      <button 
        @click="migrateAllData" 
        :disabled="isMigrating"
        class="btn-batch"
      >
        {{ isMigrating ? '批量迁移中...' : '批量迁移所有模块' }}
      </button>
      
      <button 
        @click="clearMigrationHistory" 
        class="btn-clear"
      >
        清除迁移历史
      </button>
    </div>

    <!-- 迁移历史 -->
    <div class="migration-history" v-if="migrationHistory.length > 0">
      <h4>迁移历史</h4>
      <div class="history-list">
        <div 
          v-for="record in migrationHistory" 
          :key="record.timestamp"
          class="history-item"
          :class="{ success: record.success, error: !record.success }"
        >
          <div class="history-info">
            <h6>{{ record.module }}</h6>
            <p>{{ formatDate(record.timestamp) }}</p>
            <p v-if="record.success">
              成功迁移 {{ record.migratedCount }} 条数据
            </p>
            <p v-else class="error-text">
              迁移失败: {{ record.errors.join(', ') }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { CheckCircle, AlertCircle } from 'lucide-vue-next'
import dataMigrationService, { type MigrationResult, type MigrationProgress } from '../services/dataMigrationService'
import eventBus from '../utils/eventBus'

// 响应式数据
const migrationHistory = ref<MigrationResult[]>([])
const currentMigrations = ref<MigrationProgress[]>([])
const isMigrating = ref(false)

// 各模块迁移状态
const assetMigrationStatus = ref<'pending' | 'running' | 'completed' | 'failed'>('pending')
const networkMigrationStatus = ref<'pending' | 'running' | 'completed' | 'failed'>('pending')

// 迁移结果
const assetMigrationResult = ref<MigrationResult | null>(null)
const networkMigrationResult = ref<MigrationResult | null>(null)

// 迁移进度
const assetProgress = computed(() => 
  currentMigrations.value.find(m => m.module === 'AssetManagementStore')
)
const networkProgress = computed(() => 
  currentMigrations.value.find(m => m.module === 'NetworkDevices')
)

// 迁移统计
const migrationStats = computed(() => {
  const totalMigrated = migrationHistory.value.reduce((sum, r) => sum + r.migratedCount, 0)
  const totalErrors = migrationHistory.value.reduce((sum, r) => sum + r.errors.length, 0)
  return { totalMigrated, totalErrors }
})

// 迁移方法
const migrateAssetData = async () => {
  assetMigrationStatus.value = 'running'
  try {
    const result = await dataMigrationService.migrateAssetManagementStoreData()
    assetMigrationResult.value = result
    assetMigrationStatus.value = result.success ? 'completed' : 'failed'
  } catch (error) {
    console.error('资产数据迁移失败:', error)
    assetMigrationStatus.value = 'failed'
  }
}

const migrateNetworkData = async () => {
  networkMigrationStatus.value = 'running'
  try {
    // 这里需要实现网络设备迁移方法
    console.log('网络设备迁移功能待实现')
    networkMigrationStatus.value = 'completed'
  } catch (error) {
    console.error('网络设备迁移失败:', error)
    networkMigrationStatus.value = 'failed'
  }
}

const migrateAllData = async () => {
  isMigrating.value = true
  try {
    await migrateAssetData()
    await migrateNetworkData()
  } finally {
    isMigrating.value = false
  }
}

const clearMigrationHistory = () => {
  dataMigrationService.clearMigrationHistory()
  migrationHistory.value = []
  assetMigrationResult.value = null
  networkMigrationResult.value = null
  assetMigrationStatus.value = 'pending'
  networkMigrationStatus.value = 'pending'
}

// 事件监听
const handleMigrationProgress = (progress: MigrationProgress) => {
  const index = currentMigrations.value.findIndex(m => m.module === progress.module)
  if (index >= 0) {
    currentMigrations.value[index] = progress
  } else {
    currentMigrations.value.push(progress)
  }
}

const handleMigrationComplete = (result: MigrationResult) => {
  migrationHistory.value.push(result)
  
  // 更新对应模块状态
  if (result.module === 'AssetManagementStore') {
    assetMigrationResult.value = result
    assetMigrationStatus.value = result.success ? 'completed' : 'failed'
  }
}

// 工具函数
const formatDate = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 加载迁移历史
  migrationHistory.value = dataMigrationService.getMigrationHistory()
  currentMigrations.value = dataMigrationService.getCurrentMigrations()
  
  // 监听迁移事件
  eventBus.on('migrationProgress', handleMigrationProgress)
  eventBus.on('migrationComplete', handleMigrationComplete)
})

onUnmounted(() => {
  eventBus.off('migrationProgress', handleMigrationProgress)
  eventBus.off('migrationComplete', handleMigrationComplete)
})
</script>

<style scoped>
.migration-panel {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  max-width: 800px;
  margin: 0 auto;
}

.panel-header {
  text-align: center;
  margin-bottom: 30px;
}

.panel-header h3 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.description {
  color: #6c757d;
  margin: 0;
}

.migration-overview {
  margin-bottom: 30px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: #ffc107;
  color: white;
}

.status-icon.success {
  background: #28a745;
}

.status-info h4 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.status-info p {
  margin: 4px 0;
  color: #6c757d;
}

.status-info .error {
  color: #dc3545;
}

.migration-modules {
  margin-bottom: 30px;
}

.migration-modules h4 {
  margin-bottom: 16px;
  color: #2c3e50;
}

.module-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 16px;
}

.module-info h5 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.module-info p {
  margin: 0 0 12px 0;
  color: #6c757d;
  font-size: 14px;
}

.module-status .status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.success {
  background: #d4edda;
  color: #155724;
}

.status.running {
  background: #d1ecf1;
  color: #0c5460;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.status.pending {
  background: #fff3cd;
  color: #856404;
}

.btn-migrate, .btn-batch, .btn-clear {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-migrate {
  background: #007bff;
  color: white;
}

.btn-migrate:hover:not(:disabled) {
  background: #0056b3;
}

.btn-migrate:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-batch {
  background: #28a745;
  color: white;
  margin-right: 12px;
}

.btn-batch:hover:not(:disabled) {
  background: #1e7e34;
}

.btn-clear {
  background: #6c757d;
  color: white;
}

.btn-clear:hover {
  background: #545b62;
}

.batch-actions {
  text-align: center;
  margin-bottom: 30px;
}

.migration-history h4 {
  margin-bottom: 16px;
  color: #2c3e50;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 16px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
  border-left: 4px solid #6c757d;
}

.history-item.success {
  border-left-color: #28a745;
}

.history-item.error {
  border-left-color: #dc3545;
}

.history-info h6 {
  margin: 0 0 8px 0;
  color: #2c3e50;
}

.history-info p {
  margin: 4px 0;
  color: #6c757d;
  font-size: 14px;
}

.error-text {
  color: #dc3545 !important;
}
</style>
