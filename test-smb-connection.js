/**
 * SMB连接测试脚本
 * 用于独立测试SMB连接功能
 */

const SMB2 = require('smb2')

async function testSMBConnection() {
  console.log('开始SMB连接测试...')

  // 测试配置（请根据实际情况修改）
  const config = {
    host: '************',
    share: 'it部',
    username: '<EMAIL>',
    password: 'RSgz@lbq0925!',
    domain: 'brbiotech.com',
    port: 445
  }

  console.log('SMB配置:', {
    host: config.host,
    share: config.share,
    username: config.username,
    domain: config.domain,
    port: config.port
  })

  let client = null

  try {
    // 创建SMB客户端
    client = new SMB2({
      share: `\\\\${config.host}\\${config.share}`,
      domain: config.domain,
      username: config.username,
      password: config.password,
      port: config.port,
      packetConcurrency: 20,
      autoCloseTimeout: 0
    })

    console.log('SMB客户端创建成功')

    // 测试连接 - 尝试列出根目录
    const files = await new Promise((resolve, reject) => {
      client.readdir('/', (err, files) => {
        if (err) {
          reject(err)
        } else {
          resolve(files)
        }
      })
    })

    console.log('SMB连接测试成功！')
    console.log('根目录文件列表:', files.map((f) => f.Filename).slice(0, 5))

    // 测试写入文件
    const testContent = JSON.stringify(
      {
        test: true,
        timestamp: new Date().toISOString(),
        message: 'SMB连接测试成功'
      },
      null,
      2
    )

    const testFileName = '/chaterm-test.json'

    await new Promise((resolve, reject) => {
      client.writeFile(testFileName, testContent, (err) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })

    console.log(`测试文件 ${testFileName} 写入成功！`)

    // 验证文件是否存在
    const fileExists = await new Promise((resolve, reject) => {
      client.stat(testFileName, (err, stats) => {
        if (err) {
          resolve(false)
        } else {
          resolve(true)
        }
      })
    })

    if (fileExists) {
      console.log('文件验证成功，SMB写入功能正常！')
    } else {
      console.log('警告：文件写入后无法验证存在')
    }

    return {
      success: true,
      message: 'SMB连接和写入测试成功'
    }
  } catch (error) {
    console.error('SMB连接测试失败:', error.message)

    // 提供详细的错误分析
    if (error.message.includes('ECONNREFUSED')) {
      console.log('错误分析：连接被拒绝，请检查：')
      console.log('1. SMB服务器是否运行')
      console.log('2. 端口445是否开放')
      console.log('3. 防火墙设置')
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('错误分析：主机名解析失败，请检查：')
      console.log('1. 主机地址是否正确')
      console.log('2. 网络连接是否正常')
    } else if (error.message.includes('Authentication')) {
      console.log('错误分析：认证失败，请检查：')
      console.log('1. 用户名和密码是否正确')
      console.log('2. 域名设置是否正确')
      console.log('3. 账户是否有SMB访问权限')
    } else if (error.message.includes('Access denied')) {
      console.log('错误分析：访问被拒绝，请检查：')
      console.log('1. 共享权限设置')
      console.log('2. 用户是否有访问该共享的权限')
    }

    return {
      success: false,
      error: error.message
    }
  } finally {
    // 清理连接
    if (client) {
      try {
        client.close()
        console.log('SMB连接已断开')
      } catch (err) {
        console.log('断开连接时出错:', err.message)
      }
    }
  }
}

// 运行测试
if (require.main === module) {
  testSMBConnection()
    .then((result) => {
      console.log('\n测试结果:', result)
      process.exit(result.success ? 0 : 1)
    })
    .catch((error) => {
      console.error('\n测试异常:', error)
      process.exit(1)
    })
}

module.exports = { testSMBConnection }
