<template>
  <div
    v-if="visible"
    class="context-menu"
    :style="{ top: position.y + 'px', left: position.x + 'px' }"
    @click="handleClose"
  >
    <div
      class="context-menu-item"
      @click.stop="handleConnect"
    >
      <ApiOutlined class="context-menu-icon" />
      <span>连接</span>
    </div>

    <div
      class="context-menu-item"
      @click.stop="handleEdit"
    >
      <EditOutlined class="context-menu-icon" />
      <span>编辑</span>
    </div>

    <div
      class="context-menu-item favorite-item"
      @click.stop="handleFavoriteClick"
      @mouseenter="showSubmenu = true"
      @mouseleave="showSubmenu = false"
    >
      <StarOutlined class="context-menu-icon" />
      <span>收藏到</span>
      <RightOutlined class="context-menu-arrow" />

      <!-- 收藏子菜单 -->
      <div
        v-if="showSubmenu"
        class="favorite-submenu"
        @mouseenter="showSubmenu = true"
        @mouseleave="showSubmenu = false"
      >
        <div
          v-for="folder in customFolders"
          :key="folder.uuid"
          class="context-menu-item"
          @click.stop="handleMoveToFolder(folder.uuid)"
        >
          <FolderOutlined class="context-menu-icon" />
          <span>{{ folder.name }}</span>
        </div>
        <div
          v-if="customFolders.length === 0"
          class="context-menu-item disabled"
        >
          <span>暂无收藏夹</span>
        </div>
        <div class="context-menu-divider"></div>
        <div
          class="context-menu-item"
          @click.stop="handleCreateFolder"
        >
          <PlusOutlined class="context-menu-icon" />
          <span>新建收藏夹</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ApiOutlined, EditOutlined, StarOutlined, RightOutlined, FolderOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Props
interface Props {
  visible: boolean
  position: { x: number; y: number }
  asset: any | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  connect: []
  edit: []
  createFolder: []
}>()

// 响应式数据
const showSubmenu = ref(false)
const customFolders = ref<any[]>([])

// Methods
const handleClose = () => {
  showSubmenu.value = false
  emit('close')
}

const handleConnect = () => {
  emit('connect')
}

const handleEdit = () => {
  emit('edit')
}

const handleFavoriteClick = () => {
  loadCustomFolders()
}

const handleCreateFolder = () => {
  emit('createFolder')
}

const loadCustomFolders = async () => {
  try {
    const result = await window.api.getCustomFolders()
    if (result && result.data && result.data.message === 'success') {
      customFolders.value = result.data.folders || []
    }
  } catch (error) {
    console.error('加载自定义文件夹失败:', error)
  }
}

const handleMoveToFolder = async (folderUuid: string) => {
  if (!props.asset) {
    console.error('没有选中的资产')
    message.error('没有选中的资产')
    return
  }

  try {
    console.log('开始收藏企业资产:', {
      asset: props.asset,
      folderUuid: folderUuid
    })

    // 使用新的企业资产收藏API
    const result = await window.api.moveEnterpriseAssetToFolder({
      folderUuid: folderUuid,
      assetData: {
        name: props.asset.name,
        host: props.asset.host,
        port: props.asset.port,
        type: props.asset.type,
        organization: props.asset.organization,
        department: props.asset.department,
        businessType: props.asset.businessType,
        region: props.asset.region,
        description: props.asset.description
      }
    })

    console.log('企业资产收藏API返回结果:', result)

    if (result && result.data && result.data.message === 'success') {
      if (result.data.changes === 0 && result.data.info) {
        message.info(result.data.info)
      } else {
        message.success('收藏成功')
      }
      console.log('企业资产收藏成功')

      // 触发左侧栏刷新
      const eventBus = await import('@/utils/eventBus')
      eventBus.default.emit('LocalAssetMenu')
    } else {
      const errorMsg = result?.data?.error || '未知错误'
      console.error('企业资产收藏失败:', errorMsg)
      message.error(`收藏失败: ${errorMsg}`)
    }

    handleClose()
  } catch (error) {
    console.error('企业资产收藏过程中出错:', error)
    message.error(`收藏失败: ${error.message || '网络错误'}`)
    handleClose()
  }
}

onMounted(() => {
  loadCustomFolders()
})
</script>

<style lang="less" scoped>
.context-menu {
  position: fixed;
  z-index: 1000;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  padding: 4px 0;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.2s;
  position: relative;

  &:hover {
    background: var(--bg-color-hover);
  }

  &.disabled {
    color: var(--text-color-tertiary);
    cursor: not-allowed;
  }

  &.favorite-item {
    position: relative;
  }
}

.context-menu-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.context-menu-arrow {
  margin-left: auto;
  font-size: 12px;
  color: var(--text-color-tertiary);
}

.favorite-submenu {
  position: absolute;
  left: 100%;
  top: 0;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 140px;
  padding: 4px 0;
  z-index: 1001;
}

.context-menu-divider {
  height: 1px;
  background: var(--border-color);
  margin: 4px 0;
}
</style>
