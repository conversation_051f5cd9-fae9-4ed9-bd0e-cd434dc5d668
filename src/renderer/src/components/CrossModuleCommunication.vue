<template>
  <div class="cross-module-communication">
    <div class="panel-header">
      <h3>跨模块数据通信演示</h3>
      <p class="description">展示统一数据服务的跨模块数据共享和通信能力</p>
    </div>

    <!-- 数据统计概览 -->
    <div class="data-overview">
      <div class="stat-card">
        <h4>资产管理</h4>
        <div class="stat-number">{{ assetStats.total }}</div>
        <div class="stat-label">总资产数</div>
        <div class="stat-details">
          <span>在线: {{ assetStats.online }}</span>
          <span>维护: {{ assetStats.maintenance }}</span>
        </div>
      </div>

      <div class="stat-card">
        <h4>网络设备</h4>
        <div class="stat-number">{{ networkStats.total }}</div>
        <div class="stat-label">总设备数</div>
        <div class="stat-details">
          <span>在线: {{ networkStats.online }}</span>
          <span>离线: {{ networkStats.offline }}</span>
        </div>
      </div>

      <div class="stat-card">
        <h4>机房管理</h4>
        <div class="stat-number">{{ roomStats.total }}</div>
        <div class="stat-label">总机房数</div>
        <div class="stat-details">
          <span>正常: {{ roomStats.normal }}</span>
          <span>告警: {{ roomStats.warning }}</span>
        </div>
      </div>

      <div class="stat-card">
        <h4>主机管理</h4>
        <div class="stat-number">{{ hostStats.total }}</div>
        <div class="stat-label">总主机数</div>
        <div class="stat-details">
          <span>在线: {{ hostStats.online }}</span>
          <span>离线: {{ hostStats.offline }}</span>
        </div>
      </div>
    </div>

    <!-- 实时事件日志 -->
    <div class="event-log">
      <h4>实时数据变更事件</h4>
      <div class="log-container">
        <div 
          v-for="event in recentEvents" 
          :key="event.id"
          class="log-item"
          :class="event.action"
        >
          <div class="log-time">{{ formatTime(event.timestamp) }}</div>
          <div class="log-content">
            <span class="log-module">{{ getModuleLabel(event.module) }}</span>
            <span class="log-action">{{ getActionLabel(event.action) }}</span>
            <span class="log-data">{{ getDataSummary(event.data) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 跨模块关联数据 -->
    <div class="cross-module-data">
      <h4>跨模块关联数据</h4>
      
      <!-- 资产与网络设备关联 -->
      <div class="relation-section">
        <h5>资产-网络设备关联</h5>
        <div class="relation-list">
          <div 
            v-for="relation in assetNetworkRelations" 
            :key="relation.id"
            class="relation-item"
          >
            <div class="relation-asset">
              <strong>{{ relation.assetName }}</strong>
              <span class="asset-type">{{ relation.assetCategory }}</span>
            </div>
            <div class="relation-arrow">→</div>
            <div class="relation-network">
              <strong>{{ relation.networkName }}</strong>
              <span class="network-ip">{{ relation.networkIp }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 机房设备分布 -->
      <div class="relation-section">
        <h5>机房设备分布</h5>
        <div class="room-distribution">
          <div 
            v-for="room in roomDeviceDistribution" 
            :key="room.roomName"
            class="room-item"
          >
            <div class="room-name">{{ room.roomName }}</div>
            <div class="room-devices">
              <span class="device-count">资产: {{ room.assetCount }}</span>
              <span class="device-count">网络设备: {{ room.networkCount }}</span>
              <span class="device-count">主机: {{ room.hostCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <button @click="refreshAllData" class="btn-refresh">
        刷新所有数据
      </button>
      <button @click="clearEventLog" class="btn-clear">
        清除事件日志
      </button>
      <button @click="simulateDataChange" class="btn-simulate">
        模拟数据变更
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import unifiedDataService from '../services/unifiedEnterpriseDataService'
import eventBus from '../utils/eventBus'

// 响应式数据
const recentEvents = ref<any[]>([])
const eventIdCounter = ref(0)

// 数据统计
const assetStats = computed(() => {
  const assets = unifiedDataService.getAssets()
  return {
    total: assets.length,
    online: assets.filter(a => a.status === 'online' || a.status === 'normal').length,
    maintenance: assets.filter(a => a.status === 'maintenance').length
  }
})

const networkStats = computed(() => {
  const devices = unifiedDataService.getNetworkDevices()
  return {
    total: devices.length,
    online: devices.filter(d => d.status === 'online').length,
    offline: devices.filter(d => d.status === 'offline').length
  }
})

const roomStats = computed(() => {
  const rooms = unifiedDataService.getRooms()
  return {
    total: rooms.length,
    normal: rooms.filter(r => r.status === 'normal').length,
    warning: rooms.filter(r => r.status === 'warning').length
  }
})

const hostStats = computed(() => {
  const hosts = unifiedDataService.getHosts()
  return {
    total: hosts.length,
    online: hosts.filter(h => h.status === 'online').length,
    offline: hosts.filter(h => h.status === 'offline').length
  }
})

// 跨模块关联数据
const assetNetworkRelations = computed(() => {
  const assets = unifiedDataService.getAssets()
  const networks = unifiedDataService.getNetworkDevices()
  
  return assets
    .filter(asset => asset.ipAddress)
    .map(asset => {
      const matchedNetwork = networks.find(net => net.ip === asset.ipAddress)
      if (matchedNetwork) {
        return {
          id: `${asset.id}-${matchedNetwork.id}`,
          assetName: asset.name,
          assetCategory: asset.category,
          networkName: matchedNetwork.name,
          networkIp: matchedNetwork.ip
        }
      }
      return null
    })
    .filter(Boolean)
    .slice(0, 5) // 只显示前5个关联
})

const roomDeviceDistribution = computed(() => {
  const rooms = unifiedDataService.getRooms()
  const assets = unifiedDataService.getAssets()
  const networks = unifiedDataService.getNetworkDevices()
  const hosts = unifiedDataService.getHosts()
  
  return rooms.map(room => ({
    roomName: room.name,
    assetCount: assets.filter(a => a.location?.includes(room.name)).length,
    networkCount: networks.filter(n => n.location?.includes(room.name)).length,
    hostCount: hosts.filter(h => h.location?.includes(room.name)).length
  }))
})

// 事件处理
const handleEnterpriseDataUpdate = (eventData: any) => {
  const newEvent = {
    id: ++eventIdCounter.value,
    ...eventData
  }
  
  recentEvents.value.unshift(newEvent)
  
  // 只保留最近20条事件
  if (recentEvents.value.length > 20) {
    recentEvents.value = recentEvents.value.slice(0, 20)
  }
}

// 工具函数
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

const getModuleLabel = (module: string) => {
  const labels: Record<string, string> = {
    'assets': '资产管理',
    'networkDevices': '网络设备',
    'rooms': '机房管理',
    'hosts': '主机管理',
    'monitorTargets': '监控目标'
  }
  return labels[module] || module
}

const getActionLabel = (action: string) => {
  const labels: Record<string, string> = {
    'add': '新增',
    'update': '更新',
    'delete': '删除'
  }
  return labels[action] || action
}

const getDataSummary = (data: any) => {
  if (!data) return ''
  return data.name || data.ip || data.id || '未知'
}

// 操作方法
const refreshAllData = () => {
  // 触发数据刷新事件
  eventBus.emit('refreshAllData', { timestamp: new Date().toISOString() })
  
  // 添加刷新事件到日志
  handleEnterpriseDataUpdate({
    module: 'system',
    action: 'refresh',
    data: { message: '刷新所有数据' },
    timestamp: new Date().toISOString()
  })
}

const clearEventLog = () => {
  recentEvents.value = []
}

const simulateDataChange = () => {
  // 模拟添加一个测试资产
  const testAsset = {
    name: `测试资产-${Date.now()}`,
    type: 'asset' as const,
    status: 'normal' as const,
    assetNumber: `TEST-${Date.now()}`,
    category: 'computer' as const,
    model: '测试型号',
    assignedTo: '测试用户',
    purchaseDate: new Date().toISOString().split('T')[0],
    purchasePrice: 1000,
    description: '这是一个测试资产，用于演示跨模块通信',
    tags: ['测试', '演示']
  }
  
  unifiedDataService.addAsset(testAsset)
}

// 生命周期
onMounted(() => {
  // 监听企业数据更新事件
  eventBus.on('enterpriseDataUpdated', handleEnterpriseDataUpdate)
  
  // 添加初始化事件
  handleEnterpriseDataUpdate({
    module: 'system',
    action: 'init',
    data: { message: '跨模块通信组件已初始化' },
    timestamp: new Date().toISOString()
  })
})

onUnmounted(() => {
  eventBus.off('enterpriseDataUpdated', handleEnterpriseDataUpdate)
})
</script>

<style scoped>
.cross-module-communication {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  max-width: 1200px;
  margin: 0 auto;
}

.panel-header {
  text-align: center;
  margin-bottom: 30px;
}

.panel-header h3 {
  color: #2c3e50;
  margin-bottom: 8px;
}

.description {
  color: #6c757d;
  margin: 0;
}

.data-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 14px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8px;
}

.stat-label {
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 12px;
}

.stat-details {
  display: flex;
  justify-content: space-around;
  font-size: 12px;
}

.stat-details span {
  color: #6c757d;
}

.event-log, .cross-module-data {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.event-log h4, .cross-module-data h4 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #eee;
  border-left: 4px solid #007bff;
}

.log-item.add {
  border-left-color: #28a745;
}

.log-item.update {
  border-left-color: #ffc107;
}

.log-item.delete {
  border-left-color: #dc3545;
}

.log-time {
  width: 80px;
  font-size: 12px;
  color: #6c757d;
  flex-shrink: 0;
}

.log-content {
  flex: 1;
  font-size: 14px;
}

.log-module {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  margin-right: 8px;
}

.log-action {
  color: #007bff;
  font-weight: 500;
  margin-right: 8px;
}

.log-data {
  color: #2c3e50;
}

.relation-section {
  margin-bottom: 24px;
}

.relation-section h5 {
  margin: 0 0 12px 0;
  color: #495057;
}

.relation-list, .room-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.relation-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.relation-asset, .relation-network {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.relation-arrow {
  margin: 0 16px;
  color: #6c757d;
  font-weight: bold;
}

.asset-type, .network-ip {
  font-size: 12px;
  color: #6c757d;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.room-name {
  font-weight: 500;
  color: #2c3e50;
}

.room-devices {
  display: flex;
  gap: 12px;
}

.device-count {
  font-size: 12px;
  color: #6c757d;
}

.actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.btn-refresh, .btn-clear, .btn-simulate {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-refresh {
  background: #007bff;
  color: white;
}

.btn-refresh:hover {
  background: #0056b3;
}

.btn-clear {
  background: #6c757d;
  color: white;
}

.btn-clear:hover {
  background: #545b62;
}

.btn-simulate {
  background: #28a745;
  color: white;
}

.btn-simulate:hover {
  background: #1e7e34;
}
</style>
