/**
 * 安全日志工具 - 防止敏感信息泄露
 */

// 敏感字段列表
const SENSITIVE_FIELDS = [
  'apiKey',
  'deepSeekApiKey',
  'openAiApiKey',
  'awsAccessKey',
  'awsSecretKey',
  'awsSessionToken',
  'defaultApiKey',
  'liteLlmApiKey',
  'geminiApiKey',
  'anthropicApiKey',
  'password',
  'token',
  'secret',
  'key',
  'auth',
  'credential'
]

// 代理配置中的敏感字段
const PROXY_SENSITIVE_FIELDS = ['username', 'password']

/**
 * 掩码化敏感字符串
 * @param value 原始值
 * @param showLast 显示最后几位字符，默认4位
 * @returns 掩码化后的字符串
 */
function maskSensitiveValue(value: string, showLast: number = 4): string {
  if (!value || typeof value !== 'string') {
    return value
  }

  if (value.length <= showLast) {
    return '****'
  }

  // 对于 API 密钥，保持前缀格式
  if (value.startsWith('sk-')) {
    return `sk-****${value.slice(-showLast)}`
  }

  return `****${value.slice(-showLast)}`
}

/**
 * 检查字段名是否为敏感字段
 * @param fieldName 字段名
 * @returns 是否为敏感字段
 */
function isSensitiveField(fieldName: string): boolean {
  const lowerFieldName = fieldName.toLowerCase()
  return SENSITIVE_FIELDS.some((sensitive) => lowerFieldName.includes(sensitive.toLowerCase()))
}

/**
 * 安全化对象，掩码敏感信息
 * @param obj 原始对象
 * @returns 安全化后的对象
 */
export function sanitizeObject(obj: any): any {
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => sanitizeObject(item))
  }

  const sanitized: any = {}

  for (const [key, value] of Object.entries(obj)) {
    if (isSensitiveField(key)) {
      // 敏感字段进行掩码处理
      if (typeof value === 'string' && value.trim() !== '') {
        sanitized[key] = maskSensitiveValue(value)
      } else {
        sanitized[key] = value ? '****' : value
      }
    } else if (key === 'proxyConfig' && value && typeof value === 'object') {
      // 特殊处理代理配置
      sanitized[key] = sanitizeProxyConfig(value)
    } else if (typeof value === 'object') {
      // 递归处理嵌套对象
      sanitized[key] = sanitizeObject(value)
    } else {
      sanitized[key] = value
    }
  }

  return sanitized
}

/**
 * 安全化代理配置
 * @param proxyConfig 代理配置对象
 * @returns 安全化后的代理配置
 */
function sanitizeProxyConfig(proxyConfig: any): any {
  if (!proxyConfig || typeof proxyConfig !== 'object') {
    return proxyConfig
  }

  const sanitized = { ...proxyConfig }

  PROXY_SENSITIVE_FIELDS.forEach((field) => {
    if (sanitized[field] && typeof sanitized[field] === 'string' && sanitized[field].trim() !== '') {
      sanitized[field] = '****'
    }
  })

  return sanitized
}

/**
 * 安全日志函数 - 替代 console.log
 * @param message 日志消息
 * @param data 要记录的数据
 */
export function secureLog(message: string, data?: any): void {
  if (data) {
    console.log(message, sanitizeObject(data))
  } else {
    console.log(message)
  }
}

/**
 * 安全错误日志函数 - 替代 console.error
 * @param message 错误消息
 * @param data 要记录的数据
 */
export function secureError(message: string, data?: any): void {
  if (data) {
    console.error(message, sanitizeObject(data))
  } else {
    console.error(message)
  }
}

/**
 * 安全警告日志函数 - 替代 console.warn
 * @param message 警告消息
 * @param data 要记录的数据
 */
export function secureWarn(message: string, data?: any): void {
  if (data) {
    console.warn(message, sanitizeObject(data))
  } else {
    console.warn(message)
  }
}

/**
 * 安全调试日志函数 - 替代 console.debug
 * @param message 调试消息
 * @param data 要记录的数据
 */
export function secureDebug(message: string, data?: any): void {
  if (data) {
    console.debug(message, sanitizeObject(data))
  } else {
    console.debug(message)
  }
}
