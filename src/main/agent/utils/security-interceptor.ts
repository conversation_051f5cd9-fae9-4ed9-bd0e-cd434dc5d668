/**
 * 全局安全日志拦截器
 * 防止敏感信息意外泄露到日志中
 */

import { sanitizeObject } from './secure-logging'

// 敏感信息检测模式
const SENSITIVE_PATTERNS = [
  /sk-[a-zA-Z0-9]{32,}/g, // OpenAI/DeepSeek API keys
  /AKIA[0-9A-Z]{16}/g, // AWS Access Keys
  /[A-Za-z0-9/+=]{40,}/g, // Long base64 strings (potential secrets)
  /"password":\s*"[^"]+"/gi, // Password fields in JSON
  /"token":\s*"[^"]+"/gi, // Token fields in JSON
  /"secret":\s*"[^"]+"/gi, // Secret fields in JSON
  /"apikey":\s*"[^"]+"/gi // API key fields in JSON
]

// 原始控制台方法
let originalConsole: {
  log: typeof console.log
  error: typeof console.error
  warn: typeof console.warn
  debug: typeof console.debug
} | null = null

/**
 * 检查字符串是否包含敏感信息
 */
function containsSensitiveData(text: string): boolean {
  return SENSITIVE_PATTERNS.some((pattern) => pattern.test(text))
}

/**
 * 清理参数中的敏感信息
 */
function sanitizeArgs(args: any[]): any[] {
  return args.map((arg) => {
    if (typeof arg === 'string') {
      // 检查字符串是否包含敏感信息
      if (containsSensitiveData(arg)) {
        console.warn('[SECURITY] Sensitive data detected in log, sanitizing...')
        return sanitizeString(arg)
      }
      return arg
    } else if (typeof arg === 'object' && arg !== null) {
      // 对象类型使用安全化函数
      return sanitizeObject(arg)
    }
    return arg
  })
}

/**
 * 清理字符串中的敏感信息
 */
function sanitizeString(text: string): string {
  let sanitized = text

  // 替换各种敏感模式
  sanitized = sanitized.replace(/sk-[a-zA-Z0-9]{32,}/g, 'sk-****[REDACTED]')
  sanitized = sanitized.replace(/AKIA[0-9A-Z]{16}/g, 'AKIA****[REDACTED]')
  sanitized = sanitized.replace(/"password":\s*"[^"]+"/gi, '"password": "****"')
  sanitized = sanitized.replace(/"token":\s*"[^"]+"/gi, '"token": "****"')
  sanitized = sanitized.replace(/"secret":\s*"[^"]+"/gi, '"secret": "****"')
  sanitized = sanitized.replace(/"apikey":\s*"[^"]+"/gi, '"apikey": "****"')

  return sanitized
}

/**
 * 创建安全的控制台方法
 */
function createSecureConsoleMethod(originalMethod: Function) {
  return function (...args: any[]) {
    try {
      const sanitizedArgs = sanitizeArgs(args)
      originalMethod.apply(console, sanitizedArgs)
    } catch (error) {
      // 如果清理过程出错，使用原始方法但添加警告
      originalMethod.call(console, '[SECURITY WARNING] Failed to sanitize log data:', ...args)
    }
  }
}

/**
 * 启用安全日志拦截器
 */
export function enableSecurityInterceptor(): void {
  if (originalConsole) {
    console.warn('[SECURITY] Security interceptor already enabled')
    return
  }

  // 保存原始方法
  originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    debug: console.debug
  }

  // 替换为安全方法
  console.log = createSecureConsoleMethod(originalConsole.log)
  console.error = createSecureConsoleMethod(originalConsole.error)
  console.warn = createSecureConsoleMethod(originalConsole.warn)
  console.debug = createSecureConsoleMethod(originalConsole.debug)

  console.log('[SECURITY] Security interceptor enabled - sensitive data will be automatically sanitized')
}

/**
 * 禁用安全日志拦截器
 */
export function disableSecurityInterceptor(): void {
  if (!originalConsole) {
    console.warn('[SECURITY] Security interceptor not enabled')
    return
  }

  // 恢复原始方法
  console.log = originalConsole.log
  console.error = originalConsole.error
  console.warn = originalConsole.warn
  console.debug = originalConsole.debug

  originalConsole = null

  console.log('[SECURITY] Security interceptor disabled')
}

/**
 * 检查拦截器是否已启用
 */
export function isSecurityInterceptorEnabled(): boolean {
  return originalConsole !== null
}

/**
 * 手动清理敏感数据（用于特殊情况）
 */
export function sanitizeForLogging(data: any): any {
  if (typeof data === 'string') {
    return sanitizeString(data)
  } else if (typeof data === 'object' && data !== null) {
    return sanitizeObject(data)
  }
  return data
}
