<template>
  <div class="quick-command-bar">
    <!-- 主要快捷命令栏 -->
    <div
      class="main-command-bar"
      @dblclick="openAddCommandDialog"
    >
      <a-button
        type="primary"
        size="small"
        class="add-button"
        @click="openAddCommandDialog"
      >
        <PlusOutlined />
      </a-button>

      <!-- 快捷键按钮 -->
      <a-button
        size="small"
        class="shortcut-button"
        :type="showShortcutPanel ? 'primary' : 'default'"
        @click="toggleShortcutPanel"
      >
        <ThunderboltOutlined />
        <span class="shortcut-count">{{ enabledShortcuts.length }}</span>
      </a-button>

      <div
        ref="buttonListRef"
        class="button-container"
      >
        <a-button
          v-for="cmd in quickCommands"
          :key="cmd.id"
          v-contextmenu:quickCommandMenu
          class="quick-command-btn"
          size="middle"
          @dblclick.stop
          @click="handleClick(cmd)"
          @contextmenu.prevent="showContextMenu(cmd.id)"
        >
          {{ cmd.snippet_name }}
        </a-button>
      </div>

      <!-- 智能推荐按钮 -->
      <a-button
        size="small"
        class="recommendation-button"
        :disabled="recommendations.length === 0"
        @click="showRecommendations"
      >
        <BulbOutlined />
        <span>{{ recommendations.length }}</span>
      </a-button>
    </div>

    <!-- 快捷键面板 -->
    <div
      v-if="showShortcutPanel"
      class="shortcut-panel"
    >
      <div class="panel-header">
        <span class="panel-title">{{ $t('quickCommand.shortcuts') }}</span>
        <a-button
          size="small"
          @click="openShortcutManager"
        >
          <SettingOutlined />
        </a-button>
      </div>
      <div class="shortcut-list">
        <div
          v-for="shortcut in enabledShortcuts.slice(0, 8)"
          :key="shortcut.id"
          class="shortcut-item"
          @click="executeShortcut(shortcut)"
        >
          <span class="shortcut-icon">{{ shortcut.icon }}</span>
          <span class="shortcut-name">{{ shortcut.name }}</span>
          <span class="shortcut-key">{{ shortcut.shortcut }}</span>
        </div>
      </div>
    </div>
    <a-modal
      v-model:open="showAddCommandDialog"
      :title="isEditMode ? $t('common.edit') : $t('common.add')"
      class="commandDialog"
      :cancel-text="$t('common.cancel')"
      :ok-text="$t('common.ok')"
      centered
      width="600px"
      @ok="addQuickCommand"
      @cancel="
        () => {
          showAddCommandDialog = false
          isEditMode = false
          selectedCommandId = null
        }
      "
    >
      <a-input
        v-model:value="newCommandLabel"
        :placeholder="$t('quickCommand.scriptName')"
        style="margin-bottom: 8px"
      />
      <a-textarea
        v-model:value="newCommandValue"
        :placeholder="$t('quickCommand.scriptContent')"
        :auto-size="{ minRows: 6, maxRows: 10 }"
        style="margin-bottom: 12px"
      />

      <!-- Script syntax help -->
      <div class="script-help">
        <div
          class="help-header"
          @click="toggleHelp"
        >
          <span class="help-title">{{ $t('quickCommand.scriptSyntaxHelp') }}</span>
          <span class="toggle-icon">{{ showHelp ? '▼' : '▶' }}</span>
        </div>

        <div
          v-if="showHelp"
          class="help-content"
        >
          <div class="help-layout">
            <!-- Left: Command descriptions -->
            <div class="help-left">
              <div class="help-item">
                <strong>{{ $t('quickCommand.basicCommands') }}</strong>
                <span>{{ $t('quickCommand.basicCommandsDesc') }}</span>
              </div>

              <div class="help-item">
                <strong>{{ $t('quickCommand.delayCommand') }}</strong>
                <code>sleep=={{ $t('quickCommand.seconds') }}</code>
                <span>{{ $t('quickCommand.delayCommandDesc') }}<code>sleep==3</code></span>
              </div>

              <div class="help-item">
                <strong>{{ $t('quickCommand.specialKeys') }}</strong>
                <div class="key-list">
                  <code>esc</code>
                  <code>tab</code>
                  <code>return</code>
                  <code>backspace</code>
                </div>
              </div>
            </div>

            <!-- Right: Example code -->
            <div class="help-right">
              <div class="example-header">
                <span>{{ $t('quickCommand.exampleScript') }}</span>
                <button
                  class="copy-btn"
                  :class="{ copied: copySuccess }"
                  @click="copyExample"
                >
                  {{ copySuccess ? $t('quickCommand.copied') : $t('quickCommand.copy') }}
                </button>
              </div>
              <pre class="example-code">
ls -la
sleep==2
cd /home
pwd
sleep==1
sudo systemctl status nginx</pre
              >
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 推荐命令弹窗 -->
    <a-modal
      v-model:open="showRecommendationModal"
      :title="$t('quickCommand.recommendations')"
      :footer="null"
      width="500px"
    >
      <div class="recommendation-list">
        <div
          v-for="rec in recommendations"
          :key="rec.id"
          class="recommendation-item"
          @click="executeShortcut(rec)"
        >
          <span class="rec-icon">{{ rec.icon }}</span>
          <div class="rec-content">
            <div class="rec-name">{{ rec.name }}</div>
            <div class="rec-desc">{{ rec.description }}</div>
            <div class="rec-usage">使用次数: {{ rec.usage_count }}</div>
          </div>
          <span class="rec-shortcut">{{ rec.shortcut }}</span>
        </div>
      </div>
    </a-modal>

    <!-- 快捷键管理器弹窗 -->
    <a-modal
      v-model:open="showShortcutManagerModal"
      :title="$t('quickCommand.shortcutManager')"
      width="800px"
      :footer="null"
    >
      <div class="shortcut-manager">
        <div class="manager-header">
          <a-button
            type="primary"
            @click="openAddShortcutDialog"
          >
            <PlusOutlined /> {{ $t('quickCommand.addShortcut') }}
          </a-button>
          <a-input
            v-model:value="shortcutSearchText"
            :placeholder="$t('quickCommand.searchShortcuts')"
            class="search-input"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </div>
        <div class="shortcut-table">
          <a-table
            :columns="shortcutColumns"
            :data-source="filteredShortcuts"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch
                  :checked="record.enabled"
                  @change="toggleShortcut(record.id)"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    size="small"
                    @click="editShortcut(record)"
                  >
                    <EditOutlined />
                  </a-button>
                  <a-button
                    size="small"
                    danger
                    @click="deleteShortcut(record.id)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 添加/编辑快捷键弹窗 -->
    <a-modal
      v-model:open="showAddShortcutDialog"
      :title="editingShortcut ? $t('common.edit') : $t('quickCommand.addShortcut')"
      @ok="saveShortcut"
      @cancel="cancelShortcutEdit"
    >
      <a-form
        :model="shortcutForm"
        layout="vertical"
      >
        <a-form-item
          :label="$t('quickCommand.shortcutName')"
          required
        >
          <a-input v-model:value="shortcutForm.name" />
        </a-form-item>
        <a-form-item :label="$t('quickCommand.description')">
          <a-input v-model:value="shortcutForm.description" />
        </a-form-item>
        <a-form-item
          :label="$t('quickCommand.shortcutKey')"
          required
        >
          <a-input
            v-model:value="shortcutForm.shortcut"
            :placeholder="$t('quickCommand.shortcutPlaceholder')"
            @keydown="captureShortcut"
          />
        </a-form-item>
        <a-form-item
          :label="$t('quickCommand.command')"
          required
        >
          <a-textarea
            v-model:value="shortcutForm.command"
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item :label="$t('quickCommand.category')">
          <a-select v-model:value="shortcutForm.category">
            <a-select-option value="system">⚙️ 系统</a-select-option>
            <a-select-option value="file">📁 文件</a-select-option>
            <a-select-option value="network">🌐 网络</a-select-option>
            <a-select-option value="docker">🐳 Docker</a-select-option>
            <a-select-option value="git">🔀 Git</a-select-option>
            <a-select-option value="custom">🔧 自定义</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="$t('quickCommand.icon')">
          <a-input
            v-model:value="shortcutForm.icon"
            :placeholder="$t('quickCommand.iconPlaceholder')"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <v-contextmenu
      ref="quickCommandMenu"
      :custom-class="'quickCommandMenu'"
    >
      <v-contextmenu-item @click="handleEditCommand(selectedCommandId)">{{ $t('common.edit') }}</v-contextmenu-item>
      <v-contextmenu-item @click="handleRemoveCommand(selectedCommandId)">{{ $t('common.delete') }}</v-contextmenu-item>
      <v-contextmenu-item @click="createShortcutFromCommand(selectedCommandId)">{{ $t('quickCommand.createShortcut') }}</v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, computed, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import Sortable from 'sortablejs'
import { PlusOutlined, ThunderboltOutlined, BulbOutlined, SettingOutlined, SearchOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { executeScript } from '../Ssh/commandScript'
import { inputManager } from '../Ssh/termInputManager'
import { smartShortcutManager, COMMAND_CATEGORIES, type ShortcutConfig } from '../../../utils/smartShortcutManager'
const { t } = useI18n()

interface QuickCommand {
  id: number
  snippet_name: string
  snippet_content: string
  create_at?: string
  update_at?: string
}

const quickCommands = ref<QuickCommand[]>([])

const buttonListRef = ref<HTMLElement | null>(null)
let sortable: Sortable | null = null
const showAddCommandDialog = ref(false)
const newCommandLabel = ref('')
const newCommandValue = ref('')
const selectedCommandId = ref<number | null>(null)
const isEditMode = ref(false)
const showHelp = ref(true)
const copySuccess = ref(false)

// 智能快捷键相关变量
const showShortcutPanel = ref(false)
const showRecommendationModal = ref(false)
const showShortcutManagerModal = ref(false)
const showAddShortcutDialog = ref(false)
const shortcutSearchText = ref('')
const editingShortcut = ref<ShortcutConfig | null>(null)

// 快捷键表单
const shortcutForm = reactive({
  name: '',
  description: '',
  shortcut: '',
  command: '',
  category: COMMAND_CATEGORIES.CUSTOM,
  icon: '⚡',
  enabled: true
})

// 计算属性
const enabledShortcuts = computed(() => smartShortcutManager.getEnabledShortcuts())
const recommendations = computed(() => smartShortcutManager.getRecommendations())
const filteredShortcuts = computed(() => {
  const shortcuts = smartShortcutManager.getShortcuts()
  if (!shortcutSearchText.value) {
    return shortcuts
  }
  return shortcuts.filter(
    (s) =>
      s.name.toLowerCase().includes(shortcutSearchText.value.toLowerCase()) ||
      s.description.toLowerCase().includes(shortcutSearchText.value.toLowerCase())
  )
})

// 快捷键表格列定义
const shortcutColumns = [
  { title: t('quickCommand.name'), dataIndex: 'name', key: 'name' },
  { title: t('quickCommand.shortcut'), dataIndex: 'shortcut', key: 'shortcut' },
  { title: t('quickCommand.category'), dataIndex: 'category', key: 'category' },
  { title: t('quickCommand.usageCount'), dataIndex: 'usage_count', key: 'usage_count' },
  { title: t('quickCommand.enabled'), key: 'enabled', width: 80 },
  { title: t('common.actions'), key: 'actions', width: 120 }
]

onMounted(async () => {
  await refresh()
  // 初始化智能快捷键管理器
  smartShortcutManager.initialize()

  nextTick(() => {
    if (buttonListRef.value) {
      sortable = Sortable.create(buttonListRef.value, {
        animation: 150,
        handle: '.quick-command-btn',
        onEnd: async (evt) => {
          const oldIndex = evt.oldIndex
          const newIndex = evt.newIndex
          if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
            const id1 = quickCommands.value[oldIndex].id
            const id2 = quickCommands.value[newIndex].id
            if (id1 && id2) {
              await swapCommand(id1, id2)
            }
          }
        }
      })
    }
  })
  if (buttonListRef.value) buttonListRef.value.addEventListener('wheel', handleWheel, { passive: false })
})

onBeforeUnmount(() => {
  if (sortable) {
    sortable.destroy()
    sortable = null
  }
  if (buttonListRef.value) buttonListRef.value.removeEventListener('wheel', handleWheel)
})

function handleWheel(e) {
  if (e.deltaY === 0) return
  e.preventDefault()
  if (buttonListRef.value) buttonListRef.value.scrollLeft += e.deltaY
}

const openAddCommandDialog = () => {
  newCommandLabel.value = ''
  newCommandValue.value = ''
  showAddCommandDialog.value = true
  isEditMode.value = false
  selectedCommandId.value = null
}

const showContextMenu = (id: number) => {
  selectedCommandId.value = id
}

const handleEditCommand = async (id: number | null) => {
  if (id === null) return
  const cmd = quickCommands.value.find((item) => item.id === id)
  if (!cmd) return
  newCommandLabel.value = cmd.snippet_name
  newCommandValue.value = cmd.snippet_content
  isEditMode.value = true
  showAddCommandDialog.value = true
  selectedCommandId.value = id
}

const handleRemoveCommand = async (id: number | null) => {
  if (id === null) return
  await removeCommand(id)
}

const addQuickCommand = async () => {
  if (newCommandLabel.value && newCommandValue.value) {
    const cmd = {
      snippet_name: newCommandLabel.value,
      snippet_content: newCommandValue.value
    }
    if (isEditMode.value && selectedCommandId.value !== null) {
      await editCommand({ id: selectedCommandId.value, ...cmd })
    } else {
      await addCommand(cmd)
    }
    showAddCommandDialog.value = false
    isEditMode.value = false
    selectedCommandId.value = null
  }
}

const handleClick = (cmd: QuickCommand) => {
  const terminal = {
    write: (data: string) => {
      inputManager.sendToActiveTerm(data)
    }
  }
  executeScript(cmd.snippet_content, terminal)
}

const toggleHelp = () => {
  showHelp.value = !showHelp.value
}

const copyExample = async () => {
  const exampleText = `ls -la
sleep==2
cd /home
pwd
sleep==1
sudo systemctl status nginx`

  try {
    await navigator.clipboard.writeText(exampleText)
    copySuccess.value = true
    setTimeout(() => {
      copySuccess.value = false
    }, 2000)
  } catch (err) {
    console.error(t('quickCommand.copyFailed'), err)
  }
}

const api = (window as any).api
const refresh = async () => {
  const { data } = await api.userSnippetOperation({ operation: 'list' })
  quickCommands.value = data.snippets || []
}

// Add new command
const addCommand = async (params) => {
  await api.userSnippetOperation({
    operation: 'create',
    params
  })
  await refresh()
}

// Edit command
const editCommand = async (params: QuickCommand) => {
  await api.userSnippetOperation({
    operation: 'update',
    params
  })
  await refresh()
}

// Delete command
const removeCommand = async (id: number) => {
  await api.userSnippetOperation({
    operation: 'delete',
    params: { id }
  })
  await refresh()
}

// Swap command order
const swapCommand = async (id1: number, id2: number) => {
  await api.userSnippetOperation({
    operation: 'swap',
    params: { id1, id2 }
  })
  await refresh()
}

// 智能快捷键相关方法
const toggleShortcutPanel = () => {
  showShortcutPanel.value = !showShortcutPanel.value
}

const openRecommendationModal = () => {
  showRecommendationModal.value = true
}

const openShortcutManagerModal = () => {
  showShortcutManagerModal.value = true
}

const openAddShortcutDialog = () => {
  editingShortcut.value = null
  Object.assign(shortcutForm, {
    name: '',
    description: '',
    shortcut: '',
    command: '',
    category: COMMAND_CATEGORIES.CUSTOM,
    icon: '⚡',
    enabled: true
  })
  showAddShortcutDialog.value = true
}

const editShortcut = (shortcut: ShortcutConfig) => {
  editingShortcut.value = shortcut
  Object.assign(shortcutForm, {
    name: shortcut.name,
    description: shortcut.description,
    shortcut: shortcut.shortcut,
    command: shortcut.command,
    category: shortcut.category,
    icon: shortcut.icon,
    enabled: shortcut.enabled
  })
  showAddShortcutDialog.value = true
}

const saveShortcut = () => {
  if (editingShortcut.value) {
    smartShortcutManager.updateShortcut(editingShortcut.value.id, shortcutForm)
  } else {
    smartShortcutManager.addShortcut(shortcutForm)
  }
  showAddShortcutDialog.value = false
}

const deleteShortcut = (id: string) => {
  smartShortcutManager.removeShortcut(id)
}

const toggleShortcutEnabled = (id: string) => {
  smartShortcutManager.toggleShortcut(id)
}

const executeShortcut = (shortcut: ShortcutConfig) => {
  inputManager.sendToActiveTerm(shortcut.command)
  // 更新使用统计
  const shortcuts = smartShortcutManager.getShortcuts()
  const targetShortcut = shortcuts.find((s) => s.id === shortcut.id)
  if (targetShortcut) {
    smartShortcutManager.updateShortcut(shortcut.id, {
      usage_count: targetShortcut.usage_count + 1,
      last_used: new Date()
    })
  }
}

const applyRecommendation = (recommendation: ShortcutConfig) => {
  smartShortcutManager.addShortcut(recommendation)
  showRecommendationModal.value = false
}

const createShortcutFromCommand = (command: QuickCommand) => {
  Object.assign(shortcutForm, {
    name: command.snippet_name,
    description: `快捷方式: ${command.snippet_name}`,
    shortcut: '',
    command: command.snippet_content,
    category: COMMAND_CATEGORIES.CUSTOM,
    icon: '⚡',
    enabled: true
  })
  showAddShortcutDialog.value = true
}
</script>

<style scoped lang="less">
.quick-command-bar {
  width: 100%;
  height: 30px;
  background: var(--globalInput-bg-color);
  display: flex;
  align-items: center;
  border-radius: 4px;
  .quick-command-btn {
    height: 30px;
    flex-shrink: 0;
    max-width: 120px;
    padding: 4px 15px;
    background-color: transparent;
    color: var(--text-color);
    border: none;
    &:hover {
      background-color: var(--button-bg-color);
    }
  }
  :deep(.ant-btn) {
    span {
      display: inline-block;
      max-width: 90px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .quick-command-empty {
    color: var(--text-color);
    font-size: 13px;
    margin-left: 8px;
  }
}
.button-container {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
}
.add-button {
  height: 26px;
  width: 26px;
  flex-shrink: 0;
  background-color: var(--globalInput-bg-color);
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  &:hover {
    background-color: var(--button-bg-color);
    color: var(--text-color);
  }
}

.script-help {
  background: var(--bg-color-secondary, #f8f9fa);
  border-radius: 6px;
  border: 1px solid var(--border-color, #e1e1e1);
  font-size: 12px;
  line-height: 1.4;

  .help-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 12px 16px;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color, #e1e1e1);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--bg-color-tertiary, rgba(0, 0, 0, 0.02));
    }

    .help-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color, #333);
    }

    .toggle-icon {
      font-size: 12px;
      transition: transform 0.3s ease;
      color: var(--text-color-secondary, #666);
    }
  }

  .help-content {
    padding: 16px;
    animation: slideDown 0.3s ease;
  }

  .help-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .help-left {
    padding-right: 8px;
    border-right: 1px solid var(--border-color, #e1e1e1);

    @media (max-width: 768px) {
      padding-right: 0;
      border-right: none;
      border-bottom: 1px solid var(--border-color, #e1e1e1);
      padding-bottom: 12px;
    }
  }

  .help-right {
    padding-left: 8px;

    @media (max-width: 768px) {
      padding-left: 0;
      padding-top: 12px;
    }
  }

  .help-item {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    strong {
      color: var(--text-color, #333);
      font-size: 12px;
      display: block;
      margin-bottom: 4px;
    }

    span {
      color: var(--text-color-secondary, #666);
      font-size: 11px;
    }

    code {
      background: var(--bg-color-quaternary, #f0f0f0);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 11px;
      color: #1890ff;
      margin: 0 2px;
    }

    .key-list {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-top: 4px;

      code {
        background: var(--bg-color-quaternary, #f8f9fa);
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 10px;
        color: var(--text-color, #333);
        border: 1px solid var(--border-color, #e1e1e1);
      }
    }
  }

  .example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    span {
      font-size: 12px;
      font-weight: 600;
      color: var(--text-color, #333);
    }

    .copy-btn {
      background: #1890ff;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #40a9ff;
      }

      &.copied {
        background: #52c41a;
      }
    }
  }

  .example-code {
    background: var(--bg-color-quaternary, #2d3748);
    color: var(--text-color, #333);
    padding: 12px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 11px;
    line-height: 1.5;
    margin: 0;
    overflow-x: auto;
    white-space: pre;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
</style>
