/**
 * 终端性能优化工具
 * 实现虚拟滚动、内存管理和性能监控
 */

export interface TerminalPerformanceConfig {
  maxLines: number
  compressionThreshold: number
  virtualScrollEnabled: boolean
  memoryThreshold: number // MB
}

export interface PerformanceMetrics {
  memoryUsage: number
  renderTime: number
  lineCount: number
  lastCleanup: number
}

export class TerminalOptimizer {
  private config: TerminalPerformanceConfig
  private metrics: PerformanceMetrics
  private lineBuffer: string[] = []
  private compressedLines: string[] = []
  private renderCache = new Map<number, string>()

  constructor(config: Partial<TerminalPerformanceConfig> = {}) {
    this.config = {
      maxLines: 10000,
      compressionThreshold: 5000,
      virtualScrollEnabled: true,
      memoryThreshold: 100, // 100MB
      ...config
    }

    this.metrics = {
      memoryUsage: 0,
      renderTime: 0,
      lineCount: 0,
      lastCleanup: Date.now()
    }
  }

  /**
   * 添加新行到缓冲区
   */
  addLine(line: string): void {
    const startTime = performance.now()

    this.lineBuffer.push(line)
    this.metrics.lineCount++

    // 检查是否需要压缩
    if (this.lineBuffer.length > this.config.compressionThreshold) {
      this.compressOldLines()
    }

    // 检查是否需要清理
    if (this.lineBuffer.length > this.config.maxLines) {
      this.cleanupOldLines()
    }

    this.metrics.renderTime = performance.now() - startTime
    this.updateMemoryUsage()
  }

  /**
   * 获取可见行范围
   */
  getVisibleLines(startIndex: number, endIndex: number): string[] {
    const visibleLines: string[] = []

    for (let i = startIndex; i <= endIndex; i++) {
      if (i < this.compressedLines.length) {
        visibleLines.push(this.compressedLines[i])
      } else {
        const bufferIndex = i - this.compressedLines.length
        if (bufferIndex < this.lineBuffer.length) {
          visibleLines.push(this.lineBuffer[bufferIndex])
        }
      }
    }

    return visibleLines
  }

  /**
   * 压缩旧行
   */
  private compressOldLines(): void {
    const linesToCompress = this.lineBuffer.splice(0, this.config.compressionThreshold / 2)

    // 简单压缩：合并相似行
    const compressed = this.compressLines(linesToCompress)
    this.compressedLines.push(...compressed)

    // 清理渲染缓存
    this.renderCache.clear()
  }

  /**
   * 清理旧行
   */
  private cleanupOldLines(): void {
    const excessLines = this.lineBuffer.length - this.config.maxLines
    if (excessLines > 0) {
      this.lineBuffer.splice(0, excessLines)
      this.metrics.lastCleanup = Date.now()
    }

    // 清理压缩行
    if (this.compressedLines.length > this.config.maxLines / 2) {
      this.compressedLines.splice(0, this.compressedLines.length / 4)
    }
  }

  /**
   * 压缩行算法
   */
  private compressLines(lines: string[]): string[] {
    const compressed: string[] = []
    let currentGroup: string[] = []
    let lastLine = ''

    for (const line of lines) {
      // 检查是否是重复行
      if (line === lastLine && currentGroup.length < 10) {
        currentGroup.push(line)
      } else {
        // 处理当前组
        if (currentGroup.length > 1) {
          compressed.push(`[重复 ${currentGroup.length} 次] ${currentGroup[0]}`)
        } else if (currentGroup.length === 1) {
          compressed.push(currentGroup[0])
        }

        currentGroup = [line]
        lastLine = line
      }
    }

    // 处理最后一组
    if (currentGroup.length > 1) {
      compressed.push(`[重复 ${currentGroup.length} 次] ${currentGroup[0]}`)
    } else if (currentGroup.length === 1) {
      compressed.push(currentGroup[0])
    }

    return compressed
  }

  /**
   * 更新内存使用情况
   */
  private updateMemoryUsage(): void {
    const bufferSize = this.lineBuffer.join('').length * 2 // UTF-16
    const compressedSize = this.compressedLines.join('').length * 2
    this.metrics.memoryUsage = (bufferSize + compressedSize) / (1024 * 1024) // MB
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 搜索行
   */
  searchLines(query: string, caseSensitive = false): Array<{ index: number; line: string }> {
    const results: Array<{ index: number; line: string }> = []
    const searchQuery = caseSensitive ? query : query.toLowerCase()

    // 搜索压缩行
    this.compressedLines.forEach((line, index) => {
      const searchLine = caseSensitive ? line : line.toLowerCase()
      if (searchLine.includes(searchQuery)) {
        results.push({ index, line })
      }
    })

    // 搜索缓冲区
    this.lineBuffer.forEach((line, index) => {
      const searchLine = caseSensitive ? line : line.toLowerCase()
      if (searchLine.includes(searchQuery)) {
        results.push({ index: this.compressedLines.length + index, line })
      }
    })

    return results
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    this.lineBuffer = []
    this.compressedLines = []
    this.renderCache.clear()
    this.metrics.lineCount = 0
    this.metrics.memoryUsage = 0
  }

  /**
   * 导出数据
   */
  exportData(): { lines: string[]; metrics: PerformanceMetrics } {
    return {
      lines: [...this.compressedLines, ...this.lineBuffer],
      metrics: this.getMetrics()
    }
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics = new Map<string, number[]>()
  private observers: Array<(metrics: Record<string, number>) => void> = []

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 记录指标
   */
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const values = this.metrics.get(name)!
    values.push(value)

    // 保持最近100个值
    if (values.length > 100) {
      values.shift()
    }

    this.notifyObservers()
  }

  /**
   * 获取平均值
   */
  getAverage(name: string): number {
    const values = this.metrics.get(name)
    if (!values || values.length === 0) return 0

    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  /**
   * 获取所有指标
   */
  getAllMetrics(): Record<string, { average: number; latest: number; count: number }> {
    const result: Record<string, { average: number; latest: number; count: number }> = {}

    for (const [name, values] of this.metrics) {
      result[name] = {
        average: this.getAverage(name),
        latest: values[values.length - 1] || 0,
        count: values.length
      }
    }

    return result
  }

  /**
   * 订阅指标更新
   */
  subscribe(callback: (metrics: Record<string, number>) => void): () => void {
    this.observers.push(callback)

    return () => {
      const index = this.observers.indexOf(callback)
      if (index > -1) {
        this.observers.splice(index, 1)
      }
    }
  }

  private notifyObservers(): void {
    const metrics: Record<string, number> = {}
    for (const [name] of this.metrics) {
      metrics[name] = this.getAverage(name)
    }

    this.observers.forEach((callback) => callback(metrics))
  }
}
