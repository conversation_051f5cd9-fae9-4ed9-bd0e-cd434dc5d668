<template>
  <div class="asset-search-container">
    <AssetSearchBox
      v-model="searchValue"
      :placeholder="t('common.search')"
      :assets="assetSuggestions"
      :show-suggestions="showSuggestions"
      :show-new-button="showNewButton"
      :new-button-text="t('personal.newHost')"
      @search="handleSearch"
      @suggestion-select="handleAssetSelect"
      @new-asset="handleNewAsset"
      @import-assets="handleImportAssets"
      @export-assets="handleExport"
      @clear="handleClear"
    />

    <!-- 帮助按钮 -->
    <div class="help-section">
      <a-tooltip :title="t('personal.importHelp')">
        <a-button
          size="small"
          class="help-button"
          @click="showImportHelp"
        >
          <template #icon><QuestionCircleOutlined /></template>
        </a-button>
      </a-tooltip>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileSelect"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'
import AssetSearchBox from '@/components/common/SearchBox/AssetSearchBox.vue'

const { t } = useI18n()

interface Props {
  modelValue?: string
  placeholder?: string
  showNewButton?: boolean
  newButtonText?: string
  assets?: any[]
  showSuggestions?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '',
  showNewButton: true,
  newButtonText: '',
  assets: () => [],
  showSuggestions: true
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  search: [value: string]
  'new-asset': []
  'import-assets': [assets: any[]]
  'export-assets': []
  'asset-select': [asset: any]
  clear: []
}>()

const searchValue = ref(props.modelValue)
const fileInputRef = ref<HTMLInputElement>()

// 转换资产数据为搜索建议格式
const assetSuggestions = computed(() => {
  return props.assets.map((asset) => ({
    id: asset.uuid || asset.id,
    name: asset.label || asset.name,
    host: asset.ip || asset.host,
    port: asset.port || 22,
    type: asset.asset_type || 'server',
    description: asset.description || '',
    status: asset.status || 'unknown',
    organization: asset.group_name || asset.organization,
    department: asset.department,
    tags: asset.tags || []
  }))
})

watch(
  () => props.modelValue,
  (newValue) => {
    searchValue.value = newValue
  }
)

watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue)
})

const handleSearch = () => {
  emit('search', searchValue.value)
}

const handleNewAsset = () => {
  console.log('[Debug] AssetSearch - 添加主机按钮被点击，准备发送new-asset事件')
  emit('new-asset')
  console.log('[Debug] AssetSearch - new-asset事件已发送')
}

const handleImport = () => {
  fileInputRef.value?.click()
}

const handleImportAssets = (assets: any[]) => {
  emit('import-assets', assets)
}

const handleExport = () => {
  emit('export-assets')
}

const handleAssetSelect = (asset: any) => {
  emit('asset-select', asset)
}

const handleClear = () => {
  emit('clear')
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target?.result as string
      const assets = JSON.parse(content)

      if (Array.isArray(assets)) {
        emit('import-assets', assets)
        message.success(t('personal.importSuccess'))
      } else {
        message.error(t('personal.importFormatError'))
      }
    } catch (error) {
      console.error('Import file parsing error:', error)
      message.error(t('personal.importError'))
    }
  }

  reader.readAsText(file)

  // Clear the file input field to allow selecting the same file again
  target.value = ''
}

const showImportHelp = () => {
  const helpText = `
${t('personal.importFormatGuide')}
${t('personal.importFormatStep1')}
${t('personal.importFormatStep2')}
${t('personal.importFormatStep3')}
   - username: ${t('personal.importFormatUsername')}
   - ip: ${t('personal.importFormatIp')}
   - password: ${t('personal.importFormatPassword')}
   - label: ${t('personal.importFormatLabel')}
   - group_name: ${t('personal.importFormatGroup')}
   - auth_type: ${t('personal.importFormatAuthType')}
   - keyChain: ${t('personal.importFormatKeyChain')}
   - port: ${t('personal.importFormatPort')}
   - asset_type: ${t('personal.importFormatAssetType')}

${t('personal.importFormatExample')}
[
  {
    "username": "root",
    "password": "password123",
    "ip": "*************",
    "label": "Web Server",
    "group_name": "production",
    "auth_type": "password",
    "port": 22,
    "asset_type": "person"
  }
]
  `.trim()

  Modal.info({
    title: t('personal.importFormatTitle'),
    content: helpText,
    width: 600,
    okText: t('common.ok'),
    class: 'import-help-modal'
  })
}
</script>

<style lang="less" scoped>
.asset-search-container {
  width: 100%;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.help-section {
  display: flex;
  justify-content: flex-end;

  .help-button {
    display: flex;
    align-items: center;
    gap: 4px;
    height: 32px;
    padding: 0 8px;
    border-radius: 4px;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-width: 32px;

    &:hover {
      background: var(--hover-bg-color);
      border-color: var(--primary-color);
      color: var(--primary-color);
    }

    &:active {
      background: var(--active-bg-color);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .asset-search-container {
    gap: 12px;
  }

  .help-section {
    justify-content: center;

    .help-button {
      flex: 1;
      justify-content: center;
      max-width: 200px;
    }
  }
}

:global(.import-help-modal) {
  .ant-modal-body {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    white-space: pre-wrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
