<template>
  <div class="command-history-search-box">
    <SearchBox
      v-model="searchValue"
      :placeholder="placeholder"
      :size="size"
      clearable
      :suggestions="filteredSuggestions"
      :show-suggestions="showSuggestions && filteredSuggestions.length > 0"
      class="command-history-search-box__input"
      @search="handleSearch"
      @suggestion-select="handleSuggestionSelect"
      @input="handleInput"
      @clear="handleClear"
    >
      <template #prefix-icon>
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="11" cy="11" r="8"></circle>
          <path d="m21 21-4.35-4.35"></path>
        </svg>
      </template>
      
      <template #suggestion="{ suggestion }">
        <div class="command-suggestion">
          <div class="command-suggestion__icon">
            <component :is="getCommandIcon(suggestion.category)" />
          </div>
          <div class="command-suggestion__content">
            <div class="command-suggestion__command">{{ suggestion.command }}</div>
            <div class="command-suggestion__meta">
              <span class="command-suggestion__server">{{ suggestion.server }}</span>
              <span class="command-suggestion__time">{{ formatTime(suggestion.timestamp) }}</span>
              <span v-if="suggestion.exitCode !== undefined" 
                    :class="['command-suggestion__exit-code', getExitCodeClass(suggestion.exitCode)]">
                {{ suggestion.exitCode === 0 ? '✓' : '✗' }}
              </span>
            </div>
            <div v-if="suggestion.description" class="command-suggestion__description">
              {{ suggestion.description }}
            </div>
          </div>
          <div class="command-suggestion__actions">
            <button
              type="button"
              class="command-suggestion__action"
              :title="t('commandHistory.copyCommand')"
              @click.stop="copyCommand(suggestion.command)"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"></path>
              </svg>
            </button>
            <button
              type="button"
              class="command-suggestion__action"
              :title="t('commandHistory.executeCommand')"
              @click.stop="executeCommand(suggestion.command)"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polygon points="5,3 19,12 5,21"></polygon>
              </svg>
            </button>
          </div>
        </div>
      </template>
    </SearchBox>
    
    <!-- 过滤器 -->
    <div v-if="showFilters" class="command-history-search-box__filters">
      <select 
        v-model="selectedCategory"
        class="filter-select"
        @change="handleFilterChange"
      >
        <option value="">{{ t('commandHistory.allCategories') }}</option>
        <option 
          v-for="category in categories"
          :key="category.value"
          :value="category.value"
        >
          {{ category.label }}
        </option>
      </select>
      
      <select 
        v-model="selectedServer"
        class="filter-select"
        @change="handleFilterChange"
      >
        <option value="">{{ t('commandHistory.allServers') }}</option>
        <option 
          v-for="server in servers"
          :key="server"
          :value="server"
        >
          {{ server }}
        </option>
      </select>
      
      <select 
        v-model="selectedTimeRange"
        class="filter-select"
        @change="handleFilterChange"
      >
        <option value="">{{ t('commandHistory.allTime') }}</option>
        <option value="today">{{ t('commandHistory.today') }}</option>
        <option value="week">{{ t('commandHistory.thisWeek') }}</option>
        <option value="month">{{ t('commandHistory.thisMonth') }}</option>
      </select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { TerminalOutlined, DatabaseOutlined, SettingOutlined, FileTextOutlined, CloudOutlined } from '@ant-design/icons-vue'
import SearchBox from './SearchBox.vue'

const { t } = useI18n()

// 命令历史类型定义
interface CommandHistoryItem {
  id: string
  command: string
  server: string
  timestamp: number
  category: 'system' | 'file' | 'network' | 'database' | 'custom'
  description?: string
  exitCode?: number
  duration?: number
  output?: string
}

interface Props {
  modelValue?: string
  placeholder?: string
  size?: 'small' | 'default' | 'large'
  commands?: CommandHistoryItem[]
  showSuggestions?: boolean
  showFilters?: boolean
  maxSuggestions?: number
}

interface Emits {
  'update:modelValue': [value: string]
  'search': [value: string, filters: SearchFilters]
  'suggestion-select': [command: CommandHistoryItem]
  'copy-command': [command: string]
  'execute-command': [command: string]
  'clear': []
}

interface SearchFilters {
  category: string
  server: string
  timeRange: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '搜索命令、服务器或描述...',
  size: 'default',
  commands: () => [],
  showSuggestions: true,
  showFilters: true,
  maxSuggestions: 10
})

const emit = defineEmits<Emits>()

// 响应式数据
const searchValue = ref(props.modelValue)
const selectedCategory = ref('')
const selectedServer = ref('')
const selectedTimeRange = ref('')

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

// 监听 searchValue 变化
watch(searchValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 计算属性
const categories = computed(() => [
  { value: 'system', label: t('commandHistory.systemCommands') },
  { value: 'file', label: t('commandHistory.fileCommands') },
  { value: 'network', label: t('commandHistory.networkCommands') },
  { value: 'database', label: t('commandHistory.databaseCommands') },
  { value: 'custom', label: t('commandHistory.customCommands') }
])

const servers = computed(() => {
  const serverSet = new Set(props.commands.map(cmd => cmd.server))
  return Array.from(serverSet).sort()
})

const filteredSuggestions = computed(() => {
  let filtered = props.commands

  // 应用搜索过滤
  if (searchValue.value) {
    const query = searchValue.value.toLowerCase()
    filtered = filtered.filter(cmd => 
      cmd.command.toLowerCase().includes(query) ||
      cmd.server.toLowerCase().includes(query) ||
      cmd.description?.toLowerCase().includes(query)
    )
  }

  // 应用分类过滤
  if (selectedCategory.value) {
    filtered = filtered.filter(cmd => cmd.category === selectedCategory.value)
  }

  // 应用服务器过滤
  if (selectedServer.value) {
    filtered = filtered.filter(cmd => cmd.server === selectedServer.value)
  }

  // 应用时间范围过滤
  if (selectedTimeRange.value) {
    const now = Date.now()
    const ranges = {
      today: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000
    }
    const range = ranges[selectedTimeRange.value as keyof typeof ranges]
    if (range) {
      filtered = filtered.filter(cmd => now - cmd.timestamp <= range)
    }
  }

  // 按时间戳降序排序，返回最新的结果
  return filtered
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, props.maxSuggestions)
})

// 获取命令图标
const getCommandIcon = (category: string) => {
  switch (category) {
    case 'system':
      return TerminalOutlined
    case 'file':
      return FileTextOutlined
    case 'network':
      return CloudOutlined
    case 'database':
      return DatabaseOutlined
    case 'custom':
    default:
      return SettingOutlined
  }
}

// 获取退出码样式类
const getExitCodeClass = (exitCode: number) => {
  return exitCode === 0 ? 'success' : 'error'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60 * 1000) {
    return t('commandHistory.justNow')
  } else if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return t('commandHistory.minutesAgo', { minutes })
  } else if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return t('commandHistory.hoursAgo', { hours })
  } else {
    const date = new Date(timestamp)
    return date.toLocaleDateString()
  }
}

// 事件处理
const handleSearch = (value: string) => {
  emit('search', value, {
    category: selectedCategory.value,
    server: selectedServer.value,
    timeRange: selectedTimeRange.value
  })
}

const handleInput = () => {
  // 输入时的处理逻辑
}

const handleClear = () => {
  emit('clear')
}

const handleSuggestionSelect = (suggestion: any) => {
  emit('suggestion-select', suggestion)
}

const handleFilterChange = () => {
  handleSearch(searchValue.value)
}

const copyCommand = async (command: string) => {
  try {
    await navigator.clipboard.writeText(command)
    emit('copy-command', command)
  } catch (error) {
    console.error('Failed to copy command:', error)
  }
}

const executeCommand = (command: string) => {
  emit('execute-command', command)
}
</script>

<style lang="less" scoped>
.command-history-search-box {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;

  &__input {
    width: 100%;
  }

  &__filters {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

.filter-select {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--bg-color-secondary);
  color: var(--text-color);
  font-size: 12px;
  min-width: 120px;
  
  &:focus {
    outline: none;
    border-color: var(--input-focus-border);
  }
}

// 命令建议样式
.command-suggestion {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  
  &__icon {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-top: 2px;
  }
  
  &__content {
    flex: 1;
    min-width: 0;
  }
  
  &__command {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 4px;
    word-break: break-all;
    line-height: 1.4;
  }
  
  &__meta {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 2px;
    font-size: 11px;
  }
  
  &__server {
    color: var(--text-color-secondary);
    font-weight: 500;
  }
  
  &__time {
    color: var(--text-color-tertiary);
  }
  
  &__exit-code {
    font-weight: bold;
    
    &.success {
      color: var(--success-color);
    }
    
    &.error {
      color: var(--error-color);
    }
  }
  
  &__description {
    font-size: 11px;
    color: var(--text-color-tertiary);
    line-height: 1.3;
    margin-top: 2px;
  }
  
  &__actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  &:hover &__actions {
    opacity: 1;
  }
  
  &__action {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--text-color-tertiary);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.15s ease;
    
    &:hover {
      background: var(--hover-bg-color);
      color: var(--text-color-secondary);
    }
    
    &:active {
      transform: scale(0.9);
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .command-history-search-box {
    &__filters {
      flex-direction: column;
    }
  }
  
  .filter-select {
    width: 100%;
    min-width: auto;
  }
  
  .command-suggestion {
    &__command {
      font-size: 12px;
    }
    
    &__actions {
      opacity: 1; // 在移动端始终显示操作按钮
    }
  }
}
</style>
