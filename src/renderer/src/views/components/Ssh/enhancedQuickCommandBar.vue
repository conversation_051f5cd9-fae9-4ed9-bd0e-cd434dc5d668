<template>
  <div class="enhanced-quick-command-bar">
    <!-- 主要快捷命令栏 -->
    <div class="main-command-bar">
      <!-- 添加按钮 -->
      <a-button
        type="primary"
        size="small"
        class="add-button"
        @click="openAddCommandDialog"
      >
        <PlusOutlined />
      </a-button>

      <!-- 快捷键按钮 -->
      <a-button
        size="small"
        class="shortcut-button"
        :type="showShortcutPanel ? 'primary' : 'default'"
        @click="toggleShortcutPanel"
      >
        <ThunderboltOutlined />
        <span class="shortcut-count">{{ enabledShortcuts.length }}</span>
      </a-button>

      <!-- 分类筛选 -->
      <a-select
        v-model:value="selectedCategory"
        size="small"
        class="category-select"
        @change="onCategoryChange"
      >
        <a-select-option value="all">{{ $t('quickCommand.allCategories') }}</a-select-option>
        <a-select-option
          v-for="category in categories"
          :key="category.key"
          :value="category.key"
        >
          {{ category.icon }} {{ category.name }}
        </a-select-option>
      </a-select>

      <!-- 快捷命令按钮容器 -->
      <div
        ref="buttonListRef"
        class="button-container"
      >
        <a-button
          v-for="cmd in filteredCommands"
          :key="cmd.id"
          v-contextmenu:quickCommandMenu
          class="quick-command-btn"
          size="middle"
          @click="handleClick(cmd)"
          @contextmenu.prevent="showContextMenu(cmd.id)"
        >
          <span class="command-icon">{{ getCategoryIcon(cmd.category) }}</span>
          <span class="command-name">{{ cmd.snippet_name }}</span>
          <span
            v-if="cmd.usage_count > 0"
            class="usage-badge"
            >{{ cmd.usage_count }}</span
          >
        </a-button>
      </div>

      <!-- 智能推荐按钮 -->
      <a-button
        size="small"
        class="recommendation-button"
        :disabled="recommendations.length === 0"
        @click="showRecommendations"
      >
        <BulbOutlined />
        <span>{{ recommendations.length }}</span>
      </a-button>
    </div>

    <!-- 快捷键面板 -->
    <div
      v-if="showShortcutPanel"
      class="shortcut-panel"
    >
      <div class="panel-header">
        <span class="panel-title">{{ $t('quickCommand.shortcuts') }}</span>
        <a-button
          size="small"
          @click="openShortcutManager"
        >
          <SettingOutlined />
        </a-button>
      </div>
      <div class="shortcut-list">
        <div
          v-for="shortcut in enabledShortcuts.slice(0, 8)"
          :key="shortcut.id"
          class="shortcut-item"
          @click="executeShortcut(shortcut)"
        >
          <span class="shortcut-icon">{{ shortcut.icon }}</span>
          <span class="shortcut-name">{{ shortcut.name }}</span>
          <span class="shortcut-key">{{ shortcut.shortcut }}</span>
        </div>
      </div>
    </div>

    <!-- 推荐命令弹窗 -->
    <a-modal
      v-model:open="showRecommendationModal"
      :title="$t('quickCommand.recommendations')"
      :footer="null"
      width="500px"
    >
      <div class="recommendation-list">
        <div
          v-for="rec in recommendations"
          :key="rec.id"
          class="recommendation-item"
          @click="executeShortcut(rec)"
        >
          <span class="rec-icon">{{ rec.icon }}</span>
          <div class="rec-content">
            <div class="rec-name">{{ rec.name }}</div>
            <div class="rec-desc">{{ rec.description }}</div>
            <div class="rec-usage">使用次数: {{ rec.usage_count }}</div>
          </div>
          <span class="rec-shortcut">{{ rec.shortcut }}</span>
        </div>
      </div>
    </a-modal>

    <!-- 快捷键管理器弹窗 -->
    <a-modal
      v-model:open="showShortcutManager"
      :title="$t('quickCommand.shortcutManager')"
      width="800px"
      :footer="null"
    >
      <div class="shortcut-manager">
        <div class="manager-header">
          <a-button
            type="primary"
            @click="openAddShortcutDialog"
          >
            <PlusOutlined /> {{ $t('quickCommand.addShortcut') }}
          </a-button>
          <a-input
            v-model:value="shortcutSearchText"
            :placeholder="$t('quickCommand.searchShortcuts')"
            class="search-input"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </div>
        <div class="shortcut-table">
          <a-table
            :columns="shortcutColumns"
            :data-source="filteredShortcuts"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'enabled'">
                <a-switch
                  :checked="record.enabled"
                  @change="toggleShortcut(record.id)"
                />
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button
                    size="small"
                    @click="editShortcut(record)"
                  >
                    <EditOutlined />
                  </a-button>
                  <a-button
                    size="small"
                    danger
                    @click="deleteShortcut(record.id)"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 添加/编辑快捷键弹窗 -->
    <a-modal
      v-model:open="showAddShortcutDialog"
      :title="editingShortcut ? $t('common.edit') : $t('quickCommand.addShortcut')"
      @ok="saveShortcut"
      @cancel="cancelShortcutEdit"
    >
      <a-form
        :model="shortcutForm"
        layout="vertical"
      >
        <a-form-item
          :label="$t('quickCommand.shortcutName')"
          required
        >
          <a-input v-model:value="shortcutForm.name" />
        </a-form-item>
        <a-form-item :label="$t('quickCommand.description')">
          <a-input v-model:value="shortcutForm.description" />
        </a-form-item>
        <a-form-item
          :label="$t('quickCommand.shortcutKey')"
          required
        >
          <a-input
            v-model:value="shortcutForm.shortcut"
            :placeholder="$t('quickCommand.shortcutPlaceholder')"
            @keydown="captureShortcut"
          />
        </a-form-item>
        <a-form-item
          :label="$t('quickCommand.command')"
          required
        >
          <a-textarea
            v-model:value="shortcutForm.command"
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
        </a-form-item>
        <a-form-item :label="$t('quickCommand.category')">
          <a-select v-model:value="shortcutForm.category">
            <a-select-option
              v-for="category in categories"
              :key="category.key"
              :value="category.key"
            >
              {{ category.icon }} {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item :label="$t('quickCommand.icon')">
          <a-input
            v-model:value="shortcutForm.icon"
            :placeholder="$t('quickCommand.iconPlaceholder')"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 原有的添加命令弹窗 -->
    <a-modal
      v-model:open="showAddCommandDialog"
      :title="isEditMode ? $t('common.edit') : $t('common.add')"
      @ok="addQuickCommand"
      @cancel="cancelAddCommand"
    >
      <a-input
        v-model:value="newCommandLabel"
        :placeholder="$t('quickCommand.scriptName')"
        style="margin-bottom: 8px"
      />
      <a-textarea
        v-model:value="newCommandValue"
        :placeholder="$t('quickCommand.scriptContent')"
        :auto-size="{ minRows: 6, maxRows: 10 }"
      />
    </a-modal>

    <!-- 右键菜单 -->
    <v-contextmenu
      ref="quickCommandMenu"
      :custom-class="'quickCommandMenu'"
    >
      <v-contextmenu-item @click="handleEditCommand(selectedCommandId)">
        {{ $t('common.edit') }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="handleRemoveCommand(selectedCommandId)">
        {{ $t('common.delete') }}
      </v-contextmenu-item>
      <v-contextmenu-item @click="createShortcutFromCommand(selectedCommandId)">
        {{ $t('quickCommand.createShortcut') }}
      </v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { PlusOutlined, ThunderboltOutlined, BulbOutlined, SettingOutlined, SearchOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { executeScript } from '../Ssh/commandScript'
import { inputManager } from '../Ssh/termInputManager'
import { smartShortcutManager, COMMAND_CATEGORIES, type ShortcutConfig } from '../../../utils/smartShortcutManager'
import Sortable from 'sortablejs'

const { t } = useI18n()

// 原有快捷命令接口
interface QuickCommand {
  id: number
  snippet_name: string
  snippet_content: string
  category?: string
  usage_count?: number
  create_at?: string
  update_at?: string
}

// 分类定义
const categories = [
  { key: COMMAND_CATEGORIES.SYSTEM, name: t('quickCommand.system'), icon: '⚙️' },
  { key: COMMAND_CATEGORIES.FILE, name: t('quickCommand.file'), icon: '📁' },
  { key: COMMAND_CATEGORIES.NETWORK, name: t('quickCommand.network'), icon: '🌐' },
  { key: COMMAND_CATEGORIES.DOCKER, name: t('quickCommand.docker'), icon: '🐳' },
  { key: COMMAND_CATEGORIES.GIT, name: t('quickCommand.git'), icon: '🔀' },
  { key: COMMAND_CATEGORIES.CUSTOM, name: t('quickCommand.custom'), icon: '🔧' }
]

// 响应式数据
const quickCommands = ref<QuickCommand[]>([])
const selectedCategory = ref('all')
const showShortcutPanel = ref(false)
const showRecommendationModal = ref(false)
const showShortcutManagerModal = ref(false)
const showAddShortcutDialog = ref(false)
const shortcutSearchText = ref('')
const editingShortcut = ref<ShortcutConfig | null>(null)

// 原有的添加命令相关
const showAddCommandDialog = ref(false)
const newCommandLabel = ref('')
const newCommandValue = ref('')
const selectedCommandId = ref<number | null>(null)
const isEditMode = ref(false)
const buttonListRef = ref<HTMLElement | null>(null)
let sortable: Sortable | null = null

// 快捷键表单
const shortcutForm = reactive({
  name: '',
  description: '',
  shortcut: '',
  command: '',
  category: COMMAND_CATEGORIES.CUSTOM,
  icon: '⚡',
  enabled: true
})

// 计算属性
const enabledShortcuts = computed(() => smartShortcutManager.getEnabledShortcuts())
const recommendations = computed(() => smartShortcutManager.getRecommendations())
const filteredCommands = computed(() => {
  if (selectedCategory.value === 'all') {
    return quickCommands.value
  }
  return quickCommands.value.filter((cmd) => cmd.category === selectedCategory.value)
})
const filteredShortcuts = computed(() => {
  const shortcuts = smartShortcutManager.getShortcuts()
  if (!shortcutSearchText.value) {
    return shortcuts
  }
  return shortcuts.filter(
    (s) =>
      s.name.toLowerCase().includes(shortcutSearchText.value.toLowerCase()) ||
      s.description.toLowerCase().includes(shortcutSearchText.value.toLowerCase())
  )
})

// 快捷键表格列定义
const shortcutColumns = [
  { title: t('quickCommand.name'), dataIndex: 'name', key: 'name' },
  { title: t('quickCommand.shortcut'), dataIndex: 'shortcut', key: 'shortcut' },
  { title: t('quickCommand.category'), dataIndex: 'category', key: 'category' },
  { title: t('quickCommand.usageCount'), dataIndex: 'usage_count', key: 'usage_count' },
  { title: t('quickCommand.enabled'), key: 'enabled', width: 80 },
  { title: t('common.actions'), key: 'actions', width: 120 }
]

// 方法
const getCategoryIcon = (category?: string) => {
  const cat = categories.find((c) => c.key === category)
  return cat?.icon || '📝'
}

const toggleShortcutPanel = () => {
  showShortcutPanel.value = !showShortcutPanel.value
}

const onCategoryChange = () => {
  // 分类变化时的处理逻辑
}

const showRecommendations = () => {
  showRecommendationModal.value = true
}

const openShortcutManager = () => {
  showShortcutManagerModal.value = true
}

const executeShortcut = (shortcut: ShortcutConfig) => {
  const terminal = {
    write: (data: string) => {
      inputManager.sendToActiveTerm(data)
    }
  }
  executeScript(shortcut.command, terminal)
  showRecommendationModal.value = false
}

const toggleShortcut = (id: string) => {
  smartShortcutManager.toggleShortcut(id)
}

const editShortcut = (shortcut: ShortcutConfig) => {
  editingShortcut.value = shortcut
  Object.assign(shortcutForm, shortcut)
  showAddShortcutDialog.value = true
}

const deleteShortcut = (id: string) => {
  smartShortcutManager.removeShortcut(id)
}

const openAddShortcutDialog = () => {
  editingShortcut.value = null
  Object.assign(shortcutForm, {
    name: '',
    description: '',
    shortcut: '',
    command: '',
    category: COMMAND_CATEGORIES.CUSTOM,
    icon: '⚡',
    enabled: true
  })
  showAddShortcutDialog.value = true
}

const captureShortcut = (event: KeyboardEvent) => {
  event.preventDefault()
  const keys = []
  if (event.ctrlKey) keys.push('Ctrl')
  if (event.shiftKey) keys.push('Shift')
  if (event.altKey) keys.push('Alt')
  if (event.key && !['Control', 'Shift', 'Alt'].includes(event.key)) {
    keys.push(event.key.toUpperCase())
  }
  if (keys.length > 1) {
    shortcutForm.shortcut = keys.join('+')
  }
}

const saveShortcut = () => {
  if (editingShortcut.value) {
    smartShortcutManager.updateShortcut(editingShortcut.value.id, shortcutForm)
  } else {
    smartShortcutManager.addShortcut(shortcutForm)
  }
  showAddShortcutDialog.value = false
}

const cancelShortcutEdit = () => {
  showAddShortcutDialog.value = false
  editingShortcut.value = null
}

const createShortcutFromCommand = (commandId: number | null) => {
  if (!commandId) return
  const command = quickCommands.value.find((cmd) => cmd.id === commandId)
  if (command) {
    Object.assign(shortcutForm, {
      name: command.snippet_name,
      description: `快捷键: ${command.snippet_name}`,
      shortcut: '',
      command: command.snippet_content,
      category: command.category || COMMAND_CATEGORIES.CUSTOM,
      icon: getCategoryIcon(command.category),
      enabled: true
    })
    showAddShortcutDialog.value = true
  }
}

// 原有方法
const openAddCommandDialog = () => {
  newCommandLabel.value = ''
  newCommandValue.value = ''
  showAddCommandDialog.value = true
  isEditMode.value = false
  selectedCommandId.value = null
}

const showContextMenu = (id: number) => {
  selectedCommandId.value = id
}

const handleEditCommand = async (id: number | null) => {
  if (id === null) return
  const cmd = quickCommands.value.find((item) => item.id === id)
  if (!cmd) return
  newCommandLabel.value = cmd.snippet_name
  newCommandValue.value = cmd.snippet_content
  isEditMode.value = true
  showAddCommandDialog.value = true
  selectedCommandId.value = id
}

const handleRemoveCommand = async (id: number | null) => {
  if (id === null) return
  await removeCommand(id)
}

const addQuickCommand = async () => {
  if (newCommandLabel.value && newCommandValue.value) {
    const cmd = {
      snippet_name: newCommandLabel.value,
      snippet_content: newCommandValue.value,
      category: COMMAND_CATEGORIES.CUSTOM
    }
    if (isEditMode.value && selectedCommandId.value !== null) {
      await editCommand({ id: selectedCommandId.value, ...cmd })
    } else {
      await addCommand(cmd)
    }
    showAddCommandDialog.value = false
    isEditMode.value = false
    selectedCommandId.value = null
  }
}

const cancelAddCommand = () => {
  showAddCommandDialog.value = false
  isEditMode.value = false
  selectedCommandId.value = null
}

const handleClick = (cmd: QuickCommand) => {
  // 更新使用计数
  cmd.usage_count = (cmd.usage_count || 0) + 1

  const terminal = {
    write: (data: string) => {
      inputManager.sendToActiveTerm(data)
    }
  }
  executeScript(cmd.snippet_content, terminal)
}

// API 调用
const api = (window as any).api
const refresh = async () => {
  const { data } = await api.userSnippetOperation({ operation: 'list' })
  quickCommands.value = data.snippets || []
}

const addCommand = async (params) => {
  await api.userSnippetOperation({
    operation: 'create',
    params
  })
  await refresh()
}

const editCommand = async (params: QuickCommand) => {
  await api.userSnippetOperation({
    operation: 'update',
    params
  })
  await refresh()
}

const removeCommand = async (id: number) => {
  await api.userSnippetOperation({
    operation: 'delete',
    params: { id }
  })
  await refresh()
}

// 生命周期
onMounted(async () => {
  await refresh()
  smartShortcutManager.initialize()

  nextTick(() => {
    if (buttonListRef.value) {
      sortable = Sortable.create(buttonListRef.value, {
        animation: 150,
        handle: '.quick-command-btn',
        onEnd: async (evt) => {
          const oldIndex = evt.oldIndex
          const newIndex = evt.newIndex
          if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
            // 处理排序逻辑
          }
        }
      })
    }
  })
})

onBeforeUnmount(() => {
  if (sortable) {
    sortable.destroy()
    sortable = null
  }
  smartShortcutManager.destroy()
})
</script>

<style scoped lang="less">
.enhanced-quick-command-bar {
  width: 100%;
  background: var(--globalInput-bg-color);
  border-radius: 4px;

  .main-command-bar {
    height: 30px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 4px;

    .add-button,
    .shortcut-button,
    .recommendation-button {
      height: 26px;
      flex-shrink: 0;
      background-color: var(--globalInput-bg-color);
      color: var(--text-color);
      border: 1px solid var(--border-color);

      &:hover {
        background-color: var(--button-bg-color);
      }
    }

    .shortcut-button {
      width: auto;
      padding: 0 8px;

      .shortcut-count {
        margin-left: 4px;
        font-size: 12px;
        background: var(--primary-color);
        color: white;
        border-radius: 8px;
        padding: 0 4px;
        min-width: 16px;
        text-align: center;
      }
    }

    .category-select {
      width: 120px;
      flex-shrink: 0;
    }

    .button-container {
      display: flex;
      overflow-x: auto;
      scrollbar-width: none;
      flex: 1;
      gap: 2px;

      .quick-command-btn {
        height: 26px;
        flex-shrink: 0;
        max-width: 140px;
        padding: 4px 8px;
        background-color: transparent;
        color: var(--text-color);
        border: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        gap: 4px;

        &:hover {
          background-color: var(--button-bg-color);
        }

        .command-icon {
          font-size: 12px;
        }

        .command-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;
        }

        .usage-badge {
          background: var(--success-color);
          color: white;
          border-radius: 8px;
          padding: 0 4px;
          font-size: 10px;
          min-width: 16px;
          text-align: center;
        }
      }
    }

    .recommendation-button {
      width: auto;
      padding: 0 8px;

      span {
        margin-left: 4px;
        font-size: 12px;
      }
    }
  }

  .shortcut-panel {
    background: var(--bg-color-secondary);
    border-top: 1px solid var(--border-color);
    padding: 8px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .panel-title {
        font-size: 12px;
        font-weight: 600;
        color: var(--text-color);
      }
    }

    .shortcut-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 4px;

      .shortcut-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--button-bg-color);
          border-color: var(--primary-color);
        }

        .shortcut-icon {
          font-size: 14px;
        }

        .shortcut-name {
          flex: 1;
          font-size: 12px;
          color: var(--text-color);
        }

        .shortcut-key {
          font-size: 10px;
          color: var(--text-color-secondary);
          background: var(--bg-color-tertiary);
          padding: 2px 4px;
          border-radius: 2px;
          font-family: monospace;
        }
      }
    }
  }
}

.recommendation-list {
  .recommendation-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--bg-color-secondary);
      border-color: var(--primary-color);
    }

    .rec-icon {
      font-size: 20px;
    }

    .rec-content {
      flex: 1;

      .rec-name {
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 4px;
      }

      .rec-desc {
        font-size: 12px;
        color: var(--text-color-secondary);
        margin-bottom: 2px;
      }

      .rec-usage {
        font-size: 10px;
        color: var(--text-color-tertiary);
      }
    }

    .rec-shortcut {
      font-size: 12px;
      color: var(--text-color-secondary);
      background: var(--bg-color-tertiary);
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
    }
  }
}

.shortcut-manager {
  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 12px;

    .search-input {
      width: 200px;
    }
  }

  .shortcut-table {
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>
