/**
 * Console连接管理器
 * 负责管理所有Console连接的生命周期、状态监控和资源清理
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { EventEmitter } from 'events'
import { v4 as uuidv4 } from 'uuid'
import {
  ConsoleConnection,
  ConsoleConnectionConfig,
  ConsoleConnectionResult,
  ConsoleConnectionStatus,
  ConsoleConnectionType,
  ConsoleCommandResult,
  ConsoleManagerOptions,
  IConsoleService
} from './types'
import { SerialService } from './serialService'
import { TelnetService } from './telnetService'

/**
 * Console连接管理器类
 * 实现连接池管理、状态监控、自动重连等功能
 */
export class ConsoleManager extends EventEmitter implements IConsoleService {
  private connections: Map<string, ConsoleConnection> = new Map()
  private options: Required<ConsoleManagerOptions>
  private cleanupTimer?: NodeJS.Timeout
  private logger: Console

  /**
   * 构造函数
   * @param options 管理器选项
   */
  constructor(options: ConsoleManagerOptions = {}) {
    super()

    // 设置默认选项
    this.options = {
      maxConnections: options.maxConnections ?? 50,
      defaultTimeout: options.defaultTimeout ?? 30000,
      enableLogging: options.enableLogging ?? true,
      logLevel: options.logLevel ?? 'info',
      cleanupInterval: options.cleanupInterval ?? 60000
    }

    this.logger = console

    // 启动定期清理
    this.startCleanupTimer()

    this.log('info', 'Console管理器已初始化', { options: this.options })
  }

  /**
   * 建立Console连接
   * @param config 连接配置
   * @returns 连接结果
   */
  public async connect(config: ConsoleConnectionConfig): Promise<ConsoleConnectionResult> {
    try {
      // 检查连接数限制
      if (this.connections.size >= this.options.maxConnections) {
        throw new Error(`连接数已达到最大限制: ${this.options.maxConnections}`)
      }

      // 检查是否已存在相同ID的连接
      if (this.connections.has(config.id)) {
        throw new Error(`连接ID已存在: ${config.id}`)
      }

      this.log('info', '开始建立Console连接', {
        id: config.id,
        type: config.type,
        assetId: config.assetId
      })

      // 创建连接实例
      const connection = await this.createConnection(config)

      // 设置事件监听
      this.setupConnectionEvents(connection)

      // 存储连接
      this.connections.set(config.id, connection)

      // 建立连接
      await connection.connect()

      const result: ConsoleConnectionResult = {
        connectionId: config.id,
        status: ConsoleConnectionStatus.CONNECTED,
        message: '连接建立成功',
        connectedAt: new Date()
      }

      this.log('info', 'Console连接建立成功', { connectionId: config.id })
      this.emit('connectionEstablished', result)

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      this.log('error', 'Console连接建立失败', {
        id: config.id,
        error: errorMessage
      })

      return {
        connectionId: config.id,
        status: ConsoleConnectionStatus.ERROR,
        message: `连接失败: ${errorMessage}`,
        error: error instanceof Error ? error : new Error(errorMessage)
      }
    }
  }

  /**
   * 断开Console连接
   * @param connectionId 连接ID
   */
  public async disconnect(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      throw new Error(`连接不存在: ${connectionId}`)
    }

    try {
      this.log('info', '断开Console连接', { connectionId })
      await connection.disconnect()
      this.connections.delete(connectionId)
      this.log('info', 'Console连接已断开', { connectionId })
    } catch (error) {
      this.log('error', 'Console连接断开失败', {
        connectionId,
        error: error instanceof Error ? error.message : '未知错误'
      })
      throw error
    }
  }

  /**
   * 执行命令
   * @param connectionId 连接ID
   * @param command 要执行的命令
   * @param timeout 超时时间
   * @returns 命令执行结果
   */
  public async executeCommand(connectionId: string, command: string, timeout?: number): Promise<ConsoleCommandResult> {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      throw new Error(`连接不存在: ${connectionId}`)
    }

    if (connection.status !== ConsoleConnectionStatus.CONNECTED) {
      throw new Error(`连接状态异常: ${connection.status}`)
    }

    try {
      this.log('debug', '执行Console命令', { connectionId, command })
      const result = await connection.executeCommand(command, timeout || this.options.defaultTimeout)
      this.log('debug', 'Console命令执行完成', { connectionId, command, duration: result.duration })
      return result
    } catch (error) {
      this.log('error', 'Console命令执行失败', {
        connectionId,
        command,
        error: error instanceof Error ? error.message : '未知错误'
      })
      throw error
    }
  }

  /**
   * 获取连接状态
   * @param connectionId 连接ID
   * @returns 连接信息
   */
  public async getStatus(connectionId: string) {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      return {
        connectionId,
        status: ConsoleConnectionStatus.DISCONNECTED,
        message: '连接不存在'
      }
    }

    return {
      connectionId,
      status: connection.status,
      info: connection.getConnectionInfo(),
      isAlive: connection.isAlive()
    }
  }

  /**
   * 获取所有连接列表
   * @returns 连接列表
   */
  public async listConnections() {
    const connections = Array.from(this.connections.values()).map((conn) => ({
      id: conn.id,
      status: conn.status,
      info: conn.getConnectionInfo()
    }))

    return {
      total: connections.length,
      connections
    }
  }

  /**
   * 创建连接实例
   * @param config 连接配置
   * @returns 连接实例
   */
  public async createConnection(config: ConsoleConnectionConfig): Promise<ConsoleConnection> {
    switch (config.type) {
      case ConsoleConnectionType.SERIAL:
        return new SerialService(config.id, config)
      case ConsoleConnectionType.TELNET:
        return new TelnetService(config.id, config)
      case ConsoleConnectionType.SSH:
        // SSH服务将在后续实现
        throw new Error('SSH连接服务尚未实现')
      default:
        throw new Error(`不支持的连接类型: ${config.type}`)
    }
  }

  /**
   * 获取连接实例
   * @param connectionId 连接ID
   * @returns 连接实例
   */
  public getConnection(connectionId: string): ConsoleConnection | undefined {
    return this.connections.get(connectionId)
  }

  /**
   * 关闭连接
   * @param connectionId 连接ID
   */
  public async closeConnection(connectionId: string): Promise<void> {
    await this.disconnect(connectionId)
  }

  /**
   * 获取所有连接
   * @returns 连接数组
   */
  public getAllConnections(): ConsoleConnection[] {
    return Array.from(this.connections.values())
  }

  /**
   * 清理无效连接
   */
  public async cleanup(): Promise<void> {
    const deadConnections: string[] = []

    for (const [id, connection] of this.connections) {
      if (!connection.isAlive() || connection.status === ConsoleConnectionStatus.ERROR) {
        deadConnections.push(id)
      }
    }

    if (deadConnections.length > 0) {
      this.log('info', '清理无效连接', { count: deadConnections.length, connections: deadConnections })

      for (const id of deadConnections) {
        try {
          await this.disconnect(id)
        } catch (error) {
          this.log('warn', '清理连接时发生错误', {
            connectionId: id,
            error: error instanceof Error ? error.message : '未知错误'
          })
        }
      }
    }
  }

  /**
   * 关闭管理器
   */
  public async shutdown(): Promise<void> {
    this.log('info', '关闭Console管理器')

    // 停止清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    // 关闭所有连接
    const connectionIds = Array.from(this.connections.keys())
    for (const id of connectionIds) {
      try {
        await this.disconnect(id)
      } catch (error) {
        this.log('warn', '关闭连接时发生错误', {
          connectionId: id,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    this.log('info', 'Console管理器已关闭')
  }

  /**
   * 设置连接事件监听
   * @param connection 连接实例
   */
  private setupConnectionEvents(connection: ConsoleConnection): void {
    connection.on('connect', () => {
      this.log('debug', '连接建立事件', { connectionId: connection.id })
      this.emit('connectionConnect', connection.id)
    })

    connection.on('disconnect', (reason) => {
      this.log('debug', '连接断开事件', { connectionId: connection.id, reason })
      this.emit('connectionDisconnect', connection.id, reason)
    })

    connection.on('data', (data) => {
      this.emit('connectionData', connection.id, data)
    })

    connection.on('error', (error) => {
      this.log('error', '连接错误事件', {
        connectionId: connection.id,
        error: error.message
      })
      this.emit('connectionError', connection.id, error)
    })

    connection.on('statusChange', (status) => {
      this.log('debug', '连接状态变化', { connectionId: connection.id, status })
      this.emit('connectionStatusChange', connection.id, status)
    })

    connection.on('reconnect', (attempt) => {
      this.log('info', '连接重连', { connectionId: connection.id, attempt })
      this.emit('connectionReconnect', connection.id, attempt)
    })
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup().catch((error) => {
        this.log('error', '定期清理失败', {
          error: error instanceof Error ? error.message : '未知错误'
        })
      })
    }, this.options.cleanupInterval)
  }

  /**
   * 记录日志
   * @param level 日志级别
   * @param message 日志消息
   * @param meta 元数据
   */
  private log(level: string, message: string, meta?: any): void {
    if (!this.options.enableLogging) {
      return
    }

    const logLevels = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = logLevels.indexOf(this.options.logLevel)
    const messageLevelIndex = logLevels.indexOf(level)

    if (messageLevelIndex >= currentLevelIndex) {
      const timestamp = new Date().toISOString()
      const logMessage = `[${timestamp}] [${level.toUpperCase()}] [ConsoleManager] ${message}`

      if (meta) {
        this.logger.log(logMessage, meta)
      } else {
        this.logger.log(logMessage)
      }
    }
  }
}
