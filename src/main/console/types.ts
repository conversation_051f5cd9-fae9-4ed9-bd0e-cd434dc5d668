/**
 * Console连接服务类型定义
 * 定义Console连接相关的接口、类型和配置
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { EventEmitter } from 'events'

/**
 * Console连接类型枚举
 */
export enum ConsoleConnectionType {
  SERIAL = 'serial',
  TELNET = 'telnet',
  SSH = 'ssh'
}

/**
 * Console连接状态枚举
 */
export enum ConsoleConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

/**
 * 串口配置接口
 */
export interface SerialConfig {
  /** 串口路径 */
  path: string
  /** 波特率 */
  baudRate: number
  /** 数据位 */
  dataBits: 5 | 6 | 7 | 8
  /** 停止位 */
  stopBits: 1 | 1.5 | 2
  /** 校验位 */
  parity: 'none' | 'even' | 'mark' | 'odd' | 'space'
  /** 流控制 */
  flowControl?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
}

/**
 * Telnet配置接口
 */
export interface TelnetConfig {
  /** 主机地址 */
  host: string
  /** 端口号 */
  port: number
  /** 连接超时时间（毫秒） */
  timeout?: number
  /** 登录提示符 */
  loginPrompt?: string
  /** 密码提示符 */
  passwordPrompt?: string
  /** 命令提示符 */
  shellPrompt?: string
  /** 用户名 */
  username?: string
  /** 密码 */
  password?: string
  /** 是否启用调试模式 */
  debug?: boolean
}

/**
 * SSH配置接口
 */
export interface SSHConfig {
  /** 主机地址 */
  host: string
  /** 端口号 */
  port: number
  /** 用户名 */
  username: string
  /** 密码 */
  password?: string
  /** 私钥路径 */
  privateKey?: string
  /** 私钥密码 */
  passphrase?: string
  /** 连接超时时间（毫秒） */
  timeout?: number
  /** 保持连接间隔（毫秒） */
  keepaliveInterval?: number
  /** 保持连接计数 */
  keepaliveCountMax?: number
}

/**
 * Console连接配置联合类型
 */
export type ConsoleConfig = SerialConfig | TelnetConfig | SSHConfig

/**
 * Console连接配置接口
 */
export interface ConsoleConnectionConfig {
  /** 连接ID */
  id: string
  /** 资产ID */
  assetId: string
  /** 连接名称 */
  name: string
  /** 连接类型 */
  type: ConsoleConnectionType
  /** 连接配置 */
  config: ConsoleConfig
  /** 是否自动重连 */
  autoReconnect?: boolean
  /** 重连间隔（毫秒） */
  reconnectInterval?: number
  /** 最大重连次数 */
  maxReconnectAttempts?: number
  /** 创建时间 */
  createdAt?: Date
  /** 更新时间 */
  updatedAt?: Date
}

/**
 * Console连接结果接口
 */
export interface ConsoleConnectionResult {
  /** 连接ID */
  connectionId: string
  /** 连接状态 */
  status: ConsoleConnectionStatus
  /** 状态消息 */
  message: string
  /** 错误信息 */
  error?: Error
  /** 连接时间 */
  connectedAt?: Date
}

/**
 * Console命令执行结果接口
 */
export interface ConsoleCommandResult {
  /** 命令 */
  command: string
  /** 输出结果 */
  output: string
  /** 错误信息 */
  error?: string
  /** 执行时间（毫秒） */
  duration: number
  /** 执行时间戳 */
  timestamp: Date
}

/**
 * Console连接信息接口
 */
export interface ConsoleConnectionInfo {
  /** 连接ID */
  id: string
  /** 资产ID */
  assetId: string
  /** 连接名称 */
  name: string
  /** 连接类型 */
  type: ConsoleConnectionType
  /** 连接状态 */
  status: ConsoleConnectionStatus
  /** 连接配置 */
  config: ConsoleConnectionConfig
  /** 连接开始时间 */
  startedAt: Date
  /** 最后活动时间 */
  lastActivityAt: Date
  /** 重连次数 */
  reconnectCount: number
  /** 发送字节数 */
  bytesSent: number
  /** 接收字节数 */
  bytesReceived: number
}

/**
 * Console管理器选项接口
 */
export interface ConsoleManagerOptions {
  /** 最大连接数 */
  maxConnections?: number
  /** 默认超时时间（毫秒） */
  defaultTimeout?: number
  /** 是否启用日志 */
  enableLogging?: boolean
  /** 日志级别 */
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  /** 连接池清理间隔（毫秒） */
  cleanupInterval?: number
}

/**
 * Console连接事件接口
 */
export interface ConsoleConnectionEvents {
  /** 连接建立事件 */
  connect: (connectionId: string) => void
  /** 连接断开事件 */
  disconnect: (connectionId: string, reason?: string) => void
  /** 数据接收事件 */
  data: (connectionId: string, data: Buffer | string) => void
  /** 错误事件 */
  error: (connectionId: string, error: Error) => void
  /** 状态变化事件 */
  statusChange: (connectionId: string, status: ConsoleConnectionStatus) => void
  /** 重连事件 */
  reconnect: (connectionId: string, attempt: number) => void
}

/**
 * Console连接抽象类
 */
export abstract class ConsoleConnection extends EventEmitter {
  /** 连接ID */
  public readonly id: string
  /** 连接配置 */
  public readonly config: ConsoleConnectionConfig
  /** 连接状态 */
  public status: ConsoleConnectionStatus
  /** 连接开始时间 */
  public startedAt?: Date
  /** 最后活动时间 */
  public lastActivityAt?: Date
  /** 重连次数 */
  public reconnectCount: number = 0

  constructor(id: string, config: ConsoleConnectionConfig) {
    super()
    this.id = id
    this.config = config
    this.status = ConsoleConnectionStatus.DISCONNECTED
  }

  /**
   * 建立连接
   */
  abstract connect(): Promise<void>

  /**
   * 断开连接
   */
  abstract disconnect(): Promise<void>

  /**
   * 发送数据
   * @param data 要发送的数据
   */
  abstract send(data: string | Buffer): Promise<void>

  /**
   * 执行命令
   * @param command 要执行的命令
   * @param timeout 超时时间（毫秒）
   */
  abstract executeCommand(command: string, timeout?: number): Promise<ConsoleCommandResult>

  /**
   * 获取连接信息
   */
  abstract getConnectionInfo(): ConsoleConnectionInfo

  /**
   * 检查连接是否活跃
   */
  abstract isAlive(): boolean
}

/**
 * Console服务接口
 */
export interface IConsoleService {
  /**
   * 创建连接
   * @param config 连接配置
   */
  createConnection(config: ConsoleConnectionConfig): Promise<ConsoleConnection>

  /**
   * 获取连接
   * @param connectionId 连接ID
   */
  getConnection(connectionId: string): ConsoleConnection | undefined

  /**
   * 关闭连接
   * @param connectionId 连接ID
   */
  closeConnection(connectionId: string): Promise<void>

  /**
   * 获取所有连接
   */
  getAllConnections(): ConsoleConnection[]

  /**
   * 清理无效连接
   */
  cleanup(): Promise<void>
}
