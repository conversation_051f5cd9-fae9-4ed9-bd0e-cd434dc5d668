/**
 * Console和RDP连接配置管理
 * 提供功能开关、参数配置和配置验证功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { EventEmitter } from 'events'
import { app } from 'electron'
import * as path from 'path'
import * as fs from 'fs/promises'

/**
 * Console连接配置
 */
export interface ConsoleConfig {
  enabled: boolean
  serial: {
    enabled: boolean
    timeout: number
    defaultBaudRate: number
    defaultDataBits: number
    defaultStopBits: number
    defaultParity: 'none' | 'even' | 'odd'
  }
  telnet: {
    enabled: boolean
    defaultPort: number
    timeout: number
    keepAlive: boolean
  }
  ssh: {
    enabled: boolean
    defaultPort: number
    timeout: number
    keepAlive: boolean
  }
}

/**
 * RDP连接配置
 */
export interface RdpConfig {
  enabled: boolean
  defaultPort: number
  timeout: number
  security: {
    encryption: boolean
    authentication: 'ntlm' | 'basic' | 'negotiate'
    certificateValidation: boolean
  }
  display: {
    defaultWidth: number
    defaultHeight: number
    defaultColorDepth: number
    fullscreen: boolean
  }
  performance: {
    compression: boolean
    bitmapCaching: boolean
    fontSmoothing: boolean
    desktopComposition: boolean
  }
  redirection: {
    clipboard: boolean
    drives: boolean
    printers: boolean
    audio: boolean
  }
}

/**
 * 连接管理配置
 */
export interface ConnectionManagementConfig {
  maxConcurrentConnections: number
  connectionTimeout: number
  reconnectAttempts: number
  reconnectDelay: number
  sessionLogging: boolean
  auditLogging: boolean
}

/**
 * 完整的连接配置
 */
export interface ConnectionConfig {
  console: ConsoleConfig
  rdp: RdpConfig
  management: ConnectionManagementConfig
  version: string
  lastUpdated: string
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ConnectionConfig = {
  console: {
    enabled: true,
    serial: {
      enabled: true,
      timeout: 5000,
      defaultBaudRate: 9600,
      defaultDataBits: 8,
      defaultStopBits: 1,
      defaultParity: 'none'
    },
    telnet: {
      enabled: true,
      defaultPort: 23,
      timeout: 5000,
      keepAlive: true
    },
    ssh: {
      enabled: true,
      defaultPort: 22,
      timeout: 5000,
      keepAlive: true
    }
  },
  rdp: {
    enabled: true,
    defaultPort: 3389,
    timeout: 10000,
    security: {
      encryption: true,
      authentication: 'ntlm',
      certificateValidation: false
    },
    display: {
      defaultWidth: 1920,
      defaultHeight: 1080,
      defaultColorDepth: 32,
      fullscreen: false
    },
    performance: {
      compression: true,
      bitmapCaching: true,
      fontSmoothing: true,
      desktopComposition: false
    },
    redirection: {
      clipboard: true,
      drives: false,
      printers: false,
      audio: true
    }
  },
  management: {
    maxConcurrentConnections: 10,
    connectionTimeout: 30000,
    reconnectAttempts: 3,
    reconnectDelay: 2000,
    sessionLogging: true,
    auditLogging: true
  },
  version: '1.0.0',
  lastUpdated: new Date().toISOString()
}

/**
 * 连接配置管理器
 */
export class ConnectionConfigManager extends EventEmitter {
  private static instance: ConnectionConfigManager
  private config: ConnectionConfig
  private configPath: string
  private isInitialized: boolean = false

  private constructor() {
    super()
    this.config = { ...DEFAULT_CONFIG }
    this.configPath = path.join(app.getPath('userData'), 'connection-config.json')
  }

  /**
   * 获取配置管理器单例
   */
  public static getInstance(): ConnectionConfigManager {
    if (!ConnectionConfigManager.instance) {
      ConnectionConfigManager.instance = new ConnectionConfigManager()
    }
    return ConnectionConfigManager.instance
  }

  /**
   * 初始化配置管理器
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return
    }

    try {
      await this.loadConfig()
      this.isInitialized = true
      console.log('[ConnectionConfigManager] 配置管理器已初始化')
    } catch (error) {
      console.error('[ConnectionConfigManager] 初始化失败:', error)
      // 使用默认配置
      this.config = { ...DEFAULT_CONFIG }
      await this.saveConfig()
      this.isInitialized = true
    }
  }

  /**
   * 加载配置文件
   */
  private async loadConfig(): Promise<void> {
    try {
      const configData = await fs.readFile(this.configPath, 'utf-8')
      const loadedConfig = JSON.parse(configData)

      // 合并配置，确保新增的配置项有默认值
      this.config = this.mergeConfig(DEFAULT_CONFIG, loadedConfig)

      console.log('[ConnectionConfigManager] 配置文件已加载')
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        console.log('[ConnectionConfigManager] 配置文件不存在，使用默认配置')
        this.config = { ...DEFAULT_CONFIG }
        await this.saveConfig()
      } else {
        throw error
      }
    }
  }

  /**
   * 保存配置文件
   */
  private async saveConfig(): Promise<void> {
    try {
      this.config.lastUpdated = new Date().toISOString()
      const configData = JSON.stringify(this.config, null, 2)
      await fs.writeFile(this.configPath, configData, 'utf-8')
      console.log('[ConnectionConfigManager] 配置文件已保存')
    } catch (error) {
      console.error('[ConnectionConfigManager] 保存配置失败:', error)
      throw error
    }
  }

  /**
   * 合并配置对象
   */
  private mergeConfig(defaultConfig: any, userConfig: any): any {
    const result = { ...defaultConfig }

    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (typeof userConfig[key] === 'object' && userConfig[key] !== null && !Array.isArray(userConfig[key])) {
          result[key] = this.mergeConfig(defaultConfig[key] || {}, userConfig[key])
        } else {
          result[key] = userConfig[key]
        }
      }
    }

    return result
  }

  /**
   * 获取完整配置
   */
  public getConfig(): ConnectionConfig {
    return { ...this.config }
  }

  /**
   * 获取Console配置
   */
  public getConsoleConfig(): ConsoleConfig {
    return { ...this.config.console }
  }

  /**
   * 获取RDP配置
   */
  public getRdpConfig(): RdpConfig {
    return { ...this.config.rdp }
  }

  /**
   * 获取连接管理配置
   */
  public getManagementConfig(): ConnectionManagementConfig {
    return { ...this.config.management }
  }

  /**
   * 更新Console配置
   */
  public async updateConsoleConfig(config: Partial<ConsoleConfig>): Promise<void> {
    this.config.console = { ...this.config.console, ...config }
    await this.saveConfig()
    this.emit('config-updated', { type: 'console', config: this.config.console })
  }

  /**
   * 更新RDP配置
   */
  public async updateRdpConfig(config: Partial<RdpConfig>): Promise<void> {
    this.config.rdp = { ...this.config.rdp, ...config }
    await this.saveConfig()
    this.emit('config-updated', { type: 'rdp', config: this.config.rdp })
  }

  /**
   * 更新连接管理配置
   */
  public async updateManagementConfig(config: Partial<ConnectionManagementConfig>): Promise<void> {
    this.config.management = { ...this.config.management, ...config }
    await this.saveConfig()
    this.emit('config-updated', { type: 'management', config: this.config.management })
  }

  /**
   * 重置为默认配置
   */
  public async resetToDefault(): Promise<void> {
    this.config = { ...DEFAULT_CONFIG }
    await this.saveConfig()
    this.emit('config-reset')
  }

  /**
   * 验证配置
   */
  public validateConfig(config: Partial<ConnectionConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // 验证Console配置
    if (config.console) {
      if (config.console.serial) {
        if (config.console.serial.timeout < 1000 || config.console.serial.timeout > 60000) {
          errors.push('Console串口超时时间必须在1000-60000ms之间')
        }
        if (![9600, 19200, 38400, 57600, 115200].includes(config.console.serial.defaultBaudRate)) {
          errors.push('Console串口波特率必须是标准值')
        }
      }

      if (config.console.telnet) {
        if (config.console.telnet.defaultPort < 1 || config.console.telnet.defaultPort > 65535) {
          errors.push('Console Telnet端口必须在1-65535之间')
        }
      }
    }

    // 验证RDP配置
    if (config.rdp) {
      if (config.rdp.defaultPort < 1 || config.rdp.defaultPort > 65535) {
        errors.push('RDP端口必须在1-65535之间')
      }

      if (config.rdp.display) {
        if (config.rdp.display.defaultWidth < 640 || config.rdp.display.defaultWidth > 7680) {
          errors.push('RDP显示宽度必须在640-7680之间')
        }
        if (config.rdp.display.defaultHeight < 480 || config.rdp.display.defaultHeight > 4320) {
          errors.push('RDP显示高度必须在480-4320之间')
        }
        if (![8, 16, 24, 32].includes(config.rdp.display.defaultColorDepth)) {
          errors.push('RDP颜色深度必须是8、16、24或32位')
        }
      }
    }

    // 验证管理配置
    if (config.management) {
      if (config.management.maxConcurrentConnections < 1 || config.management.maxConcurrentConnections > 100) {
        errors.push('最大并发连接数必须在1-100之间')
      }
      if (config.management.reconnectAttempts < 0 || config.management.reconnectAttempts > 10) {
        errors.push('重连尝试次数必须在0-10之间')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 检查功能是否启用
   */
  public isConsoleEnabled(): boolean {
    return this.config.console.enabled
  }

  public isRdpEnabled(): boolean {
    return this.config.rdp.enabled
  }

  public isSerialEnabled(): boolean {
    return this.config.console.enabled && this.config.console.serial.enabled
  }

  public isTelnetEnabled(): boolean {
    return this.config.console.enabled && this.config.console.telnet.enabled
  }

  public isSshConsoleEnabled(): boolean {
    return this.config.console.enabled && this.config.console.ssh.enabled
  }

  /**
   * 清理资源
   */
  public async dispose(): Promise<void> {
    this.removeAllListeners()
    console.log('[ConnectionConfigManager] 配置管理器已清理')
  }
}

/**
 * 获取配置管理器实例
 */
export function getConnectionConfigManager(): ConnectionConfigManager {
  return ConnectionConfigManager.getInstance()
}
