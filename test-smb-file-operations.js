/**
 * SMB文件操作测试脚本
 * 功能：测试SMB文件操作工具的各项功能
 * 依赖：./smb-file-operations.js
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const { initSMBProtocol, scanFiles, copySMBFilesFromOldToNew } = require('./smb-file-operations')
const fs = require('fs')
const path = require('path')
const os = require('os')

/**
 * 测试本地文件扫描功能
 */
function testLocalFileScanning() {
  console.log('🧪 测试本地文件扫描功能')
  console.log('='.repeat(40))

  // 使用当前目录进行测试
  const testFolder = process.cwd()
  const targetFiles = ['package.json', 'README.md']
  const targetExt = ['.js', '.ts', '.md']

  console.log(`📂 测试目录: ${testFolder}`)
  console.log(`🎯 目标文件: ${targetFiles.join(', ')}`)
  console.log(`🎯 目标扩展名: ${targetExt.join(', ')}`)

  try {
    const result = scanFiles(testFolder, targetFiles, targetExt)

    console.log('\n📊 扫描结果:')
    if (Object.keys(result).length === 0) {
      console.log('⚠️ 未找到符合条件的文件')
    } else {
      for (const [parentDir, files] of Object.entries(result)) {
        console.log(`📁 ${parentDir}:`)
        files.forEach((file) => {
          console.log(`   📄 ${path.basename(file)}`)
        })
      }
    }

    console.log('✅ 本地文件扫描测试完成\n')
    return true
  } catch (error) {
    console.error(`❌ 本地文件扫描测试失败: ${error.message}\n`)
    return false
  }
}

/**
 * 测试SMB连接配置
 */
function testSMBConfiguration() {
  console.log('🧪 测试SMB连接配置')
  console.log('='.repeat(40))

  try {
    // 测试配置创建（不实际连接）
    const testConfig = {
      host: '************',
      username: '<EMAIL>',
      password: 'test-password',
      domain: 'brbiotech',
      share: 'public'
    }

    console.log('🔧 测试SMB配置创建...')
    console.log(`   主机: ${testConfig.host}`)
    console.log(`   用户: ${testConfig.username}`)
    console.log(`   域: ${testConfig.domain}`)
    console.log(`   共享: ${testConfig.share}`)

    // 这里只是验证配置格式，不实际连接
    const smbClient = initSMBProtocol(testConfig.host, testConfig.username, testConfig.password, testConfig.domain, testConfig.share)

    if (smbClient) {
      console.log('✅ SMB客户端配置创建成功')
      // 立即断开，避免实际连接
      smbClient.disconnect()
    }

    console.log('✅ SMB连接配置测试完成\n')
    return true
  } catch (error) {
    console.error(`❌ SMB连接配置测试失败: ${error.message}\n`)
    return false
  }
}

/**
 * 测试SMB文件拷贝功能（模拟）
 */
async function testSMBFileCopy() {
  console.log('🧪 测试SMB文件拷贝功能（模拟）')
  console.log('='.repeat(40))

  try {
    // 创建临时测试目录
    const testDir = path.join(os.tmpdir(), 'smb-test-download')
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true })
    }

    console.log(`📁 创建测试下载目录: ${testDir}`)

    // 模拟配置（不实际连接）
    const testConfig = {
      oldIP: '************',
      username: '<EMAIL>',
      password: 'test-password',
      targetFolder: testDir,
      domain: 'brbiotech',
      share: 'public'
    }

    console.log('🔧 模拟SMB文件拷贝配置:')
    console.log(`   远程IP: ${testConfig.oldIP}`)
    console.log(`   用户名: ${testConfig.username}`)
    console.log(`   本地保存路径: ${testConfig.targetFolder}`)
    console.log(`   域: ${testConfig.domain}`)
    console.log(`   共享: ${testConfig.share}`)

    console.log('\n💡 注意: 这是模拟测试，不会实际连接SMB服务器')
    console.log('💡 实际使用时需要确保:')
    console.log('   1. SMB服务器可访问')
    console.log('   2. 用户名密码正确')
    console.log('   3. 共享文件夹存在')
    console.log('   4. 有足够的访问权限')

    // 清理测试目录
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true })
      console.log(`🗑️ 清理测试目录: ${testDir}`)
    }

    console.log('✅ SMB文件拷贝功能测试完成\n')
    return true
  } catch (error) {
    console.error(`❌ SMB文件拷贝功能测试失败: ${error.message}\n`)
    return false
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始SMB文件操作功能测试')
  console.log('='.repeat(50))
  console.log('')

  const results = []

  // 运行各项测试
  results.push(testLocalFileScanning())
  results.push(testSMBConfiguration())
  results.push(await testSMBFileCopy())

  // 输出测试总结
  console.log('📊 测试总结')
  console.log('='.repeat(30))

  const passedTests = results.filter((r) => r === true).length
  const totalTests = results.length

  console.log(`✅ 通过: ${passedTests}/${totalTests}`)
  console.log(`❌ 失败: ${totalTests - passedTests}/${totalTests}`)

  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！SMB文件操作工具准备就绪。')
    console.log('\n📋 使用说明:')
    console.log('   1. 运行 node smb-file-operations.js 启动交互式界面')
    console.log('   2. 选择操作模式（扫描文件或拷贝文件）')
    console.log('   3. 按提示输入相关参数')
    console.log('\n⚠️ 注意事项:')
    console.log('   - 确保SMB服务器可访问')
    console.log('   - 使用正确的用户名和密码')
    console.log('   - 检查网络连接和防火墙设置')
  } else {
    console.log('\n⚠️ 部分测试失败，请检查相关配置。')
  }

  console.log('\n🏁 测试完成！')
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().catch((error) => {
    console.error('💥 测试运行失败:', error.message)
    process.exit(1)
  })
}

module.exports = {
  testLocalFileScanning,
  testSMBConfiguration,
  testSMBFileCopy,
  runAllTests
}
