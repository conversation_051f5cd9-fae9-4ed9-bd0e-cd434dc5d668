<template>
  <div class="monitor-tab">
    <!-- 系统状态概览 -->
    <div class="status-overview">
      <div
        class="overview-card"
        :class="systemStatus.overall"
      >
        <div class="card-header">
          <component
            :is="systemStatus.icon"
            class="status-icon"
          />
          <div class="status-info">
            <div class="status-title">系统状态</div>
            <div class="status-value">{{ systemStatus.text }}</div>
          </div>
        </div>
        <div class="status-metrics">
          <div class="metric">
            <span class="metric-label">CPU使用率</span>
            <span class="metric-value">{{ metrics.cpu }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">内存使用率</span>
            <span class="metric-value">{{ metrics.memory }}%</span>
          </div>
          <div class="metric">
            <span class="metric-label">网络延迟</span>
            <span class="metric-value">{{ metrics.latency }}ms</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务状态 -->
    <div class="services-status">
      <div class="section-header">
        <h3>服务状态</h3>
        <a-button
          size="small"
          @click="refreshServices"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </a-button>
      </div>
      <div class="services-grid">
        <div
          v-for="service in services"
          :key="service.name"
          class="service-card"
          :class="service.status"
        >
          <div class="service-header">
            <component
              :is="service.icon"
              class="service-icon"
            />
            <div class="service-info">
              <div class="service-name">{{ service.name }}</div>
              <div class="service-desc">{{ service.description }}</div>
            </div>
            <div
              class="service-status"
              :class="service.status"
            >
              <div class="status-dot"></div>
              <span>{{ service.statusText }}</span>
            </div>
          </div>
          <div class="service-metrics">
            <div class="metric-item">
              <span class="metric-label">响应时间</span>
              <span class="metric-value">{{ service.responseTime }}ms</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">可用性</span>
              <span class="metric-value">{{ service.uptime }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="real-time-logs">
      <div class="section-header">
        <h3>实时日志</h3>
        <div class="log-controls">
          <a-select
            v-model:value="logLevel"
            size="small"
            style="width: 100px"
          >
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="error">错误</a-select-option>
            <a-select-option value="warning">警告</a-select-option>
            <a-select-option value="info">信息</a-select-option>
          </a-select>
          <a-button
            size="small"
            @click="clearLogs"
          >
            <Trash2 class="btn-icon" />
            清空
          </a-button>
        </div>
      </div>
      <div class="logs-container">
        <div
          v-for="log in filteredLogs"
          :key="log.id"
          class="log-entry"
          :class="log.level"
        >
          <div class="log-time">{{ log.time }}</div>
          <div
            class="log-level"
            :class="log.level"
          >
            <component
              :is="log.icon"
              class="level-icon"
            />
            {{ log.level.toUpperCase() }}
          </div>
          <div class="log-message">{{ log.message }}</div>
        </div>
      </div>
    </div>

    <!-- 性能图表 -->
    <div class="performance-charts">
      <div class="section-header">
        <h3>性能趋势</h3>
        <div class="chart-controls">
          <a-select
            v-model:value="chartTimeRange"
            size="small"
            style="width: 120px"
          >
            <a-select-option value="1h">最近1小时</a-select-option>
            <a-select-option value="6h">最近6小时</a-select-option>
            <a-select-option value="24h">最近24小时</a-select-option>
            <a-select-option value="7d">最近7天</a-select-option>
          </a-select>
          <a-button
            size="small"
            @click="refreshCharts"
          >
            <RefreshCw class="btn-icon" />
            刷新
          </a-button>
        </div>
      </div>
      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h4>CPU使用率</h4>
            <span class="chart-value">{{ metrics.cpu }}%</span>
          </div>
          <div class="chart-container">
            <div class="simple-chart">
              <div
                v-for="(point, index) in cpuData"
                :key="index"
                class="chart-bar"
                :style="{ height: `${point}%` }"
              ></div>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h4>内存使用率</h4>
            <span class="chart-value">{{ metrics.memory }}%</span>
          </div>
          <div class="chart-container">
            <div class="simple-chart">
              <div
                v-for="(point, index) in memoryData"
                :key="index"
                class="chart-bar"
                :style="{ height: `${point}%` }"
              ></div>
            </div>
          </div>
        </div>
        <div class="chart-card">
          <div class="chart-header">
            <h4>网络延迟</h4>
            <span class="chart-value">{{ metrics.latency }}ms</span>
          </div>
          <div class="chart-container">
            <div class="simple-chart">
              <div
                v-for="(point, index) in latencyData"
                :key="index"
                class="chart-bar"
                :style="{ height: `${Math.min(point / 2, 100)}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警管理 -->
    <div class="alerts-section">
      <div class="section-header">
        <h3>系统告警</h3>
        <div class="alert-controls">
          <a-badge
            :count="activeAlerts.length"
            :offset="[10, 0]"
          >
            <a-button
              size="small"
              @click="showAllAlerts = !showAllAlerts"
            >
              <AlertTriangle class="btn-icon" />
              {{ showAllAlerts ? '隐藏' : '显示' }}告警
            </a-button>
          </a-badge>
          <a-button
            size="small"
            @click="clearAllAlerts"
            :disabled="activeAlerts.length === 0"
          >
            <XCircle class="btn-icon" />
            清除全部
          </a-button>
        </div>
      </div>
      <div
        v-if="showAllAlerts"
        class="alerts-container"
      >
        <div
          v-for="alert in activeAlerts"
          :key="alert.id"
          class="alert-item"
          :class="alert.severity"
        >
          <div class="alert-icon">
            <component :is="alert.icon" />
          </div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-message">{{ alert.message }}</div>
            <div class="alert-time">{{ alert.time }}</div>
          </div>
          <div class="alert-actions">
            <a-button
              size="small"
              @click="dismissAlert(alert.id)"
            >
              忽略
            </a-button>
            <a-button
              size="small"
              type="primary"
              @click="handleAlert(alert)"
            >
              处理
            </a-button>
          </div>
        </div>
        <div
          v-if="activeAlerts.length === 0"
          class="no-alerts"
        >
          <CheckCircle class="no-alerts-icon" />
          <span>暂无活跃告警</span>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h3>快速操作</h3>
      </div>
      <div class="action-buttons">
        <a-button @click="navigateToPage('/sync/monitor')">
          <Activity class="btn-icon" />
          详细监控
        </a-button>
        <a-button @click="navigateToPage('/security/audit')">
          <Shield class="btn-icon" />
          安全审计
        </a-button>
        <a-button @click="exportLogs">
          <Download class="btn-icon" />
          导出日志
        </a-button>
        <a-button @click="navigateToPage('/settings/global')">
          <Settings class="btn-icon" />
          系统设置
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Activity,
  Shield,
  Download,
  Settings,
  RefreshCw,
  Trash2,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info,
  Server,
  Database,
  Cloud,
  Wifi
} from 'lucide-vue-next'

const router = useRouter()

// 系统状态
const systemStatus = ref({
  overall: 'healthy',
  icon: CheckCircle,
  text: '运行正常'
})

// 系统指标
const metrics = ref({
  cpu: 45,
  memory: 62,
  latency: 23
})

// 服务状态
const services = ref([
  {
    name: '数据同步服务',
    description: '负责数据同步和备份',
    icon: RefreshCw,
    status: 'healthy',
    statusText: '正常',
    responseTime: 120,
    uptime: 99.9
  },
  {
    name: '认证服务',
    description: '用户认证和授权',
    icon: Shield,
    status: 'healthy',
    statusText: '正常',
    responseTime: 85,
    uptime: 99.8
  },
  {
    name: '数据库服务',
    description: '数据存储和查询',
    icon: Database,
    status: 'warning',
    statusText: '警告',
    responseTime: 250,
    uptime: 98.5
  },
  {
    name: '网络服务',
    description: '网络连接和通信',
    icon: Wifi,
    status: 'healthy',
    statusText: '正常',
    responseTime: 45,
    uptime: 99.9
  }
])

// 日志级别
const logLevel = ref('all')

// 图表相关
const chartTimeRange = ref('1h')
const showAllAlerts = ref(false)

// 性能数据
const cpuData = ref([45, 52, 48, 55, 49, 46, 51, 47, 53, 50, 45, 48, 52, 49, 46])
const memoryData = ref([62, 65, 63, 68, 66, 64, 67, 65, 69, 66, 62, 64, 66, 63, 65])
const latencyData = ref([23, 28, 25, 32, 27, 24, 29, 26, 31, 28, 23, 25, 27, 24, 26])

// 告警数据
const activeAlerts = ref([
  {
    id: 1,
    title: '数据库响应时间过长',
    message: '数据库服务响应时间超过200ms，建议检查数据库性能',
    severity: 'warning',
    icon: AlertTriangle,
    time: '14:25:30'
  },
  {
    id: 2,
    title: '内存使用率偏高',
    message: '系统内存使用率达到65%，建议关注内存使用情况',
    severity: 'info',
    icon: Info,
    time: '14:20:15'
  }
])

// 实时更新定时器
let updateTimer: NodeJS.Timeout | null = null

// 实时日志
const logs = ref([
  {
    id: 1,
    time: '14:30:25',
    level: 'info',
    icon: Info,
    message: '数据同步服务启动成功'
  },
  {
    id: 2,
    time: '14:30:28',
    level: 'info',
    icon: Info,
    message: '用户认证服务连接正常'
  },
  {
    id: 3,
    time: '14:30:35',
    level: 'warning',
    icon: AlertTriangle,
    message: '数据库连接响应时间较慢'
  },
  {
    id: 4,
    time: '14:30:42',
    level: 'info',
    icon: Info,
    message: '自动备份任务执行完成'
  }
])

// 过滤后的日志
const filteredLogs = computed(() => {
  if (logLevel.value === 'all') {
    return logs.value
  }
  return logs.value.filter((log) => log.level === logLevel.value)
})

// 定时器
let metricsTimer: NodeJS.Timeout | null = null

// 刷新服务状态
const refreshServices = async () => {
  try {
    console.log('Refreshing services status...')
    // 这里可以调用API刷新服务状态
  } catch (error) {
    console.error('Failed to refresh services:', error)
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
}

// 导出日志
const exportLogs = () => {
  try {
    const logData = logs.value.map((log) => `${log.time} [${log.level.toUpperCase()}] ${log.message}`).join('\n')

    const blob = new Blob([logData], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export logs:', error)
  }
}

// 导航到页面
const navigateToPage = (path: string) => {
  router.push(path)
}

// 更新系统指标
const updateMetrics = () => {
  // 模拟实时数据更新
  metrics.value.cpu = Math.floor(Math.random() * 30) + 30
  metrics.value.memory = Math.floor(Math.random() * 20) + 50
  metrics.value.latency = Math.floor(Math.random() * 50) + 10
}

// 添加新日志
const addLog = (level: string, message: string) => {
  const now = new Date()
  const time = now.toTimeString().split(' ')[0]

  const iconMap = {
    info: Info,
    warning: AlertTriangle,
    error: XCircle
  }

  logs.value.unshift({
    id: Date.now(),
    time,
    level,
    icon: iconMap[level] || Info,
    message
  })

  // 保持最多50条日志
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 图表相关方法
const refreshCharts = () => {
  // 模拟刷新图表数据
  cpuData.value = Array.from({ length: 15 }, () => Math.floor(Math.random() * 40) + 30)
  memoryData.value = Array.from({ length: 15 }, () => Math.floor(Math.random() * 30) + 50)
  latencyData.value = Array.from({ length: 15 }, () => Math.floor(Math.random() * 40) + 10)
}

// 告警管理方法
const dismissAlert = (alertId: number) => {
  const index = activeAlerts.value.findIndex((alert) => alert.id === alertId)
  if (index > -1) {
    activeAlerts.value.splice(index, 1)
  }
}

const handleAlert = (alert: any) => {
  console.log('处理告警:', alert)
  // 这里可以添加具体的告警处理逻辑
  dismissAlert(alert.id)
}

const clearAllAlerts = () => {
  activeAlerts.value = []
}

// 实时数据更新
const startRealTimeUpdates = () => {
  updateTimer = setInterval(() => {
    updateMetrics()

    // 更新图表数据
    cpuData.value.shift()
    cpuData.value.push(metrics.value.cpu)

    memoryData.value.shift()
    memoryData.value.push(metrics.value.memory)

    latencyData.value.shift()
    latencyData.value.push(metrics.value.latency)

    // 随机添加日志
    if (Math.random() < 0.3) {
      const messages = ['系统健康检查完成', '数据同步任务执行', '用户会话更新', '缓存清理完成']
      const levels = ['info', 'info', 'info', 'warning']
      const randomIndex = Math.floor(Math.random() * messages.length)
      addLog(levels[randomIndex], messages[randomIndex])
    }

    // 随机生成告警
    if (Math.random() < 0.1 && activeAlerts.value.length < 5) {
      const alertMessages = [
        { title: 'CPU使用率过高', message: 'CPU使用率超过80%，建议检查系统负载' },
        { title: '磁盘空间不足', message: '系统磁盘使用率超过90%，建议清理磁盘空间' },
        { title: '网络连接异常', message: '检测到网络连接不稳定，建议检查网络配置' }
      ]
      const randomAlert = alertMessages[Math.floor(Math.random() * alertMessages.length)]
      const now = new Date()
      activeAlerts.value.unshift({
        id: Date.now(),
        ...randomAlert,
        severity: 'warning',
        icon: AlertTriangle,
        time: now.toTimeString().split(' ')[0]
      })
    }
  }, 5000)
}

onMounted(() => {
  startRealTimeUpdates()
})

onUnmounted(() => {
  if (updateTimer) {
    clearInterval(updateTimer)
  }
})
</script>

<style lang="less" scoped>
.monitor-tab {
  height: 100%;
  max-height: calc(100vh - 60px);
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--bg-color);
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.status-overview {
  margin-bottom: 24px;
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

.overview-card {
  padding: 24px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);

  &.healthy {
    border-left: 4px solid #52c41a;
  }

  &.warning {
    border-left: 4px solid #faad14;
  }

  &.error {
    border-left: 4px solid #ff4d4f;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.status-icon {
  width: 32px;
  height: 32px;
  color: #52c41a;
}

.status-info {
  .status-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .status-value {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.status-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--bg-color);
  border-radius: 6px;

  .metric-label {
    font-size: 14px;
    color: var(--text-color-secondary);
  }

  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
  }
}

.services-status {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.service-card {
  padding: 16px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);

  &.healthy {
    border-left: 4px solid #52c41a;
  }

  &.warning {
    border-left: 4px solid #faad14;
  }

  &.error {
    border-left: 4px solid #ff4d4f;
  }
}

.service-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.service-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.service-info {
  flex: 1;

  .service-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2px;
  }

  .service-desc {
    font-size: 12px;
    color: var(--text-color-secondary);
  }
}

.service-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &.healthy {
    color: #52c41a;

    .status-dot {
      background: #52c41a;
    }
  }

  &.warning {
    color: #faad14;

    .status-dot {
      background: #faad14;
    }
  }

  &.error {
    color: #ff4d4f;

    .status-dot {
      background: #ff4d4f;
    }
  }
}

.service-metrics {
  display: flex;
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .metric-label {
    font-size: 12px;
    color: var(--text-color-secondary);
  }

  .metric-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
  }
}

.real-time-logs {
  margin-bottom: 24px;
}

.log-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.log-entry {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: var(--hover-bg-color);
  }
}

.log-time {
  color: var(--text-color-tertiary);
  font-size: 12px;
  min-width: 60px;
}

.log-level {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 80px;
  font-size: 11px;
  font-weight: 600;

  &.info {
    color: #1890ff;
  }

  &.warning {
    color: #faad14;
  }

  &.error {
    color: #ff4d4f;
  }

  .level-icon {
    width: 12px;
    height: 12px;
  }
}

.log-message {
  flex: 1;
  color: var(--text-color);
}

.quick-actions {
  margin-bottom: 24px;

  .action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

/* 性能图表样式 */
.performance-charts {
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.chart-card {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 16px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.chart-header h4 {
  margin: 0;
  font-size: 14px;
  color: var(--text-color);
}

.chart-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.chart-container {
  height: 80px;
  display: flex;
  align-items: end;
}

.simple-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  width: 100%;
  height: 100%;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, var(--primary-color), rgba(24, 144, 255, 0.3));
  border-radius: 2px 2px 0 0;
  min-height: 4px;
  transition: all 0.3s ease;
}

/* 告警管理样式 */
.alerts-section {
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.alert-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.alerts-container {
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  margin-bottom: 8px;

  &.warning {
    border-left: 4px solid #faad14;
  }

  &.error {
    border-left: 4px solid #ff4d4f;
  }

  &.info {
    border-left: 4px solid #1890ff;
  }
}

.alert-icon {
  margin-top: 2px;

  svg {
    width: 16px;
    height: 16px;
    color: #faad14;
  }
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.alert-message {
  font-size: 13px;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.alert-time {
  font-size: 12px;
  color: var(--text-color-tertiary);
}

.alert-actions {
  display: flex;
  gap: 8px;
}

.no-alerts {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 32px;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.no-alerts-icon {
  width: 20px;
  height: 20px;
  color: #52c41a;
}
</style>
