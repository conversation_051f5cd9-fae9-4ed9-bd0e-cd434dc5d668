/**
 * 设置验证工具
 * 用于验证用户配置的完整性和有效性
 */

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface TerminalConfig {
  terminalType: string
  fontSize: number
  fontFamily: string
  scrollBack: number
  cursorStyle: string
  rightMouseEvent: string
  middleMouseEvent: string
  sshAgentsStatus: number
}

export interface ThemeConfig {
  primaryColor: string
  accentColor: string
  backgroundColor: string
  surfaceColor: string
  textColor: string
  borderColor: string
}

export interface UserSettings {
  language: string
  watermark: string
  theme: string
  dataSync: string
  terminalConfig?: TerminalConfig
  themeColors?: ThemeConfig
}

/**
 * 验证终端配置
 */
export function validateTerminalConfig(config: TerminalConfig): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // 验证终端类型
  const validTerminalTypes = ['xterm', 'xterm-256color', 'vt100', 'vt220', 'linux']
  if (!validTerminalTypes.includes(config.terminalType)) {
    errors.push(`无效的终端类型: ${config.terminalType}`)
  }

  // 验证字体大小
  if (config.fontSize < 8 || config.fontSize > 32) {
    errors.push(`字体大小必须在 8-32 之间，当前值: ${config.fontSize}`)
  }

  // 验证滚动缓冲区
  if (config.scrollBack < 100 || config.scrollBack > 10000) {
    errors.push(`滚动缓冲区必须在 100-10000 之间，当前值: ${config.scrollBack}`)
  }

  // 验证光标样式
  const validCursorStyles = ['block', 'bar', 'underline']
  if (!validCursorStyles.includes(config.cursorStyle)) {
    errors.push(`无效的光标样式: ${config.cursorStyle}`)
  }

  // 验证鼠标事件
  const validMouseEvents = ['paste', 'contextMenu', 'none']
  if (!validMouseEvents.includes(config.rightMouseEvent)) {
    errors.push(`无效的右键事件: ${config.rightMouseEvent}`)
  }
  if (!validMouseEvents.includes(config.middleMouseEvent)) {
    errors.push(`无效的中键事件: ${config.middleMouseEvent}`)
  }

  // 验证SSH代理状态
  if (![1, 2].includes(config.sshAgentsStatus)) {
    errors.push(`无效的SSH代理状态: ${config.sshAgentsStatus}`)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证主题配置
 */
export function validateThemeConfig(config: ThemeConfig): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // 验证颜色格式
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  const colorFields = ['primaryColor', 'accentColor', 'backgroundColor', 'surfaceColor', 'textColor', 'borderColor']

  for (const field of colorFields) {
    const color = config[field as keyof ThemeConfig]
    if (!colorRegex.test(color)) {
      errors.push(`无效的颜色格式 ${field}: ${color}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证用户设置
 */
export function validateUserSettings(settings: UserSettings): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // 验证语言设置
  const validLanguages = ['zh-CN', 'en-US']
  if (!validLanguages.includes(settings.language)) {
    errors.push(`不支持的语言: ${settings.language}`)
  }

  // 验证水印设置
  const validWatermarkOptions = ['open', 'close']
  if (!validWatermarkOptions.includes(settings.watermark)) {
    errors.push(`无效的水印设置: ${settings.watermark}`)
  }

  // 验证主题设置
  const validThemes = ['auto', 'light', 'dark']
  if (!validThemes.includes(settings.theme)) {
    errors.push(`无效的主题设置: ${settings.theme}`)
  }

  // 验证数据同步设置
  const validDataSyncOptions = ['enabled', 'disabled']
  if (!validDataSyncOptions.includes(settings.dataSync)) {
    errors.push(`无效的数据同步设置: ${settings.dataSync}`)
  }

  // 验证终端配置
  if (settings.terminalConfig) {
    const terminalResult = validateTerminalConfig(settings.terminalConfig)
    errors.push(...terminalResult.errors)
    warnings.push(...terminalResult.warnings)
  }

  // 验证主题颜色配置
  if (settings.themeColors) {
    const themeResult = validateThemeConfig(settings.themeColors)
    errors.push(...themeResult.errors)
    warnings.push(...themeResult.warnings)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 修复无效的配置值
 */
export function sanitizeUserSettings(settings: UserSettings): UserSettings {
  const sanitized = { ...settings }

  // 修复语言设置
  if (!['zh-CN', 'en-US'].includes(sanitized.language)) {
    sanitized.language = 'zh-CN'
  }

  // 修复水印设置
  if (!['open', 'close'].includes(sanitized.watermark)) {
    sanitized.watermark = 'open'
  }

  // 修复主题设置
  if (!['auto', 'light', 'dark'].includes(sanitized.theme)) {
    sanitized.theme = 'auto'
  }

  // 修复数据同步设置
  if (!['enabled', 'disabled'].includes(sanitized.dataSync)) {
    sanitized.dataSync = 'disabled'
  }

  // 修复终端配置
  if (sanitized.terminalConfig) {
    const config = sanitized.terminalConfig

    // 修复字体大小
    if (config.fontSize < 8 || config.fontSize > 32) {
      config.fontSize = 14
    }

    // 修复滚动缓冲区
    if (config.scrollBack < 100 || config.scrollBack > 10000) {
      config.scrollBack = 1000
    }

    // 修复光标样式
    if (!['block', 'bar', 'underline'].includes(config.cursorStyle)) {
      config.cursorStyle = 'block'
    }

    // 修复鼠标事件
    if (!['paste', 'contextMenu', 'none'].includes(config.rightMouseEvent)) {
      config.rightMouseEvent = 'paste'
    }
    if (!['paste', 'contextMenu', 'none'].includes(config.middleMouseEvent)) {
      config.middleMouseEvent = 'paste'
    }

    // 修复SSH代理状态
    if (![1, 2].includes(config.sshAgentsStatus)) {
      config.sshAgentsStatus = 1
    }
  }

  return sanitized
}

/**
 * 获取配置字段的默认值
 */
export function getDefaultSettings(): UserSettings {
  return {
    language: 'zh-CN',
    watermark: 'open',
    theme: 'auto',
    dataSync: 'disabled',
    terminalConfig: {
      terminalType: 'xterm-256color',
      fontSize: 14,
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      scrollBack: 1000,
      cursorStyle: 'block',
      rightMouseEvent: 'paste',
      middleMouseEvent: 'paste',
      sshAgentsStatus: 1
    },
    themeColors: {
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#141414',
      surfaceColor: '#1f1f1f',
      textColor: '#ffffff',
      borderColor: '#303030'
    }
  }
}
