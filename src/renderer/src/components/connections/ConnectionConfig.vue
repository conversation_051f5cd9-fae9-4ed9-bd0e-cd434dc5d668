<!--
 * 统一连接配置组件
 * 功能：根据连接类型动态显示相应的连接配置组件
 * 作者：SOLO Coding
 * 修改时间：2025-01-10
-->

<template>
  <div class="connection-config">
    <div class="config-header">
      <div class="header-title">
        <LinkOutlined class="title-icon" />
        <span>连接配置</span>
      </div>
      <div class="header-info">
        <a-tag :color="getConnectionTypeColor(connectionType)">
          {{ getConnectionTypeText(connectionType) }}
        </a-tag>
      </div>
    </div>

    <div class="config-content">
      <!-- SSH连接配置 -->
      <div
        v-if="connectionType === 'ssh'"
        class="connection-section"
      >
        <div class="section-header">
          <DesktopOutlined class="section-icon" />
          <span>SSH连接配置</span>
        </div>

        <div class="ssh-config">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="16">
                <a-form-item label="主机地址">
                  <a-input
                    v-model:value="sshConfig.host"
                    placeholder="请输入IP地址或域名"
                    @change="handleConfigChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="端口">
                  <a-input-number
                    v-model:value="sshConfig.port"
                    :min="1"
                    :max="65535"
                    style="width: 100%"
                    @change="handleConfigChange"
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="用户名">
                  <a-input
                    v-model:value="sshConfig.username"
                    placeholder="请输入用户名"
                    @change="handleConfigChange"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="认证方式">
                  <a-select
                    v-model:value="sshConfig.authType"
                    placeholder="选择认证方式"
                    @change="handleConfigChange"
                  >
                    <a-select-option value="password">密码认证</a-select-option>
                    <a-select-option value="key">密钥认证</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <a-form-item
              v-if="sshConfig.authType === 'password'"
              label="密码"
            >
              <a-input-password
                v-model:value="sshConfig.password"
                placeholder="请输入密码"
                @change="handleConfigChange"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- Console连接配置 -->
      <div
        v-else-if="connectionType === 'console'"
        class="connection-section"
      >
        <ConsoleConnector
          :asset-id="assetId"
          :initial-config="consoleConfig"
          @connect="handleConsoleConnect"
          @disconnect="handleConsoleDisconnect"
          @config-change="handleConsoleConfigChange"
        />
      </div>

      <!-- RDP连接配置 -->
      <div
        v-else-if="connectionType === 'rdp'"
        class="connection-section"
      >
        <RdpConnector
          :asset-id="assetId"
          :initial-config="rdpConfig"
          @connect="handleRdpConnect"
          @disconnect="handleRdpDisconnect"
          @fullscreen="handleRdpFullscreen"
          @config-change="handleRdpConfigChange"
        />
      </div>

      <!-- 未知连接类型 -->
      <div
        v-else
        class="connection-section"
      >
        <a-result
          status="warning"
          title="不支持的连接类型"
          :sub-title="`当前连接类型 '${connectionType}' 暂不支持`"
        >
          <template #extra>
            <a-button
              type="primary"
              @click="$emit('change-type', 'ssh')"
            >
              切换到SSH
            </a-button>
          </template>
        </a-result>
      </div>
    </div>

    <!-- 连接操作区域 -->
    <div
      v-if="connectionType === 'ssh'"
      class="config-actions"
    >
      <div class="actions-left">
        <a-button
          v-if="!isConnected"
          :loading="isTesting"
          @click="handleTestConnection"
        >
          <ExperimentOutlined />
          测试连接
        </a-button>

        <a-button
          v-if="isConnected"
          @click="handleViewLogs"
        >
          <FileTextOutlined />
          查看日志
        </a-button>
      </div>

      <div class="actions-right">
        <a-button
          v-if="!isConnected"
          type="primary"
          :loading="isConnecting"
          :disabled="!isConfigValid"
          @click="handleConnect"
        >
          <PlayCircleOutlined />
          连接
        </a-button>

        <a-button
          v-else
          danger
          @click="handleDisconnect"
        >
          <StopOutlined />
          断开连接
        </a-button>
      </div>
    </div>

    <!-- 连接状态显示 -->
    <div
      v-if="connectionStatus && connectionType === 'ssh'"
      class="status-section"
    >
      <div class="status-header">
        <div class="status-title">
          <InfoCircleOutlined class="status-icon" />
          <span>连接状态</span>
        </div>
        <a-tag :color="getStatusColor(connectionStatus.status)">
          {{ getStatusText(connectionStatus.status) }}
        </a-tag>
      </div>

      <div class="status-details">
        <div
          v-if="connectionStatus.connectedAt"
          class="status-item"
        >
          <span class="item-label">连接时间:</span>
          <span>{{ formatTime(connectionStatus.connectedAt) }}</span>
        </div>

        <div
          v-if="connectionStatus.duration"
          class="status-item"
        >
          <span class="item-label">连接时长:</span>
          <span>{{ formatDuration(connectionStatus.duration) }}</span>
        </div>

        <div
          v-if="connectionStatus.error"
          class="status-item"
        >
          <span class="item-label">错误信息:</span>
          <span class="error-text">{{ connectionStatus.error }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import {
  LinkOutlined,
  DesktopOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import ConsoleConnector from './ConsoleConnector.vue'
import RdpConnector from './RdpConnector.vue'

// 接口定义
interface SshConfig {
  host: string
  port: number
  username: string
  password: string
  authType: 'password' | 'key'
}

interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  connectedAt?: Date
  duration?: number
  error?: string
}

interface Props {
  assetId?: string
  connectionType: 'ssh' | 'console' | 'rdp'
  initialSshConfig?: Partial<SshConfig>
  initialConsoleConfig?: any
  initialRdpConfig?: any
}

const props = withDefaults(defineProps<Props>(), {
  connectionType: 'ssh',
  initialSshConfig: () => ({}),
  initialConsoleConfig: () => ({}),
  initialRdpConfig: () => ({})
})

const emit = defineEmits<{
  connect: [type: string, config: any]
  disconnect: [type: string]
  'config-change': [type: string, config: any]
  'test-connection': [type: string, config: any]
  'view-logs': [type: string]
  'change-type': [type: string]
}>()

// 响应式数据
const isConnecting = ref(false)
const isConnected = ref(false)
const isTesting = ref(false)
const connectionStatus = ref<ConnectionStatus | null>(null)

// SSH配置
const sshConfig = reactive<SshConfig>({
  host: '',
  port: 22,
  username: '',
  password: '',
  authType: 'password',
  ...props.initialSshConfig
})

// Console和RDP配置
const consoleConfig = reactive(props.initialConsoleConfig)
const rdpConfig = reactive(props.initialRdpConfig)

// 计算属性
const isConfigValid = computed(() => {
  if (props.connectionType === 'ssh') {
    return sshConfig.host && sshConfig.port && sshConfig.username && (sshConfig.authType === 'key' || sshConfig.password)
  }
  return true
})

// 监听配置变化
watch(
  () => sshConfig,
  (newConfig) => {
    if (props.connectionType === 'ssh') {
      emit('config-change', 'ssh', { ...newConfig })
    }
  },
  { deep: true }
)

// 方法
const getConnectionTypeColor = (type: string): string => {
  switch (type) {
    case 'ssh':
      return 'blue'
    case 'console':
      return 'green'
    case 'rdp':
      return 'orange'
    default:
      return 'default'
  }
}

const getConnectionTypeText = (type: string): string => {
  switch (type) {
    case 'ssh':
      return 'SSH连接'
    case 'console':
      return 'Console连接'
    case 'rdp':
      return 'RDP连接'
    default:
      return '未知类型'
  }
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
      return 'green'
    case 'connecting':
      return 'blue'
    case 'error':
      return 'red'
    case 'disconnected':
      return 'default'
    default:
      return 'default'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'error':
      return '连接错误'
    case 'disconnected':
      return '已断开'
    default:
      return '未知状态'
  }
}

const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}

const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

// SSH连接处理
const handleConfigChange = () => {
  // 配置变化时的处理逻辑
}

const handleTestConnection = async () => {
  if (!isConfigValid.value) {
    message.error('请完善连接配置')
    return
  }

  isTesting.value = true
  try {
    emit('test-connection', props.connectionType, sshConfig)
    message.success('连接测试成功')
  } catch (error: any) {
    message.error(`连接测试失败: ${error.message}`)
  } finally {
    isTesting.value = false
  }
}

const handleConnect = async () => {
  if (!isConfigValid.value) {
    message.error('请完善连接配置')
    return
  }

  isConnecting.value = true
  connectionStatus.value = {
    status: 'connecting'
  }

  try {
    emit('connect', props.connectionType, sshConfig)
    isConnected.value = true
    connectionStatus.value = {
      status: 'connected',
      connectedAt: new Date()
    }
    message.success('连接成功')
  } catch (error: any) {
    connectionStatus.value = {
      status: 'error',
      error: error.message
    }
    message.error(`连接失败: ${error.message}`)
  } finally {
    isConnecting.value = false
  }
}

const handleDisconnect = async () => {
  try {
    emit('disconnect', props.connectionType)
    isConnected.value = false
    connectionStatus.value = {
      status: 'disconnected'
    }
    message.success('连接已断开')
  } catch (error: any) {
    message.error(`断开连接失败: ${error.message}`)
  }
}

const handleViewLogs = () => {
  emit('view-logs', props.connectionType)
}

// Console连接处理
const handleConsoleConnect = (config: any) => {
  emit('connect', 'console', config)
}

const handleConsoleDisconnect = () => {
  emit('disconnect', 'console')
}

const handleConsoleConfigChange = (config: any) => {
  Object.assign(consoleConfig, config)
  emit('config-change', 'console', config)
}

// RDP连接处理
const handleRdpConnect = (config: any) => {
  emit('connect', 'rdp', config)
}

const handleRdpDisconnect = () => {
  emit('disconnect', 'rdp')
}

const handleRdpFullscreen = () => {
  // RDP全屏处理
}

const handleRdpConfigChange = (config: any) => {
  Object.assign(rdpConfig, config)
  emit('config-change', 'rdp', config)
}
</script>

<style lang="less" scoped>
.connection-config {
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.title-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.config-content {
  padding: 20px;
}

.connection-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.section-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.ssh-config {
  background: var(--bg-color-secondary);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.config-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-color-secondary);
  border-top: 1px solid var(--border-color);
}

.actions-left,
.actions-right {
  display: flex;
  gap: 8px;
}

.status-section {
  padding: 16px 20px;
  background: var(--bg-color-secondary);
  border-top: 1px solid var(--border-color);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.status-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
}

.item-label {
  min-width: 80px;
  color: var(--text-color-secondary);
  font-size: 13px;
}

.error-text {
  color: var(--error-color);
  font-size: 13px;
}

// 深色主题适配
:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-input-password) {
  background: var(--bg-color);
  border-color: var(--border-color);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &:focus,
  &.ant-input-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-form-item-label > label) {
  color: var(--text-color);
}
</style>
