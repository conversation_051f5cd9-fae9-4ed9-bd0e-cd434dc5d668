<template>
  <div
    ref="terminalContainer"
    class="terminal-container"
    :data-ssh-connect-id="currentConnectionId"
    :style="terminalContainerStyle"
  >
    <SearchComp
      v-if="showSearch && terminal"
      :search-addon="searchAddon"
      :terminal="terminal"
      @close-search="closeSearch"
    />
    <div
      ref="terminalElement"
      class="terminal"
      @contextmenu="handleRightClick"
      @mousedown="handleMouseDown"
    >
    </div>
    <a-button
      v-show="showAiButton"
      :id="`${connectionId}Button`"
      class="select-button"
      @mousedown.prevent
      @click="onChatToAiClick"
    >
      <span class="main-text">Chat to AI</span>
      <span class="shortcut-text">{{ shortcutKey }}</span>
    </a-button>

    <SuggComp
      :ref="(el) => setRef(el, connectionId)"
      :unique-key="connectionId"
      :suggestions="suggestions"
      :active-suggestion="activeSuggestion"
      :selection-mode="suggestionSelectionMode"
    />
    <v-contextmenu ref="contextmenu">
      <Context
        v-if="terminal"
        :is-connect="isConnected"
        :is-sync-input="isSyncInput"
        :term-instance="terminal"
        :copy-text="copyText"
        :terminal-id="connectionId"
        @context-act="contextAct"
      />
    </v-contextmenu>

    <!-- 性能监控组件 -->
    <PerformanceMonitor :terminal-optimizer="terminalOptimizer" />

    <!-- AI助手组件 -->
    <AIAssistantPanel
      :last-command="lastExecutedCommand"
      :last-output="lastCommandOutput"
      :command-history="commandHistory"
      @execute-command="executeAICommand"
      @copy-to-clipboard="copyToClipboard"
    />
  </div>

  <!-- Per-terminal Command Dialog -->
  <CommandDialog
    :connection-id="currentConnectionId"
    :visible="isCommandDialogVisible"
    @update:visible="(val) => (isCommandDialogVisible = val)"
  />

  <div
    v-for="editor in openEditors"
    v-show="editor?.visible"
    :key="editor?.filePath"
  >
    <EditorCode
      :editor="editor"
      :is-active="editor.key === activeEditorKey"
      @close-vim-editor="closeVimEditor"
      @handle-save="handleSave"
      @focus-editor="() => handleFocusEditor(editor)"
    />
  </div>

  <!-- MFA弹窗已移至全局组件 -->
</template>

<script lang="ts" setup>
// 响应数据接口
interface MarkedResponse {
  data: string | ArrayBuffer | Uint8Array | number[]
  marker?: string
  lineCommand?: string
}

// 连接结果接口
interface ConnectionResult {
  status: 'connected' | 'error' | 'success'
  message?: string
}

// 资产信息接口
interface AssetInfo {
  asset_ip: string
  port: number
  username: string
  password: string
  privateKey?: string
  passphrase?: string
  host: string
  sshType: string
  need_proxy: number
  proxy_name?: string
  hostname?: string
  auth_type: 'password' | 'keyBased'
}

// 代理配置接口
interface ProxyConfig {
  name: string
  host: string
  port: number
  username: string
  password?: string
  type?: string
}

// 连接数据接口
interface ConnData {
  id: string
  host: string
  port: number
  username: string
  password: string
  privateKey?: string
  passphrase?: string
  targetIp?: string
  sshType: string
  terminalType: string
  agentForward: boolean
  isOfficeDevice: boolean
  needProxy?: boolean
  proxyConfig?: ProxyConfig
}

// API interface definition
interface SshApi {
  connectAssetInfo: (params: { uuid: string }) => Promise<AssetInfo>
  connect: (connData: ConnData) => Promise<ConnectionResult>
  shell: (params: { id: string; terminalType: string }) => Promise<ConnectionResult>
  onShellData: (id: string, callback: (response: MarkedResponse) => void) => () => void
  onShellError: (id: string, callback: (data: string) => void) => () => void
  onShellClose: (id: string, callback: () => void) => () => void
  resizeShell: (id: string, cols: number, rows: number) => Promise<ConnectionResult>
  connectLocal: (config: { id: string; shell: string; termType: string }) => Promise<{ success: boolean; message?: string }>
  onDataLocal: (id: string, callback: (data: string) => void) => () => void
  onErrorLocal?: (id: string, callback: (error: { message?: string }) => void) => () => void
  onExitLocal: (id: string, callback: (exitCode: { code?: number }) => void) => () => void
  resizeLocal: (id: string, cols: number, rows: number) => Promise<ConnectionResult>
  recordTerminalState: (params: { id: string; state: Record<string, unknown> }) => Promise<void>
  sshConnExec: (params: { cmd: string; id: string }) => Promise<{ stdout: string; stderr: string; success?: boolean }>
  checkSftpConnAvailable: (id: string) => Promise<boolean>
  writeToShell: (params: { id: string; data: string; marker?: string; lineCommand?: string }) => void
  disconnect: (params: { id: string }) => Promise<ConnectionResult>
  connectReadyData: (id: string) => Promise<{ hasSudo?: boolean; commandList?: string[] }>
  queryCommand: (params: { command: string; ip: string }) => Promise<CommandSuggestion[]>
  insertCommand: (params: { command: string; ip: string }) => Promise<void>
  sendDataLocal: (id: string, data: string) => void
}

// 使用 env.d.ts 中的全局 Window 声明，不在此重复声明，按需在使用处做类型断言或窄化

interface CommandSuggestion {
  command: string
  source: 'base' | 'history'
  description?: string
  type?: string
  confidence?: number
}

// editorData 接口已从 dragEditor.vue 导入

// 语言映射类型
const LanguageMap: Record<string, string> = {
  '.js': 'javascript',
  '.ts': 'typescript',
  '.py': 'python',
  '.java': 'java',
  '.cpp': 'cpp',
  '.c': 'c',
  '.h': 'c',
  '.hpp': 'cpp',
  '.css': 'css',
  '.html': 'html',
  '.json': 'json',
  '.xml': 'xml',
  '.yaml': 'yaml',
  '.yml': 'yaml',
  '.md': 'markdown',
  '.sh': 'shell',
  '.bash': 'shell',
  '.zsh': 'shell',
  '.fish': 'shell',
  '.go': 'go',
  '.rs': 'rust',
  '.php': 'php',
  '.rb': 'ruby',
  '.sql': 'sql',
  '.dockerfile': 'dockerfile',
  '.vue': 'vue',
  '.jsx': 'javascript',
  '.tsx': 'typescript'
}

const copyText = ref('')
// 组件导入
// @ts-ignore
import SearchComp from '../Term/searchComp.vue'
// @ts-ignore
import Context from '../Term/contextComp.vue'
// @ts-ignore
import SuggComp from '../Term/suggestion.vue'
// import CommandDialog from '@/components/global/CommandDialog.vue'
// import PerformanceMonitor from '@/components/PerformanceMonitor.vue'
// import AIAssistantPanel from '@/components/AIAssistantPanel.vue'

// 工具和服务导入 - 使用 any 类型临时修复
import eventBus from '../../../utils/eventBus'
// 同时设置到window对象以保持兼容性
if (typeof window !== 'undefined') {
  ;(window as Window & { eventBus?: typeof eventBus }).eventBus = eventBus
}
// @ts-ignore
import { getActualTheme } from '@/utils/themeUtils.js'

// 临时类型定义
interface OptimizerOptions {
  [key: string]: unknown
}

interface SecurityResult {
  isSecure: boolean
  risks: Array<{ rule: { description: string } }>
  suggestions: string[]
  severity: string
}

class TerminalOptimizer {
  constructor(_options: OptimizerOptions) {
    void _options // 标记参数为有意未使用
    // 临时实现，暂时为空
  }
}

const securityChecker: { checkCommand: (command: string, context?: { user: string; host: string }) => SecurityResult } = {
  checkCommand: (_command: string, _context?: { user: string; host: string }) => {
    void _command // 标记参数为有意未使用
    void _context // 标记参数为有意未使用
    return { isSecure: true, risks: [], suggestions: [], severity: 'safe' }
  }
}

// Store 导入 - 临时实现
const useCurrentCwdStore = () => ({
  getCurrentCwd: () => '/',
  setKeyValue: (_key: string, _value: string) => {
    void _key // 标记参数为有意未使用
    void _value // 标记参数为有意未使用
  }
})

// 服务导入 - 临时实现
interface ShortcutService {
  getShortcuts: () => Record<string, unknown>
  formatShortcut: (shortcut?: unknown) => string
}

const shortcutService: ShortcutService = {
  getShortcuts: () => ({}),
  formatShortcut: (_shortcut?: unknown) => {
    void _shortcut // 标记参数为有意未使用
    return 'Ctrl+L'
  }
}

// Vue 相关导入
import { markRaw, onBeforeUnmount, onMounted, PropType, nextTick, reactive, ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
// 引入全局MFA状态（已移除本地MFA实现）
// MFA功能现在由全局组件处理

import { Terminal } from 'xterm'
import { FitAddon } from 'xterm-addon-fit'
import { SearchAddon } from 'xterm-addon-search'
// import { WebglAddon } from 'xterm-addon-webgl' // 禁用WebGL以避免背景问题
import { IDisposable } from 'xterm'
import 'xterm/css/xterm.css'

// 临时组件和工具导入
const EditorCode: null = null
// 临时类型定义
interface editorData {
  filePath: string
  visible: boolean
  vimText: string
  originVimText: string
  action: string
  vimEditorX: number
  vimEditorY: number
  contentType: string
  vimEditorHeight: number
  vimEditorWidth: number
  loading: boolean
  fileChange: boolean
  saved: boolean
  key: string
  terminalId: string
}

import { message, Modal } from 'ant-design-vue'
import { userConfigStore } from '../../../store/userConfigStore'
import { userInfoStore } from '../../../store/index'
// 临时服务实现
interface UserConfigService {
  getConfig: () => Promise<Record<string, unknown>>
  saveConfig: (config: Record<string, unknown>) => Promise<void>
}

const serviceUserConfig: UserConfigService = {
  getConfig: () => Promise.resolve({}),
  saveConfig: (_config: Record<string, unknown>) => {
    void _config // 标记参数为有意未使用
    return Promise.resolve()
  }
}
import { v4 as uuidv4 } from 'uuid'
// 临时工具实现
interface Base64Utility {
  encode: (str: string) => string
}

const Base64Util: Base64Utility = { encode: (str: string) => btoa(str) }
import stripAnsi from 'strip-ansi'
import { inputManager, commandBarHeight } from './termInputManager'
import { shellCommands } from './shellCmd'
import { createJumpServerStatusHandler, formatStatusMessage } from './jumpServerStatusHandler'
// 临时设备 store
const useDeviceStore = () => ({ getDeviceIp: '127.0.0.1', getMacAddress: '00:00:00:00:00:00' })
// 临时 API 实现
interface DeviceCheckParams {
  [key: string]: unknown
}

interface DeviceCheckResult {
  code: number
  data: { isOfficeDevice: boolean }
}

const checkUserDevice = (_params?: DeviceCheckParams): Promise<DeviceCheckResult> => {
  void _params // 标记参数为有意未使用
  return Promise.resolve({ code: 200, data: { isOfficeDevice: false } })
}
// import { createContextFetcher, type ContextFetcher } from './autocomplete/contextFetcher'
const { t } = useI18n()
const configStore = userConfigStore()

const suggestions = ref<CommandSuggestion[]>([])
const activeSuggestion = ref(-1)
const suggestionSelectionMode = ref(false) // Whether suggestion box is in selection mode
const props = defineProps({
  connectData: {
    type: Object as PropType<sshConnectData>,
    default: () => ({})
  },
  serverInfo: {
    type: Object,
    default: () => {
      return {}
    }
  },
  activeTabId: { type: String, required: true },
  currentConnectionId: { type: String, required: true }
})
const queryCommandFlag = ref(false)
export interface sshConnectData {
  uuid: string
  ip: string
  port: number
  username: string
  password: string
  privateKey: string
  authType: string
  passphrase: string
  asset_type?: string
}

const handleRightClick = (event: MouseEvent) => {
  event.preventDefault()

  switch (config.rightMouseEvent) {
    case 'paste':
      if (startStr.value == '') {
        startStr.value = beginStr.value
      }
      pasteFlag.value = true
      navigator.clipboard
        .readText()
        .then((text) => {
          sendData(text)
          terminal.value?.focus()
        })
        .catch(() => {
          console.warn(t('common.clipboardReadFailed'))
        })
      break
    case 'contextMenu':
      if (contextmenu.value && contextmenu.value.show) {
        contextmenu.value.show(event)
      }
      break
    default:
      break
  }
}

const handleMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  if (event.button === 1) {
    switch (config.middleMouseEvent) {
      case 'paste':
        if (startStr.value == '') {
          startStr.value = beginStr.value
        }
        pasteFlag.value = true
        navigator.clipboard
          .readText()
          .then((text) => {
            sendData(text)
            terminal.value?.focus()
          })
          .catch(() => {
            console.warn(t('common.clipboardReadFailed'))
          })
        break
      case 'contextMenu':
        if (contextmenu.value && contextmenu.value.show) {
          contextmenu.value.show(event)
        }
        break
      case 'none':
        break
    }
  }
}
const componentRefs = ref<Record<string, unknown>>({})
const setRef = (el: unknown, key: string) => {
  if (el) {
    componentRefs.value[key] = el
  }
}
const isConnected = ref(false)
const isSyncInput = ref(false)
const terminal = ref<Terminal | null>(null)
const fitAddon = ref<FitAddon | null>(null)
const connectionId = ref('')
const connectionHasSudo = ref(false)
const connectionSftpAvailable = ref(false)
const cleanupListeners = ref<Array<() => void>>([])
const terminalElement = ref<HTMLDivElement | null>(null)
const terminalContainer = ref<HTMLDivElement | null>(null)
const contextmenu = ref()
const cursorStartX = ref(0)
const api = (window as unknown as { api: SshApi }).api
const encoder = new TextEncoder()
let cusWrite: ((data: string, options?: { isUserCall?: boolean }) => void) | null = null
let resizeObserver: ResizeObserver | null = null
const showSearch = ref(false)
const searchAddon = ref<SearchAddon | null>(null)
const showAiButton = ref(false)
let jumpServerStatusHandler: ReturnType<typeof createJumpServerStatusHandler> | null = null
const isCommandDialogVisible = ref(false)
const wasDialogVisibleBeforeDeactivation = ref(false)

// let contextFetcher: ContextFetcher | null = null

// 计算快捷键显示文本
const shortcutKey = computed(() => {
  const shortcuts = shortcutService.getShortcuts()
  if (shortcuts && shortcuts['sendOrToggleAi']) {
    return shortcutService.formatShortcut(shortcuts['sendOrToggleAi'])
  }
  const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0
  return isMac ? '⌘L' : 'Ctrl+L'
})
const activeEditorKey = ref<string | null>(null)
const handleFocusEditor = (editor: { key: string }) => {
  activeEditorKey.value = editor.key
}
const dataBuffer = ref<number[]>([])
const EDITOR_SEQUENCES = {
  enter: [
    { pattern: [0x1b, 0x5b, 0x3f, 0x31, 0x30, 0x34, 0x39, 0x68], editor: 'vim' },
    { pattern: [0x1b, 0x5b, 0x3f, 0x34, 0x37, 0x68], editor: 'vim' },
    { pattern: [0x1b, 0x5b, 0x3f, 0x31, 0x68, 0x1b, 0x3d], editor: 'nano' },
    // 添加更多vim检测模式
    { pattern: [0x1b, 0x5b, 0x3f, 0x32, 0x35, 0x68], editor: 'vim' }, // \x1b[?25h
    { pattern: [0x1b, 0x5b, 0x3f, 0x32, 0x35, 0x6c], editor: 'vim' }, // \x1b[?25l
    { pattern: [0x1b, 0x5b, 0x3f, 0x31, 0x30, 0x34, 0x39, 0x6c], editor: 'vim' },
    { pattern: [0x1b, 0x5b, 0x3f, 0x34, 0x37, 0x6c], editor: 'vim' }
  ],
  exit: [
    { pattern: [0x1b, 0x5b, 0x3f, 0x31, 0x30, 0x34, 0x39, 0x6c], editor: 'vim' },
    { pattern: [0x1b, 0x5b, 0x3f, 0x34, 0x37, 0x6c], editor: 'vim' },
    { pattern: [0x1b, 0x5b, 0x3f, 0x31, 0x6c, 0x1b, 0x3e], editor: 'nano' },
    // 添加更多vim退出检测模式
    { pattern: [0x1b, 0x5b, 0x3f, 0x32, 0x35, 0x68], editor: 'vim' }, // \x1b[?25h
    { pattern: [0x1b, 0x5b, 0x3f, 0x32, 0x35, 0x6c], editor: 'vim' } // \x1b[?25l
  ]
}
const userInputFlag = ref(false)
const currentCwdStore = useCurrentCwdStore()
const lastCloseTimestamp = ref<number | null>(null)
let termOndata: IDisposable | null = null
let handleInput: ((data: string, isInputManagerCall?: boolean) => void) | null = null
let textareaCompositionListener: ((e: CompositionEvent) => void) | null = null
let textareaPasteListener: (() => void) | null = null
const pasteFlag = ref(false)

// Terminal background configuration
const terminalBackground = ref({
  type: 'default',
  color: '#1a1a1a',
  imageUrl: '',
  opacity: 1.0
})

// Computed style for terminal container
const terminalContainerStyle = computed(() => {
  const style: Record<string, string | number> = {}

  console.log('SSH Terminal computing container style, background config:', terminalBackground.value)

  if (terminalBackground.value.type === 'color') {
    style.backgroundColor = terminalBackground.value.color
    style.opacity = terminalBackground.value.opacity
    // 添加重要性标记
    style.backgroundImage = 'none'
    console.log('SSH Terminal applying color background:', terminalBackground.value.color)
  } else if (terminalBackground.value.type === 'image' && terminalBackground.value.imageUrl) {
    style.backgroundImage = `url(${terminalBackground.value.imageUrl})`
    style.backgroundSize = 'cover'
    style.backgroundPosition = 'center'
    style.backgroundRepeat = 'no-repeat'
    style.backgroundColor = 'transparent' // 确保图片背景时移除默认背景色
    style.opacity = terminalBackground.value.opacity
    console.log('SSH Terminal applying image background:', terminalBackground.value.imageUrl)
  } else {
    // 默认情况下使用透明背景，让用户设置的背景显示
    style.backgroundColor = 'transparent'
    style.backgroundImage = 'none'
    console.log('SSH Terminal applying transparent background')
  }

  console.log('SSH Terminal container style computed:', style)
  return style
})

// Get terminal theme based on background configuration
/**
 * 计算颜色的亮度值 (0-255)
 * @param color - 十六进制颜色值，如 '#ffffff' 或 '#fff'
 * @returns 亮度值，0为最暗，255为最亮
 */
const getColorBrightness = (color: string): number => {
  // 移除 # 符号
  const hex = color.replace('#', '')

  // 处理3位和6位十六进制颜色
  let r: number, g: number, b: number
  if (hex.length === 3) {
    r = parseInt(hex[0] + hex[0], 16)
    g = parseInt(hex[1] + hex[1], 16)
    b = parseInt(hex[2] + hex[2], 16)
  } else {
    r = parseInt(hex.substring(0, 2), 16)
    g = parseInt(hex.substring(2, 4), 16)
    b = parseInt(hex.substring(4, 6), 16)
  }

  // 使用相对亮度公式 (ITU-R BT.709)
  return Math.round(0.299 * r + 0.587 * g + 0.114 * b)
}

/**
 * 根据背景颜色智能选择合适的字体颜色
 * @param backgroundColor - 背景颜色
 * @param actualTheme - 当前主题
 * @returns 包含字体颜色配置的对象
 */
const getAdaptiveTextColors = (backgroundColor: string, actualTheme: string) => {
  // 如果是透明背景或图片背景，使用默认主题颜色
  if (backgroundColor === 'transparent' || backgroundColor.includes('var(')) {
    return actualTheme === 'light'
      ? {
          foreground: '#000000',
          cursor: '#000000',
          cursorAccent: '#000000',
          selectionBackground: 'rgba(0, 0, 0, 0.3)'
        }
      : {
          foreground: '#e2e8f0',
          cursor: '#e2e8f0',
          cursorAccent: '#e2e8f0',
          selectionBackground: 'rgba(255, 255, 255, 0.3)'
        }
  }

  // 计算背景颜色亮度
  const brightness = getColorBrightness(backgroundColor)
  const isLightBackground = brightness > 128

  console.log(`Background ${backgroundColor} brightness: ${brightness}, isLight: ${isLightBackground}`)

  // 根据背景亮度选择合适的字体颜色
  if (isLightBackground) {
    // 浅色背景使用深色字体
    return {
      foreground: '#1a1a1a',
      cursor: '#2d3748',
      cursorAccent: '#2d3748',
      selectionBackground: 'rgba(45, 55, 72, 0.3)',
      // ANSI颜色适配浅色背景
      black: '#1a1a1a',
      red: '#dc2626',
      green: '#16a34a',
      yellow: '#ca8a04',
      blue: '#2563eb',
      magenta: '#9333ea',
      cyan: '#0891b2',
      white: '#374151',
      brightBlack: '#6b7280',
      brightRed: '#ef4444',
      brightGreen: '#22c55e',
      brightYellow: '#eab308',
      brightBlue: '#3b82f6',
      brightMagenta: '#a855f7',
      brightCyan: '#06b6d4',
      brightWhite: '#111827'
    }
  } else {
    // 深色背景使用浅色字体
    return {
      foreground: '#e2e8f0',
      cursor: '#f1f5f9',
      cursorAccent: '#f1f5f9',
      selectionBackground: 'rgba(241, 245, 249, 0.3)',
      // ANSI颜色适配深色背景
      black: '#1e293b',
      red: '#f87171',
      green: '#4ade80',
      yellow: '#facc15',
      blue: '#60a5fa',
      magenta: '#c084fc',
      cyan: '#22d3ee',
      white: '#e2e8f0',
      brightBlack: '#64748b',
      brightRed: '#fca5a5',
      brightGreen: '#86efac',
      brightYellow: '#fde047',
      brightBlue: '#93c5fd',
      brightMagenta: '#d8b4fe',
      brightCyan: '#67e8f9',
      brightWhite: '#f8fafc'
    }
  }
}

const getTerminalTheme = (actualTheme: string) => {
  return actualTheme === 'light'
    ? {
        background: 'transparent',
        foreground: '#000000',
        cursor: '#000000',
        cursorAccent: '#000000',
        selectionBackground: 'rgba(0, 0, 0, 0.3)'
      }
    : {
        background: 'transparent',
        foreground: '#f0f0f0',
        cursor: '#f0f0f0',
        cursorAccent: '#f0f0f0',
        selectionBackground: 'rgba(255, 255, 255, 0.3)'
      }
}

// 强制背景修复系统
const startBackgroundFixSystem = () => {
  console.log('Starting background fix system for SSH terminal')

  const fixBackground = () => {
    const container = terminalContainer.value
    if (!container) return

    // 强制设置容器背景
    if (terminalBackground.value?.type === 'color' && terminalBackground.value?.color) {
      container.style.setProperty('background-color', terminalBackground.value.color, 'important')
      container.style.setProperty('background-image', 'none', 'important')
    }

    // 强制设置所有子元素透明
    const allChildren = container.querySelectorAll('*')
    allChildren.forEach((child: Element) => {
      const element = child as HTMLElement
      const computedStyle = window.getComputedStyle(element)

      // 如果发现黑色背景，强制设置为透明
      if (
        computedStyle.backgroundColor === 'rgb(0, 0, 0)' ||
        computedStyle.backgroundColor === 'black' ||
        computedStyle.backgroundColor.includes('rgb(0, 0, 0)')
      ) {
        element.style.setProperty('background-color', 'transparent', 'important')
        element.style.setProperty('background', 'transparent', 'important')
        console.log('Fixed black background on element:', element.tagName, element.className)
      }
    })

    // 特别处理Canvas元素
    const canvases = container.querySelectorAll('canvas')
    canvases.forEach((canvas: Element) => {
      const canvasElement = canvas as HTMLCanvasElement
      canvasElement.style.setProperty('background-color', 'transparent', 'important')
      canvasElement.style.setProperty('background', 'transparent', 'important')
    })
  }

  // 立即执行一次
  fixBackground()

  // 每100ms检查一次
  const fixInterval = setInterval(fixBackground, 100)

  // 在组件卸载时清理
  onBeforeUnmount(() => {
    clearInterval(fixInterval)
  })
}

// 性能优化相关
const terminalOptimizer = new TerminalOptimizer({
  maxLines: 10000,
  compressionThreshold: 5000,
  virtualScrollEnabled: true,
  memoryThreshold: 100
})
// const performanceMonitor = PerformanceMonitorService.getInstance()

// AI助手相关数据
const lastExecutedCommand = ref('')
const lastCommandOutput = ref('')
const commandHistory = ref<string[]>([])
const maxHistorySize = 100

// AI助手事件处理
const executeAICommand = (command: string) => {
  console.log('[AI Assistant] 执行AI建议的命令:', command)
  // 直接发送命令到终端
  sendData(command + '\r')

  // 记录到历史
  addToCommandHistory(command)
}

const copyToClipboard = (text: string) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      console.log('[AI Assistant] 已复制到剪贴板:', text)
    })
    .catch((err) => {
      console.error('[AI Assistant] 复制失败:', err)
    })
}

const addToCommandHistory = (command: string) => {
  if (command.trim()) {
    commandHistory.value.push(command.trim())
    if (commandHistory.value.length > maxHistorySize) {
      commandHistory.value.shift()
    }
  }
}
let dbConfigStash: {
  aliasStatus?: number
  autoCompleteStatus?: number
  scrollBack?: number
  highlightStatus?: number
  [key: string]: unknown
} = {}
let config: Record<string, unknown>

const deviceStore = useDeviceStore()
const isOfficeDevice = ref(false)
const isLocalConnect = ref(false)

const getUserInfo = async () => {
  try {
    const res = (await checkUserDevice({
      ip: deviceStore.getDeviceIp as string,
      macAddress: deviceStore.getMacAddress as string
    })) as {
      code: number
      data: { isOfficeDevice: boolean }
    }
    if (res && res.code === 200) {
      isOfficeDevice.value = res.data.isOfficeDevice
    }
  } catch (error) {
    console.error(t('common.getUserInfoFailed'), error)
  }
}

onMounted(async () => {
  await getUserInfo()
  config = await serviceUserConfig.getConfig()
  dbConfigStash = config
  // 强制启用智能补全进行测试
  queryCommandFlag.value = true

  // 调试信息：显示智能补全配置状态
  console.log('[SSH Debug] 智能补全配置状态:', {
    autoCompleteStatus: config.autoCompleteStatus,
    originalSetting: config.autoCompleteStatus == 1,
    queryCommandFlag: queryCommandFlag.value,
    forcedEnabled: true,
    config: config
  })

  // 如果智能补全被禁用，提供手动启用的方法
  if (!queryCommandFlag.value) {
    console.log('[SSH Debug] 智能补全已禁用。要启用，请在控制台运行: window.enableSmartCompletion()')
    // 添加全局函数用于调试
    const debugWindow = window as unknown as {
      enableSmartCompletion?: () => void
    }
    debugWindow.enableSmartCompletion = () => {
      queryCommandFlag.value = true
      console.log('[SSH Debug] 智能补全已手动启用')
    }

    // 添加直接测试函数
    ;(
      window as unknown as {
        testDirectCompletion?: (cmd?: string) => void
        forceShowSuggestions?: () => void
        debugTerminalState?: () => void
      }
    ).testDirectCompletion = (cmd = 'ls') => {
      console.log('[SSH Debug] 直接测试智能补全，命令:', cmd)
      tryGetCurrentLineAndQuery()
    }

    // 强制显示建议的调试函数
    ;(window as unknown as { forceShowSuggestions?: () => void }).forceShowSuggestions = () => {
      console.log('[SSH Debug] 强制显示测试建议')
      suggestions.value = [
        { command: 'ls -la', source: 'base', description: '详细列出文件', type: 'command', confidence: 0.9 },
        { command: 'ls -lh', source: 'base', description: '人类可读格式列出文件', type: 'command', confidence: 0.8 },
        { command: 'ls --help', source: 'base', description: '显示ls命令帮助', type: 'command', confidence: 0.7 },
        { command: 'pwd', source: 'base', description: '显示当前目录', type: 'command', confidence: 0.6 },
        { command: 'cd ~', source: 'history', description: '切换到主目录', type: 'command', confidence: 0.5 }
      ]
      activeSuggestion.value = 0
      suggestionSelectionMode.value = false
      console.log('[SSH Debug] 建议已设置:', suggestions.value)

      // 立即更新建议位置
      setTimeout(() => {
        const componentInstance = componentRefs.value[connectionId.value]
        ;(componentInstance as { updateSuggestionsPosition?: (terminal: unknown) => void })?.updateSuggestionsPosition?.(terminal.value)
      }, 100)
    }

    // 调试终端状态的函数
    ;(window as unknown as { debugTerminalState?: () => void }).debugTerminalState = () => {
      console.log('[SSH Debug] 当前终端状态:', {
        terminalState: terminalState.value,
        suggestions: suggestions.value,
        queryCommandFlag: queryCommandFlag.value,
        selectFlag: selectFlag.value,
        suggestionSelectionMode: suggestionSelectionMode.value,
        isConnected: isConnected.value
      })
    }

    // 完整的智能补全测试函数
    ;(window as unknown as { fullSmartCompletionTest?: () => void }).fullSmartCompletionTest = () => {
      console.log('[SSH Debug] 开始完整智能补全测试')

      // 1. 强制设置终端状态
      terminalState.value.content = 'ls'
      terminalState.value.beforeCursor = 'ls'

      // 2. 确保配置正确
      queryCommandFlag.value = true
      selectFlag.value = false
      suggestionSelectionMode.value = false

      // 3. 强制更新终端状态
      updateTerminalState(false, false, false)

      // 4. 触发查询
      setTimeout(() => {
        console.log('[SSH Debug] 执行查询，当前状态:', {
          content: terminalState.value.content,
          beforeCursor: terminalState.value.beforeCursor,
          queryCommandFlag: queryCommandFlag.value
        })
        queryCommand('ls')
      }, 100)
    }

    // 测试API可用性的函数
    ;(window as unknown as { testSmartCompletionAPI?: () => void }).testSmartCompletionAPI = async () => {
      console.log('[SSH Debug] 测试智能补全API可用性')

      const api = (window as unknown as { api?: { getSmartSuggestions?: (params: unknown) => Promise<unknown> } }).api
      if (!api) {
        console.error('[SSH Debug] window.api 不可用')
        return
      }

      if (!api.getSmartSuggestions) {
        console.error('[SSH Debug] getSmartSuggestions 方法不可用')
        return
      }

      try {
        const result = await api.getSmartSuggestions({
          input: 'ls',
          context: {
            serverIp: props.connectData.ip,
            username: props.connectData.username,
            sessionId: connectionId.value
          }
        })
        console.log('[SSH Debug] API测试成功:', result)
      } catch (error) {
        console.error('[SSH Debug] API测试失败:', error)
      }
    }

    // 清除所有建议的函数
    ;(window as unknown as { clearSuggestions?: () => void }).clearSuggestions = () => {
      console.log('[SSH Debug] 清除所有建议')
      suggestions.value = []
      activeSuggestion.value = -1
      suggestionSelectionMode.value = false
    }

    // 测试建议选择功能
    ;(window as unknown as { testSuggestionSelection?: () => void }).testSuggestionSelection = () => {
      console.log('[SSH Debug] 测试建议选择功能')

      // 1. 显示测试建议
      suggestions.value = [
        { command: 'ls -la', source: 'base', description: '详细列出文件', type: 'command', confidence: 0.9 },
        { command: 'ls -lh', source: 'base', description: '人类可读格式列出文件', type: 'command', confidence: 0.8 }
      ]
      activeSuggestion.value = 0
      suggestionSelectionMode.value = false

      console.log('[SSH Debug] 建议已显示，现在可以：')
      console.log('1. 按回车键选择第一个建议')
      console.log('2. 按右箭头键进入选择模式，然后用上下箭头选择')
      console.log('3. 在选择模式下按回车键确认选择')

      // 更新建议位置
      setTimeout(() => {
        const componentInstance = componentRefs.value[connectionId.value]
        ;(componentInstance as { updateSuggestionsPosition?: (terminal: unknown) => void })?.updateSuggestionsPosition?.(terminal.value)
      }, 100)
    }
  }

  // Load terminal background configuration BEFORE creating terminal
  if (config.terminalBackground && typeof config.terminalBackground === 'object') {
    const bgConfig = config.terminalBackground as {
      type?: string
      color?: string
      imageUrl?: string
      opacity?: number
    }
    terminalBackground.value = {
      type: bgConfig.type || 'default',
      color: bgConfig.color || '#1a1a1a',
      imageUrl: bgConfig.imageUrl || '',
      opacity: bgConfig.opacity || 1.0
    }
    console.log('SSH Terminal loaded background config:', terminalBackground.value)
  } else {
    console.log('SSH Terminal: No background config found, using default')
  }

  // 立即应用背景配置
  nextTick(() => {
    applyBackgroundToContainer()
    console.log('Applied background immediately after config load')
  })

  const actualTheme = getActualTheme(config.theme as string)

  // Create terminal instance with proper theme that includes background
  const termInstance = markRaw(
    new Terminal({
      scrollback: (config.scrollBack as number) || 1000,
      cursorBlink: true,
      cursorStyle: (config.cursorStyle as 'bar' | 'block' | 'underline') || 'block',
      fontSize: (config.fontSize as number) || 12,
      fontFamily: (config.fontFamily as string) || 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace',
      disableStdin: false, // 明确启用输入
      allowTransparency: true, // 启用透明度支持
      convertEol: true,
      // 添加关键配置确保输入正常工作
      macOptionIsMeta: true,
      macOptionClickForcesSelection: false,
      rightClickSelectsWord: false,
      // 确保终端可以接收所有输入
      screenReaderMode: false,
      theme: getTerminalTheme(actualTheme)
    })
  )

  console.log('SSH Terminal created with theme:', getTerminalTheme(actualTheme))
  terminal.value = termInstance

  // 在终端创建后立即应用背景设置
  nextTick(() => {
    applyBackgroundToContainer()
    console.log('Applied background after terminal creation:', terminalBackground.value)

    // 多次尝试应用背景，确保生效
    setTimeout(() => {
      applyBackgroundToContainer()
      console.log('Applied background after 500ms delay')
    }, 500)

    setTimeout(() => {
      applyBackgroundToContainer()
      console.log('Applied background after 1000ms delay')
    }, 1000)

    setTimeout(() => {
      applyBackgroundToContainer()
      console.log('Applied background after 2000ms delay')
    }, 2000)
  })

  termInstance?.onKey(handleKeyInput)
  termInstance?.onSelectionChange(() => {
    if (termInstance.hasSelection()) {
      copyText.value = termInstance.getSelection()
      if (copyText.value.trim()) {
        navigator.clipboard.writeText(copyText.value.trim()).catch(() => {
          console.warn('Failed to copy to clipboard')
        })
      }
    }
    updateSelectionButtonPosition()
  })
  nextTick(() => {
    const viewport = terminalElement.value?.querySelector('.xterm-viewport')
    if (viewport) {
      viewport.addEventListener('scroll', () => updateSelectionButtonPosition())
    }
  })

  fitAddon.value = new FitAddon()
  termInstance.loadAddon(fitAddon.value)
  if (terminalElement.value) {
    termInstance.open(terminalElement!.value)
  }
  fitAddon?.value.fit()
  searchAddon.value = new SearchAddon()
  termInstance.loadAddon(searchAddon.value)
  termInstance.scrollToBottom()
  termInstance.focus()

  // 在终端完全初始化后应用背景
  setTimeout(() => {
    applyBackgroundToContainer()
    console.log('Applied background after terminal fully initialized')
  }, 100)

  // 不使用WebGL渲染器，避免Canvas背景问题
  // const webgl = new WebglAddon()
  // termInstance.loadAddon(webgl)

  console.log('SSH Terminal: Skipping WebGL addon to avoid background issues')

  // 启动强制背景修复系统
  startBackgroundFixSystem()
  termInstance.onResize((size) => {
    resizeSSH(size.cols, size.rows)
  })
  const textarea = termInstance?.element?.querySelector('.xterm-helper-textarea') as HTMLTextAreaElement | null
  if (textarea) {
    textareaCompositionListener = (e) => {
      const encoded = encoder.encode(e.data)
      handleKeyInput({
        domEvent: {
          keyCode: encoded.length > 0 ? encoded[0] : 0,
          code: e.data,
          altKey: false,
          metaKey: false,
          ctrlKey: false,
          ...e
        },
        key: e.data
      })
    }
    textareaPasteListener = () => {
      pasteFlag.value = true
    }
    textarea.addEventListener('compositionend', textareaCompositionListener)
    textarea.addEventListener('paste', textareaPasteListener)
  }
  const core = (termInstance as unknown as { _core: unknown })._core as {
    _renderService: {
      _renderDebouncer: { refresh: () => void }
      refreshRows: (start: number, end: number) => void
    }
    _bufferService: { rows: number }
  }
  const renderService = core._renderService
  const originalWrite = termInstance.write.bind(termInstance)
  const debouncedUpdateTerminalState = (data: string, currentIsUserCall: boolean) => {
    if (updateTimeout) {
      clearTimeout(updateTimeout)
    }
    // 总是更新终端状态，无论是否为用户调用
    updateTerminalState(JSON.stringify(data).endsWith(startStr.value), enterPress.value, tagPress.value)

    console.log('[SSH Debug] 终端状态已更新:', {
      isUserCall: currentIsUserCall,
      data: data,
      terminalContent: terminalState.value.content,
      terminalBeforeCursor: terminalState.value.beforeCursor
    })
    let highLightFlag: boolean = true
    if (enterPress.value || specialCode.value) {
      highLightFlag = false
    }
    if (terminalMode.value !== 'none') {
      highLightFlag = false
    }
    if (currentIsUserCall) {
      highLightFlag = false
    }
    if (pasteFlag.value) {
      highLightFlag = true
    }
    if (highLightFlag) {
      if ((config.highlightStatus as number) == 1) {
        highlightSyntax(terminalState.value)
        pasteFlag.value = false
      }
      if (!selectFlag.value) {
        // 延迟调用智能补全，确保终端状态已更新
        setTimeout(() => {
          if (!selectFlag.value) {
            console.log('[SSH Debug] 高亮后触发智能补全查询')
            queryCommand()
          }
        }, 100)
      }
    }
    updateTimeout = null
  }

  cusWrite = function (data: string, options?: { isUserCall?: boolean }): void {
    console.log('[SSH Debug] cusWrite called:', {
      hasData: !!data,
      dataLength: data?.length,
      isUserCall: options?.isUserCall,
      dataPreview: data?.substring(0, 50)
    })
    const currentIsUserCall = options?.isUserCall ?? false
    userInputFlag.value = currentIsUserCall
    const originalRequestRefresh = renderService.refreshRows.bind(renderService)
    const originalTriggerRedraw = renderService._renderDebouncer.refresh.bind(renderService._renderDebouncer)
    renderService.refreshRows = () => {}
    renderService._renderDebouncer.refresh = () => {}

    originalWrite(data, () => {
      // 总是更新终端状态，无论是否为用户调用
      debouncedUpdateTerminalState(data, currentIsUserCall)

      // Ensure scroll to bottom after write completion
      if (!currentIsUserCall) {
        nextTick(() => {
          terminal.value?.scrollToBottom()
        })
      }
    })

    renderService.refreshRows = originalRequestRefresh
    renderService._renderDebouncer.refresh = originalTriggerRedraw
    renderService.refreshRows(0, core._bufferService.rows - 1)
  }
  termInstance.write = cusWrite as typeof termInstance.write
  if (terminalContainer.value) {
    resizeObserver = new ResizeObserver(
      debounce(
        () => {
          handleResize()
        },
        30,
        true
      )
    )
    resizeObserver.observe(terminalContainer.value)
  }
  window.addEventListener('resize', handleResize)
  window.addEventListener('wheel', handleWheel)
  window.addEventListener('keydown', handleGlobalKeyDown)
  window.addEventListener('click', () => {
    if (contextmenu.value && typeof contextmenu.value.hide === 'function') {
      contextmenu.value.hide()
    }
  })

  nextTick(() => {
    setTimeout(() => {
      handleResize()
      // 预先设置输入处理器，确保在SSH连接建立前就能处理输入
      setupTerminalInput()
      inputManager.registerInstances(
        {
          termOndata: handleExternalInput,
          syncInput: false
        },
        connectionId.value
      )
    }, 100)
    terminalContainerResize()
  })

  const handleSendOrToggleAi = () => {
    if (props.activeTabId !== props.currentConnectionId) {
      console.log('Not active tab, ignoring event')
      return
    }

    const activeElement = document.activeElement
    const terminalContainer = terminalElement.value?.closest('.terminal-container')
    const isTerminalFocused =
      activeElement === terminal.value?.textarea ||
      terminalContainer?.contains(activeElement) ||
      activeElement?.classList.contains('xterm-helper-textarea')

    if (termInstance && termInstance.hasSelection() && isTerminalFocused) {
      const selectedText = termInstance.getSelection().trim()
      if (selectedText) {
        eventBus.emit('openAiRight')
        nextTick(() => {
          const formattedText = `Terminal output:\n\`\`\`\n${selectedText}\n\`\`\``
          eventBus.emit('chatToAi', formattedText)
        })
        return
      }
    }
    eventBus.emit('toggleSideBar', 'right')
  }

  const handleSendOrToggleAiForTab = (targetTabId: string) => {
    if (targetTabId !== props.currentConnectionId) {
      return
    }

    handleSendOrToggleAi()
  }

  if (props.connectData.asset_type === 'shell') {
    config.highlightStatus = 2
    // 保持原有的自动补全设置，不强制禁用
    // config.autoCompleteStatus = 2
    isLocalConnect.value = true
    connectLocalSSH()
  } else {
    connectSSH()
  }

  const handleExecuteCommand = (command: string) => {
    if (props.activeTabId !== props.currentConnectionId) return
    sendMarkedData(command, 'Chaterm:command')
    termInstance.focus()
  }

  const handleRequestUpdateCwdForHost = (hostIp: string) => {
    if (props.connectData.ip !== hostIp) return

    sendMarkedData('pwd\r', 'Chaterm:pwd')
  }
  const handleUpdateTheme = (theme: string) => {
    if (terminal.value) {
      const actualTheme = getActualTheme(theme)
      terminal.value.options.theme =
        actualTheme === 'light'
          ? {
              background: 'transparent',
              foreground: '#000000',
              cursor: '#000000',
              cursorAccent: '#000000',
              selectionBackground: 'rgba(0, 0, 0, 0.3)'
            }
          : {
              background: 'transparent',
              foreground: '#f0f0f0',
              cursor: '#f0f0f0',
              cursorAccent: '#f0f0f0',
              selectionBackground: 'rgba(255, 255, 255, 0.3)'
            }
    }
  }

  // Listen for terminal background update events
  const handleUpdateTerminalBackground = (backgroundConfig: { type?: string; color?: string; imageUrl?: string; opacity?: number }) => {
    console.log('SSH Terminal received background update:', backgroundConfig)
    terminalBackground.value = {
      type: backgroundConfig.type || 'default',
      color: backgroundConfig.color || 'transparent',
      imageUrl: backgroundConfig.imageUrl || '',
      opacity: backgroundConfig.opacity || 1.0
    }

    // 更新终端主题以反映背景变化
    if (terminal.value) {
      // 重新生成主题，包含新的背景色
      const actualTheme = getActualTheme(config.theme as string)
      const newTheme = getTerminalTheme(actualTheme)
      terminal.value.options.theme = newTheme

      // 强制刷新终端显示
      terminal.value.refresh(0, terminal.value.rows - 1)

      console.log('SSH Terminal theme updated with new background:', newTheme)
      console.log('SSH Terminal background config updated:', terminalBackground.value)
    }

    // 强制应用容器背景样式
    nextTick(() => {
      applyBackgroundToContainer()
    })

    console.log('Background update completed')
  }

  // 应用背景到容器的方法
  const applyBackgroundToContainer = () => {
    const container = terminalContainer.value
    if (!container) {
      console.log('No terminal container found for background application')
      return
    }

    console.log('Applying background to SSH terminal container:', terminalBackground.value)

    // 验证背景配置
    if (!terminalBackground.value || !terminalBackground.value.type) {
      console.warn('Invalid terminal background configuration:', terminalBackground.value)
      return
    }

    // 添加全局CSS规则来强制覆盖样式
    const styleId = `ssh-terminal-bg-${props.currentConnectionId}`
    let styleElement = document.getElementById(styleId) as HTMLStyleElement
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = styleId
      document.head.appendChild(styleElement)
    }

    let cssRules = ''
    const containerSelector = `[data-ssh-connect-id="${props.currentConnectionId}"]`

    // 增强CSS选择器优先级
    const highPrioritySelector = `body ${containerSelector}.terminal-container`

    if (terminalBackground.value.type === 'color' && terminalBackground.value.color) {
      // 验证颜色格式
      const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
      if (!colorRegex.test(terminalBackground.value.color)) {
        console.error('Invalid color format:', terminalBackground.value.color)
        return
      }

      cssRules = `
        ${highPrioritySelector} {
          background-color: ${terminalBackground.value.color} !important;
          background-image: none !important;
          opacity: ${terminalBackground.value.opacity || 1.0} !important;
        }
        ${containerSelector} {
          background-color: ${terminalBackground.value.color} !important;
          background-image: none !important;
          opacity: ${terminalBackground.value.opacity || 1.0} !important;
        }
        ${containerSelector} .xterm,
        ${containerSelector} .xterm-screen,
        ${containerSelector} .xterm-viewport,
        ${containerSelector} .xterm-rows,
        ${containerSelector} .xterm-text-layer,
        ${containerSelector} .xterm-cursor-layer,
        ${containerSelector} .xterm-selection-layer,
        ${containerSelector} canvas {
          background: transparent !important;
          background-color: transparent !important;
        }
      `
      console.log('Applied color background CSS:', terminalBackground.value.color)
    } else if (terminalBackground.value.type === 'image' && terminalBackground.value.imageUrl) {
      // 特别处理图片背景，确保不被遮挡
      const ultraHighPrioritySelector = `html body div${containerSelector}.terminal-container`
      cssRules = `
        ${ultraHighPrioritySelector} {
          background-image: url(${terminalBackground.value.imageUrl}) !important;
          background-size: cover !important;
          background-position: center !important;
          background-repeat: no-repeat !important;
          background-attachment: local !important;
          background-color: ${terminalBackground.value.color || 'transparent'} !important;
          opacity: ${terminalBackground.value.opacity || 1.0} !important;
          position: relative !important;
          z-index: 1 !important;
          isolation: isolate !important;
        }
        ${highPrioritySelector} {
          background-image: url(${terminalBackground.value.imageUrl}) !important;
          background-size: cover !important;
          background-position: center !important;
          background-repeat: no-repeat !important;
          background-attachment: local !important;
          background-color: ${terminalBackground.value.color || 'transparent'} !important;
          opacity: ${terminalBackground.value.opacity || 1.0} !important;
        }
        ${containerSelector} {
          background-image: url(${terminalBackground.value.imageUrl}) !important;
          background-size: cover !important;
          background-position: center !important;
          background-repeat: no-repeat !important;
          background-color: ${terminalBackground.value.color || 'transparent'} !important;
          opacity: ${terminalBackground.value.opacity || 1.0} !important;
        }
        ${containerSelector} *,
        ${containerSelector} .xterm,
        ${containerSelector} .xterm-screen,
        ${containerSelector} .xterm-viewport,
        ${containerSelector} .xterm-rows,
        ${containerSelector} .xterm-text-layer,
        ${containerSelector} .xterm-cursor-layer,
        ${containerSelector} .xterm-selection-layer,
        ${containerSelector} .xterm-link-layer,
        ${containerSelector} .xterm-helper-textarea {
          background: transparent !important;
          background-color: transparent !important;
          background-image: none !important;
          backdrop-filter: none !important;
        }
        ${containerSelector}::before,
        ${containerSelector}::after {
          background: transparent !important;
          background-color: transparent !important;
          background-image: none !important;
        }
      `
      console.log('Applied image background CSS with anti-blocking measures:', terminalBackground.value.imageUrl)
    } else {
      // 默认背景 - 使用透明背景让用户设置显示
      cssRules = `
        ${highPrioritySelector} {
          background-color: transparent !important;
          background-image: none !important;
          opacity: 1 !important;
        }
        ${containerSelector} {
          background-color: transparent !important;
          background-image: none !important;
          opacity: 1 !important;
        }
      `
      console.log('Applied default background CSS')
    }

    styleElement.textContent = cssRules

    // 同时直接设置内联样式作为备用
    if (terminalBackground.value.type === 'color' && terminalBackground.value.color) {
      container.style.setProperty('background-color', terminalBackground.value.color, 'important')
      container.style.setProperty('background-image', 'none', 'important')
      container.style.setProperty('opacity', (terminalBackground.value.opacity || 1.0).toString(), 'important')

      // 强制刷新样式
      container.offsetHeight // 触发重排

      console.log('Direct style applied:', {
        backgroundColor: terminalBackground.value.color,
        opacity: terminalBackground.value.opacity || 1.0
      })
    } else if (terminalBackground.value.type === 'image' && terminalBackground.value.imageUrl) {
      // 图片背景的特殊处理 - 防止遮挡
      container.style.setProperty('background-image', `url(${terminalBackground.value.imageUrl})`, 'important')
      container.style.setProperty('background-size', 'cover', 'important')
      container.style.setProperty('background-position', 'center', 'important')
      container.style.setProperty('background-repeat', 'no-repeat', 'important')
      container.style.setProperty('background-attachment', 'local', 'important')
      container.style.setProperty('background-color', terminalBackground.value.color || 'transparent', 'important')
      container.style.setProperty('opacity', (terminalBackground.value.opacity || 1.0).toString(), 'important')
      container.style.setProperty('position', 'relative', 'important')
      container.style.setProperty('z-index', '1', 'important')
      container.style.setProperty('isolation', 'isolate', 'important')

      console.log('Direct image background style applied:', terminalBackground.value.imageUrl)
    }

    // 确保xterm元素背景透明 - 增强版本
    const makeXtermTransparent = () => {
      const xtermElements = container.querySelectorAll(
        '.xterm, .xterm-screen, .xterm-viewport, .xterm-rows, .xterm-text-layer, .xterm-cursor-layer, .xterm-selection-layer, .xterm-link-layer, .xterm-helper-textarea'
      )
      xtermElements.forEach((element: Element) => {
        const htmlElement = element as HTMLElement
        htmlElement.style.setProperty('background', 'transparent', 'important')
        htmlElement.style.setProperty('background-color', 'transparent', 'important')
        htmlElement.style.setProperty('background-image', 'none', 'important')
        htmlElement.style.setProperty('backdrop-filter', 'none', 'important')

        // 特别处理可能有固定背景的元素
        if (element.classList.contains('xterm-screen') || element.classList.contains('xterm-viewport')) {
          htmlElement.style.setProperty('background-attachment', 'initial', 'important')
        }
      })

      // 特别处理WebGL Canvas元素
      const canvasElements = container.querySelectorAll('canvas')
      canvasElements.forEach((canvas: Element) => {
        const canvasElement = canvas as HTMLCanvasElement
        canvasElement.style.setProperty('background', 'transparent', 'important')
        canvasElement.style.setProperty('background-color', 'transparent', 'important')
        canvasElement.style.setProperty('background-image', 'none', 'important')

        // 设置WebGL清除颜色为透明
        const gl = canvasElement.getContext('webgl') || canvasElement.getContext('webgl2')
        if (gl) {
          gl.clearColor(0.0, 0.0, 0.0, 0.0)
          gl.clear(gl.COLOR_BUFFER_BIT)
          console.log('WebGL canvas clear color set to transparent')
        }
      })

      console.log('Made xterm elements transparent, found', xtermElements.length, 'elements and', canvasElements.length, 'canvas elements')
    }

    // 立即执行一次
    makeXtermTransparent()

    // 延迟执行多次确保生效，特别是对图片背景
    setTimeout(makeXtermTransparent, 50)
    setTimeout(makeXtermTransparent, 100)
    setTimeout(makeXtermTransparent, 200)
    setTimeout(makeXtermTransparent, 500) // 额外延迟确保xterm完全初始化

    // 强制刷新容器渲染（特别是图片背景）
    if (terminalBackground.value.type === 'image') {
      setTimeout(() => {
        container.style.display = 'none'
        container.offsetHeight // 强制重排
        container.style.display = ''
        makeXtermTransparent() // 再次确保透明
      }, 100)
    }

    // 验证应用结果
    setTimeout(() => {
      const computedStyle = window.getComputedStyle(container)
      console.log('Background application result:', {
        backgroundColor: computedStyle.backgroundColor,
        backgroundImage: computedStyle.backgroundImage,
        backgroundSize: computedStyle.backgroundSize,
        backgroundPosition: computedStyle.backgroundPosition,
        opacity: computedStyle.opacity,
        position: computedStyle.position,
        zIndex: computedStyle.zIndex,
        expected: terminalBackground.value
      })

      // 特别检查图片背景是否正确应用
      if (terminalBackground.value.type === 'image' && terminalBackground.value.imageUrl) {
        const hasBackgroundImage = computedStyle.backgroundImage !== 'none'
        console.log('Image background check:', {
          hasImage: hasBackgroundImage,
          imageUrl: terminalBackground.value.imageUrl,
          computedImage: computedStyle.backgroundImage
        })

        if (!hasBackgroundImage) {
          console.warn('⚠️ 图片背景未正确应用，可能被其他样式覆盖')

          // 尝试强制重新应用
          console.log('🔄 尝试强制重新应用图片背景...')
          container.style.setProperty('background-image', `url(${terminalBackground.value.imageUrl})`, 'important')
          container.style.setProperty('background-size', 'cover', 'important')
          container.style.setProperty('background-position', 'center', 'important')
          container.style.setProperty('background-repeat', 'no-repeat', 'important')
        } else {
          console.log('✅ 图片背景已正确应用')
        }
      }
    }, 600)
  }
  const handleGetCursorPosition = (callback: (position: ReturnType<typeof getCursorLinePosition>) => void) => {
    if (props.activeTabId !== props.currentConnectionId) return

    const position = getCursorLinePosition()
    if (position) {
      callback(position)
    }
  }

  eventBus.on('executeTerminalCommand', handleExecuteCommand)
  eventBus.on('autoExecuteCode', autoExecuteCode)
  eventBus.on('getCursorPosition', handleGetCursorPosition)
  eventBus.on('sendOrToggleAiFromTerminalForTab', handleSendOrToggleAiForTab)
  eventBus.on('requestUpdateCwdForHost', handleRequestUpdateCwdForHost)
  eventBus.on('updateTheme', handleUpdateTheme)
  eventBus.on('updateTerminalBackground', handleUpdateTerminalBackground)
  eventBus.on('openSearch', openSearch)

  // 调试：监听所有事件
  console.log('SSH Terminal event listeners registered for connection:', props.currentConnectionId)
  console.log('EventBus instance:', eventBus)

  // 监听来自preload的搜索和关闭触发事件（Windows系统专用）
  const handlePostMessage = (event: MessageEvent) => {
    if (event.data?.type === 'TRIGGER_SEARCH') {
      // 只有当前活跃的终端才响应搜索事件
      if (props.activeTabId === props.currentConnectionId) {
        openSearch()
      }
    } else if (event.data?.type === 'TRIGGER_CLOSE') {
      // 只在Windows系统下处理 TRIGGER_CLOSE 消息
      const isWindows = navigator.userAgent.toLowerCase().includes('win')
      if (isWindows) {
        // 检查时间戳，避免重复处理
        const messageTimestamp = event.data?.timestamp
        if (messageTimestamp && lastCloseTimestamp.value === messageTimestamp) {
          return
        }
        lastCloseTimestamp.value = messageTimestamp

        // 检查消息是否发送给当前连接
        const targetConnectionId = event.data?.targetConnectionId
        if (targetConnectionId && targetConnectionId !== props.currentConnectionId) {
          return
        }

        // 只有当前活跃的终端才响应关闭事件
        if (props.activeTabId === props.currentConnectionId) {
          contextAct('close')
        }
      }
    }
  }
  window.addEventListener('message', handlePostMessage)

  eventBus.on('clearCurrentTerminal', () => {
    contextAct('clearTerm')
  })

  // Listen for font size change events
  eventBus.on('fontSizeIncrease', () => {
    contextAct('fontsizeLargen')
  })

  eventBus.on('fontSizeDecrease', () => {
    contextAct('fontsizeSmaller')
  })

  // Listen for font update events
  const handleUpdateFont = (newFontFamily: string) => {
    if (terminal.value) {
      terminal.value.options.fontFamily = newFontFamily
    }
  }
  eventBus.on('updateTerminalFont', handleUpdateFont)
  eventBus.on('updateTerminalFontSize', (newFontSize: number) => {
    if (terminal.value) {
      terminal.value.options.fontSize = newFontSize
      // Trigger resize to adjust terminal layout
      setTimeout(() => {
        handleResize()
      }, 50)
    }
  })
  cleanupListeners.value.push(() => {
    eventBus.off('updateTheme', handleUpdateTheme)
    eventBus.off('updateTerminalBackground', handleUpdateTerminalBackground)
    eventBus.off('executeTerminalCommand', handleExecuteCommand)
    eventBus.off('autoExecuteCode', autoExecuteCode)
    eventBus.off('getCursorPosition', handleGetCursorPosition)
    eventBus.off('sendOrToggleAiFromTerminalForTab', handleSendOrToggleAiForTab)
    eventBus.off('requestUpdateCwdForHost', handleRequestUpdateCwdForHost)
    eventBus.off('updateTerminalFont', handleUpdateFont)
    eventBus.off('updateTerminalFontSize')
    eventBus.off('openSearch', openSearch)
    eventBus.off('clearCurrentTerminal')
    eventBus.off('fontSizeIncrease')
    eventBus.off('fontSizeDecrease')
    window.removeEventListener('keydown', handleGlobalKeyDown)
    window.removeEventListener('message', handlePostMessage)
  })

  if (terminal.value?.textarea) {
    terminal.value.textarea.addEventListener('focus', () => {
      inputManager.setActiveTerm(connectionId.value)
    })
    terminal.value.textarea.addEventListener('blur', hideSelectionButton)
    cleanupListeners.value.push(() => {
      if (terminal.value?.textarea) {
        terminal.value.textarea.removeEventListener('focus', () => {
          inputManager.setActiveTerm(connectionId.value)
        })
        terminal.value.textarea.removeEventListener('blur', hideSelectionButton)
      }
    })
  }
})
const getCmdList = async (systemCommands: string[]) => {
  const allCommands = [...systemCommands, ...shellCommands]
  commands.value = [...new Set(allCommands)].sort()
}

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('wheel', handleWheel)
  inputManager.unregisterInstances(connectionId.value)
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
  cleanupListeners.value.forEach((cleanup) => cleanup())
  cleanupListeners.value = []

  if (terminal.value) {
    const textarea = terminal.value.element?.querySelector('.xterm-helper-textarea') as HTMLTextAreaElement | null
    if (textarea) {
      if (textareaCompositionListener) {
        textarea.removeEventListener('compositionend', textareaCompositionListener)
        textareaCompositionListener = null
      }
      if (textareaPasteListener) {
        textarea.removeEventListener('paste', textareaPasteListener)
        textareaPasteListener = null
      }
    }
  }

  if (isConnected.value) {
    disconnectSSH()
  }

  const viewport = terminalElement.value?.querySelector('.xterm-viewport')
  if (viewport) {
    viewport.removeEventListener('scroll', () => updateSelectionButtonPosition())
  }
})
const getFileExt = (fileName: string): string => {
  const match = fileName.match(/\.[^.\/\\]+$/)
  return match ? match[0] : ''
}

const isOnlyAnsiCodes = (str: string): boolean => {
  const cleaned = stripAnsi(str)
    .replace(/[\x00-\x1F\x7F]/g, '')
    .trim()
  return cleaned.length === 0
}

const cleanForFileName = (str: string): string => {
  return stripAnsi(str)
    .replace(/[\x00-\x1F\x7F]/g, '')
    .replace(/\s+/g, ' ')
    .trim()
}

const normalizeControl = (str: string): string => {
  str = str.replace(/\x08\x1B\[K/g, '')
  const chars: string[] = []
  for (const ch of str) {
    if (ch === '\b') {
      chars.pop()
    } else {
      chars.push(ch)
    }
  }
  return chars.join('')
}

const parseVimLine = (raw: string) => {
  const originalLines = raw.split(/\r?\n/)
  const cleaned = normalizeControl(raw)
    .replace(/\x1B\][0-9]*;[^\x07]*\x07/g, '')
    .replace(/\x1BP.*?\x1B\\/g, '')
    .replace(/\x1B\[\?[0-9;]*[hl]/g, '')
    .replace(/\x1B\[[0-9;]*[ABCDEFGJKST]/g, '')
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
    .replace(/[ \t]+/g, ' ')
    .trim()

  const lines = cleaned.split(/\r?\n/)
  let filePath = ''
  if (lines.length > 1) {
    filePath = stripAnsi(lines[1])
      .replace(/[\x00-\x1F\x7F]/g, '')
      .trim()
  }

  const ext = getFileExt(filePath)
  const contentType = LanguageMap[ext] || 'python'

  let lastLine = ''
  for (let i = originalLines.length - 1; i >= 0; i--) {
    const line = originalLines[i].trim()
    if (line && !isOnlyAnsiCodes(line)) {
      lastLine = '\r\n' + originalLines[i]
      break
    }
  }

  if (filePath.includes('No such file or directory')) {
    filePath = cleanForFileName(filePath.replace('No such file or directory', ''))
  }

  return {
    lastLine,
    filePath,
    contentType
  }
}

const openEditors = reactive<editorData[]>([])
const closeVimEditor = (data: { key: string }) => {
  const { key } = data
  const editor = openEditors.find((editor) => editor?.key === key)
  if (editor?.fileChange) {
    if (!editor?.saved) {
      Modal.confirm({
        title: t('common.saveConfirmTitle'),
        content: t('common.saveConfirmContent', { filePath: editor?.filePath }),
        okText: t('common.confirm'),
        cancelText: t('common.cancel'),
        onOk() {
          handleSave({ key: editor?.key, needClose: true })
        },
        onCancel() {
          const index = openEditors.indexOf(editor)
          if (index !== -1) {
            openEditors.splice(index, 1)
          }
        }
      })
    }
  } else {
    const index = editor ? openEditors.indexOf(editor) : -1
    if (index !== -1) {
      openEditors.splice(index, 1)
    }
  }
}

const handleSave = async (data: { key: string; needClose?: boolean }) => {
  const { key, needClose } = data
  let errMsg = ''
  const editor = openEditors.find((editor) => editor?.key === key)
  if (editor?.fileChange) {
    const newContent = editor.vimText.replace(/\r\n/g, '\n')
    let cmd = `cat <<'EOFChaterm:save' > ${editor.filePath}\n${newContent}\nEOFChaterm:save\n`
    if (connectionHasSudo.value) {
      cmd = `cat <<'EOFChaterm:save' | sudo tee  ${editor.filePath} > /dev/null \n${newContent}\nEOFChaterm:save\n`
    }
    const { stderr } = await api.sshConnExec({
      cmd: cmd,
      id: connectionId.value
    })
    errMsg = stderr
  }
  if (errMsg !== '') {
    message.error(`${t('common.saveFailed')}: ${errMsg}`)
  } else {
    message.success(t('common.saveSuccess'))
    if (editor) {
      if (needClose) {
        const index = openEditors.indexOf(editor)
        if (index !== -1) {
          openEditors.splice(index, 1)
        }
      } else {
        editor.loading = false
        editor.saved = true
        editor.fileChange = false
      }
    }
  }
}

const createEditor = async (filePath: string, contentType: string) => {
  const { stdout, stderr } = await api.sshConnExec({
    cmd: `cat ${filePath}`,
    id: connectionId.value
  })
  let action = 'editor'
  if (stderr.indexOf('No such file or directory') !== -1) {
    action = 'create'
  }
  if (stderr.indexOf('Permission denied') !== -1) {
    message.error('Permission denied')
  } else {
    const existingEditor = openEditors.find((editor) => editor.filePath === filePath)
    if (!existingEditor) {
      openEditors.push({
        filePath: filePath,
        visible: true,
        vimText: stdout,
        originVimText: stdout,
        action: action,
        vimEditorX: Math.round(window.innerWidth * 0.5) - Math.round(window.innerWidth * 0.7 * 0.5),
        vimEditorY: Math.round(window.innerHeight * 0.5) - Math.round(window.innerHeight * 0.7 * 0.5),
        contentType: contentType,
        vimEditorHeight: Math.round(window.innerHeight * 0.7),
        vimEditorWidth: Math.round(window.innerWidth * 0.7),
        loading: false,
        fileChange: false,
        saved: false,
        key: connectionId.value + '-' + filePath,
        terminalId: connectionId.value
      } as editorData)
    } else {
      existingEditor.visible = true
      existingEditor.vimText = stdout
    }
  }
}

const debounce = (func: (...args: unknown[]) => void, wait: number, immediate = false) => {
  let timeout: NodeJS.Timeout | null = null
  let isFirstCall = true
  let isDragging = false
  let lastCallTime = 0

  return function executedFunction(...args: unknown[]) {
    const now = Date.now()
    const timeSinceLastCall = now - lastCallTime
    lastCallTime = now
    isDragging = timeSinceLastCall < 50
    const later = () => {
      if (timeout) clearTimeout(timeout)
      timeout = null
      if (!immediate) func(...args)
      isDragging = false
    }
    const callNow = immediate && !timeout
    if (timeout) clearTimeout(timeout)
    let dynamicWait: number
    if (isDragging) {
      dynamicWait = 5
    } else if (isFirstCall) {
      dynamicWait = 0
    } else {
      dynamicWait = wait
    }

    timeout = setTimeout(later, dynamicWait)

    if (callNow) {
      func(...args)
      isFirstCall = false
    }
  }
}
const autoExecuteCode = (command: string) => {
  if (props.activeTabId !== props.currentConnectionId) return
  sendData(command)
}
const handleResize = debounce(() => {
  if (fitAddon.value && terminal.value && terminalElement.value) {
    try {
      const rect = terminalElement.value.getBoundingClientRect()
      if (rect.width > 0 && rect.height > 0) {
        fitAddon.value.fit()
        const { cols, rows } = terminal.value
        if (isLocalConnect.value) {
          resizeLocalSSH(cols, rows)
        } else {
          resizeSSH(cols, rows)
        }
      }
    } catch (error) {
      console.error('Failed to resize terminal:', error)
    }
  }
}, 100)

const emit = defineEmits(['connectSSH', 'disconnectSSH', 'closeTabInTerm', 'createNewTerm'])

const connectSSH = async () => {
  if (termOndata) {
    termOndata.dispose()
    termOndata = null
  }

  if (terminal.value) {
    const textarea = terminal.value.element?.querySelector('.xterm-helper-textarea') as HTMLTextAreaElement | null
    if (textarea) {
      if (textareaCompositionListener) {
        textarea.removeEventListener('compositionend', textareaCompositionListener)
        textareaCompositionListener = null
      }
      if (textareaPasteListener) {
        textarea.removeEventListener('paste', textareaPasteListener)
        textareaPasteListener = null
      }
    }
  }

  try {
    const assetInfo = await api.connectAssetInfo({ uuid: props.connectData.uuid })
    const password = ref('')
    const privateKey = ref('')
    const passphrase = ref('')
    if (assetInfo) {
      password.value = assetInfo.auth_type === 'password' ? assetInfo.password : ''
      privateKey.value = assetInfo.auth_type === 'keyBased' ? assetInfo.privateKey || '' : ''
      passphrase.value = assetInfo.auth_type === 'keyBased' ? assetInfo.passphrase || '' : ''
    } else {
      password.value = props.connectData.authType === 'password' ? props.connectData.password : ''
      privateKey.value = props.connectData.authType === 'privateKey' ? props.connectData.privateKey : ''
      passphrase.value = props.connectData.passphrase || ''
    }

    const userStore = userInfoStore()
    const email = userStore.userInfo.email || '<EMAIL>'
    console.log('[SSH Debug] 使用用户邮箱:', email)

    const orgType = props.serverInfo.organizationId === 'personal' ? 'local' : 'local-team'
    const hostnameBase64 =
      props.serverInfo.organizationId === 'personal' ? Base64Util.encode(assetInfo.asset_ip || '') : Base64Util.encode(assetInfo.hostname || '')
    connectionId.value = `${assetInfo.username}@${props.connectData.ip}:${orgType}:${hostnameBase64}:${uuidv4()}`

    if (assetInfo.sshType === 'jumpserver' && terminal.value) {
      jumpServerStatusHandler = createJumpServerStatusHandler(terminal.value, connectionId.value)
      jumpServerStatusHandler.setupStatusListener(api)
    }

    const connData: ConnData = {
      id: connectionId.value,
      host: assetInfo.asset_ip,
      port: assetInfo.port,
      username: assetInfo.username,
      password: assetInfo.password,
      privateKey: privateKey.value,
      passphrase: passphrase.value,
      targetIp: assetInfo.host,
      sshType: assetInfo.sshType,
      terminalType: (config.terminalType as string) || 'xterm-256color',
      agentForward: config.sshAgentsStatus === 1,
      isOfficeDevice: isOfficeDevice.value
    }
    connData.needProxy = assetInfo.need_proxy === 1 || false
    if (connData.needProxy) {
      connData.proxyConfig = (config.sshProxyConfigs as ProxyConfig[])?.find((item: ProxyConfig) => item.name === assetInfo.proxy_name)
    }

    const result = await api.connect(connData)

    if (jumpServerStatusHandler) {
      jumpServerStatusHandler.cleanup()
      jumpServerStatusHandler = null
    }

    api
      .connectReadyData(connectionId.value)
      .then((connectReadyData) => {
        connectionHasSudo.value = connectReadyData?.hasSudo || false
        getCmdList(connectReadyData?.commandList || [])
      })
      .catch(() => {
        connectionHasSudo.value = false
        getCmdList([])
      })

    if (result.status === 'connected') {
      const welcomeName = email.split('@')[0]
      const welcome = '\x1b[38;2;22;119;255m' + t('ssh.welcomeMessage', { username: welcomeName }) + ' \x1b[m\r\n'
      terminal.value?.writeln('') // 添加空行分隔
      terminal.value?.writeln(welcome)
      terminal.value?.writeln(t('ssh.connectingTo', { ip: props.connectData.ip }))
      await startShell()
      // setupTerminalInput() 现在在 startShell() 内部调用
      handleResize()
      setTimeout(() => {
        handleResize()
        // Ensure scroll to bottom after successful connection
        terminal.value?.scrollToBottom()
        terminal.value?.focus()
      }, 200)
    } else {
      const errorMsg = formatStatusMessage(t('ssh.connectionFailed', { message: result.message }), 'error')
      terminal.value?.writeln(errorMsg)
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (jumpServerStatusHandler) {
      jumpServerStatusHandler.cleanup()
      jumpServerStatusHandler = null
    }

    const errorMsg = formatStatusMessage(t('ssh.connectionError', { message: errorMessage || t('ssh.unknownError') }), 'error')
    terminal.value?.writeln(errorMsg)
  }
  emit('connectSSH', { isConnected: isConnected })
}

const startShell = async () => {
  try {
    const result = await api.shell({ id: connectionId.value, terminalType: config.terminalType as string })
    if (result.status === 'success') {
      isConnected.value = true

      // 立即设置输入处理器，不要延迟
      console.log('[SSH] Setting up terminal input immediately after shell start')
      setupTerminalInput()

      // 设置数据监听器
      const removeDataListener = api.onShellData(connectionId.value, (response: MarkedResponse) => {
        // 将数据转换为字符串用于调试
        let debugDataStr = ''
        if (typeof response.data === 'string') {
          debugDataStr = response.data
        } else if (response.data instanceof ArrayBuffer) {
          debugDataStr = new TextDecoder().decode(response.data)
        } else if (response.data instanceof Uint8Array) {
          debugDataStr = new TextDecoder().decode(response.data)
        } else if (Array.isArray(response.data)) {
          debugDataStr = new TextDecoder().decode(new Uint8Array(response.data))
        }

        console.log('[SSH Debug] Received shell data:', {
          hasData: !!response.data,
          dataLength: debugDataStr.length,
          marker: response.marker,
          dataPreview: debugDataStr.substring(0, 100)
        })
        checkEditorMode(response)
        handleServerOutput(response)
      })
      const removeErrorListener = api.onShellError(connectionId.value, (data) => {
        console.log('[SSH Debug] Received shell error:', data)
        cusWrite?.(data)
      })
      const removeCloseListener = api.onShellClose(connectionId.value, () => {
        isConnected.value = false
        cusWrite?.('\r\n' + t('ssh.connectionClosed') + '\r\n\r\n')
        cusWrite?.(t('ssh.disconnectedFromHost', { host: props.serverInfo.title, date: new Date().toDateString() }) + '\r\n')
        cusWrite?.('\r\n' + t('ssh.pressEnterToReconnect') + '\r\n', { isUserCall: true })
      })

      cleanupListeners.value.push(removeDataListener, removeErrorListener, removeCloseListener)

      // 确保终端获得焦点并准备接收输入
      setTimeout(() => {
        console.log('[SSH] Ensuring terminal focus and readiness')
        terminal.value?.focus()

        // 验证输入处理器是否正确设置
        const textarea = terminal.value?.element?.querySelector('.xterm-helper-textarea') as HTMLTextAreaElement
        console.log('[SSH] Terminal input verification:', {
          hasOnDataHandler: !!termOndata,
          hasTextarea: !!textarea,
          isConnected: isConnected.value,
          terminalFocused: document.activeElement === textarea
        })

        // 如果输入处理器没有正确设置，重新设置
        if (!termOndata) {
          console.log('[SSH] Re-setting up terminal input as it was not properly initialized')
          setupTerminalInput()
        }
      }, 300)

      // contextFetcher = createContextFetcher({
      //   connectionId: connectionId.value,
      //   api: api,
      //   enableOutput: false,
      //   outputCallback: (message) => {
      //     console.log('[ContextFetcher] Context information:', message)
      //   }
      // })
    } else {
      terminal.value?.writeln(
        JSON.stringify({
          cmd: t('ssh.shellStartFailed', { message: result.message }),
          isUserCall: true
        })
      )
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    terminal.value?.writeln(
      JSON.stringify({
        cmd: t('ssh.shellError', { message: errorMessage || t('ssh.unknownError') }),
        isUserCall: true
      })
    )
  }
  emit('connectSSH', { isConnected: isConnected })
}

const resizeSSH = async (cols: number, rows: number) => {
  try {
    const result = await api.resizeShell(connectionId.value, cols, rows)
    if (result.status === 'error') {
      console.error('Resize failed:', result.message)
    } else {
      // console.log('terminal resized:', result.message)
    }
  } catch (error) {
    console.error('Failed to resize terminal:', error)
  }
}

const connectLocalSSH = async () => {
  if (termOndata) {
    termOndata.dispose()
    termOndata = null
  }

  if (terminal.value) {
    const textarea = terminal.value.element?.querySelector('.xterm-helper-textarea') as HTMLTextAreaElement | null
    if (textarea) {
      if (textareaCompositionListener) {
        textarea.removeEventListener('compositionend', textareaCompositionListener)
        textareaCompositionListener = null
      }
      if (textareaPasteListener) {
        textarea.removeEventListener('paste', textareaPasteListener)
        textareaPasteListener = null
      }
    }
  }

  connectionId.value = `localhost@127.0.0.1:local:${Base64Util.encode(props.serverInfo.data.key)}:${uuidv4()}`

  try {
    const userStore = userInfoStore()
    const email = userStore.userInfo.email || '<EMAIL>'
    console.log('[SSH Debug] 本地连接使用用户邮箱:', email)

    const localConfig = {
      id: connectionId.value,
      shell: props.serverInfo.data.uuid,
      termType: config.terminalType as string
    }

    const result = await api.connectLocal(localConfig)
    if (result.success) {
      const welcomeName = email.split('@')[0]
      const welcome = '\x1b[38;2;22;119;255m' + t('ssh.welcomeMessage', { username: welcomeName }) + ' \x1b[m\r\n'
      terminal.value?.writeln('') // 添加空行分隔
      terminal.value?.writeln(welcome)

      await startLocalShell()

      // 设置输入处理器（使用统一的setupTerminalInput）
      setupTerminalInput()

      handleResize()
      setTimeout(() => {
        handleResize()
      }, 200)
    } else {
      const errorMsg = formatStatusMessage(`连接失败: ${result.message}`, 'error')
      terminal.value?.writeln(errorMsg)
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const errorMsg = formatStatusMessage(`连接错误: ${errorMessage || '未知错误'}`, 'error')
    terminal.value?.writeln(errorMsg)
  }
  emit('connectSSH', { isConnected: isConnected })
}

const startLocalShell = async () => {
  isConnected.value = true
  const removeDataListener = api.onDataLocal(connectionId.value, (data: string) => {
    if (terminal.value) {
      terminal.value.write(data)
    }
  })
  const removeErrorListener = api.onErrorLocal?.(connectionId.value, (error: { message?: string } | string) => {
    if (terminal.value) {
      const errorMessage = typeof error === 'string' ? error : error.message || 'Unknown error'
      terminal.value.write(`\r\n[错误]: ${errorMessage}\r\n`)
    }
  })

  const removeCloseListener = api.onExitLocal(connectionId.value, (exitCode: { code?: number }) => {
    isConnected.value = false
    if (terminal.value) {
      terminal.value.write('\r\nConnection closed.\r\n\r\n')
      terminal.value.write(`Disconnected from local shell at ${new Date().toDateString()}\r\n`)
      terminal.value.write(`Exit code: ${exitCode?.code || 'unknown'}\r\n`)
    }
  })

  if (removeErrorListener) {
    cleanupListeners.value.push(removeDataListener, removeErrorListener, removeCloseListener)
  } else {
    cleanupListeners.value.push(removeDataListener, removeCloseListener)
  }
}

const resizeLocalSSH = async (cols: number, rows: number) => {
  try {
    const result = await api.resizeLocal(connectionId.value, cols, rows)
    if (result.status === 'error') {
      console.error('Resize failed:', result.message)
    } else {
    }
  } catch (error) {
    console.error('Failed to resize terminal:', error)
  }
}
const terminalState = ref({
  content: '',
  cursorPosition: {
    row: 0,
    col: 0
  },
  beforeCursor: '',
  contentCrossRowStatus: false,
  contentCrossRowLines: 0,
  contentCrossStartLine: 0,
  contentCurrentCursorCrossRowLines: 0
})

const substrWidth = (str: string, startWidth: number, endWidth?: number): string => {
  let currentWidth = 0
  let startIndex = 0
  let endIndex = str.length
  for (let i = 0; i < str.length; i++) {
    const code = str.codePointAt(i) || 0
    const charWidth =
      (code >= 0x3000 && code <= 0x9fff) ||
      (code >= 0xac00 && code <= 0xd7af) ||
      (code >= 0xf900 && code <= 0xfaff) ||
      (code >= 0xff00 && code <= 0xffef) ||
      (code >= 0x20000 && code <= 0x2fa1f)
        ? 2
        : 1

    if (currentWidth < startWidth) {
      currentWidth += charWidth
      if (currentWidth > startWidth) {
        startIndex = i + 1
        break
      } else if (currentWidth === startWidth) {
        startIndex = i + 1
        break
      }
    } else {
      startIndex = i
      break
    }
    if (code > 0xffff) {
      i++
    }
  }
  if (endWidth === undefined) {
    return str.substring(startIndex)
  }

  currentWidth = 0
  for (let i = 0; i < str.length; i++) {
    const code = str.codePointAt(i) || 0
    const charWidth =
      (code >= 0x3000 && code <= 0x9fff) ||
      (code >= 0xac00 && code <= 0xd7af) ||
      (code >= 0xf900 && code <= 0xfaff) ||
      (code >= 0xff00 && code <= 0xffef) ||
      (code >= 0x20000 && code <= 0x2fa1f)
        ? 2
        : 1

    currentWidth += charWidth

    if (currentWidth > endWidth) {
      endIndex = i
      break
    }
    if (code > 0xffff) {
      i++
    }
  }

  return str.substring(startIndex, endIndex)
}

const cursorLastY = ref(0)
const cursorLastX = ref(0)
const cursorEndY = ref(0)
const cursorMaxY = ref(0)
const cursorMaxX = ref(0)
let updateTimeout: NodeJS.Timeout | null = null
const getLogicalInputStartLine = () => {
  const bufferService = (terminal as unknown as { value: { _core: { _bufferService: { buffer: TerminalBuffer } } } }).value._core._bufferService
  const buffer = bufferService.buffer
  let y = (terminal.value?.buffer.active.baseY || 0) + buffer.y
  while (y > 0 && buffer.lines.get(y)?.isWrapped) {
    y--
  }
  return y
}
const getWrappedContentLastLineY = () => {
  const bufferService = (terminal as unknown as { value: { _core: { _bufferService: { buffer: TerminalBuffer } } } }).value._core._bufferService
  const buffer = bufferService.buffer
  let lastY = (terminal.value?.buffer.active.baseY || 0) + buffer.y
  const maxLineIndex = buffer.lines.length - 1
  while (lastY < maxLineIndex) {
    const nextLine = buffer.lines.get(lastY + 1)
    if (!nextLine || !nextLine.isWrapped) {
      break
    }
    lastY++
  }
  return lastY
}

const updateTerminalState = (quickInit: boolean, enterPress: boolean, tagPress: boolean) => {
  if (!terminal.value) return

  try {
    const terminalCore = (terminal as unknown as { value: { _core: { _bufferService: { buffer: TerminalBuffer }; cols: number; rows: number } } })
      .value._core
    const buffer = terminalCore._bufferService.buffer
    const { x: cursorX, y: cursorY } = buffer
    const { cols: maxCols, rows: maxRows } = terminalCore
    const maxX = maxCols - 1
    const maxY = maxRows - 1
    let contentCursorX = cursorX
    let parseStrTag = true
    const isResizeTriggered = shouldSkipParseOnResize(maxX, maxY)
    if (isResizeTriggered) {
      parseStrTag = false
    }
    const currentCursorEndY = getWrappedContentLastLineY() - terminal.value?.buffer.active.baseY
    const refreshCrossRow = shouldRefreshCrossRow(currentCursorEndY, cursorX)
    cursorEndY.value = currentCursorEndY
    const currentLine = buffer.lines.get(terminal.value?.buffer.active.baseY + cursorY)
    let isCrossRow = determineCrossRowStatus(currentLine, cursorY, currentCursorEndY)
    if (!tagPress) {
      updateCursorStartPosition(cursorX, quickInit)
    }
    if (enterPress) {
      isCrossRow = false
    }
    const { lineContent, finalContentCursorX } = processLineContent(
      currentLine,
      isCrossRow,
      refreshCrossRow,
      parseStrTag,
      cursorX,
      cursorY,
      buffer,
      contentCursorX
    )
    updateCursorHistory(cursorX, cursorY, maxX, maxY)
    if (parseStrTag) {
      if (!tagPress) {
        updateContentStrings(lineContent, cursorX)
      }
      updateTerminalContent(lineContent, finalContentCursorX)
    }
    updateTerminalStateObject(cursorX, cursorY, isCrossRow)
    sendTerminalStateToServer()

    // Ensure terminal scrolls to bottom, keeping cursor in visible area
    if (!userInputFlag.value) {
      nextTick(() => {
        terminal.value?.scrollToBottom()
      })
    }
  } catch (error) {
    console.error(t('common.updateTerminalStatusError'), error)
  }
}

const shouldSkipParseOnResize = (maxX: number, maxY: number): boolean => {
  return cursorMaxX.value !== 0 && cursorMaxY.value !== 0 && (cursorMaxX.value !== maxX || cursorMaxY.value !== maxY)
}

const shouldRefreshCrossRow = (currentCursorEndY: number, cursorX: number): boolean => {
  return currentCursorEndY < cursorEndY.value && currentCursorEndY !== 0 && cursorLastX.value === cursorX
}

interface TerminalLine {
  isWrapped?: boolean
}

const determineCrossRowStatus = (currentLine: TerminalLine, cursorY: number, currentCursorEndY: number): boolean => {
  if (currentLine.isWrapped) return true
  if (!currentLine.isWrapped && cursorY !== currentCursorEndY) return true
  if (terminalState.value.contentCrossRowStatus && cursorY === currentCursorEndY) return true
  return false
}

const updateCursorStartPosition = (cursorX: number, quickInit: boolean): void => {
  if (cursorStartX.value === 0 || quickInit) {
    cursorStartX.value = cursorX
  } else {
    cursorStartX.value = Math.min(cursorStartX.value, cursorX)
  }
}

interface TerminalLineWithTranslate extends TerminalLine {
  translateToString: (trim: boolean) => string
}

interface TerminalBuffer {
  lines: {
    get: (index: number) => TerminalLineWithTranslate
    length: number
  }
  y: number
  x: number
}

const processLineContent = (
  currentLine: TerminalLineWithTranslate,
  isCrossRow: boolean,
  refreshCrossRow: boolean,
  parseStrTag: boolean,
  cursorX: number,
  cursorY: number,
  buffer: TerminalBuffer,
  contentCursorX: number
) => {
  let lineContent = currentLine.translateToString(true)
  let finalContentCursorX = contentCursorX

  if (isCrossRow) {
    const crossRowData = processCrossRowContent(parseStrTag, refreshCrossRow, cursorX, cursorY, buffer)
    lineContent = crossRowData.fullContent
    finalContentCursorX = crossRowData.totalCharacterPosition
    terminalState.value.contentCrossRowLines = crossRowData.crossRowLines
    terminalState.value.contentCrossStartLine = crossRowData.crossStartLine
    terminalState.value.contentCurrentCursorCrossRowLines = crossRowData.currentCursorCrossRowLines
    cursorStartX.value = startStr.value.length
  }

  return { lineContent, finalContentCursorX }
}

const processCrossRowContent = (parseStrTag: boolean, refreshCrossRow: boolean, cursorX: number, cursorY: number, buffer: TerminalBuffer) => {
  const currentBufferLine = terminal.value?.buffer.active.baseY || 0
  let { contentCrossRowLines: crossRowLines, contentCrossStartLine: crossStartLine } = terminalState.value
  let { contentCurrentCursorCrossRowLines: currentCursorCrossRowLines } = terminalState.value
  if ((crossStartLine === 0 && crossRowLines === 0) || (!parseStrTag && cursorY !== cursorLastY.value)) {
    crossStartLine = getLogicalInputStartLine() - currentBufferLine
  }
  if (refreshCrossRow) {
    crossStartLine = cursorY - currentCursorCrossRowLines + 1
  }
  if (crossRowLines === 0 || cursorY > cursorLastY.value || (!parseStrTag && cursorY !== cursorLastY.value)) {
    crossRowLines = cursorEndY.value - crossStartLine + 1
  }
  currentCursorCrossRowLines = cursorY - crossStartLine + 1
  let totalCharacterPosition = 0
  let fullContent = ''
  for (let i = 0; i < currentCursorCrossRowLines; i++) {
    const lineIndex = currentBufferLine + crossStartLine + i
    const lineContent = buffer.lines.get(lineIndex).translateToString(true)
    if (i === currentCursorCrossRowLines - 1) {
      totalCharacterPosition += cursorX
    } else {
      totalCharacterPosition += lineContent.length
    }
  }
  for (let i = 0; i < crossRowLines; i++) {
    const lineIndex = currentBufferLine + crossStartLine + i
    const lineContent = buffer.lines.get(lineIndex).translateToString(true)
    fullContent += lineContent
  }
  return {
    fullContent,
    totalCharacterPosition,
    crossRowLines,
    crossStartLine,
    currentCursorCrossRowLines
  }
}

const updateCursorHistory = (cursorX: number, cursorY: number, maxX: number, maxY: number): void => {
  cursorLastY.value = cursorY
  cursorLastX.value = cursorX
  cursorMaxX.value = maxX
  cursorMaxY.value = maxY
}

const updateContentStrings = (lineContent: string, cursorX: number): void => {
  if (startStr.value !== '') {
    const newStartStr = lineContent.substring(0, cursorStartX.value)
    if (newStartStr !== startStr.value) {
      cursorStartX.value = cursorX
      startStr.value = lineContent.substring(0, cursorX)
    }
  } else {
    beginStr.value = lineContent.substring(0, cursorStartX.value)
  }
}

const updateTerminalContent = (lineContent: string, contentCursorX: number): void => {
  terminalState.value.content = substrWidth(lineContent, cursorStartX.value)
  terminalState.value.beforeCursor = substrWidth(lineContent, cursorStartX.value, contentCursorX)

  console.log('[SSH Debug] Terminal content updated:', {
    content: terminalState.value.content,
    beforeCursor: terminalState.value.beforeCursor,
    cursorStartX: cursorStartX.value,
    contentCursorX: contentCursorX
  })
}

const updateTerminalStateObject = (cursorX: number, cursorY: number, isCrossRow: boolean): void => {
  terminalState.value.cursorPosition = { col: cursorX, row: cursorY }
  terminalState.value.contentCrossRowStatus = isCrossRow
}

const sendTerminalStateToServer = async (): Promise<void> => {
  try {
    await api.recordTerminalState({
      id: connectionId.value,
      state: {
        cursorPosition: {
          row: terminalState.value.cursorPosition.row,
          col: terminalState.value.cursorPosition.col
        },
        beforeCursor: terminalState.value.beforeCursor,
        content: terminalState.value.content
      }
    })
  } catch (err) {
    console.error(t('common.sendTerminalStatusError'), err)
  }
}
function handleExternalInput(data: string) {
  handleInput && handleInput(data, false)
}

const setupTerminalInput = () => {
  if (!terminal.value) {
    console.error('[SSH] Cannot setup terminal input: terminal not available')
    return
  }

  console.log('[SSH] Setting up terminal input handlers')

  // 清理现有的输入处理器
  if (termOndata) {
    termOndata.dispose()
    termOndata = null
  }

  handleInput = async (data: string, isInputManagerCall?: boolean) => {
    console.log('[SSH Debug] handleInput called:', {
      data: data,
      dataLength: data?.length,
      isInputManagerCall,
      isConnected: isConnected.value,
      connectionId: connectionId.value,
      queryCommandFlag: queryCommandFlag.value
    })

    // 如果未连接，只处理回车键用于重连
    if (!isConnected.value) {
      if (data === '\r') {
        console.log('[SSH] Enter pressed while disconnected, attempting reconnect')
        cusWrite?.('\r\n' + t('ssh.reconnecting') + '\r\n', { isUserCall: true })
        connectSSH()
      } else {
        console.warn('[SSH] Input ignored: not connected (data:', data, ')')
      }
      return
    }

    if (isInputManagerCall && isSyncInput.value) {
      inputManager.sendToOthers(connectionId.value, data)
    }
    if (startStr.value == '') {
      startStr.value = beginStr.value
    }
    // Intercept Delete/Backspace when suggestions are visible: clear and forward once to actually delete char
    if (isDeleteKeyData(data) && suggestions.value.length > 0) {
      suggestions.value = []
      activeSuggestion.value = -1
      suggestionSelectionMode.value = false
      // Avoid immediate re-query; user will continue typing
      selectFlag.value = true
      // Forward the delete/backspace to remote to actually remove character
      sendData(data)
      return
    }
    if (data === '\t') {
      const cmd = JSON.parse(JSON.stringify(terminalState.value.content))
      selectFlag.value = true
      suggestions.value = []
      activeSuggestion.value = -1
      suggestionSelectionMode.value = false
      setTimeout(() => {
        queryCommand(cmd)
      }, 100)
    }
    if (data === '\x03') {
      if (suggestions.value.length) {
        suggestions.value = []
        activeSuggestion.value = -1
        suggestionSelectionMode.value = false
        nextTick(() => {})
      }
      selectFlag.value = true
      sendData(data)
    } else if (data === '\x0c') {
      if (suggestions.value.length) {
        suggestions.value = []
        activeSuggestion.value = -1
        suggestionSelectionMode.value = false
        nextTick(() => {})
      }
      selectFlag.value = true
      sendData(data)
    } else if (data === '\x1b') {
      if (contextmenu.value && typeof contextmenu.value.hide === 'function') {
        contextmenu.value.hide()
      }
      if (suggestions.value.length) {
        suggestions.value = []
        activeSuggestion.value = -1
        suggestionSelectionMode.value = false
        nextTick(() => {})
        return
      } else {
        sendData(data)
      }
    } else if (data === '\x16') {
      // Check if we're in vim mode (alternate mode)
      if (terminalMode.value === 'alternate') {
        // In vim mode, pass Ctrl+V to remote terminal for visual block mode
        sendData(data)
      } else {
        // In normal mode, perform paste operation
        navigator.clipboard
          .readText()
          .then((text) => {
            sendData(text)
            // Ensure scroll to bottom after paste
            nextTick(() => {
              terminal.value?.scrollToBottom()
              terminal.value?.focus()
            })
          })
          .catch(() => {})
      }
    } else if (data == '\r' || data === '\n') {
      // 设置回车键标志
      enterPress.value = true
      // 规范化换行：部分环境可能传入"\n"，统一转换为"\r"
      if (data === '\n') {
        data = '\r'
      }

      console.log('[SSH Debug] Enter key pressed:', {
        data: data,
        dataCharCode: data.charCodeAt(0),
        isConnected: isConnected.value,
        command: terminalState.value.content,
        suggestionsLength: suggestions.value.length,
        activeSuggestion: activeSuggestion.value
      })

      if (!isConnected.value) {
        cusWrite?.('\r\n' + t('ssh.reconnecting') + '\r\n', { isUserCall: true })
        connectSSH()
        enterPress.value = false
        return
      }

      const command = terminalState.value.content

      // 安全检查
      if (command && command.trim()) {
        const securityResult = securityChecker.checkCommand(command.trim(), {
          user: props.connectData.username,
          host: props.connectData.ip
        })

        if (!securityResult.isSecure) {
          console.warn('[Security] 检测到潜在风险命令:', command)
          console.warn('[Security] 风险详情:', securityResult.risks)

          // 对于严重风险，显示警告
          if (securityResult.severity === 'critical' || securityResult.severity === 'high') {
            const shouldContinue = confirm(
              `⚠️ 安全警告：检测到高风险命令\n\n` +
                `命令: ${command}\n` +
                `风险: ${securityResult.risks.map((r: { rule: { description: string } }) => r.rule.description).join(', ')}\n\n` +
                `建议: ${securityResult.suggestions.join('; ')}\n\n` +
                `是否继续执行？`
            )

            if (!shouldContinue) {
              console.log('[Security] 用户取消了高风险命令执行')
              return
            }
          }
        }
      }

      // 回车键：不再“选择建议”，而是执行当前输入（兼容用户预期：右键选择，回车执行）
      if (suggestions.value.length) {
        if (suggestionSelectionMode.value && activeSuggestion.value >= 0) {
          // 选择当前高亮的建议
          console.log('[SSH Debug] 回车键选择建议:', suggestions.value[activeSuggestion.value])
          selectSuggestion(suggestions.value[activeSuggestion.value])
          return // 阻止继续执行，因为已经选择了建议
        } else if (suggestions.value.length > 0) {
          // 如果不在选择模式但有建议，选择第一个建议
          console.log('[SSH Debug] 回车键选择第一个建议:', suggestions.value[0])
          selectSuggestion(suggestions.value[0])
          return // 阻止继续执行，因为已经选择了建议
        } else {
          // 清理建议状态，继续执行当前命令
          suggestions.value = []
          activeSuggestion.value = -1
          suggestionSelectionMode.value = false
        }
      }
      {
        const delData = String.fromCharCode(127)
        const newCommand = (configStore as { getCommand?: (cmd: string) => string | null }).getCommand?.(command)
        if (dbConfigStash.aliasStatus === 1 && newCommand !== null) {
          sendData(delData.repeat(command.length) + newCommand + '\r')
        } else if (config.quickVimStatus === 1 && !isSyncInput.value) {
          connectionSftpAvailable.value = await api.checkSftpConnAvailable(connectionId.value)
          const vimMatch = command.match(/^\s*vim\s+(.+)$/i)
          if (vimMatch && connectionSftpAvailable.value) {
            let vimData = data
            if (vimMatch[1].startsWith('/')) {
              vimData = delData.repeat(command.length) + 'echo "' + vimMatch[1] + '"  #Chaterm:vim  \r'
            } else {
              vimData = delData.repeat(command.length) + 'echo "$(pwd)/' + vimMatch[1] + '"  #Chaterm:vim  \r'
            }
            sendMarkedData(vimData, 'Chaterm:vim')
          } else {
            sendData(data)
          }
        } else {
          console.log('[SSH Debug] Sending enter key data:', {
            data: data,
            dataLength: data.length,
            dataCharCode: data.charCodeAt(0),
            isEnterKey: data === '\r'
          })
          sendData(data)
        }
      }
      if (command && command.trim()) {
        insertCommand(command)

        // 检测vim命令，设置vim模式
        const trimmedCommand = command.trim()
        if (trimmedCommand.startsWith('vim ') || trimmedCommand === 'vim') {
          // 延迟设置vim模式，等待vim启动
          setTimeout(() => {
            terminalMode.value = 'alternate'
          }, 500)
        }
      }
      suggestions.value = []
      activeSuggestion.value = -1
      suggestionSelectionMode.value = false

      // 重置回车键标志
      enterPress.value = false

      // Ensure scroll to bottom and maintain focus
      nextTick(() => {
        terminal.value?.scrollToBottom()
        terminal.value?.focus()
      })
    } else if (JSON.stringify(data) === '"\\u001b[A"' && terminalMode.value === 'none') {
      if (suggestions.value.length && suggestionSelectionMode.value) {
        if (data == '\u001b[A') {
          // 上方向键：循环向上导航 - only when in selection mode
          if (activeSuggestion.value <= 0) {
            activeSuggestion.value = suggestions.value.length - 1
          } else {
            activeSuggestion.value -= 1
          }
        }
      } else {
        sendMarkedData(data, 'Chaterm:[A')
      }
    } else if (JSON.stringify(data) === '"\\u001b[B"' && terminalMode.value === 'none') {
      if (suggestions.value.length && suggestionSelectionMode.value) {
        if (data == '\u001b[B') {
          // 下方向键：循环向下导航 - only when in selection mode
          if (activeSuggestion.value >= suggestions.value.length - 1) {
            activeSuggestion.value = 0
          } else {
            activeSuggestion.value += 1
          }
        }
      } else {
        sendMarkedData(data, 'Chaterm:[B')
      }
    } else if (data == '\u001b[C') {
      // Right arrow key - enter selection mode or select suggestion
      if (suggestions.value.length) {
        if (!suggestionSelectionMode.value) {
          // Enter selection mode and select first item
          suggestionSelectionMode.value = true
          activeSuggestion.value = 0
          // Prevent immediate query refresh which would reset selection mode
          selectFlag.value = true
        } else {
          // Already in selection mode, select current suggestion
          selectSuggestion(suggestions.value[activeSuggestion.value])
          selectFlag.value = true
        }
      } else {
        sendData(data)
      }
    } else if (data == '\u001b[D') {
      // Left arrow key - exit selection mode or pass through
      if (suggestions.value.length && suggestionSelectionMode.value) {
        suggestionSelectionMode.value = false
      } else {
        sendData(data)
      }
    } else {
      sendData(data)
      selectFlag.value = false

      // 检查是否是可打印字符（触发智能补全的条件）
      const isPrintableChar = data.length === 1 && data.charCodeAt(0) >= 32 && data.charCodeAt(0) <= 126

      if (isPrintableChar && queryCommandFlag.value) {
        console.log('[SSH Debug] 检测到可打印字符，准备触发智能补全查询，输入数据:', data)

        // 延迟调用智能补全，确保终端状态已更新
        setTimeout(() => {
          if (!selectFlag.value && !suggestionSelectionMode.value) {
            console.log('[SSH Debug] 触发智能补全查询')
            // 直接从终端获取当前行内容
            tryGetCurrentLineAndQuery()
          } else {
            console.log('[SSH Debug] 跳过智能补全查询，selectFlag:', selectFlag.value, 'suggestionSelectionMode:', suggestionSelectionMode.value)
          }
        }, 50) // 减少延迟时间，提高响应速度
      } else {
        console.log('[SSH Debug] 不触发智能补全:', {
          isPrintableChar,
          queryCommandFlag: queryCommandFlag.value,
          data: data,
          charCode: data.charCodeAt(0)
        })
      }

      // 额外的强制触发机制：如果是字母或数字，强制触发一次
      if (isPrintableChar && queryCommandFlag.value && /[a-zA-Z0-9]/.test(data)) {
        setTimeout(() => {
          console.log('[SSH Debug] 强制触发智能补全（字母数字检测）')
          // 强制更新终端状态
          updateTerminalState(false, false, false)
          // 延迟触发查询
          setTimeout(() => {
            if (terminalState.value.content && terminalState.value.content.length >= 2) {
              console.log('[SSH Debug] 执行强制查询，内容:', terminalState.value.content)
              queryCommand()
            } else {
              // 如果终端状态还没更新，直接从终端获取
              console.log('[SSH Debug] 终端状态未更新，直接从终端获取内容')
              tryGetCurrentLineAndQuery()
            }
          }, 50)
        }, 150)
      }

      // 简化的直接触发机制
      if (isPrintableChar && queryCommandFlag.value) {
        setTimeout(() => {
          console.log('[SSH Debug] 简化触发机制：直接获取终端内容')
          tryGetCurrentLineAndQuery()
        }, 200)
      }
    }
  }

  // 设置终端数据处理器
  try {
    termOndata = terminal.value?.onData((data: string) => handleInput?.(data, true))
    console.log('[SSH] Terminal input handler set successfully:', {
      hasHandler: !!termOndata,
      connectionId: connectionId.value,
      isConnected: isConnected.value
    })

    // 确保终端获得焦点
    setTimeout(() => {
      terminal.value?.focus()
      console.log('[SSH] Terminal focused after input setup')
    }, 100)
  } catch (error) {
    console.error('[SSH] Failed to set terminal input handler:', error)
  }
}

const sendData = (data: string) => {
  // 统一规范：若收到 "\n"，转换为 "\r"，并在回车流程缺失时自动补齐
  if (data === '\n') {
    data = '\r'
  }
  if (enterPress.value && typeof data === 'string' && !data.endsWith('\r')) {
    data = `${data}\r`
  }
  // 修复回车键发送空字符串的问题
  if (data === '' && enterPress.value) {
    console.log('[SSH Debug] Detected empty data when enter was pressed, forcing carriage return')
    data = '\r'
  }

  // 额外检查：如果数据为空但不是明确的回车键，且当前有命令内容，可能是回车键丢失
  if (data === '' && terminalState.value.content && terminalState.value.content.trim()) {
    console.log('[SSH Debug] Empty data with command content, likely missing enter key')
    data = '\r'
  }

  console.log('[SSH Debug] sendData called:', {
    data: data,
    dataLength: data?.length,
    connectionId: connectionId.value,
    isConnected: isConnected.value,
    isLocalConnect: isLocalConnect.value,
    hasWriteToShell: !!api.writeToShell,
    hasSendDataLocal: !!api.sendDataLocal,
    enterPressFlag: enterPress.value,
    commandContent: terminalState.value.content
  })

  // 如果是命令执行（以回车结尾），记录到AI助手
  if (data.endsWith('\r') && data.length > 1) {
    const command = data.slice(0, -1) // 移除回车符
    lastExecutedCommand.value = command
    addToCommandHistory(command)
  }

  if (!isConnected.value) {
    console.warn('[SSH] Cannot send data: not connected')
    return
  }

  try {
    if (isLocalConnect.value) {
      // 本地SSH连接
      api.sendDataLocal(connectionId.value, data)
      console.log('[SSH Debug] Data sent successfully to local shell')
    } else {
      // 远程SSH连接
      api.writeToShell({
        id: connectionId.value,
        data: data,
        lineCommand: terminalState.value.content
      })
      console.log('[SSH Debug] Data sent successfully to remote shell')
    }
  } catch (error) {
    console.error('[SSH] Failed to send data to shell:', error)
  }
}
const sendMarkedData = (data: string, marker: string) => {
  console.log('[SSH Debug] sendMarkedData called:', {
    data: data,
    marker: marker,
    isLocalConnect: isLocalConnect.value
  })

  if (isLocalConnect.value) {
    // 本地SSH连接，直接发送数据（不支持marker）
    api.sendDataLocal(connectionId.value, data)
  } else {
    // 远程SSH连接，支持marker
    api.writeToShell({
      id: connectionId.value,
      data: data,
      marker: marker,
      lineCommand: terminalState.value.content
    })
  }
}
// MarkedResponse 接口已在上面定义

const matchPattern = (data: number[], pattern: number[]): boolean => {
  if (data.length < pattern.length) return false
  for (let i = data.length - pattern.length; i >= Math.max(0, data.length - 500); i--) {
    let match = true
    for (let j = 0; j < pattern.length; j++) {
      if (data[i + j] !== pattern[j]) {
        match = false
        break
      }
    }
    if (match) return true
  }
  return false
}

type TerminalMode = 'none' | 'alternate' | 'ui'
const terminalMode = ref<TerminalMode>('none')

// 监听terminalMode变化，通知preload更新vim模式状态
watch(
  terminalMode,
  (newMode) => {
    const isVimMode = newMode === 'alternate'
    window.postMessage(
      {
        type: 'VIM_MODE_UPDATE',
        isVimMode
      },
      '*'
    )
  },
  { immediate: true }
)

const checkFullScreenClear = (data: string) => {
  const isSimpleCtrlL = data.includes('\x1b[H\x1b[2J')
  if (isSimpleCtrlL) return false
  const clearScreenPatterns = [
    /\x1b\[H\x1b\[J/,
    /\x1b\[2J\x1b\[H/,
    /\x1b\[H.*?\x1b\[J/s,
    /\x1b\[J.*?\x1b\[H/s,
    /\x1b\[\d+;\d+H.*?\x1b\[J/s,
    /\x1b\[2J(?:\x1b\[H)?/
  ]
  return clearScreenPatterns.some((pattern) => pattern.test(data))
}

const checkHeavyUiStyle = (data: string) => {
  const moveCount = (data.match(/\x1b\[\d+;\d+H/g) || []).length
  const clearCount = (data.match(/\x1b\[\d*K/g) || []).length
  const hasTable = /NUM\s+NAME\s+IP:PORT/.test(data) || /=+/.test(data)

  return moveCount >= 5 && clearCount >= 5 && hasTable
}

const BUFFER_SIZE = 1024
const checkEditorMode = (response: MarkedResponse) => {
  if (response.marker === 'Chaterm:command') {
    return
  }
  let bytes: number[] = []

  if (response.data) {
    if (typeof response.data === 'string') bytes = Array.from(new TextEncoder().encode(response.data))
    else if (response.data instanceof ArrayBuffer) bytes = Array.from(new Uint8Array(response.data))
    else if (response.data instanceof Uint8Array) bytes = Array.from(response.data)
    else if (Array.isArray(response.data)) bytes = response.data
  }

  if (bytes.length === 0) return

  // 只保留最近 1024字节
  dataBuffer.value = bytes.length > BUFFER_SIZE ? bytes.slice(-BUFFER_SIZE) : bytes

  const text = new TextDecoder().decode(new Uint8Array(dataBuffer.value))

  if (terminalMode.value === 'none') {
    if (EDITOR_SEQUENCES.enter.some((seq) => matchPattern(dataBuffer.value, seq.pattern))) {
      terminalMode.value = 'alternate'
      nextTick(handleResize)
      return
    }

    // 更宽松的vim检测：检查是否包含常见的vim进入序列
    const text = new TextDecoder().decode(new Uint8Array(dataBuffer.value))
    if (
      text.includes('\x1b[?1049h') ||
      text.includes('\x1b[?47h') ||
      text.includes('\x1b[?1047h') ||
      text.includes('\x1b[?25h') ||
      text.includes('\x1b[?25l') ||
      text.includes('\x1b[?1h') ||
      text.includes('\x1b[?1l')
    ) {
      terminalMode.value = 'alternate'
      nextTick(handleResize)
      return
    }

    // 检测vim特有的序列组合
    if (text.includes('\x1b[?25h') && (text.includes('All') || text.includes('H'))) {
      terminalMode.value = 'alternate'
      nextTick(handleResize)
      return
    }
  }

  if (terminalMode.value === 'alternate') {
    // 只有在明确检测到shell提示符时才退出vim模式
    const text = new TextDecoder().decode(new Uint8Array(dataBuffer.value))
    if (text.includes('$ ') || text.includes('# ') || text.includes('~$')) {
      terminalMode.value = 'none'
      dataBuffer.value = []
      nextTick(handleResize)
      return
    }
  }

  if (terminalMode.value === 'none') {
    if (checkFullScreenClear(text) || checkHeavyUiStyle(text)) {
      terminalMode.value = 'ui'
      nextTick(handleResize)
      return
    }
  }

  if (terminalMode.value === 'ui') {
    let score = 0
    if (text.includes('\x1b[?2004h')) score += 2
    if (text.includes('\x1b[?1034h')) score += 2
    if (text.includes('\x1b]0;') && text.includes('\x07')) score += 1
    const commonJumps = ['\x1b[23;1H', '\x1b[39;1H', '\x1b[11;1H']
    const cursorGone = !commonJumps.some((seq) => text.includes(seq))
    if (cursorGone) score += 1
    if (score >= 3) {
      terminalMode.value = 'none'
      dataBuffer.value = []
      nextTick(handleResize)
      return
    }
  }
}

const handleServerOutput = (response: MarkedResponse) => {
  // 将数据转换为字符串
  let dataStr = ''
  if (typeof response.data === 'string') {
    dataStr = response.data
  } else if (response.data instanceof ArrayBuffer) {
    dataStr = new TextDecoder().decode(response.data)
  } else if (response.data instanceof Uint8Array) {
    dataStr = new TextDecoder().decode(response.data)
  } else if (Array.isArray(response.data)) {
    dataStr = new TextDecoder().decode(new Uint8Array(response.data))
  }

  console.log('[SSH Debug] handleServerOutput called:', {
    hasData: !!response.data,
    dataLength: dataStr.length,
    marker: response.marker,
    dataPreview: dataStr.substring(0, 50)
  })

  // 确保数据存在
  if (!response.data) {
    console.log('[SSH Debug] No data received, skipping processing')
    return
  }

  // 记录性能指标 - 使用临时实现
  // performanceMonitor.recordMetric('outputDataSize', dataStr.length)

  // 将数据添加到优化器（用于大量输出的性能优化） - 使用临时实现
  // if (dataStr) {
  //   const lines = dataStr.split('\n')
  //   lines.forEach((line) => {
  //     if (line.trim()) {
  //       terminalOptimizer.addLine(line)
  //     }
  //   })
  // }

  let data = dataStr

  // 数据清理：移除不必要的空字节和异常控制字符
  // 注意：这个清理逻辑只应用于接收到的数据，不应影响发送的数据
  if (typeof data === 'string') {
    const originalData = data

    // 第一步：移除所有空字节
    data = data.replace(/\x00/g, '')

    // 第二步：更谨慎地处理退格和清除序列，避免过度清理
    // 只清理明确的异常模式：退格+清除+多个空字节
    data = data.replace(/\x08\x1B\[K[\x00]{3,}/g, '')

    // 第三步：移除异常的退格+空字节组合（但保留单个退格）
    data = data.replace(/\x08[\x00]{2,}/g, '\x08')

    // 第四步：更精确地清理控制字符，保留重要的终端控制序列
    data = data.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F]+/g, (match) => {
      // 保留重要的控制字符：\t(09), \n(0A), \r(0D), ESC(1B)
      // 同时保留退格(08)用于终端编辑
      const preserved = match.replace(/[\x00-\x07\x0B\x0C\x0E-\x1A\x1C-\x1F]/g, '')
      return preserved
    })

    // 第五步：修复损坏的ANSI转义序列
    data = data.replace(/\x1B\[[\x00-\x1F]*([A-Za-z])/g, '\x1B[$1')

    // 第六步：清理过多的连续退格字符（保留合理数量）
    data = data.replace(/\x08{5,}/g, '\x08\x08\x08\x08')

    // 第七步：最终清理 - 移除任何剩余的空字节
    data = data.replace(/\x00/g, '')

    // 如果数据被大幅清理，记录详细信息
    if (originalData.length - data.length > 5) {
      console.log('[SSH Debug] Data cleaning performed:', {
        originalLength: originalData.length,
        cleanedLength: data.length,
        removedBytes: originalData.length - data.length,
        originalPreview: originalData.substring(0, 100).replace(/[\x00-\x1F]/g, (c) => `\\x${c.charCodeAt(0).toString(16).padStart(2, '0')}`),
        cleanedPreview: data.substring(0, 100)
      })
    }
  }

  // 特殊标记处理
  if (response.marker === 'Chaterm:vim') {
    const { lastLine: lastLine, filePath: filePath, contentType: contentType } = parseVimLine(data)
    createEditor(filePath, contentType)
    sendMarkedData('history -s "vim ' + filePath + '"' + '\r', 'Chaterm:history')
    data = lastLine
    cusWrite?.(data)
  } else if (response.marker === 'Chaterm:save' || response.marker === 'Chaterm:history' || response.marker === 'Chaterm:pass') {
    // 这些标记不需要额外处理，直接写入
    cusWrite?.(data)
  } else if (response.marker === 'Chaterm:[A') {
    if (data.indexOf('Chaterm:vim') !== -1) {
      cusWrite?.(data)
      sendData(String.fromCharCode(21))
      sendMarkedData(String.fromCharCode(27, 91, 65), 'Chaterm:[A')
    } else {
      cusWrite?.(data)
    }
  } else if (response.marker === 'Chaterm:[B') {
    if (data.indexOf('Chaterm:vim') !== -1) {
      cusWrite?.(data)
      sendData(String.fromCharCode(21))
      sendMarkedData(String.fromCharCode(27, 91, 64), 'Chaterm:[B')
    } else {
      cusWrite?.(data)
    }
  } else if (response.marker === 'Chaterm:pwd') {
    let currentCwd = ''
    const temp = stripAnsi(data)

    const lines = temp.trim().split(/\r?\n/)

    if (lines.length >= 2 && lines[0].trim() === 'pwd') {
      currentCwd = lines[1].trim()
    }

    currentCwdStore.setKeyValue(props.connectData.ip, currentCwd)

    eventBus.emit('cwdUpdatedForHost', props.connectData.ip)
    cusWrite?.(data)
  } else if (response.marker === 'Chaterm:command') {
    isCollectingOutput.value = true
    handleCommandOutput(data, true)
  } else if (isCollectingOutput.value) {
    handleCommandOutput(data, false)
  } else {
    // 普通shell输出 - 包括初始连接时的输出和bash提示符
    console.log('[SSH Debug] Processing normal shell output:', {
      dataLength: data.length,
      dataPreview: data.substring(0, 100),
      hasNewline: data.includes('\n'),
      hasCarriageReturn: data.includes('\r')
    })

    // 检查是否包含bash提示符模式（更宽松的匹配）
    const cleanData = stripAnsi(data)
    const hasBashPrompt =
      /[$#]\s*$/.test(cleanData) ||
      /\[.*@.*\][$#]\s*$/.test(cleanData) ||
      /.*:~\s*[$#]\s*$/.test(cleanData) ||
      /.*@.*:.*[$#]\s*$/.test(cleanData) ||
      /root@.*[$#]\s*$/.test(cleanData) ||
      /.*\$\s*$/.test(cleanData)

    if (hasBashPrompt) {
      console.log('[SSH Debug] Detected bash prompt in output:', cleanData.trim())
    }

    // 直接写入数据到终端 - 不管是否为空
    cusWrite?.(data)

    // 如果是bash提示符或者是初始连接输出，确保终端状态正确
    if (hasBashPrompt || data.includes('Welcome') || data.includes('Last login')) {
      nextTick(() => {
        // 确保终端滚动到底部
        terminal.value?.scrollToBottom()
        // 确保终端获得焦点
        setTimeout(() => {
          terminal.value?.focus()
          console.log('[SSH Debug] Terminal focused after prompt detection')
        }, 50)
      })
    }
  }

  // 记录命令输出到AI助手
  if (typeof response.data === 'string' && response.data.trim()) {
    lastCommandOutput.value = response.data
  }

  // 记录处理完成时间 - 临时注释
  // performanceMonitor.recordMetric('outputProcessingTime', performance.now() - startTime)
}

// Helper function to handle command output processing
const handleCommandOutput = (data: string, isInitialCommand: boolean) => {
  const cleanOutput = stripAnsi(data).trim()
  commandOutput.value += cleanOutput + '\n'

  const promptRegex = /(?:\[([^@]+)@([^\]]+)\][#$]|([^@]+)@([^:]+):(?:[^$]*|\s*~)\s*[$#]|\[([^@]+)@([^\]]+)\s+[^\]]*\][#$])\s*$/

  if (promptRegex.test(cleanOutput)) {
    isCollectingOutput.value = false
    const lines = commandOutput.value
      .replace(/\r\n|\r/g, '\n')
      .split('\n')
      .filter((line) => line.trim())

    const outputLines = lines.slice(1, -1)
    const finalOutput = outputLines.join('\n').trim()

    if (finalOutput) {
      nextTick(() => {
        const formattedOutput = `Terminal output:\n\`\`\`\n${finalOutput}\n\`\`\``
        eventBus.emit('chatToAi', formattedOutput)
        setTimeout(() => {
          eventBus.emit('triggerAiSend')
        }, 100)
      })
    } else {
      const output = configStore.getUserConfig.language == 'en-US' ? 'Command executed successfully, no output returned' : '执行完成，没有输出返回'
      // For initial command, use formatted output; for ongoing collection, use plain output
      const messageToSend = isInitialCommand ? `Terminal output:\n\`\`\`\n${output}\n\`\`\`` : output

      eventBus.emit('chatToAi', messageToSend)
      setTimeout(() => {
        eventBus.emit('triggerAiSend')
      }, 100)
    }

    commandOutput.value = ''
  }

  cusWrite?.(data)
}

const specialCode = ref(false)
const keyCode = ref(0)
const currentLine = ref('')
interface TerminalMarker {
  dispose: () => void
}

const activeMarkers = ref<TerminalMarker[]>([])
const commands = ref()
const cursorY = ref(0)
const cursorX = ref(0)
const enterPress = ref(false)
const tagPress = ref(false)
const beginStr = ref<string>('')
const startStr = ref<string>('')
const selectFlag = ref(false)

// Helper function to calculate display width position
const calculateDisplayPosition = (str: string, charIndex: number): number => {
  let displayPos = 0
  for (let i = 0; i < charIndex && i < str.length; i++) {
    const code = str.codePointAt(i) || 0
    const charWidth =
      (code >= 0x3000 && code <= 0x9fff) ||
      (code >= 0xac00 && code <= 0xd7af) ||
      (code >= 0xf900 && code <= 0xfaff) ||
      (code >= 0xff00 && code <= 0xffef) ||
      (code >= 0x20000 && code <= 0x2fa1f)
        ? 2
        : 1
    displayPos += charWidth
    if (code > 0xffff) {
      i++
    }
  }
  return displayPos
}

interface HighlightData {
  content: string
  beforeCursor: string
  cursorPosition: { row: number; col: number }
  contentCrossRowStatus?: boolean
  contentCrossStartLine?: number
  contentCrossRowLines?: number
  contentCurrentCursorCrossRowLines?: number
}

const highlightSyntax = (allData: HighlightData) => {
  const { content, beforeCursor, cursorPosition } = allData
  let command = ''
  let arg = ''
  const currentCursorX = cursorStartX.value + beforeCursor.length
  const index = content.indexOf(' ')
  const i = content.indexOf(' ')
  if (i != -1) {
    command = content.slice(0, i)
    arg = content.slice(i)
  } else {
    command = content
    arg = ''
  }

  activeMarkers.value.forEach((marker) => marker.dispose())
  activeMarkers.value = []
  let startY = (terminal.value as unknown as { _core?: { buffer?: { y?: number } } })?._core?.buffer?.y || 0
  if (allData.contentCrossRowStatus && allData.contentCrossStartLine !== undefined) {
    startY = allData.contentCrossStartLine
  }
  const isValidCommand = commands.value?.includes(command)
  if (command) {
    const commandMarker = terminal.value?.registerMarker(startY)
    if (commandMarker) {
      activeMarkers.value.push(commandMarker)
    }
    cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + 1}H`, {
      isUserCall: true
    })
    const colorCode = isValidCommand ? '38;2;24;144;255' : '31'
    cusWrite?.(`\x1b[${colorCode}m${command}\x1b[0m`, {
      isUserCall: true
    })
    setTimeout(() => {
      cusWrite?.(`\x1b[${cursorPosition.row + 1};${cursorPosition.col + 1}H`, {
        isUserCall: true
      })
    })
  }
  if (!arg) return
  if (arg.includes("'") || arg.includes('"') || arg.includes('(') || arg.includes('{') || arg.includes('[')) {
    const afterCommandArr: ResultItem[] = processString(arg)
    let unMatchFlag = false
    for (let i = 0; i < afterCommandArr.length; i++) {
      if (afterCommandArr[i].type == 'unmatched') {
        cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + 1}H`, {
          isUserCall: true
        })

        cusWrite?.(`\x1b[31m${content}\x1b[0m`, {
          isUserCall: true
        })

        cusWrite?.(`\x1b[${cursorPosition.row + 1};${cursorPosition.col + 1}H`, {
          isUserCall: true
        })
        unMatchFlag = true
      }
    }
    if (!unMatchFlag) {
      for (let i = 0; i < afterCommandArr.length; i++) {
        if (afterCommandArr[i].content == ' ') {
          // Calculate correct display position for Chinese characters
          const displayPos = calculateDisplayPosition(arg, afterCommandArr[i].startIndex)
          const commandDisplayWidth = calculateDisplayPosition(command, command.length)
          cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + commandDisplayWidth + 1 + displayPos}H`, {
            isUserCall: true
          })
          cusWrite?.(`${afterCommandArr[i].content}\x1b[0m`, {
            isUserCall: true
          })
        } else {
          // Calculate correct display position for Chinese characters
          const displayPos = calculateDisplayPosition(arg, afterCommandArr[i].startIndex)
          const commandDisplayWidth = calculateDisplayPosition(command, command.length)
          cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + commandDisplayWidth + 1 + displayPos}H`, {
            isUserCall: true
          })
          const colorCode = afterCommandArr[i].type == 'matched' ? '38;2;250;173;20' : '38;2;126;193;255'
          cusWrite?.(`\x1b[${colorCode}m${afterCommandArr[i].content}\x1b[0m`, {
            isUserCall: true
          })
        }
      }
    }
  } else {
    const commandDisplayWidth = calculateDisplayPosition(command, command.length)
    if (index == -1 && currentCursorX >= cursorStartX.value + commandDisplayWidth) {
      cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + commandDisplayWidth + 1}H`, {
        isUserCall: true
      })
      cusWrite?.(`\x1b[38;2;126;193;255m${arg}\x1b[0m`, { isUserCall: true })
      cusWrite?.(`\x1b[${cursorPosition.row + 1};${cursorPosition.col + 1}H`, {
        isUserCall: true
      })
    } else if (currentCursorX < cursorStartX.value + commandDisplayWidth) {
      cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + commandDisplayWidth + 1}H`, {
        isUserCall: true
      })
      cusWrite?.(`\x1b[38;2;126;193;255m${arg}\x1b[0m`, { isUserCall: true })
      cusWrite?.(`\x1b[${cursorPosition.row + 1};${cursorPosition.col + 1}H`, {
        isUserCall: true
      })
    } else {
      cusWrite?.(`\x1b[${startY + 1};${cursorStartX.value + commandDisplayWidth + 1}H`, {
        isUserCall: true
      })
      cusWrite?.(`\x1b[38;2;126;193;255m${arg}\x1b[0m`, { isUserCall: true })
      cusWrite?.(`\x1b[${cursorPosition.row + 1};${cursorPosition.col + 1}H`, {
        isUserCall: true
      })
    }
  }
}

type ResultItem = { type: string; content: string; startIndex: number; endIndex?: number }
const processString = (str: string): ResultItem[] => {
  const result: ResultItem[] = []
  let i = 0

  while (i < str.length) {
    if (str[i] === '"' || str[i] === "'") {
      const quote = str[i]
      let j = i + 1
      while (j < str.length && str[j] !== quote) {
        if (str[j] === '\\' && str[j + 1] === quote) {
          j += 2
        } else {
          j++
        }
      }
      if (j < str.length) {
        result.push({
          type: 'matched',
          startIndex: i,
          endIndex: j,
          content: str.slice(i, j + 1)
        })
        i = j + 1
      } else {
        result.push({
          type: 'unmatched',
          content: str[i],
          startIndex: i
        })
        i++
      }
      continue
    }
    if (str[i] === '{' && str[i + 1] === '{') {
      let depth = 1
      let j = i + 2
      while (j < str.length) {
        if (str[j] === '{' && str[j + 1] === '{') {
          depth++
          j++
        } else if (str[j] === '}' && str[j + 1] === '}') {
          depth--
          if (depth === 0) break
          j++
        }
        j++
      }
      if (depth === 0 && j < str.length) {
        result.push({
          type: 'matched',
          startIndex: i,
          endIndex: j + 1,
          content: str.slice(i, j + 2)
        })
        i = j + 2
      } else {
        result.push({
          type: 'unmatched',
          content: str[i],
          startIndex: i
        })
        i++
      }
      continue
    }
    if (str[i] === '{' || str[i] === '[' || str[i] === '(') {
      const openChar = str[i]
      const closeChar = openChar === '{' ? '}' : openChar === '[' ? ']' : ')'
      let depth = 1
      let j = i + 1
      while (j < str.length) {
        if (str[j] === openChar) {
          depth++
        } else if (str[j] === closeChar) {
          depth--
          if (depth === 0) break
        }
        j++
      }
      if (depth === 0 && j < str.length) {
        result.push({
          type: 'matched',
          startIndex: i,
          endIndex: j,
          content: str.slice(i, j + 1)
        })
        i = j + 1
      } else {
        result.push({
          type: 'unmatched',
          content: str[i],
          startIndex: i
        })
        i++
      }
      continue
    }
    let start = i
    while (
      i < str.length &&
      str[i] !== '"' &&
      str[i] !== "'" &&
      !(str[i] === '{' && str[i + 1] === '{') &&
      str[i] !== '{' &&
      str[i] !== '[' &&
      str[i] !== '('
    ) {
      i++
    }

    if (start < i) {
      result.push({
        type: 'afterMatched',
        content: str.slice(start, i),
        startIndex: start
      })
    }
    if (i === start) {
      result.push({
        type: 'afterMatched',
        content: str[i],
        startIndex: i
      })
      i++
    }
  }
  return result
}

const selectSuggestion = (suggestion: CommandSuggestion) => {
  selectFlag.value = true
  const DELCODE = String.fromCharCode(127)
  const RIGHTCODE = String.fromCharCode(27, 91, 67)
  sendData(RIGHTCODE.repeat(terminalState.value.content.length - terminalState.value.beforeCursor.length))
  sendData(DELCODE.repeat(terminalState.value.content.length))
  sendData(suggestion.command)
  suggestions.value = []
  activeSuggestion.value = -1
  suggestionSelectionMode.value = false
}
// 尝试直接从终端获取当前行内容并触发查询
const tryGetCurrentLineAndQuery = () => {
  try {
    if (!terminal.value) return

    const buffer = terminal.value.buffer.active
    const currentLine = buffer.getLine(buffer.cursorY)
    if (!currentLine) return

    // 获取当前行的文本内容
    const lineText = currentLine.translateToString(true)
    console.log('[SSH Debug] 从终端获取当前行:', lineText)

    // 查找命令开始位置（通常在提示符之后）
    const promptMatch = lineText.match(/[\]$#>]\s*(.*)$/)
    const command = promptMatch ? promptMatch[1] : lineText.trim()

    if (command && command.length >= 2) {
      console.log('[SSH Debug] 提取的命令:', command)
      queryCommand(command)
    } else {
      console.log('[SSH Debug] 命令太短或为空，跳过查询')
    }
  } catch (error) {
    console.log('[SSH Debug] 获取终端行内容失败:', error)
    // 降级到原有方法
    queryCommand()
  }
}

const queryCommand = async (cmd = '') => {
  console.log('[SSH Debug] queryCommand called:', {
    queryCommandFlag: queryCommandFlag.value,
    isSyncInput: isSyncInput.value,
    terminalStateContent: terminalState.value.content,
    terminalStateBeforeCursor: terminalState.value.beforeCursor,
    cmd: cmd,
    selectFlag: selectFlag.value,
    suggestionSelectionMode: suggestionSelectionMode.value,
    currentSuggestions: suggestions.value.length
  })

  // 检查自动补全配置状态
  if (!queryCommandFlag.value) {
    console.log('[SSH Debug] 自动补全已禁用，跳过查询')
    suggestions.value = []
    return
  }

  if (!queryCommandFlag.value || isSyncInput.value) {
    console.log('[SSH Debug] queryCommand skipped:', { queryCommandFlag: queryCommandFlag.value, isSyncInput: isSyncInput.value })
    return
  }

  const isAtEndOfLine = terminalState.value.beforeCursor.length === terminalState.value.content.length
  if (!isAtEndOfLine) {
    suggestions.value = []
    suggestionSelectionMode.value = false
    console.log('[SSH Debug] Not at end of line, clearing suggestions')
    return
  }

  // 定义 commandToQuery 在 try-catch 块外部，确保在所有作用域中都可访问
  const commandToQuery = cmd ? cmd : terminalState.value.beforeCursor

  try {
    if (suggestionSelectionMode.value) {
      return
    }
    console.log('[SSH Debug] Querying command suggestions for:', commandToQuery)

    // 确保命令不为空且长度大于等于1（降低触发阈值）
    if (!commandToQuery || commandToQuery.trim().length < 1) {
      console.log('[SSH Debug] Command too short, skipping query:', commandToQuery)
      suggestions.value = []
      return
    }

    // 检查 API 是否可用
    const api = (
      window as unknown as { api?: { getSmartSuggestions?: (params: unknown) => Promise<{ success: boolean; data: unknown[]; error?: string }> } }
    ).api
    if (!api || !api.getSmartSuggestions) {
      console.log('[SSH Debug] getSmartSuggestions API 不可用，尝试传统查询')
      throw new Error('Smart suggestions API not available')
    }

    // 使用增强的智能补全服务
    const result = await api.getSmartSuggestions({
      input: commandToQuery,
      context: {
        currentDirectory: '/', // 暂时使用根目录，后续可从终端状态获取
        serverIp: props.connectData.ip,
        username: props.connectData.username,
        sessionId: connectionId.value
      }
    })

    console.log('[SSH Debug] Smart suggestions result:', result)

    // 处理智能补全响应格式
    if (result && result.success && result.data && result.data.length > 0) {
      // 转换为兼容的CommandSuggestion格式
      suggestions.value = result.data.map((suggestion: unknown) => {
        const s = suggestion as { text?: string; command?: string; description?: string; type?: string; confidence?: number }
        return {
          command: s.text || s.command || '',
          description: s.description || '',
          type: s.type || 'command',
          confidence: s.confidence || 0.5,
          source: 'base' as const
        }
      }) as CommandSuggestion[]
      console.log('[SSH Debug] Set enhanced suggestions:', suggestions.value)
      setTimeout(() => {
        const componentInstance = componentRefs.value[connectionId.value]
        ;(componentInstance as { updateSuggestionsPosition?: (terminal: unknown) => void })?.updateSuggestionsPosition?.(terminal.value)
      }, 1)
    } else {
      suggestions.value = []
      if (result && !result.success) {
        console.log('[SSH Debug] Smart suggestions failed:', result.error)
      }
    }
  } catch (error) {
    console.log('[SSH Debug] 智能补全查询失败:', error)
    // 降级到传统查询
    try {
      const fallbackApi = (
        window as unknown as {
          api?: { queryCommand?: (params: { command: string; ip: string }) => Promise<{ success?: boolean; data?: CommandSuggestion[] }> }
        }
      ).api
      if (!fallbackApi || !fallbackApi.queryCommand) {
        console.log('[SSH Debug] 传统查询 API 也不可用')
        suggestions.value = []
        return
      }

      console.log('[SSH Debug] 尝试传统查询:', { command: commandToQuery, ip: props.connectData.ip })
      const fallbackResult = await fallbackApi.queryCommand({
        command: commandToQuery,
        ip: props.connectData.ip
      })

      console.log('[SSH Debug] 传统查询结果:', fallbackResult)
      if (fallbackResult && fallbackResult.success && fallbackResult.data) {
        // 确保数据格式正确
        const rawData = fallbackResult.data as {
          command?: string
          text?: string
          source?: string
          description?: string
          type?: string
          confidence?: number
        }[]
        suggestions.value = rawData.map((item) => ({
          command: item.command || item.text || '',
          source: (item.source as 'base' | 'history') || 'base',
          description: item.description || '',
          type: item.type || 'command',
          confidence: item.confidence || 0.5
        })) as CommandSuggestion[]
        console.log('[SSH Debug] 传统查询成功，设置建议:', suggestions.value)

        // 更新建议位置
        setTimeout(() => {
          const componentInstance = componentRefs.value[connectionId.value]
          ;(componentInstance as { updateSuggestionsPosition?: (terminal: unknown) => void })?.updateSuggestionsPosition?.(terminal.value)
        }, 1)
      } else {
        suggestions.value = []
        console.log('[SSH Debug] 传统查询无结果')
      }
    } catch (fallbackError) {
      console.log('[SSH Debug] 传统查询也失败:', fallbackError)
      suggestions.value = []
    }
  }
}
const insertCommand = async (cmd: string) => {
  try {
    // 使用增强命令历史服务记录命令
    const api = (window as unknown as { api?: { addToEnhancedHistory?: (params: unknown) => Promise<void> } }).api
    if (api?.addToEnhancedHistory) {
      await api.addToEnhancedHistory({
        command: cmd,
        context: {
          serverIp: props.connectData.ip,
          username: props.connectData.username,
          currentDirectory: '/', // 暂时使用根目录，后续可从终端状态获取
          sessionId: connectionId.value
        }
      })
    }

    // 保持向后兼容，同时调用传统方法
    const insertApi = (window as unknown as { api?: { insertCommand?: (params: { command: string; ip: string }) => Promise<void> } }).api
    if (insertApi?.insertCommand) {
      await insertApi.insertCommand({
        command: cmd,
        ip: props.connectData.ip
      })
    }
  } catch (error) {
    console.log('[SSH Debug] 记录命令历史失败:', error)
  }
}

interface KeyInputEvent {
  key: string
  domEvent?: {
    keyCode?: number
    altKey?: boolean
    ctrlKey?: boolean
    metaKey?: boolean
    code?: string
  }
}

const handleKeyInput = (e: KeyInputEvent) => {
  console.log('[SSH Debug] handleKeyInput called:', {
    key: e.key,
    keyCode: e.domEvent?.keyCode,
    isConnected: isConnected.value
  })

  // 重要：handleKeyInput 只负责更新状态，不发送数据
  // 数据发送由 terminal.onData 处理器（handleInput）负责

  enterPress.value = false
  tagPress.value = false
  specialCode.value = false
  const ev = e.domEvent
  if (!ev) return

  const printable = !ev.altKey && !ev.ctrlKey && !ev.metaKey
  const buffer = terminal.value?.buffer.active as { cursorX: number; cursorY: number } | undefined
  if (!buffer) return

  cursorX.value = buffer.cursorX
  cursorY.value = buffer.cursorY
  keyCode.value = ev.keyCode || 0
  let index = 0
  if (cursorStartX.value == 0) {
    cursorStartX.value = cursorX.value
  } else {
    cursorX.value !== 0 && cursorX.value < cursorStartX.value && (cursorStartX.value = cursorX.value)
  }

  // 处理回车键 - 只更新状态
  if (ev.keyCode === 13 || e.key === '\u0003') {
    if (suggestions.value.length && activeSuggestion.value >= 0 && suggestionSelectionMode.value) {
      // 在选择模式下，让onData处理器来处理建议选择
      console.log('[SSH Debug] handleKeyInput: 检测到回车键，在选择模式下，让onData处理器处理')
      return
    }

    enterPress.value = true
    selectFlag.value = true
    currentLine.value = ''
    cursorStartX.value = 0
    terminalState.value.contentCrossRowStatus = false
    terminalState.value.contentCrossStartLine = 0
    terminalState.value.contentCrossRowLines = 0

    // 修复：如果onData没有正确触发，直接处理回车键
    console.log('[SSH Debug] Enter key detected in handleKeyInput, ensuring proper handling')

    // 延迟一点时间让onData处理器有机会执行
    setTimeout(() => {
      if (enterPress.value) {
        console.log('[SSH Debug] Enter key not handled by onData, forcing manual handling')
        handleInput?.('\r', true)
      }
    }, 10)

    // 确保滚动到底部并保持焦点
    nextTick(() => {
      terminal.value?.scrollToBottom()
      terminal.value?.focus()
    })
  }
  // 处理退格键 - 只更新状态
  else if (ev.keyCode === 8) {
    index = cursorX.value - 1 - cursorStartX.value
    currentLine.value = currentLine.value.slice(0, index) + currentLine.value.slice(index + 1)
  }
  // 处理方向键 - 只更新状态
  else if (ev.keyCode == 38 || ev.keyCode == 40) {
    specialCode.value = true
  } else if (ev.keyCode == 37 || ev.keyCode == 39) {
    specialCode.value = true
    if (suggestions.value.length) {
      specialCode.value = false
    }
  }
  // 处理Tab键 - 只更新状态
  else if (ev.keyCode == 9) {
    tagPress.value = true
  }
  // 处理普通字符 - 只更新状态
  else if (printable && e.key && e.key.length === 1) {
    selectFlag.value = false
  }
  // 处理其他特殊键 - 只更新状态
  else {
    selectFlag.value = false
  }
}

const disconnectSSH = async () => {
  try {
    const result = await api.disconnect({ id: connectionId.value })
    if (result.status === 'success') {
      cleanupListeners.value.forEach((cleanup) => cleanup())
      cleanupListeners.value = []
      isConnected.value = false
      cusWrite?.('\r\n' + t('ssh.disconnected'), { isUserCall: true })
      cusWrite?.('\r\n' + t('ssh.pressEnterToReconnect') + '\r\n', { isUserCall: true })
    } else {
      cusWrite?.('\r\n' + t('ssh.disconnectError', { message: result.message }), { isUserCall: true })
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : t('ssh.unknownError')
    cusWrite?.('\r\n' + t('ssh.disconnectError', { message: errorMessage }), {
      isUserCall: true
    })
  }
  emit('disconnectSSH', { isConnected: isConnected })
}
const contextAct = (action: string) => {
  switch (action) {
    case 'paste':
      if (startStr.value == '') {
        startStr.value = beginStr.value
      }
      pasteFlag.value = true
      navigator.clipboard.readText().then((text) => {
        sendData(text)
        terminal.value?.focus()
      })
      break
    case 'disconnect':
      disconnectSSH()
      termOndata?.dispose()
      termOndata = null
      break
    case 'reconnect':
      connectSSH()
      break
    case 'newTerminal':
      emit('createNewTerm', props.serverInfo)
      break
    case 'close':
      emit('closeTabInTerm', props.serverInfo.id)
      break
    case 'clearTerm':
      terminal.value?.clear()
      break
    case 'shrotenName':
      sendData('export PS1="[\\u@\\W]\\$"')
      sendData('\r')
      break
    case 'fontsizeLargen':
      adjustFontSize(1)
      break
    case 'fontsizeSmaller':
      adjustFontSize(-1)
      break
    case 'registerSyncInput':
      if (isSyncInput.value) {
        inputManager.unregisterSyncInput(connectionId.value)
        isSyncInput.value = false
      } else {
        inputManager.registerSyncInput(connectionId.value)
        isSyncInput.value = true
      }
      break
    case 'fileManager':
      eventBus.emit('openUserTab', 'files')
      break
    default:
      break
  }
}

const focus = () => {
  if (terminal.value) {
    // Ensure terminal scrolls to bottom, keeping cursor in visible area
    terminal.value.scrollToBottom()
    terminal.value.focus()
    inputManager.setActiveTerm(connectionId.value)
  }
}

const hideSelectionButton = () => {
  showAiButton.value = false
}

// Adjust font size function
const adjustFontSize = async (delta: number) => {
  if (!terminal.value) return

  const currentFontSize = terminal.value.options.fontSize || 12
  const newFontSize = Math.max(8, Math.min(32, currentFontSize + delta))

  if (newFontSize !== currentFontSize) {
    // Update current terminal font size
    terminal.value.options.fontSize = newFontSize

    // Save to user config
    try {
      const config = await serviceUserConfig.getConfig()
      await serviceUserConfig.saveConfig({
        ...config,
        fontSize: newFontSize
      })
    } catch (error) {
      console.error('Failed to save font size:', error)
    }

    // Notify other terminals to update font size
    eventBus.emit('updateTerminalFontSize', newFontSize)

    // Trigger resize to adjust terminal layout
    setTimeout(() => {
      handleResize()
    }, 50)
  }
}

// Handle wheel event for font size adjustment
const handleWheel = (e: WheelEvent) => {
  if (e.ctrlKey && terminal.value) {
    e.preventDefault()
    if (e.deltaY < 0) {
      // Wheel up - increase font size
      adjustFontSize(1)
    } else {
      // Wheel down - decrease font size
      adjustFontSize(-1)
    }
  }
}

const handleGlobalKeyDown = (e: KeyboardEvent) => {
  if (contextmenu.value && typeof contextmenu.value.hide === 'function') {
    contextmenu.value.hide()
  }
  if (props.activeTabId !== props.currentConnectionId) return

  const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0

  // Search functionality
  // Windows uses the method of listening for key messages, window.addEventListener('message', handlePostMessage)
  if ((isMac ? e.metaKey : e.ctrlKey) && e.key === 'f') {
    e.preventDefault()
    e.stopPropagation()
    openSearch()
  }

  if (e.key === 'Escape' && showSearch.value) {
    e.preventDefault()
    e.stopPropagation()
    closeSearch()
  }
}
// Open command dialog only for the active tab and focused terminal
const tryOpenLocalCommandDialog = () => {
  if (props.activeTabId !== props.currentConnectionId) return
  const activeElement = document.activeElement
  const container = terminalElement.value?.closest('.terminal-container')
  const isTerminalFocused =
    activeElement === terminal.value?.textarea ||
    container?.contains(activeElement as Node) ||
    (activeElement as HTMLElement)?.classList?.contains('xterm-helper-textarea')

  if (!isTerminalFocused) return
  // Ensure terminal is focused for accurate positioning
  terminal.value?.focus()
  isCommandDialogVisible.value = true
}
const handleOpenCommandDialog = () => {
  tryOpenLocalCommandDialog()
}

// Listen to global openCommandDialog and restrict to this terminal/tab
eventBus.on('openCommandDialog', handleOpenCommandDialog)

cleanupListeners.value.push(() => {
  eventBus.off('openCommandDialog', handleOpenCommandDialog)
})

// Hide dialog when tab deactivates; restore when re-activates
watch(
  () => props.activeTabId,
  (newActive) => {
    if (newActive !== props.currentConnectionId) {
      wasDialogVisibleBeforeDeactivation.value = isCommandDialogVisible.value
      isCommandDialogVisible.value = false
    } else {
      if (wasDialogVisibleBeforeDeactivation.value) {
        // Restore visibility and reposition
        nextTick(() => {
          terminal.value?.focus()
          isCommandDialogVisible.value = true
        })
      }
    }
  }
)

const openSearch = () => {
  showSearch.value = true
}

const closeSearch = () => {
  showSearch.value = false
  searchAddon.value?.clearDecorations()
  terminal.value?.focus()
}

watch(
  () => commandBarHeight.value,
  () => {
    terminalContainerResize()
  }
)

const terminalContainerResize = () => {
  const currentHeight = commandBarHeight.value
  if (currentHeight > 0) {
    terminalContainer.value?.style.setProperty('height', `calc(100% - ${currentHeight}px)`)
  } else {
    terminalContainer.value?.style.setProperty('height', '100%')
    if (terminal.value) {
      terminal.value.scrollToBottom()
      terminal.value.focus()
    }
  }
}

const onChatToAiClick = () => {
  if (terminal.value && terminal.value.hasSelection()) {
    const text = terminal.value.getSelection()
    eventBus.emit('openAiRight')
    nextTick(() => {
      const formattedText = `Terminal output:\n\`\`\`\n${text.trim()}\n\`\`\``
      eventBus.emit('chatToAi', formattedText)
    })
    terminal.value.clearSelection()
  }
}

const getCursorLinePosition = () => {
  if (!terminal.value) {
    console.warn('Terminal not available')
    return null
  }

  try {
    const termInstance = terminal.value
    const terminalCore = (
      termInstance as unknown as {
        _core: {
          _bufferService: { buffer: { x: number; y: number; lines: { get: (index: number) => { translateToString: (trim: boolean) => string } } } }
          _renderService: { dimensions: { css: { cell: { height: number; width: number } } } }
        }
      }
    )._core
    const buffer = terminalCore._bufferService.buffer
    const renderService = terminalCore._renderService

    const { x: cursorX, y: cursorY } = buffer
    const baseY = (termInstance as { buffer: { active: { baseY: number } } }).buffer.active.baseY

    const cellHeight = renderService.dimensions.css.cell.height || 16
    const cellWidth = renderService.dimensions.css.cell.width || 8

    const terminalRect = terminalElement.value?.getBoundingClientRect()

    const cursorPixelX = cursorX * cellWidth
    const cursorPixelY = cursorY * cellHeight

    const screenPosition = terminalRect
      ? {
          x: terminalRect.left + cursorPixelX,
          y: terminalRect.top + cursorPixelY
        }
      : null

    return {
      logicalX: cursorX,
      logicalY: baseY + cursorY,

      screenX: cursorX,
      screenY: cursorY,

      pixelX: cursorPixelX,
      pixelY: cursorPixelY,

      absoluteX: screenPosition?.x || null,
      absoluteY: screenPosition?.y || null,

      cellHeight,
      cellWidth,

      terminalRect: terminalRect
        ? {
            left: terminalRect.left,
            top: terminalRect.top,
            width: terminalRect.width,
            height: terminalRect.height
          }
        : null,

      currentLineContent: buffer.lines.get(baseY + cursorY)?.translateToString(true) || '',

      isCrossRow: cursorEndY.value !== cursorY
    }
  } catch (error) {
    console.error(t('common.getCursorPositionFailed'), error)
    return null
  }
}
defineExpose({
  handleResize,
  autoExecuteCode,
  terminal,
  focus,
  getCursorLinePosition,
  triggerResize: () => {
    handleResize()
  }
})

const commandOutput = ref('')
const isCollectingOutput = ref(false)

function updateSelectionButtonPosition() {
  if (!terminal.value) return
  const termInstance = terminal.value
  if (!termInstance.hasSelection()) {
    showAiButton.value = false
    return
  }
  const position = termInstance.getSelectionPosition()
  if (position && termInstance.getSelection().trim()) {
    const button = document.getElementById(`${connectionId.value}Button`) as HTMLElement
    if (!button) return
    const viewportY = termInstance.buffer.active.viewportY
    const viewportRows = termInstance.rows
    const visibleStart = viewportY
    const visibleEnd = viewportY + viewportRows - 1
    const { y: startY } = position.start
    const { y: endY } = position.end
    if ((startY < visibleStart || startY > visibleEnd) && (endY < visibleStart || endY > visibleEnd)) {
      showAiButton.value = false
      return
    }
    const visibleRow = startY - viewportY
    const cellHeight = (termInstance as unknown as { _core: { _renderService: { dimensions: { css: { cell: { height: number } } } } } })._core
      ._renderService.dimensions.css.cell.height
    const top = visibleRow - 2 > 0 ? (visibleRow - 2) * cellHeight : 0
    button.style.right = `26px`
    button.style.top = `${top}px`
    showAiButton.value = true
  } else {
    showAiButton.value = false
  }
}

// Helpers for suggestion handling
const isDeleteKeyData = (d: string) => d === '\x7f' || d === '\b' || d === '\x1b[3~'
</script>

<style lang="less">
.ant-form-item .ant-form-item-label > label {
  color: var(--text-color);
}

.ant-radio-wrapper {
  color: var(--text-color);
}

.terminal-container {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px 4px 0px 12px;
  position: relative;
  /* 确保容器可以接收用户交互 */
  pointer-events: auto;
  user-select: text;
  /* 移除固定背景色，使用动态背景 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* 确保xterm.js的背景不会覆盖容器背景 */
.terminal-container :deep(.xterm) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-screen) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-viewport) {
  background: transparent !important;
}

.terminal-container :deep(.xterm .xterm-screen) {
  background: transparent !important;
}

.terminal-container :deep(.xterm .xterm-viewport) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-helper-textarea) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-rows) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-cursor-layer) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-text-layer) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-selection-layer) {
  background: transparent !important;
}

.terminal-container :deep(.xterm-link-layer) {
  background: transparent !important;
}

/* WebGL Canvas背景透明 */
.terminal-container :deep(canvas) {
  background: transparent !important;
  background-color: transparent !important;
}

.terminal :deep(canvas) {
  background: transparent !important;
  background-color: transparent !important;
}

/* 仅强制xterm相关子元素透明背景 */

.terminal-container :deep(.xterm-screen) {
  background-color: transparent !important;
  background: transparent !important;
}

.terminal-container :deep(.xterm-viewport) {
  background-color: transparent !important;
  background: transparent !important;
}

/* 确保SSH终端文字正常显示 */
.terminal-container :deep(.xterm-text-layer),
.terminal-container :deep(.xterm-rows),
.terminal-container :deep(.xterm-text-layer *) {
  color: inherit !important;
  opacity: 1 !important;
}

/* 特别针对可能的黑色背景元素 */
.terminal-container :deep([style*='background-color: rgb(0, 0, 0)']),
.terminal-container :deep([style*='background-color: black']),
.terminal-container :deep([style*='background: rgb(0, 0, 0)']),
.terminal-container :deep([style*='background: black']) {
  background-color: transparent !important;
  background: transparent !important;
}

.terminal {
  width: 100%;
  height: 100%;
  /* 确保终端元素可以接收用户交互 */
  pointer-events: auto;
  user-select: text;
}

.xterm-screen {
  -webkit-font-smoothing: subpixel-antialiased;
  transform: translateZ(0);
  /* 确保xterm屏幕可以接收用户交互 */
  pointer-events: auto;
}

.terminal .xterm-viewport {
  background-color: transparent;
  /* 确保视口可以接收用户交互 */
  pointer-events: auto;
}

.terminal ::-webkit-scrollbar {
  width: 0px !important;
}

/* 确保xterm相关元素可以正常接收输入 */
.terminal .xterm-helper-textarea {
  pointer-events: auto !important;
  user-select: text !important;
  /* 确保textarea可见且可交互 */
  opacity: 1 !important;
  z-index: 1 !important;
}

.terminal .xterm-cursor-layer {
  pointer-events: none;
}

.terminal .xterm-selection-layer {
  pointer-events: none;
}

.terminal .xterm-link-layer {
  pointer-events: auto;
}

/* 确保终端内容层可以接收点击事件 */
.terminal .xterm-rows {
  pointer-events: auto;
}

.terminal .xterm-char-measure-element {
  pointer-events: none;
}
.select-button {
  position: absolute;
  z-index: 10;
  padding: 4px 8px;
  border-radius: 4px;
  color: var(--text-color);
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color-light);

  .main-text {
    color: var(--text-color);
    font-size: 12px;
    font-weight: 500;
  }

  .shortcut-text {
    color: var(--text-color-secondary);
    font-size: 10px;
    margin-left: 4px;
    font-weight: 400;
  }

  &:hover {
    color: var(--text-color) !important;
    border: 1px solid var(--border-color-light) !important;

    .main-text {
      color: var(--text-color) !important;
    }

    .shortcut-text {
      color: var(--text-color-secondary) !important;
    }
  }
}
</style>
