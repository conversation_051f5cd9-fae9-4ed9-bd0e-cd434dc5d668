/**
 * 串口连接服务
 * 提供串口设备的连接、通信和管理功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { SerialPort } from 'serialport'
import { ReadlineParser } from '@serialport/parser-readline'
import {
  ConsoleConnection,
  ConsoleConnectionConfig,
  ConsoleConnectionInfo,
  ConsoleConnectionStatus,
  ConsoleCommandResult,
  SerialConfig
} from './types'

/**
 * 串口连接服务类
 * 继承自ConsoleConnection，实现串口特定的连接逻辑
 */
export class SerialService extends ConsoleConnection {
  private serialPort?: SerialPort
  private parser?: ReadlineParser
  private isConnecting: boolean = false
  private reconnectTimer?: NodeJS.Timeout
  private commandQueue: Array<{
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
  }> = []
  private currentCommand?: {
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
    output: string
  }
  private bytesSent: number = 0
  private bytesReceived: number = 0

  /**
   * 构造函数
   * @param id 连接ID
   * @param config 连接配置
   */
  constructor(id: string, config: ConsoleConnectionConfig) {
    super(id, config)
    this.validateConfig()
  }

  /**
   * 建立串口连接
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.status === ConsoleConnectionStatus.CONNECTED) {
      return
    }

    this.isConnecting = true
    this.status = ConsoleConnectionStatus.CONNECTING
    this.emit('statusChange', this.status)

    try {
      const serialConfig = this.config.config as SerialConfig

      // 创建串口实例
      this.serialPort = new SerialPort({
        path: serialConfig.path,
        baudRate: serialConfig.baudRate,
        dataBits: serialConfig.dataBits,
        stopBits: serialConfig.stopBits,
        parity: serialConfig.parity,
        autoOpen: false
      })

      // 创建解析器
      this.parser = this.serialPort.pipe(new ReadlineParser({ delimiter: '\r\n' }))

      // 设置事件监听
      this.setupSerialEvents()

      // 打开串口
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`串口连接超时: ${serialConfig.timeout || 5000}ms`))
        }, serialConfig.timeout || 5000)

        this.serialPort!.open((error) => {
          clearTimeout(timeout)
          if (error) {
            reject(new Error(`串口打开失败: ${error.message}`))
          } else {
            resolve()
          }
        })
      })

      this.status = ConsoleConnectionStatus.CONNECTED
      this.startedAt = new Date()
      this.lastActivityAt = new Date()
      this.isConnecting = false

      this.emit('statusChange', this.status)
      this.emit('connect')
    } catch (error) {
      this.isConnecting = false
      this.status = ConsoleConnectionStatus.ERROR
      this.emit('statusChange', this.status)
      this.emit('error', error instanceof Error ? error : new Error('串口连接失败'))

      // 如果启用自动重连，则尝试重连
      if (this.config.autoReconnect) {
        this.scheduleReconnect()
      }

      throw error
    }
  }

  /**
   * 断开串口连接
   */
  public async disconnect(): Promise<void> {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = undefined
    }

    // 清理命令队列
    this.clearCommandQueue('连接已断开')

    if (this.serialPort && this.serialPort.isOpen) {
      await new Promise<void>((resolve) => {
        this.serialPort!.close((error) => {
          if (error) {
            this.emit('error', new Error(`串口关闭失败: ${error.message}`))
          }
          resolve()
        })
      })
    }

    this.serialPort = undefined
    this.parser = undefined
    this.status = ConsoleConnectionStatus.DISCONNECTED
    this.emit('statusChange', this.status)
    this.emit('disconnect', '主动断开')
  }

  /**
   * 发送数据到串口
   * @param data 要发送的数据
   */
  public async send(data: string | Buffer): Promise<void> {
    if (!this.serialPort || !this.serialPort.isOpen) {
      throw new Error('串口未连接')
    }

    const buffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8')

    await new Promise<void>((resolve, reject) => {
      this.serialPort!.write(buffer, (error) => {
        if (error) {
          reject(new Error(`数据发送失败: ${error.message}`))
        } else {
          this.bytesSent += buffer.length
          this.lastActivityAt = new Date()
          resolve()
        }
      })
    })
  }

  /**
   * 执行命令
   * @param command 要执行的命令
   * @param timeout 超时时间（毫秒）
   * @returns 命令执行结果
   */
  public async executeCommand(command: string, timeout: number = 30000): Promise<ConsoleCommandResult> {
    if (!this.serialPort || !this.serialPort.isOpen) {
      throw new Error('串口未连接')
    }

    return new Promise<ConsoleCommandResult>((resolve, reject) => {
      const startTime = Date.now()

      // 设置超时
      const timeoutHandle = setTimeout(() => {
        reject(new Error(`命令执行超时: ${timeout}ms`))
      }, timeout)

      const commandExecution = {
        command,
        resolve: (result: ConsoleCommandResult) => {
          clearTimeout(timeoutHandle)
          resolve(result)
        },
        reject: (error: Error) => {
          clearTimeout(timeoutHandle)
          reject(error)
        },
        timeout: timeoutHandle,
        startTime
      }

      // 如果当前没有正在执行的命令，立即执行
      if (!this.currentCommand) {
        this.executeNextCommand(commandExecution)
      } else {
        // 否则加入队列
        this.commandQueue.push(commandExecution)
      }
    })
  }

  /**
   * 获取连接信息
   * @returns 连接信息
   */
  public getConnectionInfo(): ConsoleConnectionInfo {
    const serialConfig = this.config.config as SerialConfig

    return {
      id: this.id,
      assetId: this.config.assetId,
      name: this.config.name,
      type: this.config.type,
      status: this.status,
      config: this.config,
      startedAt: this.startedAt || new Date(),
      lastActivityAt: this.lastActivityAt || new Date(),
      reconnectCount: this.reconnectCount,
      bytesSent: this.bytesSent,
      bytesReceived: this.bytesReceived
    }
  }

  /**
   * 检查连接是否活跃
   * @returns 是否活跃
   */
  public isAlive(): boolean {
    return this.serialPort?.isOpen === true && this.status === ConsoleConnectionStatus.CONNECTED
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    const serialConfig = this.config.config as SerialConfig

    if (!serialConfig.path) {
      throw new Error('串口路径不能为空')
    }

    if (!serialConfig.baudRate || serialConfig.baudRate <= 0) {
      throw new Error('波特率必须大于0')
    }

    if (![5, 6, 7, 8].includes(serialConfig.dataBits)) {
      throw new Error('数据位必须是5、6、7或8')
    }

    if (![1, 1.5, 2].includes(serialConfig.stopBits)) {
      throw new Error('停止位必须是1、1.5或2')
    }

    if (!['none', 'even', 'mark', 'odd', 'space'].includes(serialConfig.parity)) {
      throw new Error('校验位必须是none、even、mark、odd或space')
    }
  }

  /**
   * 设置串口事件监听
   */
  private setupSerialEvents(): void {
    if (!this.serialPort || !this.parser) {
      return
    }

    // 数据接收事件
    this.parser.on('data', (data: string) => {
      this.bytesReceived += Buffer.byteLength(data, 'utf8')
      this.lastActivityAt = new Date()
      this.emit('data', data)

      // 处理命令响应
      if (this.currentCommand) {
        this.currentCommand.output += data + '\n'

        // 简单的命令完成检测（可根据具体设备协议调整）
        if (this.isCommandComplete(data)) {
          this.completeCurrentCommand()
        }
      }
    })

    // 错误事件
    this.serialPort.on('error', (error) => {
      this.emit('error', new Error(`串口错误: ${error.message}`))
      this.status = ConsoleConnectionStatus.ERROR
      this.emit('statusChange', this.status)

      if (this.config.autoReconnect) {
        this.scheduleReconnect()
      }
    })

    // 关闭事件
    this.serialPort.on('close', () => {
      this.status = ConsoleConnectionStatus.DISCONNECTED
      this.emit('statusChange', this.status)
      this.emit('disconnect', '串口关闭')

      if (this.config.autoReconnect && this.reconnectCount < (this.config.maxReconnectAttempts || 5)) {
        this.scheduleReconnect()
      }
    })
  }

  /**
   * 执行下一个命令
   * @param commandExecution 命令执行对象
   */
  private async executeNextCommand(commandExecution: {
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
  }): Promise<void> {
    this.currentCommand = {
      ...commandExecution,
      output: ''
    }

    try {
      // 发送命令（添加换行符）
      await this.send(commandExecution.command + '\r\n')
    } catch (error) {
      this.currentCommand = undefined
      commandExecution.reject(error instanceof Error ? error : new Error('命令发送失败'))
      this.processNextCommand()
    }
  }

  /**
   * 完成当前命令
   */
  private completeCurrentCommand(): void {
    if (!this.currentCommand) {
      return
    }

    const duration = Date.now() - this.currentCommand.startTime
    const result: ConsoleCommandResult = {
      command: this.currentCommand.command,
      output: this.currentCommand.output.trim(),
      duration,
      timestamp: new Date()
    }

    this.currentCommand.resolve(result)
    this.currentCommand = undefined

    // 处理队列中的下一个命令
    this.processNextCommand()
  }

  /**
   * 处理队列中的下一个命令
   */
  private processNextCommand(): void {
    if (this.commandQueue.length > 0) {
      const nextCommand = this.commandQueue.shift()!
      this.executeNextCommand(nextCommand)
    }
  }

  /**
   * 判断命令是否完成
   * @param data 接收到的数据
   * @returns 是否完成
   */
  private isCommandComplete(data: string): boolean {
    // 简单的命令完成检测逻辑
    // 可根据具体设备的提示符进行调整
    const prompts = ['>', '#', '$', '%']
    return prompts.some((prompt) => data.trim().endsWith(prompt))
  }

  /**
   * 清理命令队列
   * @param reason 清理原因
   */
  private clearCommandQueue(reason: string): void {
    // 清理当前命令
    if (this.currentCommand) {
      this.currentCommand.reject(new Error(reason))
      this.currentCommand = undefined
    }

    // 清理队列中的命令
    while (this.commandQueue.length > 0) {
      const command = this.commandQueue.shift()!
      command.reject(new Error(reason))
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return
    }

    const interval = this.config.reconnectInterval || 5000
    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = undefined
      this.reconnectCount++

      this.emit('reconnect', this.reconnectCount)

      try {
        await this.connect()
        this.reconnectCount = 0 // 重连成功后重置计数
      } catch (error) {
        // 重连失败，如果还有重连次数则继续尝试
        if (this.reconnectCount < (this.config.maxReconnectAttempts || 5)) {
          this.scheduleReconnect()
        } else {
          this.emit('error', new Error('重连次数已达上限'))
        }
      }
    }, interval)
  }
}
