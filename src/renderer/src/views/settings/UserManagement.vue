<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <Users class="title-icon" />
          用户管理
        </h1>
        <p class="page-description">管理系统用户账户、角色分配和权限审核</p>
      </div>
      <div class="header-actions">
        <a-button @click="importUsers">
          <Upload class="btn-icon" />
          批量导入
        </a-button>
        <a-button
          type="primary"
          @click="showAddUserModal"
        >
          <UserPlus class="btn-icon" />
          添加用户
        </a-button>
      </div>
    </div>

    <!-- 用户统计 -->
    <div class="user-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总用户数"
              :value="users.length"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Users class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃用户"
              :value="activeUsersCount"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <UserCheck class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="管理员"
              :value="adminUsersCount"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <Crown class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日登录"
              :value="todayLoginCount"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <LogIn class="stat-icon" />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <a-card>
        <a-form
          layout="inline"
          :model="filterForm"
        >
          <a-form-item label="搜索用户">
            <a-input-search
              v-model:value="filterForm.keyword"
              placeholder="搜索用户名、邮箱"
              style="width: 250px"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="角色">
            <a-select
              v-model:value="filterForm.role"
              placeholder="选择角色"
              style="width: 150px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部角色</a-select-option>
              <a-select-option value="system_admin">系统管理员</a-select-option>
              <a-select-option value="enterprise_admin">企业管理员</a-select-option>
              <a-select-option value="personal">个人用户</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="状态">
            <a-select
              v-model:value="filterForm.status"
              placeholder="用户状态"
              style="width: 120px"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="active">活跃</a-select-option>
              <a-select-option value="inactive">禁用</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="注册时间">
            <a-range-picker
              v-model:value="filterForm.dateRange"
              :placeholder="['开始时间', '结束时间']"
              @change="handleFilterChange"
            />
          </a-form-item>
          <a-form-item>
            <a-button @click="resetFilters">
              <RotateCcw class="btn-icon" />
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 用户列表 -->
    <div class="users-section">
      <a-card>
        <template #title>
          <div class="users-header">
            <span>用户列表</span>
            <div class="users-actions">
              <a-space>
                <span class="user-count">共 {{ filteredUsers.length }} 个用户</span>
                <a-button
                  size="small"
                  @click="exportUsers"
                >
                  <Download class="btn-icon" />
                  导出
                </a-button>
                <a-button
                  size="small"
                  :loading="refreshing"
                  @click="refreshUsers"
                >
                  <RefreshCw class="btn-icon" />
                  刷新
                </a-button>
              </a-space>
            </div>
          </div>
        </template>

        <a-table
          :columns="userColumns"
          :data-source="paginatedUsers"
          :pagination="tablePagination"
          :loading="loading"
          row-key="id"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'user'">
              <div class="user-cell">
                <a-avatar :size="40">
                  {{ record.username.charAt(0).toUpperCase() }}
                </a-avatar>
                <div class="user-info">
                  <div class="username">{{ record.username }}</div>
                  <div class="email">{{ record.email }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key === 'role'">
              <a-tag :color="getRoleColor(record.role_type)">{{ getRoleLabel(record.role_type) }}</a-tag>
            </template>
            <template v-else-if="column.key === 'status'">
              <a-badge
                :status="record.is_active ? 'success' : 'default'"
                :text="record.is_active ? '活跃' : '禁用'"
              />
            </template>
            <template v-else-if="column.key === 'last_login'">
              <div class="login-info">
                <div class="login-time">{{ formatTime(record.last_login_at) }}</div>
                <div
                  v-if="record.last_login_ip"
                  class="login-ip"
                  >{{ record.last_login_ip }}</div
                >
              </div>
            </template>
            <template v-else-if="column.key === 'created_at'">
              {{ formatDate(record.created_at) }}
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button
                  size="small"
                  @click="viewUser(record)"
                >
                  <Eye class="action-icon" />
                </a-button>
                <a-button
                  size="small"
                  @click="editUser(record)"
                >
                  <Edit class="action-icon" />
                </a-button>
                <a-dropdown>
                  <a-button size="small">
                    <MoreHorizontal class="action-icon" />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click="resetPassword(record)">
                        <Key class="menu-icon" />
                        重置密码
                      </a-menu-item>
                      <a-menu-item @click="toggleUserStatus(record)">
                        <Power class="menu-icon" />
                        {{ record.is_active ? '禁用' : '启用' }}用户
                      </a-menu-item>
                      <a-menu-item @click="viewUserSessions(record)">
                        <Monitor class="menu-icon" />
                        查看会话
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item
                        class="danger-item"
                        @click="deleteUser(record)"
                      >
                        <Trash2 class="menu-icon" />
                        删除用户
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 用户详情模态框 -->
    <a-modal
      v-model:open="showUserDetailsModal"
      title="用户详情"
      width="800px"
      :footer="null"
    >
      <div
        v-if="selectedUser"
        class="user-details"
      >
        <div class="user-profile">
          <div class="profile-header">
            <a-avatar :size="80">
              {{ selectedUser.username.charAt(0).toUpperCase() }}
            </a-avatar>
            <div class="profile-info">
              <h2 class="profile-name">{{ selectedUser.username }}</h2>
              <p class="profile-email">{{ selectedUser.email }}</p>
              <a-tag :color="getRoleColor(selectedUser.role_type)">{{ getRoleLabel(selectedUser.role_type) }}</a-tag>
              <a-badge
                :status="selectedUser.is_active ? 'success' : 'default'"
                :text="selectedUser.is_active ? '活跃' : '禁用'"
                style="margin-left: 8px"
              />
            </div>
          </div>

          <a-tabs>
            <a-tab-pane
              key="basic"
              tab="基本信息"
            >
              <a-descriptions
                :column="2"
                bordered
              >
                <a-descriptions-item label="用户ID">{{ selectedUser.id }}</a-descriptions-item>
                <a-descriptions-item label="用户名">{{ selectedUser.username }}</a-descriptions-item>
                <a-descriptions-item label="邮箱">{{ selectedUser.email }}</a-descriptions-item>
                <a-descriptions-item label="角色">{{ getRoleLabel(selectedUser.role_type) }}</a-descriptions-item>
                <a-descriptions-item label="LDAP DN">{{ selectedUser.ldap_dn || '未配置' }}</a-descriptions-item>
                <a-descriptions-item label="状态">
                  <a-badge
                    :status="selectedUser.is_active ? 'success' : 'default'"
                    :text="selectedUser.is_active ? '活跃' : '禁用'"
                  />
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">{{ formatFullTime(selectedUser.created_at) }}</a-descriptions-item>
                <a-descriptions-item label="最后登录">{{ formatFullTime(selectedUser.last_login_at) }}</a-descriptions-item>
                <a-descriptions-item label="登录IP">{{ selectedUser.last_login_ip || '未知' }}</a-descriptions-item>
                <a-descriptions-item label="登录次数">{{ selectedUser.login_count || 0 }}</a-descriptions-item>
              </a-descriptions>
            </a-tab-pane>

            <a-tab-pane
              key="permissions"
              tab="权限信息"
            >
              <div class="permissions-info">
                <h4>角色权限</h4>
                <div class="permission-tags">
                  <a-tag
                    v-for="permission in getUserPermissions(selectedUser)"
                    :key="permission"
                    color="blue"
                  >
                    {{ getPermissionLabel(permission) }}
                  </a-tag>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane
              key="activity"
              tab="活动记录"
            >
              <div class="activity-log">
                <a-timeline>
                  <a-timeline-item
                    v-for="activity in getUserActivities(selectedUser)"
                    :key="activity.id"
                    :color="getActivityColor(activity.type)"
                  >
                    <div class="activity-item">
                      <div class="activity-header">
                        <span class="activity-type">{{ activity.type }}</span>
                        <span class="activity-time">{{ formatTime(activity.timestamp) }}</span>
                      </div>
                      <div class="activity-description">{{ activity.description }}</div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-modal>

    <!-- 添加/编辑用户模态框 -->
    <a-modal
      v-model:open="showUserModal"
      :title="editingUser ? '编辑用户' : '添加用户'"
      width="600px"
      @ok="handleSaveUser"
      @cancel="handleCancelUser"
    >
      <a-form
        ref="userForm"
        :model="userFormData"
        :rules="userFormRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="用户名"
              name="username"
            >
              <a-input
                v-model:value="userFormData.username"
                placeholder="请输入用户名"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="邮箱"
              name="email"
            >
              <a-input
                v-model:value="userFormData.email"
                placeholder="请输入邮箱"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="角色"
              name="role_type"
            >
              <a-select
                v-model:value="userFormData.role_type"
                placeholder="请选择角色"
              >
                <a-select-option value="personal">个人用户</a-select-option>
                <a-select-option value="enterprise_admin">企业管理员</a-select-option>
                <a-select-option value="system_admin">系统管理员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item>
              <a-checkbox v-model:checked="userFormData.is_active"> 启用用户 </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item
          label="LDAP DN"
          name="ldap_dn"
        >
          <a-input
            v-model:value="userFormData.ldap_dn"
            placeholder="cn=user,ou=users,dc=company,dc=com"
          />
        </a-form-item>
        <a-form-item
          v-if="!editingUser"
          label="密码"
          name="password"
        >
          <a-input-password
            v-model:value="userFormData.password"
            placeholder="请输入密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, notification } from 'ant-design-vue'
import {
  Users,
  Upload,
  UserPlus,
  UserCheck,
  Crown,
  LogIn,
  RotateCcw,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal,
  Key,
  Power,
  Monitor,
  Trash2
} from 'lucide-vue-next'
import { formatDistanceToNow, format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 用户管理页面
 * 功能：管理系统用户账户、角色分配和权限审核
 * 依赖：Ant Design Vue、Lucide Vue Next、date-fns
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const showUserDetailsModal = ref(false)
const showUserModal = ref(false)
const selectedUser = ref(null)
const editingUser = ref(null)

// 筛选表单
const filterForm = reactive({
  keyword: '',
  role: '',
  status: '',
  dateRange: null
})

// 用户数据
const users = ref([
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role_type: 'system_admin',
    is_active: true,
    ldap_dn: 'cn=admin,ou=admins,dc=company,dc=com',
    created_at: new Date('2025-01-01T00:00:00Z'),
    last_login_at: new Date('2025-01-15T10:30:00Z'),
    last_login_ip: '*************',
    login_count: 245
  },
  {
    id: '2',
    username: 'john.doe',
    email: '<EMAIL>',
    role_type: 'enterprise_admin',
    is_active: true,
    ldap_dn: 'cn=john.doe,ou=users,dc=company,dc=com',
    created_at: new Date('2025-01-05T00:00:00Z'),
    last_login_at: new Date('2025-01-15T09:15:00Z'),
    last_login_ip: '*************',
    login_count: 89
  },
  {
    id: '3',
    username: 'jane.smith',
    email: '<EMAIL>',
    role_type: 'personal',
    is_active: true,
    ldap_dn: null,
    created_at: new Date('2025-01-10T00:00:00Z'),
    last_login_at: new Date('2025-01-14T16:45:00Z'),
    last_login_ip: '*************',
    login_count: 34
  },
  {
    id: '4',
    username: 'bob.wilson',
    email: '<EMAIL>',
    role_type: 'personal',
    is_active: false,
    ldap_dn: null,
    created_at: new Date('2025-01-12T00:00:00Z'),
    last_login_at: new Date('2025-01-13T14:20:00Z'),
    last_login_ip: '*************',
    login_count: 12
  }
])

// 表单数据
const userFormData = reactive({
  username: '',
  email: '',
  role_type: '',
  is_active: true,
  ldap_dn: '',
  password: ''
})

// 表单验证规则
const userFormRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
  role_type: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 表格列配置
const userColumns = [
  {
    title: '用户',
    key: 'user',
    width: 200
  },
  {
    title: '角色',
    key: 'role',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '最后登录',
    key: 'last_login',
    width: 150
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 表格分页配置
const tablePagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 计算属性
const activeUsersCount = computed(() => {
  return users.value.filter((user) => user.is_active).length
})

const adminUsersCount = computed(() => {
  return users.value.filter((user) => user.role_type.includes('admin')).length
})

const todayLoginCount = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return users.value.filter((user) => {
    if (!user.last_login_at) return false
    const loginDate = new Date(user.last_login_at)
    return loginDate >= today
  }).length
})

const filteredUsers = computed(() => {
  let filtered = users.value

  // 关键词搜索
  if (filterForm.keyword) {
    const keyword = filterForm.keyword.toLowerCase()
    filtered = filtered.filter((user) => user.username.toLowerCase().includes(keyword) || user.email.toLowerCase().includes(keyword))
  }

  // 角色过滤
  if (filterForm.role) {
    filtered = filtered.filter((user) => user.role_type === filterForm.role)
  }

  // 状态过滤
  if (filterForm.status) {
    const isActive = filterForm.status === 'active'
    filtered = filtered.filter((user) => user.is_active === isActive)
  }

  // 时间范围过滤
  if (filterForm.dateRange && filterForm.dateRange.length === 2) {
    const [start, end] = filterForm.dateRange
    filtered = filtered.filter((user) => {
      const createTime = new Date(user.created_at)
      return createTime >= start && createTime <= end
    })
  }

  return filtered
})

const paginatedUsers = computed(() => {
  const start = (tablePagination.current - 1) * tablePagination.pageSize
  const end = start + tablePagination.pageSize
  return filteredUsers.value.slice(start, end)
})

// 方法
const formatTime = (date: Date) => {
  if (!date) return '从未登录'
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const formatDate = (date: Date) => {
  return format(date, 'yyyy-MM-dd', { locale: zhCN })
}

const formatFullTime = (date: Date) => {
  if (!date) return '未知'
  return format(date, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
}

const getRoleColor = (role: string) => {
  const colors = {
    system_admin: 'red',
    enterprise_admin: 'orange',
    personal: 'blue'
  }
  return colors[role] || 'default'
}

const getRoleLabel = (role: string) => {
  const labels = {
    system_admin: '系统管理员',
    enterprise_admin: '企业管理员',
    personal: '个人用户'
  }
  return labels[role] || role
}

const getPermissionLabel = (permission: string) => {
  // 这里应该从权限配置中获取标签
  return permission
}

const getUserPermissions = (user: any) => {
  // 根据用户角色返回权限列表
  const rolePermissions = {
    system_admin: ['user.manage', 'system.config', 'security.audit'],
    enterprise_admin: ['resource.manage', 'sync.config'],
    personal: ['resource.personal']
  }
  return rolePermissions[user.role_type] || []
}

const getUserActivities = (user: any) => {
  // 模拟用户活动记录
  return [
    {
      id: '1',
      type: '登录',
      description: '用户登录系统',
      timestamp: new Date('2025-01-15T10:30:00Z')
    },
    {
      id: '2',
      type: '创建资源',
      description: '创建了新的主机配置',
      timestamp: new Date('2025-01-15T09:15:00Z')
    }
  ]
}

const getActivityColor = (type: string) => {
  const colors = {
    登录: 'green',
    创建资源: 'blue',
    删除资源: 'red',
    修改配置: 'orange'
  }
  return colors[type] || 'gray'
}

const handleSearch = () => {
  handleFilterChange()
}

const handleFilterChange = () => {
  tablePagination.current = 1
  tablePagination.total = filteredUsers.value.length
}

const resetFilters = () => {
  Object.assign(filterForm, {
    keyword: '',
    role: '',
    status: '',
    dateRange: null
  })
  handleFilterChange()
}

const handleTableChange = (pagination: any) => {
  tablePagination.current = pagination.current
  tablePagination.pageSize = pagination.pageSize
}

const refreshUsers = async () => {
  refreshing.value = true
  try {
    // 模拟刷新数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
    message.success('用户列表刷新成功')
  } finally {
    refreshing.value = false
  }
}

const exportUsers = () => {
  // 实现导出用户功能
  const data = filteredUsers.value.map((user) => ({
    用户名: user.username,
    邮箱: user.email,
    角色: getRoleLabel(user.role_type),
    状态: user.is_active ? '活跃' : '禁用',
    创建时间: formatDate(user.created_at),
    最后登录: formatFullTime(user.last_login_at)
  }))

  message.success(`导出 ${data.length} 个用户信息`)
}

const importUsers = () => {
  // 实现批量导入用户功能
  message.info('批量导入功能开发中')
}

const showAddUserModal = () => {
  editingUser.value = null
  resetUserForm()
  showUserModal.value = true
}

const viewUser = (user: any) => {
  selectedUser.value = user
  showUserDetailsModal.value = true
}

const editUser = (user: any) => {
  editingUser.value = user
  Object.assign(userFormData, user)
  showUserModal.value = true
}

const resetPassword = (user: any) => {
  message.success(`${user.username} 的密码重置成功`)
}

const toggleUserStatus = (user: any) => {
  user.is_active = !user.is_active
  message.success(`用户已${user.is_active ? '启用' : '禁用'}`)
}

const viewUserSessions = (user: any) => {
  message.info(`查看 ${user.username} 的活跃会话`)
}

const deleteUser = (user: any) => {
  const index = users.value.findIndex((u) => u.id === user.id)
  if (index > -1) {
    users.value.splice(index, 1)
    message.success('用户删除成功')
  }
}

const resetUserForm = () => {
  Object.assign(userFormData, {
    username: '',
    email: '',
    role_type: '',
    is_active: true,
    ldap_dn: '',
    password: ''
  })
}

const handleSaveUser = () => {
  if (editingUser.value) {
    // 更新现有用户
    Object.assign(editingUser.value, userFormData)
    message.success('用户信息更新成功')
  } else {
    // 添加新用户
    const newUser = {
      ...userFormData,
      id: Date.now().toString(),
      created_at: new Date(),
      last_login_at: null,
      last_login_ip: null,
      login_count: 0
    }
    users.value.push(newUser)
    message.success('用户创建成功')
  }
  showUserModal.value = false
}

const handleCancelUser = () => {
  showUserModal.value = false
  resetUserForm()
}

// 生命周期
onMounted(() => {
  tablePagination.total = filteredUsers.value.length
})
</script>

<style scoped>
.user-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  margin-right: 12px;
  color: #1890ff;
}

.page-description {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.user-stats {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.stat-icon {
  width: 20px;
  height: 20px;
}

.filter-section {
  margin-bottom: 24px;
}

.users-section {
  margin-bottom: 24px;
}

.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.users-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-count {
  color: #6b7280;
  font-size: 14px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  flex: 1;
}

.username {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.email {
  font-size: 12px;
  color: #6b7280;
}

.login-info {
  text-align: center;
}

.login-time {
  font-size: 12px;
  color: #1f2937;
}

.login-ip {
  font-size: 11px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.action-icon {
  width: 14px;
  height: 14px;
}

.menu-icon {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}

.danger-item {
  color: #ef4444;
}

.user-details {
  padding: 8px;
}

.user-profile {
  margin-bottom: 24px;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.profile-info {
  margin-left: 20px;
  flex: 1;
}

.profile-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.profile-email {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 16px;
}

.permissions-info h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.permission-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.activity-log {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  margin-bottom: 8px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.activity-type {
  font-weight: 500;
  color: #1f2937;
}

.activity-time {
  font-size: 12px;
  color: #6b7280;
}

.activity-description {
  color: #6b7280;
  font-size: 14px;
}
</style>
