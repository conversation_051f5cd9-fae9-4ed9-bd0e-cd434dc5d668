<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">终端设置</span>
          </template>
        </a-form-item>

        <a-form-item
          label="终端类型"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.terminalType"
            class="terminal-type-select"
          >
            <a-select-option value="xterm">xterm</a-select-option>
            <a-select-option value="xterm-256color">xterm-256color</a-select-option>
            <a-select-option value="vt100">vt100</a-select-option>
            <a-select-option value="vt102">vt102</a-select-option>
            <a-select-option value="vt220">vt220</a-select-option>
            <a-select-option value="vt320">vt320</a-select-option>
            <a-select-option value="linux">linux</a-select-option>
            <a-select-option value="scoansi">scoansi</a-select-option>
            <a-select-option value="ansi">ansi</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="字体"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.fontFamily"
            class="font-family-select"
            :options="fontFamilyOptions"
          />
        </a-form-item>

        <a-form-item
          label="字体大小(px)"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.fontSize"
            :bordered="false"
            style="width: 20%"
            :min="8"
            :max="64"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>

        <a-form-item
          label="终端回滚"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.scrollBack"
            :bordered="false"
            style="width: 20%"
            :min="1"
            class="user_my-ant-form-item-content"
          />
        </a-form-item>

        <a-form-item
          label="光标样式"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.cursorStyle"
            class="custom-radio-group"
          >
            <a-radio value="bar">条</a-radio>
            <a-radio value="block">实线</a-radio>
            <a-radio value="underline">下划线</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item
          label="SSH Agents"
          class="user_my-ant-form-item"
        >
          <a-switch
            :checked="userConfig.sshAgentsStatus === 1"
            class="user_my-ant-form-item-content"
            @change="handleSshAgentsStatusChange"
          />
        </a-form-item>

        <a-form-item
          label="鼠标事件"
          class="user_my-ant-form-item"
        >
          <div class="mouse-events-container">
            <div class="mouse-event-row">
              <span class="mouse-event-label">鼠标右键事件:</span>
              <a-select
                v-model:value="userConfig.middleMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="paste">粘贴内容</a-select-option>
                <a-select-option value="contextMenu">右键菜单</a-select-option>
                <a-select-option value="none">无</a-select-option>
              </a-select>
            </div>
            <div class="mouse-event-row">
              <span class="mouse-event-label">鼠标右键事件:</span>
              <a-select
                v-model:value="userConfig.rightMouseEvent"
                class="mouse-event-select"
              >
                <a-select-option value="contextMenu">右键菜单</a-select-option>
                <a-select-option value="paste">粘贴内容</a-select-option>
                <a-select-option value="none">无</a-select-option>
              </a-select>
            </div>
          </div>
        </a-form-item>

        <!-- 终端背景设置 -->
        <a-form-item
          label="终端背景"
          class="user_my-ant-form-item"
        >
          <div class="terminal-background-container">
            <div class="background-color-row">
              <span class="background-label">背景颜色:</span>
              <a-color-picker
                v-model:value="userConfig.terminalBackground.color"
                class="background-color-picker"
              />
            </div>
            <div class="background-opacity-row">
              <span class="background-label">背景透明度:</span>
              <a-slider
                v-model:value="userConfig.terminalBackground.opacity"
                :min="0.1"
                :max="1"
                :step="0.1"
                class="opacity-slider"
              />
            </div>
          </div>
        </a-form-item>

        <!-- 背景预设 -->
        <a-form-item
          :label="'背景预设'"
          class="user_my-ant-form-item"
        >
          <div class="background-presets-container">
            <div class="preset-grid">
              <div
                v-for="preset in backgroundPresets"
                :key="preset.name"
                class="preset-item"
                :class="{ active: isPresetActive(preset) }"
                @click="applyBackgroundPreset(preset)"
              >
                <div
                  class="preset-preview"
                  :style="getPresetStyle(preset)"
                ></div>
                <span class="preset-name">{{ preset.name }}</span>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { userConfigStore } from '@/services/userConfigStoreService'
import eventBus from '@/utils/eventBus'

const { t } = useI18n()

// 字体选项
const fontFamilyOptions = ref([
  { value: 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace', label: 'Menlo (默认)' },
  { value: 'Monaco, "Courier New", Consolas, Courier, monospace', label: 'Monaco' },
  { value: 'Consolas, "Courier New", Courier, monospace', label: 'Consolas' },
  { value: '"Courier New", Courier, monospace', label: 'Courier New' },
  { value: '"SF Mono", Monaco, Menlo, Consolas, "Ubuntu Mono", monospace', label: 'SF Mono' },
  { value: '"JetBrains Mono", Monaco, Menlo, Consolas, monospace', label: 'JetBrains Mono' }
])

// 用户配置
const userConfig = ref({
  fontSize: 12,
  fontFamily: 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace',
  scrollBack: 1000,
  cursorStyle: 'block',
  middleMouseEvent: 'paste',
  rightMouseEvent: 'contextMenu',
  terminalType: 'vt100',
  sshAgentsStatus: 2,
  terminalBackground: {
    type: 'default',
    color: 'transparent',
    imageUrl: '',
    opacity: 1.0
  },
  themeColors: {
    primaryColor: '#1890ff',
    accentColor: '#52c41a',
    backgroundColor: '#141414',
    surfaceColor: '#1f1f1f',
    textColor: '#ffffff',
    borderColor: '#303030'
  }
})

/**
 * 简化的终端背景预设
 * 只包含纯色背景，确保稳定性和可靠性
 * 包含深色和浅色主题，确保可访问性和视觉舒适度
 */
const backgroundPresets = ref([
  // === 深色主题系列 ===
  {
    name: '经典深色',
    type: 'color',
    color: '#0d1117', // GitHub深色主题色
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '经典的深色背景，护眼且专业'
  },
  {
    name: '午夜蓝',
    type: 'color',
    color: '#1e293b', // Tailwind slate-800
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '深邃的蓝色调，适合长时间编程'
  },
  {
    name: '暗夜森林',
    type: 'color',
    color: '#14532d', // Tailwind green-900
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '深绿色调，自然舒缓'
  },
  {
    name: '深紫魅影',
    type: 'color',
    color: '#581c87', // Tailwind purple-900
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '神秘的紫色，激发创造力'
  },
  {
    name: '炭墨黑',
    type: 'color',
    color: '#18181b', // Tailwind zinc-900
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '纯净的深色，极简主义'
  },
  {
    name: '赛博朋克',
    type: 'color',
    color: '#0c1222',
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '未来科技感的深蓝色'
  },
  {
    name: '深海蓝',
    type: 'color',
    color: '#0f172a',
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '深邃的海洋蓝色'
  },
  {
    name: '暗红酒',
    type: 'color',
    color: '#7f1d1d',
    imageUrl: '',
    opacity: 1.0,
    category: 'dark',
    description: '优雅的暗红色调'
  },
  // === 浅色主题系列 ===
  {
    name: '纯净白',
    type: 'color',
    color: '#fafafa', // 更温和的白色
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '纯净优雅的白色背景'
  },
  {
    name: '晨雾灰',
    type: 'color',
    color: '#f1f5f9', // Tailwind slate-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '温和的浅灰色，护眼舒适'
  },
  {
    name: '云朵白',
    type: 'color',
    color: '#f8fafc', // Tailwind slate-25
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '如云朵般轻盈的白色'
  },
  {
    name: '天空蓝',
    type: 'color',
    color: '#eff6ff', // Tailwind blue-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '清新的天空蓝色调'
  },
  {
    name: '薄荷绿',
    type: 'color',
    color: '#f0fdf4', // Tailwind green-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '清新的薄荷绿，自然护眼'
  },
  {
    name: '淡雅紫',
    type: 'color',
    color: '#faf5ff', // Tailwind purple-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '优雅的淡紫色调'
  },
  {
    name: '樱花粉',
    type: 'color',
    color: '#fdf2f8', // Tailwind pink-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '温柔的樱花粉色'
  },
  {
    name: '暖阳橙',
    type: 'color',
    color: '#fffbeb', // Tailwind amber-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '温暖的阳光橙色'
  },
  {
    name: '柠檬黄',
    type: 'color',
    color: '#fefce8', // Tailwind yellow-50
    imageUrl: '',
    opacity: 1.0,
    category: 'light',
    description: '清新的柠檬黄色调'
  },

  {
    name: '樱花飘落',
    type: 'image',
    color: '#fff0f5',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjZmZmMGY1Ii8+CjxjaXJjbGUgY3g9IjEwIiBjeT0iMTAiIHI9IjMiIGZpbGw9IiNmZmI3Yzc"IG9wYWNpdHk9IjAuNiIvPgo8Y2lyY2xlIGN4PSI0MCIgY3k9IjE1IiByPSIyIiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjUiLz4KPGNpcmNsZSBjeD0iMjUiIGN5PSIzNSIgcj0iMi41IiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjciLz4KPGNpcmNsZSBjeD0iOCIgY3k9IjQwIiByPSIyIiBmaWxsPSIjZmZiN2M3IiBvcGFjaXR5PSIwLjQiLz4KPC9zdmc+Cg==',
    opacity: 0.9
  },
  {
    name: '云朵',
    type: 'image',
    color: '#f0f8ff',
    imageUrl:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjBmOGZmIi8+CjxlbGxpcHNlIGN4PSIyMCIgY3k9IjE1IiByeD0iMTIiIHJ5PSI4IiBmaWxsPSIjZTNmMmZkIiBvcGFjaXR5PSIwLjciLz4KPGVsbGlwc2UgY3g9IjQ1IiBjeT0iMjUiIHJ4PSIxMCIgcnk9IjYiIGZpbGw9IiNlM2YyZmQiIG9wYWNpdHk9IjAuNiIvPgo8ZWxsaXBzZSBjeD0iMTAiIGN5PSIzMCIgcng9IjgiIHJ5PSI1IiBmaWxsPSIjZTNmMmZkIiBvcGFjaXR5PSIwLjUiLz4KPC9zdmc+Cg==',
    opacity: 0.8
  }
])

// SSH Agents 状态变更处理
const handleSshAgentsStatusChange = (checked) => {
  userConfig.value.sshAgentsStatus = checked ? 1 : 2
}

// 图片上传处理
const handleImageUpload = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    userConfig.value.terminalBackground.imageUrl = e.target.result
  }
  reader.readAsDataURL(file)
  return false // 阻止默认上传行为
}

// 背景预设相关函数
const isPresetActive = (preset) => {
  const bg = userConfig.value.terminalBackground
  return bg.type === preset.type && bg.color === preset.color && bg.imageUrl === preset.imageUrl
}

const getPresetStyle = (preset) => {
  if (preset.type === 'image') {
    return {
      backgroundImage: `url(${preset.imageUrl})`,
      backgroundColor: preset.color,
      backgroundSize: 'cover',
      backgroundRepeat: 'repeat'
    }
  } else if (preset.type === 'color') {
    return {
      backgroundColor: preset.color
    }
  } else {
    return {
      backgroundColor: '#1a1a1a'
    }
  }
}

const applyBackgroundPreset = async (preset) => {
  console.log('Applying background preset:', preset)
  userConfig.value.terminalBackground = {
    type: preset.type,
    color: preset.color,
    imageUrl: preset.imageUrl,
    opacity: preset.opacity
  }

  // 立即保存配置并通知终端组件
  await saveConfig()

  console.log('Background preset applied:', preset.name, 'Config:', userConfig.value.terminalBackground)
}

// 加载保存的配置
const loadSavedConfig = async () => {
  try {
    const config = await userConfigStore.getConfig()
    if (config) {
      userConfig.value = {
        fontSize: config.fontSize || 12,
        fontFamily: config.fontFamily || 'Menlo, Monaco, "Courier New", Consolas, Courier, monospace',
        scrollBack: config.scrollBack || 1000,
        cursorStyle: config.cursorStyle || 'block',
        middleMouseEvent: config.middleMouseEvent || 'paste',
        rightMouseEvent: config.rightMouseEvent || 'contextMenu',
        terminalType: config.terminalType || 'vt100',
        sshAgentsStatus: config.sshAgentsStatus || 2,
        // 确保terminalBackground字段正确初始化
        terminalBackground: config.terminalBackground || {
          type: 'default',
          color: '#1a1a1a',
          imageUrl: '',
          opacity: 1.0
        },
        // 确保themeColors字段正确初始化
        themeColors: config.themeColors || {
          primaryColor: '#1890ff',
          accentColor: '#52c41a',
          backgroundColor: '#141414',
          surfaceColor: '#1f1f1f',
          textColor: '#ffffff',
          borderColor: '#303030'
        }
      }
    }
  } catch (error) {
    console.error('Failed to load config:', error)
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    await userConfigStore.saveConfig(userConfig.value)

    // 发送事件通知终端组件更新背景
    try {
      eventBus.emit('updateTerminalBackground', userConfig.value.terminalBackground)
      console.log('Terminal background update event sent')
    } catch (eventError) {
      console.warn('Failed to send terminal background update event:', eventError)
    }

    console.log('Terminal config saved successfully')
  } catch (error) {
    console.error('Failed to save terminal config:', error)
  }
}

// 监听配置变化并自动保存
watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

// 监听字体变化
watch(
  () => userConfig.value.fontFamily,
  (newFontFamily) => {
    try {
      eventBus.emit('updateTerminalFont', newFontFamily)
    } catch (error) {
      console.warn('Failed to send font update event:', error)
    }
  }
)

onMounted(async () => {
  console.log('Terminal settings component mounted')
  await loadSavedConfig()
})
</script>

<style scoped>
/* 使用与原组件相同的样式 */
.userInfo {
  height: 100%;
  overflow-y: auto;
}

.userInfo-container {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.custom-form {
  color: var(--text-color) !important;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color) !important;
}

.label-text {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
}

.user_my-ant-form-item {
  margin-bottom: 16px;
}

.user_my-ant-form-item :deep(.ant-form-item-label) {
  text-align: left;
  padding-right: 8px;
}

.user_my-ant-form-item :deep(.ant-form-item-control) {
  text-align: right;
}

.user_my-ant-form-item-content {
  background-color: var(--bg-color-secondary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.terminal-type-select,
.font-family-select,
.mouse-event-select {
  min-width: 150px;
}

.custom-radio-group :deep(.ant-radio-wrapper) {
  color: var(--text-color) !important;
}

.mouse-events-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mouse-event-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mouse-event-label {
  min-width: 100px;
  color: var(--text-color);
  font-size: 12px;
}

.mouse-event-select {
  flex: 1;
  max-width: 120px;
}

/* 终端背景设置样式 */
.terminal-background-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.background-type-row,
.background-color-row,
.background-image-row,
.background-opacity-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.background-label {
  min-width: 120px;
  color: var(--text-color);
  font-size: 12px;
}

.background-type-select {
  min-width: 150px;
}

.color-picker {
  width: 50px;
  height: 30px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
}

.image-url-input {
  flex: 1;
  max-width: 200px;
}

.upload-button {
  margin-left: 8px;
}

.opacity-slider {
  flex: 1;
  max-width: 150px;
}

/* 背景预设样式 */
.background-presets-container {
  width: 100%;
}

.preset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(75px, 1fr));
  gap: 10px;
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preset-item:hover {
  border-color: var(--primary-color, #1890ff);
  background-color: var(--bg-color-secondary);
}

.preset-item.active {
  border-color: var(--primary-color, #1890ff);
  background-color: var(--bg-color-secondary);
}

.preset-preview {
  width: 45px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-size: cover;
  background-repeat: repeat;
  background-position: center;
}

.preset-name {
  font-size: 10px;
  color: var(--text-color);
  text-align: center;
  line-height: 1.2;
}
</style>
