<!--
  文件：RoomConfigManagement.vue
  功能：机房配置管理 - 告警配置
  依赖：vue, vue-router, ant-design-vue
  作者：AI 助手
  修改时间：自动生成
-->
<template>
  <div class="config-management">
    <!-- 页面头部 -->
    <header class="page-header">
      <div class="header-left">
        <button
          class="back-btn"
          @click="goBack"
        >
          <svg
            class="back-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
          返回机房管理
        </button>
      </div>
      <div class="header-center">
        <h1 class="page-title">机房配置管理</h1>
        <p class="page-subtitle">告警阈值 · 通知策略 · 监控配置</p>
      </div>
      <div class="header-actions">
        <button
          class="action-btn primary"
          @click="showAddAlertModal = true"
        >
          <svg
            class="btn-icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M12 5v14M5 12h14" />
          </svg>
          新增告警规则
        </button>
      </div>
    </header>

    <!-- 配置内容 -->
    <div class="config-content">
      <!-- 告警规则列表 -->
      <div class="config-section">
        <div class="section-header">
          <h2 class="section-title">告警规则配置</h2>
          <span class="rule-count">共 {{ alertRules.length }} 条规则</span>
        </div>

        <div class="rules-table">
          <div class="table-header">
            <div class="header-cell">规则名称</div>
            <div class="header-cell">监控指标</div>
            <div class="header-cell">阈值条件</div>
            <div class="header-cell">告警级别</div>
            <div class="header-cell">状态</div>
            <div class="header-cell">操作</div>
          </div>

          <div class="table-body">
            <div
              v-for="rule in alertRules"
              :key="rule.id"
              class="rule-row"
              :class="{ disabled: !rule.enabled }"
            >
              <div class="table-cell">
                <div class="rule-name">{{ rule.name }}</div>
                <div class="rule-description">{{ rule.description }}</div>
              </div>
              <div class="table-cell">
                <div class="metric-info">
                  <span class="metric-name">{{ getMetricName(rule.metric) }}</span>
                  <span class="metric-unit">{{ getMetricUnit(rule.metric) }}</span>
                </div>
              </div>
              <div class="table-cell">
                <div class="condition"> {{ rule.operator }} {{ rule.threshold }}{{ getMetricUnit(rule.metric) }} </div>
              </div>
              <div class="table-cell">
                <span
                  class="severity-badge"
                  :class="rule.severity"
                >
                  {{ getSeverityText(rule.severity) }}
                </span>
              </div>
              <div class="table-cell">
                <span
                  class="status-badge"
                  :class="{ enabled: rule.enabled }"
                >
                  {{ rule.enabled ? '启用' : '禁用' }}
                </span>
              </div>
              <div class="table-cell">
                <div class="action-buttons">
                  <button
                    class="action-btn small"
                    title="编辑"
                    @click="editRule(rule)"
                  >
                    <svg
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" />
                      <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" />
                    </svg>
                  </button>
                  <button
                    class="action-btn small"
                    :class="{ danger: rule.enabled }"
                    :title="rule.enabled ? '禁用' : '启用'"
                    @click="toggleRule(rule)"
                  >
                    <svg
                      v-if="rule.enabled"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path d="M10 9v6M14 9v6M4 7h16M6 7V4a1 1 0 011-1h10a1 1 0 011 1v3" />
                    </svg>
                    <svg
                      v-else
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path d="M8 6l4-4 4 4M8 18l4 4 4-4M12 2v20" />
                    </svg>
                  </button>
                  <button
                    class="action-btn small danger"
                    title="删除"
                    @click="deleteRule(rule)"
                  >
                    <svg
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const showAddAlertModal = ref(false)
const alertRules = ref([
  {
    id: '1',
    name: '温度过高告警',
    description: '机房温度超过阈值时触发告警',
    metric: 'temperature',
    operator: '>',
    threshold: 28,
    severity: 'warning',
    enabled: true,
    notificationChannels: ['email', 'sms']
  },
  {
    id: '2',
    name: '湿度异常告警',
    description: '机房湿度超出正常范围时触发告警',
    metric: 'humidity',
    operator: '>',
    threshold: 70,
    severity: 'critical',
    enabled: true,
    notificationChannels: ['email', 'sms', 'webhook']
  },
  {
    id: '3',
    name: '机柜利用率告警',
    description: '机柜利用率过高时触发告警',
    metric: 'rack_utilization',
    operator: '>',
    threshold: 90,
    severity: 'warning',
    enabled: false,
    notificationChannels: ['email']
  }
])

/**
 * 返回机房管理总览
 */
const goBack = () => {
  router.push({ name: 'RoomManagement' })
}

/**
 * 获取指标名称
 */
const getMetricName = (metric) => {
  const metricNames = {
    temperature: '温度',
    humidity: '湿度',
    rack_utilization: '机柜利用率',
    power_utilization: '电力利用率'
  }
  return metricNames[metric] || metric
}

/**
 * 获取指标单位
 */
const getMetricUnit = (metric) => {
  const metricUnits = {
    temperature: '°C',
    humidity: '%',
    rack_utilization: '%',
    power_utilization: '%'
  }
  return metricUnits[metric] || ''
}

/**
 * 获取告警级别文本
 */
const getSeverityText = (severity) => {
  const severityTexts = {
    info: '信息',
    warning: '警告',
    critical: '严重',
    emergency: '紧急'
  }
  return severityTexts[severity] || severity
}

/**
 * 编辑规则
 */
const editRule = (rule) => {
  console.log('编辑规则:', rule)
  // TODO: 实现编辑功能
}

/**
 * 切换规则状态
 */
const toggleRule = (rule) => {
  rule.enabled = !rule.enabled
  console.log('切换规则状态:', rule.name, rule.enabled ? '启用' : '禁用')
}

/**
 * 删除规则
 */
const deleteRule = (rule) => {
  if (confirm(`确定要删除告警规则"${rule.name}"吗？`)) {
    const index = alertRules.value.findIndex((r) => r.id === rule.id)
    if (index > -1) {
      alertRules.value.splice(index, 1)
      console.log('删除规则:', rule.name)
    }
  }
}

onMounted(() => {
  console.log('配置管理页面已加载')
})
</script>

<style scoped>
/* 页面整体布局 */
.config-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.header-left .back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.header-left .back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-2px);
}

.back-icon {
  width: 16px;
  height: 16px;
}

.header-center {
  text-align: center;
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.header-actions .action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 配置内容 */
.config-content {
  padding: 32px;
}

.config-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.rule-count {
  font-size: 14px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 12px;
  border-radius: 20px;
}

/* 表格样式 */
.rules-table {
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 24px;
  padding: 20px 32px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-cell {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-body {
  max-height: 600px;
  overflow-y: auto;
}

.rule-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1.5fr;
  gap: 24px;
  padding: 24px 32px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.rule-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.rule-row.disabled {
  opacity: 0.6;
}

.table-cell {
  display: flex;
  align-items: center;
  min-height: 48px;
}

.rule-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.rule-description {
  font-size: 14px;
  color: #64748b;
}

.metric-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.metric-unit {
  font-size: 12px;
  color: #9ca3af;
}

.condition {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
}

.severity-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-badge.info {
  background: #dbeafe;
  color: #1e40af;
}

.severity-badge.warning {
  background: #fef3c7;
  color: #d97706;
}

.severity-badge.critical {
  background: #fee2e2;
  color: #dc2626;
}

.severity-badge.emergency {
  background: #fdf2f8;
  color: #be185d;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.enabled {
  background: #dcfce7;
  color: #166534;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn.small {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.small:hover {
  background: #e5e7eb;
  color: #374151;
}

.action-btn.small.danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

.action-btn.small svg {
  width: 14px;
  height: 14px;
}
</style>
