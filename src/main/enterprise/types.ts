/**
 * 企业级资源共享系统 - 类型定义
 * 包含LDAP配置、认证结果、主机信息等核心类型
 */

/**
 * LDAP配置接口
 */
export interface LDAPConfig {
  enabled: boolean
  server: string
  port?: number
  baseDN: string
  bindDN?: string
  bindPassword?: string
  searchFilter?: string
  timeout?: number
}

/**
 * 认证结果接口
 */
export interface AuthResult {
  success: boolean
  user?: {
    id: string
    username: string
    email?: string
    displayName?: string
    groups?: string[]
  }
  error?: string
}

/**
 * 企业主机信息接口
 */
export interface EnterpriseHost {
  id: string
  name: string
  hostname: string
  port: number
  username?: string
  description?: string
  tags?: string[]
  department?: string
  owner?: string
  lastAccessed?: Date
  status: 'online' | 'offline' | 'unknown'
}
