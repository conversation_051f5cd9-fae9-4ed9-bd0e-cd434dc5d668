/**
 * 企业资源同步系统 - 入口文件
 * 功能：导出所有企业级模块，提供统一的访问接口
 * 依赖：所有企业级模块
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 类型定义
export * from './types/EnterpriseTypes'

// 核心管理器
export { EnterpriseConfigManager } from './manager/EnterpriseConfigManager'
export { ResourceSeparationManager } from './manager/ResourceSeparationManager'

// 核心接口
export { UnifiedConfigInterface } from './core/UnifiedConfigInterface'

// 安全模块
export { AuthenticationService } from './security/AuthenticationService'
export { PermissionService, PermissionAction } from './security/PermissionService'

// 存储适配器
export { DatabaseAdapter } from './adapters/DatabaseAdapter'
export { ConsulAdapter } from './adapters/ConsulAdapter'

// 服务层
export { EnterpriseSyncService } from './services/EnterpriseSyncService'

// 控制器层
export { EnterpriseSyncController } from './controllers/EnterpriseSyncController'

/**
 * 企业资源同步系统主类
 * 提供系统初始化和统一管理功能
 */
export class EnterpriseResourceSyncSystem {
  private static instance: EnterpriseResourceSyncSystem | null = null
  private isInitialized: boolean = false

  // 核心服务实例
  private configManager: EnterpriseConfigManager
  private separationManager: ResourceSeparationManager
  private unifiedInterface: UnifiedConfigInterface
  private authService: AuthenticationService
  private permissionService: PermissionService
  private syncService: EnterpriseSyncService
  private syncController: EnterpriseSyncController

  /**
   * 获取系统单例实例
   * @returns 系统实例
   */
  static getInstance(): EnterpriseResourceSyncSystem {
    if (!EnterpriseResourceSyncSystem.instance) {
      EnterpriseResourceSyncSystem.instance = new EnterpriseResourceSyncSystem()
    }
    return EnterpriseResourceSyncSystem.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    // 初始化所有服务实例
    this.configManager = EnterpriseConfigManager.getInstance()
    this.separationManager = ResourceSeparationManager.getInstance()
    this.unifiedInterface = UnifiedConfigInterface.getInstance()
    this.authService = AuthenticationService.getInstance()
    this.permissionService = PermissionService.getInstance()
    this.syncService = EnterpriseSyncService.getInstance()
    this.syncController = new EnterpriseSyncController()
  }

  /**
   * 初始化企业资源同步系统
   * @param config 初始化配置
   * @returns 是否初始化成功
   */
  async initialize(config?: {
    ldap?: {
      enabled: boolean
      server: string
      port?: number
      base_dn: string
      bind_dn?: string
      bind_password?: string
      user_search_filter: string
      group_search_filter?: string
      tls_enabled?: boolean
    }
    failover?: {
      enabled: boolean
      max_retries: number
      retry_delay_ms: number
      health_check_interval_ms: number
      failover_threshold: number
    }
    auto_sync?: {
      enabled: boolean
      interval_ms: number
    }
  }): Promise<boolean> {
    try {
      console.log('正在初始化企业资源同步系统...')

      // 配置LDAP（如果提供）
      if (config?.ldap) {
        this.authService.configureLDAP(config.ldap)
        console.log('LDAP配置已设置')
      }

      // 配置故障转移（如果提供）
      if (config?.failover) {
        this.unifiedInterface.updateFailoverConfig(config.failover)
        console.log('故障转移配置已设置')
      }

      // 初始化资源分离策略
      this.separationManager.updateSeparationStrategy({
        personal_backends: [], // 将在运行时配置
        enterprise_backends: [], // 将在运行时配置
        isolation_mode: 'strict'
      })

      // 启用自动同步（如果配置）
      if (config?.auto_sync?.enabled) {
        // 注意：这里需要有效的管理员令牌，实际使用时需要在用户登录后配置
        console.log('自动同步将在管理员登录后启用')
      }

      this.isInitialized = true
      console.log('企业资源同步系统初始化完成')

      return true
    } catch (error: any) {
      console.error('企业资源同步系统初始化失败:', error)
      return false
    }
  }

  /**
   * 检查系统是否已初始化
   * @returns 是否已初始化
   */
  isSystemInitialized(): boolean {
    return this.isInitialized
  }

  /**
   * 获取配置管理器
   * @returns 配置管理器实例
   */
  getConfigManager(): EnterpriseConfigManager {
    return this.configManager
  }

  /**
   * 获取资源分离管理器
   * @returns 资源分离管理器实例
   */
  getSeparationManager(): ResourceSeparationManager {
    return this.separationManager
  }

  /**
   * 获取统一配置接口
   * @returns 统一配置接口实例
   */
  getUnifiedInterface(): UnifiedConfigInterface {
    return this.unifiedInterface
  }

  /**
   * 获取认证服务
   * @returns 认证服务实例
   */
  getAuthService(): AuthenticationService {
    return this.authService
  }

  /**
   * 获取权限服务
   * @returns 权限服务实例
   */
  getPermissionService(): PermissionService {
    return this.permissionService
  }

  /**
   * 获取同步服务
   * @returns 同步服务实例
   */
  getSyncService(): EnterpriseSyncService {
    return this.syncService
  }

  /**
   * 获取同步控制器
   * @returns 同步控制器实例
   */
  getSyncController(): EnterpriseSyncController {
    return this.syncController
  }

  /**
   * 获取系统健康状态
   * @returns 健康状态信息
   */
  async getSystemHealth(): Promise<{
    overall_healthy: boolean
    components: {
      config_manager: boolean
      auth_service: boolean
      sync_service: boolean
      backends: {
        total: number
        healthy: number
        unhealthy: number
      }
    }
    last_check: string
  }> {
    try {
      // 检查后端健康状态
      const allBackends = await this.configManager.getAllBackends()
      const healthyBackends = await this.unifiedInterface.getHealthyBackends()

      const components = {
        config_manager: true, // 配置管理器总是健康的（如果能调用到这里）
        auth_service: true, // 认证服务总是健康的
        sync_service: !this.syncService.isAutoSyncActive() || true, // 同步服务健康检查
        backends: {
          total: allBackends.length,
          healthy: healthyBackends.length,
          unhealthy: allBackends.length - healthyBackends.length
        }
      }

      const overallHealthy =
        components.config_manager &&
        components.auth_service &&
        components.sync_service &&
        (components.backends.total === 0 || components.backends.healthy > 0)

      return {
        overall_healthy: overallHealthy,
        components,
        last_check: new Date().toISOString()
      }
    } catch (error) {
      return {
        overall_healthy: false,
        components: {
          config_manager: false,
          auth_service: false,
          sync_service: false,
          backends: {
            total: 0,
            healthy: 0,
            unhealthy: 0
          }
        },
        last_check: new Date().toISOString()
      }
    }
  }

  /**
   * 销毁系统，清理所有资源
   */
  async destroy(): Promise<void> {
    try {
      console.log('正在销毁企业资源同步系统...')

      // 销毁各个服务
      this.syncService.destroy()
      this.unifiedInterface.destroy()
      await this.configManager.destroy()
      this.separationManager.destroy()
      this.authService.destroy()
      this.permissionService.destroy()

      this.isInitialized = false
      console.log('企业资源同步系统已销毁')
    } catch (error) {
      console.error('销毁企业资源同步系统时出错:', error)
    }
  }
}

// 导出系统实例的便捷访问方法
export const getEnterpriseSystem = () => EnterpriseResourceSyncSystem.getInstance()

// 默认导出主系统类
export default EnterpriseResourceSyncSystem
