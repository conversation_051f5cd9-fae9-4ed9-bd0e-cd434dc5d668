import mitt from 'mitt'

/**
 * 定义事件类型
 */
export interface AppEvents {
  currentClickServer: any // 可根据实际 item 类型替换 any
  updateRightIcon: boolean // 更新右侧图标状态
  executeTerminalCommand: string // 执行终端命令
  focusActiveTerminal: void // 将焦点返回到当前活跃的终端
  getActiveTabAssetInfo: void // 请求获取当前活跃tab的资产信息
  assetInfoResult: any // 返回资产信息结果
  LocalAssetMenu: any // 更新资产目录
  SettingModelChanged: any // 设置页面模型变更事件
  AiTabModelChanged: any // AI Tab模型变更事件
  apiProviderChanged: any
  activeTabChanged: any
  chatToAi: any
  toggleMenu: any
  updateWatermark: string // 更新水印状态
  updateSecretRedaction: string // 更新密文脱敏状态
  updateDataSync: string // 更新数据同步状态
  updateTheme: string // 更新主题状态
  keyChainUpdated: void // 密钥更新事件，用于同步主机配置中的密钥选项
  aliasStatusChanged: number // 别名状态变更事件，1表示启用，2表示禁用
  openUserTab: any // 打开Tab

  // 企业资源管理模块间通信事件
  enterpriseResourceUpdated: { type: 'host' | 'keychain' | 'snippet'; data: any } // 企业资源更新事件
  enterpriseResourceDeleted: { type: 'host' | 'keychain' | 'snippet'; id: string } // 企业资源删除事件
  enterpriseResourceSelected: { type: 'host' | 'keychain' | 'snippet'; data: any } // 企业资源选择事件
  enterpriseModuleDataRequest: { module: string; dataType: string; params?: any } // 模块间数据请求
  enterpriseModuleDataResponse: { module: string; dataType: string; data: any } // 模块间数据响应
  enterpriseEnvironmentChanged: { environment: 'production' | 'staging' | 'development' } // 环境切换事件
  enterprisePermissionChanged: { userId: string; permissions: string[] } // 权限变更事件
  enterpriseSyncStatusChanged: { status: 'syncing' | 'completed' | 'failed'; progress?: number } // 同步状态变更
  enterpriseAlertTriggered: { type: 'warning' | 'error' | 'info'; message: string; module: string } // 告警事件

  // 机房管理模块事件
  roomEnvironmentUpdated: { roomId: string; data: { temperature: number; humidity: number; powerConsumption: number } } // 机房环境数据更新
  roomDeviceStatusChanged: { roomId: string; deviceId: string; status: 'online' | 'offline' | 'warning' | 'error' } // 设备状态变更
  roomAlertTriggered: { roomId: string; type: 'temperature' | 'humidity' | 'power' | 'device'; severity: 'low' | 'medium' | 'high'; message: string } // 机房告警
  roomLayoutChanged: { roomId: string; layout: any } // 机房布局变更

  // 可扩展更多事件
  [key: string | symbol]: any
}

const emitter = mitt<AppEvents>()

export default emitter
