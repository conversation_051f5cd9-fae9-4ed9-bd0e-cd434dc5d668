/**
 * 统一企业数据管理服务
 * 负责管理所有企业资源数据，支持跨模块数据共享和调用
 */

import { ref, reactive, computed } from 'vue'
import eventBus from '../utils/eventBus'

// 统一资源类型定义
export interface BaseResource {
  id: string | number
  name: string
  type: string
  status: 'online' | 'offline' | 'maintenance' | 'error' | 'normal' | 'warning' | 'critical'
  createdAt: string
  updatedAt: string
  tags?: string[]
  description?: string
}

// 主机设备资源
export interface HostResource extends BaseResource {
  type: 'host'
  ip: string
  hostname?: string
  port?: number
  username?: string
  password?: string
  privateKey?: string
  os?: string
  osVersion?: string
  architecture?: string
  location?: string
  owner?: string
  environment?: 'production' | 'staging' | 'development'
}

// 网络设备资源 - 扩展以兼容现有网络设备管理模块
export interface NetworkDevice extends BaseResource {
  type: 'network_device'
  ip: string
  deviceType: 'switch' | 'router' | 'firewall' | 'ap' | 'server' | 'storage' | 'load-balancer'
  model?: string
  serialNumber?: string
  macAddress?: string
  firmwareVersion?: string
  location?: string
  rackInfo?: string
  snmpCommunity?: string
  sshPort?: number
  managementInterface?: string
  // 兼容NetworkAssetsService字段
  lifecycle?: 'new' | 'active' | 'aging' | 'eol'
  purchaseDate?: string
  warrantyExpiry?: string
  hasBackup?: boolean
  canBackup?: boolean
  username?: string
  password?: string
  lastUpdate?: string
  // 兼容NetworkDevicesMonitor字段
  cpu?: number
  memory?: number
  latency?: number
}

// 固定资产资源 - 扩展以兼容现有assetManagementStore
export interface AssetResource extends BaseResource {
  type: 'asset'
  assetNumber: string
  category: 'computer' | 'network' | 'server' | 'storage' | 'printer' | 'furniture' | 'other'
  brand?: string
  model?: string
  serialNumber?: string
  purchaseDate?: string
  purchasePrice?: number
  warrantyExpiry?: string
  location?: string
  owner?: string
  department?: string
  assignedTo?: string // 兼容现有字段
  // 硬件配置字段 - 兼容现有assetManagementStore
  cpu?: string
  memory?: string
  storage?: string
  networkInterface?: string
  operatingSystem?: string
  ipAddress?: string
  macAddress?: string
  portCount?: string
}

// 机房资源
export interface RoomResource extends BaseResource {
  type: 'room'
  location: string
  area: number
  rackCount: number
  temperature?: number
  humidity?: number
  utilization?: number
  powerConsumption?: number
}

// 监控目标资源
export interface MonitorTarget extends BaseResource {
  type: 'monitor_target'
  targetType: 'latency' | 'bandwidth' | 'security' | 'performance'
  target: string
  protocol?: string
  interval?: number
  threshold?: number
  enabled: boolean
  lastCheck?: string
  currentValue?: number
}

// 联合资源类型
export type UnifiedResource = HostResource | NetworkDevice | AssetResource | RoomResource | MonitorTarget

// 数据存储键名
const STORAGE_KEYS = {
  hosts: 'unified_enterprise_hosts',
  networkDevices: 'unified_enterprise_network_devices',
  assets: 'unified_enterprise_assets',
  rooms: 'unified_enterprise_rooms',
  monitorTargets: 'unified_enterprise_monitor_targets'
}

class UnifiedEnterpriseDataService {
  // 数据存储
  private hosts = ref<HostResource[]>([])
  private networkDevices = ref<NetworkDevice[]>([])
  private assets = ref<AssetResource[]>([])
  private rooms = ref<RoomResource[]>([])
  private monitorTargets = ref<MonitorTarget[]>([])

  // 数据变更监听器
  private listeners = new Map<string, Function[]>()

  constructor() {
    this.loadAllData()
    this.initializeEventListeners()
  }

  // 加载所有数据
  private loadAllData() {
    this.hosts.value = this.loadFromStorage(STORAGE_KEYS.hosts, [])
    this.networkDevices.value = this.loadFromStorage(STORAGE_KEYS.networkDevices, [])
    this.assets.value = this.loadFromStorage(STORAGE_KEYS.assets, [])
    this.rooms.value = this.loadFromStorage(STORAGE_KEYS.rooms, [])
    this.monitorTargets.value = this.loadFromStorage(STORAGE_KEYS.monitorTargets, [])
  }

  // 从本地存储加载数据
  private loadFromStorage<T>(key: string, defaultValue: T): T {
    try {
      const stored = localStorage.getItem(key)
      return stored ? JSON.parse(stored) : defaultValue
    } catch (error) {
      console.error(`加载${key}数据失败:`, error)
      return defaultValue
    }
  }

  // 保存数据到本地存储
  private saveToStorage<T>(key: string, data: T) {
    try {
      localStorage.setItem(key, JSON.stringify(data))
      console.log(`${key}数据已保存到本地存储`)
    } catch (error) {
      console.error(`保存${key}数据失败:`, error)
    }
  }

  // 初始化事件监听
  private initializeEventListeners() {
    eventBus.on('unified-data-request', this.handleDataRequest.bind(this))
  }

  // 处理数据请求
  private handleDataRequest(request: { type: string; filters?: any; callback: Function }) {
    const { type, filters, callback } = request
    let data: UnifiedResource[] = []

    switch (type) {
      case 'hosts':
        data = this.getHosts(filters)
        break
      case 'network_devices':
        data = this.getNetworkDevices(filters)
        break
      case 'assets':
        data = this.getAssets(filters)
        break
      case 'rooms':
        data = this.getRooms(filters)
        break
      case 'monitor_targets':
        data = this.getMonitorTargets(filters)
        break
      case 'all_executable_targets':
        data = this.getAllExecutableTargets(filters)
        break
    }

    callback(data)
  }

  // 获取所有可执行目标（主机 + 网络设备）
  getAllExecutableTargets(filters?: any): (HostResource | NetworkDevice)[] {
    const allTargets = [
      ...this.hosts.value,
      ...this.networkDevices.value.filter(
        (device) => device.deviceType === 'server' || device.username // 有登录信息的设备
      )
    ]

    if (!filters) return allTargets

    return allTargets.filter((target) => {
      if (filters.status && target.status !== filters.status) return false
      if (filters.environment && 'environment' in target && target.environment !== filters.environment) return false
      if (filters.location && 'location' in target && target.location !== filters.location) return false
      if (filters.tags && filters.tags.length > 0) {
        const targetTags = target.tags || []
        return filters.tags.some((tag: string) => targetTags.includes(tag))
      }
      return true
    })
  }

  // 主机管理方法
  getHosts(filters?: any): HostResource[] {
    if (!filters) return this.hosts.value
    return this.hosts.value.filter((host) => this.applyFilters(host, filters))
  }

  addHost(host: Omit<HostResource, 'id' | 'createdAt' | 'updatedAt'>): HostResource {
    const newHost: HostResource = {
      ...host,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.hosts.value.push(newHost)
    this.saveToStorage(STORAGE_KEYS.hosts, this.hosts.value)
    this.notifyListeners('hosts', 'add', newHost)

    return newHost
  }

  updateHost(id: string | number, updates: Partial<HostResource>): boolean {
    const index = this.hosts.value.findIndex((h) => h.id === id)
    if (index === -1) return false

    this.hosts.value[index] = {
      ...this.hosts.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.saveToStorage(STORAGE_KEYS.hosts, this.hosts.value)
    this.notifyListeners('hosts', 'update', this.hosts.value[index])

    return true
  }

  deleteHost(id: string | number): boolean {
    const index = this.hosts.value.findIndex((h) => h.id === id)
    if (index === -1) return false

    const deletedHost = this.hosts.value.splice(index, 1)[0]
    this.saveToStorage(STORAGE_KEYS.hosts, this.hosts.value)
    this.notifyListeners('hosts', 'delete', deletedHost)

    return true
  }

  // 网络设备管理方法
  getNetworkDevices(filters?: any): NetworkDevice[] {
    if (!filters) return this.networkDevices.value
    return this.networkDevices.value.filter((device) => this.applyFilters(device, filters))
  }

  addNetworkDevice(device: Omit<NetworkDevice, 'id' | 'createdAt' | 'updatedAt'>): NetworkDevice {
    const newDevice: NetworkDevice = {
      ...device,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.networkDevices.value.push(newDevice)
    this.saveToStorage(STORAGE_KEYS.networkDevices, this.networkDevices.value)
    this.notifyListeners('network_devices', 'add', newDevice)

    // 发送跨模块数据更新事件
    eventBus.emit('enterpriseDataUpdated', {
      module: 'networkDevices',
      action: 'add',
      data: newDevice,
      timestamp: new Date().toISOString()
    })

    return newDevice
  }

  updateNetworkDevice(id: string | number, updates: Partial<NetworkDevice>): boolean {
    const index = this.networkDevices.value.findIndex((d) => d.id === id)
    if (index === -1) return false

    this.networkDevices.value[index] = {
      ...this.networkDevices.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.saveToStorage(STORAGE_KEYS.networkDevices, this.networkDevices.value)
    this.notifyListeners('network_devices', 'update', this.networkDevices.value[index])

    return true
  }

  deleteNetworkDevice(id: string | number): boolean {
    const index = this.networkDevices.value.findIndex((d) => d.id === id)
    if (index === -1) return false

    const deletedDevice = this.networkDevices.value.splice(index, 1)[0]
    this.saveToStorage(STORAGE_KEYS.networkDevices, this.networkDevices.value)
    this.notifyListeners('network_devices', 'delete', deletedDevice)

    return true
  }

  // 网络设备统计方法
  getNetworkDeviceStats() {
    const devices = this.networkDevices.value
    const stats = {
      total: devices.length,
      online: devices.filter((d) => d.status === 'online').length,
      offline: devices.filter((d) => d.status === 'offline').length,
      maintenance: devices.filter((d) => d.status === 'maintenance').length,
      byType: {} as Record<string, number>
    }

    devices.forEach((device) => {
      const type = device.deviceType
      stats.byType[type] = (stats.byType[type] || 0) + 1
    })

    return stats
  }

  // 批量操作网络设备
  batchDeleteNetworkDevices(ids: (string | number)[]): number {
    let deletedCount = 0
    ids.forEach((id) => {
      if (this.deleteNetworkDevice(id)) {
        deletedCount++
      }
    })
    return deletedCount
  }

  // 搜索网络设备
  searchNetworkDevices(query: string): NetworkDevice[] {
    const lowerQuery = query.toLowerCase()
    return this.networkDevices.value.filter(
      (device) =>
        device.name.toLowerCase().includes(lowerQuery) ||
        device.ip.includes(lowerQuery) ||
        (device.serialNumber && device.serialNumber.toLowerCase().includes(lowerQuery)) ||
        (device.location && device.location.toLowerCase().includes(lowerQuery))
    )
  }

  // 资产管理方法
  getAssets(filters?: any): AssetResource[] {
    if (!filters) return this.assets.value
    return this.assets.value.filter((asset) => this.applyFilters(asset, filters))
  }

  addAsset(asset: Omit<AssetResource, 'id' | 'createdAt' | 'updatedAt'>): AssetResource {
    const newAsset: AssetResource = {
      ...asset,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.assets.value.push(newAsset)
    this.saveToStorage(STORAGE_KEYS.assets, this.assets.value)
    this.notifyListeners('assets', 'add', newAsset)

    // 发送跨模块数据更新事件
    eventBus.emit('enterpriseDataUpdated', {
      module: 'assets',
      action: 'add',
      data: newAsset,
      timestamp: new Date().toISOString()
    })

    return newAsset
  }

  updateAsset(id: string | number, updates: Partial<AssetResource>): boolean {
    const index = this.assets.value.findIndex((a) => a.id === id)
    if (index === -1) return false

    this.assets.value[index] = {
      ...this.assets.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.saveToStorage(STORAGE_KEYS.assets, this.assets.value)
    this.notifyListeners('assets', 'update', this.assets.value[index])

    return true
  }

  deleteAsset(id: string | number): boolean {
    const index = this.assets.value.findIndex((a) => a.id === id)
    if (index === -1) return false

    const deletedAsset = this.assets.value.splice(index, 1)[0]
    this.saveToStorage(STORAGE_KEYS.assets, this.assets.value)
    this.notifyListeners('assets', 'delete', deletedAsset)

    return true
  }

  // 资产统计方法 - 兼容assetManagementStore
  getAssetStats() {
    const assets = this.assets.value
    const stats = {
      total: assets.length,
      active: assets.filter((a) => a.status === 'normal' || a.status === 'online').length,
      maintenance: assets.filter((a) => a.status === 'maintenance').length,
      idle: assets.filter((a) => a.status === 'offline').length,
      retired: assets.filter((a) => a.status === 'error' || a.status === 'critical').length,
      byCategory: {} as Record<string, number>,
      totalValue: assets.reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0),
      activeValue: assets.filter((a) => a.status === 'normal' || a.status === 'online').reduce((sum, asset) => sum + (asset.purchasePrice || 0), 0)
    }

    assets.forEach((asset) => {
      const category = asset.category
      stats.byCategory[category] = (stats.byCategory[category] || 0) + 1
    })

    return stats
  }

  // 批量操作资产
  batchDeleteAssets(ids: (string | number)[]): number {
    let deletedCount = 0
    ids.forEach((id) => {
      if (this.deleteAsset(id)) {
        deletedCount++
      }
    })
    return deletedCount
  }

  // 搜索资产
  searchAssets(query: string): AssetResource[] {
    const lowerQuery = query.toLowerCase()
    return this.assets.value.filter(
      (asset) =>
        asset.name.toLowerCase().includes(lowerQuery) ||
        asset.assetNumber.toLowerCase().includes(lowerQuery) ||
        (asset.model && asset.model.toLowerCase().includes(lowerQuery)) ||
        (asset.location && asset.location.toLowerCase().includes(lowerQuery))
    )
  }

  // 按条件筛选资产
  filterAssets(filters: { category?: string; status?: string; owner?: string }): AssetResource[] {
    return this.assets.value.filter((asset) => {
      if (filters.category && asset.category !== filters.category) return false
      if (filters.status) {
        // 状态映射兼容
        const statusMap: Record<string, string[]> = {
          active: ['normal', 'online'],
          maintenance: ['maintenance'],
          idle: ['offline'],
          retired: ['error', 'critical']
        }
        const validStatuses = statusMap[filters.status] || [filters.status]
        if (!validStatuses.includes(asset.status)) return false
      }
      if (filters.owner && asset.owner !== filters.owner && asset.assignedTo !== filters.owner) return false
      return true
    })
  }

  // 机房管理方法
  getRooms(filters?: any): RoomResource[] {
    if (!filters) return this.rooms.value
    return this.rooms.value.filter((room) => this.applyFilters(room, filters))
  }

  addRoom(room: Omit<RoomResource, 'id' | 'createdAt' | 'updatedAt'>): RoomResource {
    const newRoom: RoomResource = {
      ...room,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.rooms.value.push(newRoom)
    this.saveToStorage(STORAGE_KEYS.rooms, this.rooms.value)
    this.notifyListeners('rooms', 'add', newRoom)

    return newRoom
  }

  updateRoom(id: string | number, updates: Partial<RoomResource>): boolean {
    const index = this.rooms.value.findIndex((r) => r.id === id)
    if (index === -1) return false

    this.rooms.value[index] = {
      ...this.rooms.value[index],
      ...updates,
      updatedAt: new Date().toISOString()
    }

    this.saveToStorage(STORAGE_KEYS.rooms, this.rooms.value)
    this.notifyListeners('rooms', 'update', this.rooms.value[index])

    return true
  }

  deleteRoom(id: string | number): boolean {
    const index = this.rooms.value.findIndex((r) => r.id === id)
    if (index === -1) return false

    const deletedRoom = this.rooms.value.splice(index, 1)[0]
    this.saveToStorage(STORAGE_KEYS.rooms, this.rooms.value)
    this.notifyListeners('rooms', 'delete', deletedRoom)

    return true
  }

  // 监控目标管理方法
  getMonitorTargets(filters?: any): MonitorTarget[] {
    if (!filters) return this.monitorTargets.value
    return this.monitorTargets.value.filter((target) => this.applyFilters(target, filters))
  }

  addMonitorTarget(target: Omit<MonitorTarget, 'id' | 'createdAt' | 'updatedAt'>): MonitorTarget {
    const newTarget: MonitorTarget = {
      ...target,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    this.monitorTargets.value.push(newTarget)
    this.saveToStorage(STORAGE_KEYS.monitorTargets, this.monitorTargets.value)
    this.notifyListeners('monitor_targets', 'add', newTarget)

    return newTarget
  }

  // 通用过滤器
  private applyFilters(resource: UnifiedResource, filters: any): boolean {
    if (filters.status && resource.status !== filters.status) return false
    if (filters.type && resource.type !== filters.type) return false
    if (filters.name && !resource.name.toLowerCase().includes(filters.name.toLowerCase())) return false
    if (filters.tags && filters.tags.length > 0) {
      const resourceTags = resource.tags || []
      return filters.tags.some((tag: string) => resourceTags.includes(tag))
    }
    return true
  }

  // 监听器管理
  addListener(type: string, callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(callback)
  }

  removeListener(type: string, callback: Function) {
    const callbacks = this.listeners.get(type)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  private notifyListeners(type: string, action: string, data: any) {
    const callbacks = this.listeners.get(type)
    if (callbacks) {
      callbacks.forEach((callback) => callback({ action, data }))
    }

    // 全局事件通知
    eventBus.emit('unified-data-changed', { type, action, data })
  }

  // 数据统计
  getStatistics() {
    return {
      hosts: this.hosts.value.length,
      networkDevices: this.networkDevices.value.length,
      assets: this.assets.value.length,
      rooms: this.rooms.value.length,
      monitorTargets: this.monitorTargets.value.length,
      totalResources:
        this.hosts.value.length +
        this.networkDevices.value.length +
        this.assets.value.length +
        this.rooms.value.length +
        this.monitorTargets.value.length
    }
  }

  // 数据导出
  exportData() {
    return {
      hosts: this.hosts.value,
      networkDevices: this.networkDevices.value,
      assets: this.assets.value,
      rooms: this.rooms.value,
      monitorTargets: this.monitorTargets.value,
      exportedAt: new Date().toISOString()
    }
  }

  // 数据导入
  importData(data: any) {
    if (data.hosts) {
      this.hosts.value = data.hosts
      this.saveToStorage(STORAGE_KEYS.hosts, this.hosts.value)
    }
    if (data.networkDevices) {
      this.networkDevices.value = data.networkDevices
      this.saveToStorage(STORAGE_KEYS.networkDevices, this.networkDevices.value)
    }
    if (data.assets) {
      this.assets.value = data.assets
      this.saveToStorage(STORAGE_KEYS.assets, this.assets.value)
    }
    if (data.rooms) {
      this.rooms.value = data.rooms
      this.saveToStorage(STORAGE_KEYS.rooms, this.rooms.value)
    }
    if (data.monitorTargets) {
      this.monitorTargets.value = data.monitorTargets
      this.saveToStorage(STORAGE_KEYS.monitorTargets, this.monitorTargets.value)
    }
  }

  // 数据迁移工具方法
  migrateFromAssetManagementStore(assetStoreData: any[]): number {
    let migratedCount = 0

    assetStoreData.forEach((asset) => {
      try {
        // 转换数据格式以兼容统一服务
        const unifiedAsset: Omit<AssetResource, 'id' | 'createdAt' | 'updatedAt'> = {
          name: asset.name,
          type: 'asset',
          status: this.mapAssetStatus(asset.status),
          assetNumber: asset.assetNumber,
          category: asset.category,
          model: asset.model || '',
          assignedTo: asset.assignedTo,
          purchaseDate: asset.purchaseDate,
          purchasePrice: asset.purchasePrice,
          description: asset.description,
          // 硬件配置字段
          cpu: asset.cpu,
          memory: asset.memory,
          storage: asset.storage,
          networkInterface: asset.networkInterface,
          operatingSystem: asset.operatingSystem,
          ipAddress: asset.ipAddress,
          macAddress: asset.macAddress,
          portCount: asset.portCount,
          tags: []
        }

        this.addAsset(unifiedAsset)
        migratedCount++
      } catch (error) {
        console.error('迁移资产数据失败:', asset, error)
      }
    })

    console.log(`成功迁移 ${migratedCount} 个资产数据`)
    return migratedCount
  }

  migrateFromNetworkAssetsService(networkAssets: any[]): number {
    let migratedCount = 0

    networkAssets.forEach((asset) => {
      try {
        // 转换数据格式以兼容统一服务
        const unifiedDevice: Omit<NetworkDevice, 'id' | 'createdAt' | 'updatedAt'> = {
          name: asset.name,
          type: 'network_device',
          status: asset.status,
          ip: asset.ip,
          deviceType: asset.type,
          model: asset.model,
          serialNumber: asset.serialNumber,
          macAddress: asset.macAddress,
          firmwareVersion: asset.firmwareVersion,
          location: asset.location,
          rackInfo: asset.rackInfo,
          snmpCommunity: asset.snmpCommunity,
          sshPort: asset.sshPort,
          lifecycle: asset.lifecycle,
          purchaseDate: asset.purchaseDate,
          warrantyExpiry: asset.warrantyExpiry,
          hasBackup: asset.hasBackup,
          canBackup: asset.canBackup,
          username: asset.username,
          password: asset.password,
          lastUpdate: asset.lastUpdate,
          description: asset.description,
          tags: []
        }

        this.addNetworkDevice(unifiedDevice)
        migratedCount++
      } catch (error) {
        console.error('迁移网络设备数据失败:', asset, error)
      }
    })

    console.log(`成功迁移 ${migratedCount} 个网络设备数据`)
    return migratedCount
  }

  // 状态映射辅助方法
  private mapAssetStatus(oldStatus: string): string {
    const statusMap: Record<string, string> = {
      active: 'online',
      maintenance: 'maintenance',
      idle: 'offline',
      retired: 'error'
    }
    return statusMap[oldStatus] || 'normal'
  }

  // 清空所有数据
  clearAllData() {
    this.hosts.value = []
    this.networkDevices.value = []
    this.assets.value = []
    this.rooms.value = []
    this.monitorTargets.value = []

    // 清空本地存储
    Object.values(STORAGE_KEYS).forEach((key) => {
      localStorage.removeItem(key)
    })

    console.log('所有企业数据已清空')
  }
}

// 创建单例实例
export const unifiedDataService = new UnifiedEnterpriseDataService()
export default unifiedDataService
