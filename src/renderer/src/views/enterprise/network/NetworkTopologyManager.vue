<template>
  <div
    class="network-topology-manager"
    style="min-height: 100vh; overflow-y: auto; overflow-x: hidden; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Network class="title-icon" />
            网络拓扑可视化管理
          </h1>
          <p class="page-description">交互式网络拓扑图、设备连接关系和路径分析</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshTopology">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新拓扑
        </a-button>
        <a-button @click="addDevice">
          <Plus class="btn-icon" />
          添加设备
        </a-button>
        <a-button @click="autoDiscovery">
          <Search class="btn-icon" />
          自动发现
        </a-button>
        <a-button
          type="primary"
          @click="exportTopology"
        >
          <Download class="btn-icon" />
          导出拓扑
        </a-button>
      </div>
    </div>

    <!-- 拓扑工具栏 -->
    <div class="topology-toolbar">
      <div class="toolbar-left">
        <a-radio-group
          v-model:value="viewMode"
          button-style="solid"
          @change="changeViewMode"
        >
          <a-radio-button value="logical">逻辑视图</a-radio-button>
          <a-radio-button value="physical">物理视图</a-radio-button>
          <a-radio-button value="layer3">三层视图</a-radio-button>
        </a-radio-group>
      </div>
      <div class="toolbar-center">
        <a-space>
          <a-button
            size="small"
            @click="zoomIn"
          >
            <ZoomIn class="btn-icon" />
          </a-button>
          <a-button
            size="small"
            @click="zoomOut"
          >
            <ZoomOut class="btn-icon" />
          </a-button>
          <a-button
            size="small"
            @click="resetZoom"
          >
            <RotateCcw class="btn-icon" />
            重置
          </a-button>
        </a-space>
      </div>
      <div class="toolbar-right">
        <a-select
          v-model:value="selectedLayout"
          style="width: 120px"
          @change="changeLayout"
        >
          <a-select-option value="force">力导向</a-select-option>
          <a-select-option value="hierarchical">层次布局</a-select-option>
          <a-select-option value="circular">环形布局</a-select-option>
          <a-select-option value="grid">网格布局</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 拓扑图区域 -->
      <div class="topology-container">
        <div
          class="topology-canvas"
          ref="topologyCanvas"
        >
          <!-- 这里将集成网络拓扑可视化组件 -->
          <div class="topology-placeholder">
            <Network class="topology-icon" />
            <h3>网络拓扑图</h3>
            <p>{{ topologyStats.deviceCount }} 个设备，{{ topologyStats.connectionCount }} 个连接</p>
          </div>
        </div>

        <!-- 拓扑图例 -->
        <div class="topology-legend">
          <h4>设备图例</h4>
          <div class="legend-items">
            <div
              v-for="legend in deviceLegends"
              :key="legend.type"
              class="legend-item"
            >
              <div
                class="legend-icon"
                :class="legend.type"
              ></div>
              <span class="legend-label">{{ legend.label }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边面板 -->
      <div class="side-panel">
        <!-- 设备详情 -->
        <div class="panel-section">
          <h3 class="section-title">设备详情</h3>
          <div
            v-if="selectedDevice"
            class="device-details"
          >
            <div class="device-header">
              <component
                :is="getDeviceIcon(selectedDevice.type)"
                class="device-icon"
              />
              <div class="device-info">
                <h4>{{ selectedDevice.name }}</h4>
                <p>{{ selectedDevice.ip }}</p>
              </div>
            </div>
            <div class="device-properties">
              <div class="property-item">
                <span class="property-label">类型:</span>
                <span class="property-value">{{ getDeviceTypeLabel(selectedDevice.type) }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">状态:</span>
                <a-tag :color="getStatusColor(selectedDevice.status)">
                  {{ getStatusLabel(selectedDevice.status) }}
                </a-tag>
              </div>
              <div class="property-item">
                <span class="property-label">位置:</span>
                <span class="property-value">{{ selectedDevice.location }}</span>
              </div>
              <div class="property-item">
                <span class="property-label">连接数:</span>
                <span class="property-value">{{ selectedDevice.connections }}</span>
              </div>
            </div>
            <div class="device-actions">
              <a-button
                size="small"
                @click="pingDevice(selectedDevice)"
              >
                <Activity class="btn-icon" />
                Ping测试
              </a-button>
              <a-button
                size="small"
                @click="configureDevice(selectedDevice)"
              >
                <Settings class="btn-icon" />
                配置
              </a-button>
            </div>
          </div>
          <div
            v-else
            class="no-selection"
          >
            <p>点击设备查看详情</p>
          </div>
        </div>

        <!-- 路径分析 -->
        <div class="panel-section">
          <h3 class="section-title">路径分析</h3>
          <div class="path-analysis">
            <div class="path-input">
              <a-input
                v-model:value="pathSource"
                placeholder="源设备IP"
                style="margin-bottom: 8px"
              />
              <a-input
                v-model:value="pathTarget"
                placeholder="目标设备IP"
                style="margin-bottom: 8px"
              />
              <a-button
                block
                @click="analyzePath"
                :loading="analyzingPath"
              >
                <Route class="btn-icon" />
                分析路径
              </a-button>
            </div>
            <div
              v-if="pathResult"
              class="path-result"
            >
              <h4>路径结果</h4>
              <div class="path-hops">
                <div
                  v-for="(hop, index) in pathResult.hops"
                  :key="index"
                  class="hop-item"
                >
                  <span class="hop-number">{{ index + 1 }}</span>
                  <span class="hop-device">{{ hop.name }}</span>
                  <span class="hop-ip">{{ hop.ip }}</span>
                </div>
              </div>
              <div class="path-metrics">
                <div class="metric-item">
                  <span class="metric-label">跳数:</span>
                  <span class="metric-value">{{ pathResult.hopCount }}</span>
                </div>
                <div class="metric-item">
                  <span class="metric-label">延迟:</span>
                  <span class="metric-value">{{ pathResult.latency }}ms</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 故障影响分析 -->
        <div class="panel-section">
          <h3 class="section-title">故障影响分析</h3>
          <div class="impact-analysis">
            <a-select
              v-model:value="selectedFailureDevice"
              placeholder="选择故障设备"
              style="width: 100%; margin-bottom: 12px"
            >
              <a-select-option
                v-for="device in networkDevices"
                :key="device.id"
                :value="device.id"
              >
                {{ device.name }} ({{ device.ip }})
              </a-select-option>
            </a-select>
            <a-button
              block
              @click="analyzeImpact"
              :loading="analyzingImpact"
            >
              <AlertTriangle class="btn-icon" />
              分析影响
            </a-button>
            <div
              v-if="impactResult"
              class="impact-result"
            >
              <h4>影响范围</h4>
              <div class="impact-stats">
                <div class="impact-stat">
                  <span class="stat-number">{{ impactResult.affectedDevices }}</span>
                  <span class="stat-label">受影响设备</span>
                </div>
                <div class="impact-stat">
                  <span class="stat-number">{{ impactResult.affectedUsers }}</span>
                  <span class="stat-label">受影响用户</span>
                </div>
              </div>
              <div class="affected-devices">
                <h5>受影响设备列表</h5>
                <div
                  v-for="device in impactResult.devices"
                  :key="device.id"
                  class="affected-device"
                >
                  <component
                    :is="getDeviceIcon(device.type)"
                    class="device-icon small"
                  />
                  <span class="device-name">{{ device.name }}</span>
                  <span
                    class="impact-level"
                    :class="device.impactLevel"
                  >
                    {{ device.impactLevel }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加设备对话框 -->
    <a-modal
      v-model:open="showAddDeviceModal"
      title="添加网络设备"
      width="600px"
      :footer="null"
    >
      <a-form
        :model="deviceForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveDevice"
      >
        <a-form-item
          label="设备名称"
          name="name"
          :rules="[{ required: true, message: '请输入设备名称' }]"
        >
          <a-input
            v-model:value="deviceForm.name"
            placeholder="请输入设备名称"
          />
        </a-form-item>

        <a-form-item
          label="设备类型"
          name="type"
          :rules="[{ required: true, message: '请选择设备类型' }]"
        >
          <a-select
            v-model:value="deviceForm.type"
            placeholder="请选择设备类型"
          >
            <a-select-option value="router">路由器</a-select-option>
            <a-select-option value="switch">交换机</a-select-option>
            <a-select-option value="firewall">防火墙</a-select-option>
            <a-select-option value="server">服务器</a-select-option>
            <a-select-option value="ap">无线AP</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="IP地址"
          name="ip"
          :rules="[{ required: true, message: '请输入IP地址' }]"
        >
          <a-input
            v-model:value="deviceForm.ip"
            placeholder="请输入IP地址"
          />
        </a-form-item>

        <a-form-item
          label="设备位置"
          name="location"
        >
          <a-input
            v-model:value="deviceForm.location"
            placeholder="请输入设备位置"
          />
        </a-form-item>

        <a-form-item
          label="SNMP Community"
          name="snmpCommunity"
        >
          <a-input
            v-model:value="deviceForm.snmpCommunity"
            placeholder="SNMP团体名"
          />
        </a-form-item>

        <a-form-item
          label="认证信息"
          name="credentials"
        >
          <a-input-group compact>
            <a-input
              v-model:value="deviceForm.credentials.username"
              placeholder="用户名"
              style="width: 50%"
            />
            <a-input-password
              v-model:value="deviceForm.credentials.password"
              placeholder="密码"
              style="width: 50%"
            />
          </a-input-group>
        </a-form-item>

        <a-form-item
          label="设备描述"
          name="description"
        >
          <a-textarea
            v-model:value="deviceForm.description"
            :rows="3"
            placeholder="设备描述信息"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeAddDeviceModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  ArrowLeft,
  Network,
  RefreshCw,
  Plus,
  Search,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Activity,
  Settings,
  Route,
  AlertTriangle,
  Router,
  Shield,
  Wifi,
  Server,
  HardDrive
} from 'lucide-vue-next'

const router = useRouter()
const loading = ref(false)
const viewMode = ref('logical')
const selectedLayout = ref('force')
const selectedDevice = ref(null)
const pathSource = ref('')
const pathTarget = ref('')
const pathResult = ref(null)
const analyzingPath = ref(false)
const selectedFailureDevice = ref(null)
const impactResult = ref(null)
const analyzingImpact = ref(false)
const showAddDeviceModal = ref(false)
const saving = ref(false)

// 添加设备表单数据
const deviceForm = ref({
  name: '',
  type: 'router',
  ip: '',
  location: '',
  description: '',
  snmpCommunity: 'public',
  credentials: {
    username: '',
    password: ''
  }
})

// 拓扑统计数据
const topologyStats = reactive({
  deviceCount: 24,
  connectionCount: 31,
  subnetCount: 5
})

// 设备图例
const deviceLegends = ref([
  { type: 'router', label: '路由器' },
  { type: 'switch', label: '交换机' },
  { type: 'firewall', label: '防火墙' },
  { type: 'server', label: '服务器' },
  { type: 'ap', label: '无线AP' }
])

// 网络设备数据
const networkDevices = ref([
  {
    id: 1,
    name: 'Core-Router-01',
    type: 'router',
    ip: '***********',
    status: 'online',
    location: '机房A-机柜01',
    connections: 8
  },
  {
    id: 2,
    name: 'Core-Switch-01',
    type: 'switch',
    ip: '***********0',
    status: 'online',
    location: '机房A-机柜01',
    connections: 24
  },
  {
    id: 3,
    name: 'Firewall-Main',
    type: 'firewall',
    ip: '*************',
    status: 'warning',
    location: '机房A-机柜02',
    connections: 4
  }
])

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshTopology = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const addDevice = () => {
  showAddDeviceModal.value = true
  resetDeviceForm()
}

const resetDeviceForm = () => {
  deviceForm.value = {
    name: '',
    type: 'router',
    ip: '',
    location: '',
    description: '',
    snmpCommunity: 'public',
    credentials: {
      username: '',
      password: ''
    }
  }
}

const closeAddDeviceModal = () => {
  showAddDeviceModal.value = false
  resetDeviceForm()
}

const saveDevice = async () => {
  saving.value = true

  try {
    // 模拟保存设备
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建新设备
    const newDevice = {
      id: Date.now(),
      name: deviceForm.value.name,
      type: deviceForm.value.type,
      ip: deviceForm.value.ip,
      status: 'online',
      location: deviceForm.value.location,
      connections: 0
    }

    // 添加到设备列表
    networkDevices.value.push(newDevice)

    // 更新统计
    topologyStats.deviceCount++

    // 关闭对话框
    closeAddDeviceModal()

    console.log('设备添加成功:', newDevice)
  } catch (error) {
    console.error('保存设备失败:', error)
  } finally {
    saving.value = false
  }
}

const autoDiscovery = () => {
  loading.value = true
  console.log('开始自动发现网络设备...')

  setTimeout(() => {
    // 模拟发现新设备
    const discoveredDevices = [
      {
        id: Date.now() + 1,
        name: 'Auto-Switch-01',
        type: 'switch',
        ip: '************',
        status: 'online',
        location: '自动发现',
        connections: 12
      },
      {
        id: Date.now() + 2,
        name: 'Auto-AP-01',
        type: 'ap',
        ip: '************',
        status: 'online',
        location: '自动发现',
        connections: 8
      }
    ]

    // 添加到设备列表
    networkDevices.value.push(...discoveredDevices)

    // 更新统计
    topologyStats.deviceCount += discoveredDevices.length
    topologyStats.connectionCount += 5

    loading.value = false
    console.log('自动发现完成，发现', discoveredDevices.length, '个新设备')
  }, 2000)
}

const exportTopology = () => {
  console.log('开始导出拓扑图...')

  // 模拟导出过程
  const topologyData = {
    devices: networkDevices.value,
    connections: topologyStats.connectionCount,
    exportTime: new Date().toISOString(),
    viewMode: viewMode.value,
    layout: selectedLayout.value
  }

  // 创建下载链接
  const dataStr = JSON.stringify(topologyData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  // 创建下载链接
  const link = document.createElement('a')
  link.href = url
  link.download = `network-topology-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // 清理URL对象
  URL.revokeObjectURL(url)

  console.log('拓扑图导出完成')
}

const changeViewMode = () => {
  console.log('切换视图模式:', viewMode.value)
}

const changeLayout = () => {
  console.log('切换布局:', selectedLayout.value)
}

const zoomIn = () => {
  console.log('放大拓扑图')
}

const zoomOut = () => {
  console.log('缩小拓扑图')
}

const resetZoom = () => {
  console.log('重置缩放')
}

const pingDevice = (device: any) => {
  console.log('Ping设备:', device)
}

const configureDevice = (device: any) => {
  console.log('配置设备:', device)
}

const analyzePath = () => {
  analyzingPath.value = true
  setTimeout(() => {
    pathResult.value = {
      hops: [
        { name: 'Source', ip: pathSource.value },
        { name: 'Core-Router-01', ip: '***********' },
        { name: 'Core-Switch-01', ip: '***********0' },
        { name: 'Target', ip: pathTarget.value }
      ],
      hopCount: 4,
      latency: 12
    }
    analyzingPath.value = false
  }, 1500)
}

const analyzeImpact = () => {
  analyzingImpact.value = true
  setTimeout(() => {
    impactResult.value = {
      affectedDevices: 8,
      affectedUsers: 156,
      devices: [
        { id: 1, name: 'Switch-Floor1', type: 'switch', impactLevel: 'high' },
        { id: 2, name: 'AP-Office-01', type: 'ap', impactLevel: 'medium' },
        { id: 3, name: 'Server-Web', type: 'server', impactLevel: 'low' }
      ]
    }
    analyzingImpact.value = false
  }, 1500)
}

const getDeviceIcon = (type: string) => {
  const icons: Record<string, any> = {
    router: Router,
    switch: Network,
    firewall: Shield,
    server: Server,
    ap: Wifi
  }
  return icons[type] || HardDrive
}

const getDeviceTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    router: '路由器',
    switch: '交换机',
    firewall: '防火墙',
    server: '服务器',
    ap: '无线AP'
  }
  return labels[type] || type
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    online: 'green',
    offline: 'red',
    warning: 'orange'
  }
  return colors[status] || 'default'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    warning: '警告'
  }
  return labels[status] || status
}

onMounted(() => {
  refreshTopology()
})
</script>

<style scoped>
.network-topology-manager {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-app-region: no-drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.topology-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.main-content {
  display: flex;
  gap: 24px;
  height: calc(100vh - 200px);
}

.topology-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.topology-canvas {
  width: 100%;
  height: calc(100% - 80px);
  position: relative;
}

.topology-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.topology-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #1890ff;
}

.topology-legend {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 16px 24px;
  border-top: 1px solid #e8e8e8;
}

.topology-legend h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #1a1a1a;
}

.legend-items {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-icon.router {
  background: #1890ff;
}

.legend-icon.switch {
  background: #52c41a;
}

.legend-icon.firewall {
  background: #fa8c16;
}

.legend-icon.server {
  background: #722ed1;
}

.legend-icon.ap {
  background: #eb2f96;
}

.legend-label {
  font-size: 12px;
  color: #666;
}

.side-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.panel-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #1a1a1a;
  font-weight: 600;
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.device-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.device-icon {
  width: 32px;
  height: 32px;
  color: #1890ff;
}

.device-icon.small {
  width: 16px;
  height: 16px;
}

.device-info h4 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.device-info p {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: #666;
}

.device-properties {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.property-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.property-label {
  font-size: 12px;
  color: #666;
}

.property-value {
  font-size: 12px;
  color: #1a1a1a;
  font-weight: 500;
}

.device-actions {
  display: flex;
  gap: 8px;
}

.no-selection {
  text-align: center;
  color: #666;
  padding: 40px 0;
}

.path-analysis,
.impact-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.path-result,
.impact-result {
  border-top: 1px solid #e8e8e8;
  padding-top: 12px;
}

.path-result h4,
.impact-result h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #1a1a1a;
}

.path-hops {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hop-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
}

.hop-number {
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

.hop-device {
  flex: 1;
  font-size: 12px;
  color: #1a1a1a;
  font-weight: 500;
}

.hop-ip {
  font-size: 10px;
  color: #666;
}

.path-metrics {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 10px;
  color: #666;
}

.metric-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

.impact-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.impact-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
}

.stat-label {
  font-size: 10px;
  color: #666;
}

.affected-devices {
  border-top: 1px solid #e8e8e8;
  padding-top: 12px;
}

.affected-devices h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #1a1a1a;
}

.affected-device {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
}

.device-name {
  flex: 1;
  font-size: 12px;
  color: #1a1a1a;
}

.impact-level {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.impact-level.high {
  background: #fff2f0;
  color: #ff4d4f;
}

.impact-level.medium {
  background: #fff7e6;
  color: #fa8c16;
}

.impact-level.low {
  background: #f6ffed;
  color: #52c41a;
}

.btn-icon {
  width: 14px;
  height: 14px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.back-icon {
  width: 16px;
  height: 16px;
}
</style>
