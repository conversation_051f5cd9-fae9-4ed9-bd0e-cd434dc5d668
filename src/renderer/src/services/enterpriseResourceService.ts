import eventBus from '@/utils/eventBus'
import { useEnterpriseResourceStore, type EnterpriseResource, type RoomEnvironmentData, type DeviceStatus } from '@/store/enterpriseResourceStore'

/**
 * 企业资源管理通用服务类
 * 提供模块间数据共享、通信和调用的统一接口
 */
export class EnterpriseResourceService {
  private static instance: EnterpriseResourceService
  private store = useEnterpriseResourceStore()
  private moduleCallbacks: Map<string, Map<string, Function[]>> = new Map()

  private constructor() {
    this.initializeEventListeners()
  }

  public static getInstance(): EnterpriseResourceService {
    if (!EnterpriseResourceService.instance) {
      EnterpriseResourceService.instance = new EnterpriseResourceService()
    }
    return EnterpriseResourceService.instance
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners() {
    // 监听企业资源更新事件
    eventBus.on('enterpriseResourceUpdated', (data) => {
      this.notifyModules('resourceUpdated', data)
    })

    // 监听企业资源删除事件
    eventBus.on('enterpriseResourceDeleted', (data) => {
      this.notifyModules('resourceDeleted', data)
    })

    // 监听环境变更事件
    eventBus.on('enterpriseEnvironmentChanged', (data) => {
      this.notifyModules('environmentChanged', data)
    })

    // 监听机房环境数据更新
    eventBus.on('roomEnvironmentUpdated', (data) => {
      this.notifyModules('roomEnvironmentUpdated', data)
    })

    // 监听设备状态变更
    eventBus.on('roomDeviceStatusChanged', (data) => {
      this.notifyModules('deviceStatusChanged', data)
    })

    // 监听告警事件
    eventBus.on('enterpriseAlertTriggered', (data) => {
      this.notifyModules('alertTriggered', data)
    })
  }

  /**
   * 注册模块回调函数
   * @param moduleName 模块名称
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public registerModuleCallback(moduleName: string, eventType: string, callback: Function) {
    if (!this.moduleCallbacks.has(moduleName)) {
      this.moduleCallbacks.set(moduleName, new Map())
    }
    
    const moduleCallbacks = this.moduleCallbacks.get(moduleName)!
    if (!moduleCallbacks.has(eventType)) {
      moduleCallbacks.set(eventType, [])
    }
    
    moduleCallbacks.get(eventType)!.push(callback)
  }

  /**
   * 取消注册模块回调函数
   * @param moduleName 模块名称
   * @param eventType 事件类型
   * @param callback 回调函数
   */
  public unregisterModuleCallback(moduleName: string, eventType: string, callback: Function) {
    const moduleCallbacks = this.moduleCallbacks.get(moduleName)
    if (moduleCallbacks) {
      const callbacks = moduleCallbacks.get(eventType)
      if (callbacks) {
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }

  /**
   * 通知所有注册的模块
   * @param eventType 事件类型
   * @param data 事件数据
   */
  private notifyModules(eventType: string, data: any) {
    this.moduleCallbacks.forEach((moduleCallbacks, moduleName) => {
      const callbacks = moduleCallbacks.get(eventType)
      if (callbacks) {
        callbacks.forEach(callback => {
          try {
            callback(data)
          } catch (error) {
            console.error(`Error in module ${moduleName} callback for ${eventType}:`, error)
          }
        })
      }
    })
  }

  /**
   * 请求其他模块的数据
   * @param targetModule 目标模块
   * @param dataType 数据类型
   * @param params 请求参数
   * @returns Promise<any>
   */
  public async requestModuleData(targetModule: string, dataType: string, params?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Request timeout for ${targetModule}.${dataType}`))
      }, 5000)

      const responseHandler = (response: { module: string; dataType: string; data: any }) => {
        if (response.module === targetModule && response.dataType === dataType) {
          clearTimeout(timeout)
          eventBus.off('enterpriseModuleDataResponse', responseHandler)
          resolve(response.data)
        }
      }

      eventBus.on('enterpriseModuleDataResponse', responseHandler)
      eventBus.emit('enterpriseModuleDataRequest', { module: targetModule, dataType, params })
    })
  }

  /**
   * 共享资源给其他模块
   * @param resource 要共享的资源
   * @param targetModules 目标模块列表
   */
  public shareResource(resource: EnterpriseResource, targetModules: string[]) {
    const sharedResource = {
      ...resource,
      shared_with: [...(resource.shared_with || []), ...targetModules]
    }
    
    this.store.updateResource(resource.id, sharedResource)
    
    // 通知目标模块
    targetModules.forEach(module => {
      eventBus.emit('enterpriseResourceShared', {
        targetModule: module,
        resource: sharedResource
      })
    })
  }

  /**
   * 获取当前环境的资源
   * @param resourceType 资源类型
   * @returns EnterpriseResource[]
   */
  public getCurrentEnvironmentResources(resourceType?: 'host' | 'keychain' | 'snippet'): EnterpriseResource[] {
    const resources = this.store.resourcesByEnvironment
    return resourceType ? resources.filter(r => r.resource_type === resourceType) : resources
  }

  /**
   * 获取机房环境数据
   * @param roomId 机房ID
   * @returns RoomEnvironmentData | undefined
   */
  public getRoomEnvironmentData(roomId: string): RoomEnvironmentData | undefined {
    return this.store.roomEnvironments.get(roomId)
  }

  /**
   * 获取设备状态
   * @param deviceId 设备ID
   * @returns DeviceStatus | undefined
   */
  public getDeviceStatus(deviceId: string): DeviceStatus | undefined {
    return this.store.deviceStatuses.get(deviceId)
  }

  /**
   * 获取机房的所有设备状态
   * @param roomId 机房ID
   * @returns DeviceStatus[]
   */
  public getRoomDeviceStatuses(roomId: string): DeviceStatus[] {
    return Array.from(this.store.deviceStatuses.values()).filter(device => device.roomId === roomId)
  }

  /**
   * 触发企业级告警
   * @param type 告警类型
   * @param message 告警消息
   * @param module 触发模块
   */
  public triggerAlert(type: 'warning' | 'error' | 'info', message: string, module: string) {
    eventBus.emit('enterpriseAlertTriggered', { type, message, module })
  }

  /**
   * 更新同步状态
   * @param status 同步状态
   * @param progress 进度
   */
  public updateSyncStatus(status: 'syncing' | 'completed' | 'failed', progress?: number) {
    this.store.updateSyncStatus(status, progress)
  }

  /**
   * 切换环境
   * @param environment 目标环境
   */
  public switchEnvironment(environment: 'production' | 'staging' | 'development') {
    this.store.switchEnvironment(environment)
  }

  /**
   * 批量操作资源
   * @param resourceIds 资源ID列表
   * @param operation 操作类型
   * @param params 操作参数
   */
  public async batchOperateResources(
    resourceIds: string[], 
    operation: 'delete' | 'update' | 'export' | 'sync',
    params?: any
  ): Promise<{ success: string[]; failed: string[] }> {
    const results = { success: [], failed: [] }
    
    for (const resourceId of resourceIds) {
      try {
        switch (operation) {
          case 'delete':
            this.store.deleteResource(resourceId)
            results.success.push(resourceId)
            break
          case 'update':
            this.store.updateResource(resourceId, params)
            results.success.push(resourceId)
            break
          case 'export':
            // 导出逻辑
            results.success.push(resourceId)
            break
          case 'sync':
            // 同步逻辑
            results.success.push(resourceId)
            break
        }
      } catch (error) {
        console.error(`Failed to ${operation} resource ${resourceId}:`, error)
        results.failed.push(resourceId)
      }
    }
    
    return results
  }

  /**
   * 获取模块统计信息
   * @param moduleName 模块名称
   * @returns 统计信息对象
   */
  public getModuleStats(moduleName: string) {
    switch (moduleName) {
      case 'resource-management':
        return {
          totalResources: this.store.resources.length,
          resourcesByType: this.store.resourcesByType,
          currentEnvironment: this.store.currentEnvironment,
          syncStatus: this.store.syncStatus
        }
      case 'room-management':
        return {
          totalRooms: this.store.roomEnvironments.size,
          totalDevices: this.store.deviceStatuses.size,
          activeAlerts: this.store.activeAlerts.length,
          criticalAlerts: this.store.criticalAlerts.length
        }
      default:
        return {}
    }
  }
}

// 导出单例实例
export const enterpriseResourceService = EnterpriseResourceService.getInstance()
