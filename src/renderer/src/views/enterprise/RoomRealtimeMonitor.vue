<!--
  文件：RoomRealtimeMonitor.vue
  功能：机房实时环境监控
  依赖：vue, vue-router, lucide-vue-next
  作者：AI 助手
  修改时间：2025-01-16
-->
<template>
  <div class="room-realtime-monitor">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回机房管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Activity class="title-icon" />
            机房实时环境监控
          </h1>
          <p class="page-description">实时监控机房环境参数，确保设备运行环境安全</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          @click="refreshData"
          :disabled="loading"
        >
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </button>
        <button
          class="btn btn-secondary"
          @click="exportReport"
        >
          <Download class="btn-icon" />
          导出报告
        </button>
        <button
          class="btn btn-primary"
          @click="toggleAutoRefresh"
        >
          <Play
            v-if="!autoRefresh"
            class="btn-icon"
          />
          <Pause
            v-else
            class="btn-icon"
          />
          {{ autoRefresh ? '停止' : '开始' }}自动刷新
        </button>
      </div>
    </div>

    <!-- 状态概览卡片 -->
    <div class="status-overview">
      <div
        class="overview-card"
        :class="overallStatus"
      >
        <div class="card-header">
          <component
            :is="statusIcon"
            class="status-icon"
          />
          <div class="status-info">
            <div class="status-title">整体状态</div>
            <div class="status-value">{{ statusText }}</div>
          </div>
        </div>
        <div class="status-metrics">
          <div class="metric">
            <span class="metric-label">监控机房</span>
            <span class="metric-value">{{ totalRooms }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">在线设备</span>
            <span class="metric-value">{{ onlineDevices }}</span>
          </div>
          <div class="metric">
            <span class="metric-label">活跃告警</span>
            <span class="metric-value">{{ activeAlerts }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时环境监控图表 -->
    <div class="monitoring-section">
      <div class="section-header">
        <h2 class="section-title">实时环境监控</h2>
        <div class="time-range-selector">
          <button
            v-for="range in timeRanges"
            :key="range.value"
            class="time-btn"
            :class="{ active: selectedTimeRange === range.value }"
            @click="selectedTimeRange = range.value"
          >
            {{ range.label }}
          </button>
        </div>
      </div>

      <div class="charts-grid">
        <!-- 温度监控图表 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <Thermometer class="chart-icon" />
              温度监控
            </div>
            <div
              class="chart-value"
              :class="getTemperatureClass(currentEnvironment.temperature)"
            >
              {{ currentEnvironment.temperature }}°C
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-container">
              <canvas
                ref="temperatureChart"
                class="chart-canvas"
              ></canvas>
            </div>
            <div class="chart-info">
              <div class="threshold-info">
                <span class="threshold-label">正常范围: 18-25°C</span>
                <span
                  class="threshold-status"
                  :class="getTemperatureClass(currentEnvironment.temperature)"
                >
                  {{ getTemperatureStatus(currentEnvironment.temperature) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 湿度监控图表 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <Droplets class="chart-icon" />
              湿度监控
            </div>
            <div
              class="chart-value"
              :class="getHumidityClass(currentEnvironment.humidity)"
            >
              {{ currentEnvironment.humidity }}%
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-container">
              <canvas
                ref="humidityChart"
                class="chart-canvas"
              ></canvas>
            </div>
            <div class="chart-info">
              <div class="threshold-info">
                <span class="threshold-label">正常范围: 40-60%</span>
                <span
                  class="threshold-status"
                  :class="getHumidityClass(currentEnvironment.humidity)"
                >
                  {{ getHumidityStatus(currentEnvironment.humidity) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 功耗监控图表 -->
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <Zap class="chart-icon" />
              功耗监控
            </div>
            <div
              class="chart-value"
              :class="getPowerClass(currentEnvironment.powerConsumption)"
            >
              {{ currentEnvironment.powerConsumption }}kW
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-container">
              <canvas
                ref="powerChart"
                class="chart-canvas"
              ></canvas>
            </div>
            <div class="chart-info">
              <div class="threshold-info">
                <span class="threshold-label">总容量: 100kW</span>
                <span
                  class="threshold-status"
                  :class="getPowerClass(currentEnvironment.powerConsumption)"
                >
                  {{ getPowerStatus(currentEnvironment.powerConsumption) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 综合趋势图表 -->
        <div class="chart-card chart-card-wide">
          <div class="chart-header">
            <div class="chart-title">
              <TrendingUp class="chart-icon" />
              综合趋势分析
            </div>
            <div class="chart-controls">
              <select
                v-model="selectedMetric"
                class="metric-selector"
              >
                <option value="all">全部指标</option>
                <option value="temperature">温度</option>
                <option value="humidity">湿度</option>
                <option value="power">功耗</option>
              </select>
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-container">
              <canvas
                ref="trendChart"
                class="chart-canvas"
              ></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 告警管理面板 -->
    <div class="alerts-section">
      <div class="section-header">
        <h2 class="section-title">实时告警</h2>
        <div class="alert-controls">
          <button
            class="btn btn-secondary"
            @click="clearAllAlerts"
            :disabled="alerts.length === 0"
          >
            <X class="btn-icon" />
            清除所有
          </button>
          <button
            class="btn btn-secondary"
            @click="toggleAlertSound"
          >
            <Volume2
              v-if="alertSoundEnabled"
              class="btn-icon"
            />
            <VolumeX
              v-else
              class="btn-icon"
            />
            {{ alertSoundEnabled ? '关闭' : '开启' }}声音
          </button>
        </div>
      </div>

      <div class="alerts-container">
        <div
          v-if="alerts.length === 0"
          class="no-alerts"
        >
          <CheckCircle class="no-alerts-icon" />
          <p class="no-alerts-text">当前无告警信息</p>
        </div>

        <div
          v-else
          class="alerts-list"
        >
          <div
            v-for="alert in alerts"
            :key="alert.id"
            class="alert-item"
            :class="alert.severity"
          >
            <div class="alert-icon">
              <AlertTriangle v-if="alert.severity === 'high'" />
              <AlertCircle v-else-if="alert.severity === 'medium'" />
              <Info v-else />
            </div>
            <div class="alert-content">
              <div class="alert-header">
                <span class="alert-type">{{ getAlertTypeText(alert.type) }}</span>
                <span class="alert-time">{{ formatTime(alert.timestamp) }}</span>
              </div>
              <div class="alert-message">{{ alert.message }}</div>
              <div class="alert-room">机房: {{ alert.roomId }}</div>
            </div>
            <div class="alert-actions">
              <button
                class="alert-action-btn"
                @click="acknowledgeAlert(alert.id)"
                title="确认告警"
              >
                <Check class="action-icon" />
              </button>
              <button
                class="alert-action-btn"
                @click="dismissAlert(alert.id)"
                title="忽略告警"
              >
                <X class="action-icon" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史数据查看面板 -->
    <div class="history-section">
      <div class="section-header">
        <h2 class="section-title">历史数据分析</h2>
        <div class="history-controls">
          <select
            v-model="selectedHistoryRange"
            class="history-range-selector"
          >
            <option value="1h">最近1小时</option>
            <option value="6h">最近6小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <button
            class="btn btn-secondary"
            @click="exportHistoryData"
          >
            <Download class="btn-icon" />
            导出数据
          </button>
        </div>
      </div>

      <div class="history-stats">
        <div class="stat-card">
          <div class="stat-header">
            <Thermometer class="stat-icon temperature" />
            <span class="stat-title">温度统计</span>
          </div>
          <div class="stat-values">
            <div class="stat-item">
              <span class="stat-label">平均值</span>
              <span class="stat-value">{{ temperatureStats.average }}°C</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最高值</span>
              <span class="stat-value">{{ temperatureStats.max }}°C</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最低值</span>
              <span class="stat-value">{{ temperatureStats.min }}°C</span>
            </div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <Droplets class="stat-icon humidity" />
            <span class="stat-title">湿度统计</span>
          </div>
          <div class="stat-values">
            <div class="stat-item">
              <span class="stat-label">平均值</span>
              <span class="stat-value">{{ humidityStats.average }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最高值</span>
              <span class="stat-value">{{ humidityStats.max }}%</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最低值</span>
              <span class="stat-value">{{ humidityStats.min }}%</span>
            </div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-header">
            <Zap class="stat-icon power" />
            <span class="stat-title">功耗统计</span>
          </div>
          <div class="stat-values">
            <div class="stat-item">
              <span class="stat-label">平均值</span>
              <span class="stat-value">{{ powerStats.average }}kW</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最高值</span>
              <span class="stat-value">{{ powerStats.max }}kW</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">最低值</span>
              <span class="stat-value">{{ powerStats.min }}kW</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Activity,
  RefreshCw,
  Download,
  Play,
  Pause,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Thermometer,
  Droplets,
  Zap,
  TrendingUp,
  X,
  Volume2,
  VolumeX,
  AlertCircle,
  Info,
  Check
} from 'lucide-vue-next'
import { useEnterpriseResourceStore } from '../../store/enterpriseResourceStore'
import { enterpriseResourceService } from '../../services/enterpriseResourceService'

const router = useRouter()
const enterpriseStore = useEnterpriseResourceStore()

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const refreshInterval = ref<number | null>(null)
const selectedTimeRange = ref('1h')
const selectedMetric = ref('all')

// 环境数据
const currentEnvironment = ref({
  temperature: 22,
  humidity: 45,
  powerConsumption: 75
})

// 历史数据
const temperatureHistory = ref<number[]>([])
const humidityHistory = ref<number[]>([])
const powerHistory = ref<number[]>([])
const timeLabels = ref<string[]>([])

// 统计数据
const totalRooms = ref(5)
const onlineDevices = ref(48)
const activeAlerts = ref(3)

// 告警系统
const alerts = ref<
  Array<{
    id: string
    type: 'temperature' | 'humidity' | 'power' | 'device'
    severity: 'low' | 'medium' | 'high'
    message: string
    roomId: string
    timestamp: string
    acknowledged: boolean
  }>
>([])
const alertSoundEnabled = ref(true)

// 历史数据分析
const selectedHistoryRange = ref('24h')
const temperatureStats = ref({
  average: 22.5,
  max: 28.3,
  min: 18.7
})
const humidityStats = ref({
  average: 45.2,
  max: 62.1,
  min: 32.8
})
const powerStats = ref({
  average: 75.8,
  max: 89.2,
  min: 58.4
})

// 图表引用
const temperatureChart = ref<HTMLCanvasElement | null>(null)
const humidityChart = ref<HTMLCanvasElement | null>(null)
const powerChart = ref<HTMLCanvasElement | null>(null)
const trendChart = ref<HTMLCanvasElement | null>(null)

// 时间范围选项
const timeRanges = [
  { label: '1小时', value: '1h' },
  { label: '6小时', value: '6h' },
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' }
]

// 计算属性
const overallStatus = computed(() => {
  if (activeAlerts.value > 5) return 'critical'
  if (activeAlerts.value > 0) return 'warning'
  return 'normal'
})

const statusIcon = computed(() => {
  switch (overallStatus.value) {
    case 'critical':
      return XCircle
    case 'warning':
      return AlertTriangle
    default:
      return CheckCircle
  }
})

const statusText = computed(() => {
  switch (overallStatus.value) {
    case 'critical':
      return '严重告警'
    case 'warning':
      return '存在告警'
    default:
      return '运行正常'
  }
})

// 方法
const goBack = () => {
  // 直接返回到机房管理页面
  router.push({ name: 'RoomManagement' })
}

const refreshData = async () => {
  loading.value = true

  try {
    // 模拟数据更新
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新当前环境数据
    currentEnvironment.value = {
      temperature: Math.floor(Math.random() * 15) + 18, // 18-32°C
      humidity: Math.floor(Math.random() * 40) + 30, // 30-70%
      powerConsumption: Math.floor(Math.random() * 40) + 60 // 60-100kW
    }

    // 更新历史数据
    updateHistoryData()

    // 检查是否需要生成告警
    generateAlert('temperature', currentEnvironment.value.temperature)
    generateAlert('humidity', currentEnvironment.value.humidity)
    generateAlert('power', currentEnvironment.value.powerConsumption)

    // 更新历史统计
    updateHistoryStats()

    // 更新统计数据
    totalRooms.value = 5
    onlineDevices.value = Math.floor(Math.random() * 10) + 45

    // 更新企业store中的机房环境数据
    enterpriseStore.updateRoomEnvironment({
      roomId: 'room-1',
      temperature: currentEnvironment.value.temperature,
      humidity: currentEnvironment.value.humidity,
      powerConsumption: currentEnvironment.value.powerConsumption,
      timestamp: new Date().toISOString()
    })

    // 重新绘制图表
    await nextTick()
    drawCharts()

    console.log('实时监控数据已刷新')
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  // 导出监控报告
  const reportData = {
    timestamp: new Date().toISOString(),
    currentEnvironment: currentEnvironment.value,
    statistics: {
      totalRooms: totalRooms.value,
      onlineDevices: onlineDevices.value,
      activeAlerts: activeAlerts.value
    },
    historyData: {
      temperature: temperatureHistory.value,
      humidity: humidityHistory.value,
      power: powerHistory.value,
      timeLabels: timeLabels.value
    }
  }

  const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `room-monitor-report-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  console.log('监控报告已导出')
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value

  if (autoRefresh.value) {
    // 开始自动刷新，每30秒刷新一次
    refreshInterval.value = window.setInterval(() => {
      refreshData()
    }, 30000)
    console.log('自动刷新已开启')
  } else {
    // 停止自动刷新
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    console.log('自动刷新已停止')
  }
}

// 告警系统方法
const generateAlert = (type: 'temperature' | 'humidity' | 'power' | 'device', value: number, roomId: string = 'room-1') => {
  let severity: 'low' | 'medium' | 'high' = 'low'
  let message = ''

  switch (type) {
    case 'temperature':
      if (value > 30) {
        severity = 'high'
        message = `温度过高: ${value}°C，超出安全范围`
      } else if (value > 25) {
        severity = 'medium'
        message = `温度偏高: ${value}°C，建议关注`
      } else if (value < 18) {
        severity = 'medium'
        message = `温度过低: ${value}°C，可能影响设备性能`
      }
      break
    case 'humidity':
      if (value > 70 || value < 30) {
        severity = 'high'
        message = `湿度异常: ${value}%，超出安全范围`
      } else if (value > 60 || value < 40) {
        severity = 'medium'
        message = `湿度偏离正常范围: ${value}%`
      }
      break
    case 'power':
      if (value > 90) {
        severity = 'high'
        message = `功耗过载: ${value}kW，存在安全风险`
      } else if (value > 80) {
        severity = 'medium'
        message = `功耗较高: ${value}kW，建议优化`
      }
      break
  }

  if (message) {
    const alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      message,
      roomId,
      timestamp: new Date().toISOString(),
      acknowledged: false
    }

    alerts.value.unshift(alert)

    // 播放告警声音
    if (alertSoundEnabled.value && severity === 'high') {
      playAlertSound()
    }

    // 更新活跃告警数量
    activeAlerts.value = alerts.value.filter((a) => !a.acknowledged).length

    // 通过企业资源服务发送告警事件
    enterpriseResourceService.shareResource(
      {
        id: alert.id,
        type: 'alert',
        name: `${getAlertTypeText(type)}告警`,
        data: alert,
        timestamp: alert.timestamp
      },
      ['enterprise-main', 'room-management']
    )
  }
}

const playAlertSound = () => {
  // 创建简单的告警音效
  const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
  const oscillator = audioContext.createOscillator()
  const gainNode = audioContext.createGain()

  oscillator.connect(gainNode)
  gainNode.connect(audioContext.destination)

  oscillator.frequency.setValueAtTime(800, audioContext.currentTime)
  oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1)
  oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2)

  gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
  gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3)

  oscillator.start(audioContext.currentTime)
  oscillator.stop(audioContext.currentTime + 0.3)
}

const getAlertTypeText = (type: string) => {
  const typeMap = {
    temperature: '温度',
    humidity: '湿度',
    power: '功耗',
    device: '设备'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const acknowledgeAlert = (alertId: string) => {
  const alert = alerts.value.find((a) => a.id === alertId)
  if (alert) {
    alert.acknowledged = true
    activeAlerts.value = alerts.value.filter((a) => !a.acknowledged).length
    console.log('告警已确认:', alertId)
  }
}

const dismissAlert = (alertId: string) => {
  const index = alerts.value.findIndex((a) => a.id === alertId)
  if (index !== -1) {
    alerts.value.splice(index, 1)
    activeAlerts.value = alerts.value.filter((a) => !a.acknowledged).length
    console.log('告警已忽略:', alertId)
  }
}

const clearAllAlerts = () => {
  alerts.value = []
  activeAlerts.value = 0
  console.log('所有告警已清除')
}

const toggleAlertSound = () => {
  alertSoundEnabled.value = !alertSoundEnabled.value
  console.log('告警声音', alertSoundEnabled.value ? '已开启' : '已关闭')
}

// 历史数据分析方法
const exportHistoryData = () => {
  const historyData = {
    range: selectedHistoryRange.value,
    timestamp: new Date().toISOString(),
    temperature: {
      data: temperatureHistory.value,
      stats: temperatureStats.value
    },
    humidity: {
      data: humidityHistory.value,
      stats: humidityStats.value
    },
    power: {
      data: powerHistory.value,
      stats: powerStats.value
    },
    timeLabels: timeLabels.value
  }

  const blob = new Blob([JSON.stringify(historyData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `history-data-${selectedHistoryRange.value}-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  console.log('历史数据已导出')
}

const updateHistoryStats = () => {
  // 更新温度统计
  if (temperatureHistory.value.length > 0) {
    const temps = temperatureHistory.value
    temperatureStats.value = {
      average: Math.round((temps.reduce((a, b) => a + b, 0) / temps.length) * 10) / 10,
      max: Math.max(...temps),
      min: Math.min(...temps)
    }
  }

  // 更新湿度统计
  if (humidityHistory.value.length > 0) {
    const humidities = humidityHistory.value
    humidityStats.value = {
      average: Math.round((humidities.reduce((a, b) => a + b, 0) / humidities.length) * 10) / 10,
      max: Math.max(...humidities),
      min: Math.min(...humidities)
    }
  }

  // 更新功耗统计
  if (powerHistory.value.length > 0) {
    const powers = powerHistory.value
    powerStats.value = {
      average: Math.round((powers.reduce((a, b) => a + b, 0) / powers.length) * 10) / 10,
      max: Math.max(...powers),
      min: Math.min(...powers)
    }
  }
}

// 状态判断方法
const getTemperatureClass = (temperature: number) => {
  if (temperature > 30) return 'danger'
  if (temperature > 25) return 'warning'
  return 'normal'
}

const getTemperatureStatus = (temperature: number) => {
  if (temperature > 30) return '过高'
  if (temperature > 25) return '偏高'
  if (temperature < 18) return '过低'
  return '正常'
}

const getHumidityClass = (humidity: number) => {
  if (humidity > 70 || humidity < 30) return 'danger'
  if (humidity > 60 || humidity < 40) return 'warning'
  return 'normal'
}

const getHumidityStatus = (humidity: number) => {
  if (humidity > 70) return '过高'
  if (humidity > 60) return '偏高'
  if (humidity < 30) return '过低'
  if (humidity < 40) return '偏低'
  return '正常'
}

const getPowerClass = (power: number) => {
  if (power > 90) return 'danger'
  if (power > 80) return 'warning'
  return 'normal'
}

const getPowerStatus = (power: number) => {
  if (power > 90) return '过载'
  if (power > 80) return '高负载'
  return '正常'
}

// 历史数据更新
const updateHistoryData = () => {
  const now = new Date()
  const timeLabel = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })

  // 保持最近50个数据点
  if (temperatureHistory.value.length >= 50) {
    temperatureHistory.value.shift()
    humidityHistory.value.shift()
    powerHistory.value.shift()
    timeLabels.value.shift()
  }

  temperatureHistory.value.push(currentEnvironment.value.temperature)
  humidityHistory.value.push(currentEnvironment.value.humidity)
  powerHistory.value.push(currentEnvironment.value.powerConsumption)
  timeLabels.value.push(timeLabel)
}

// 图表绘制方法
const drawCharts = () => {
  drawTemperatureChart()
  drawHumidityChart()
  drawPowerChart()
  drawTrendChart()
}

const drawTemperatureChart = () => {
  const canvas = temperatureChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置画布大小
  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 绘制温度曲线
  if (temperatureHistory.value.length > 1) {
    ctx.strokeStyle =
      getTemperatureClass(currentEnvironment.value.temperature) === 'danger'
        ? '#ef4444'
        : getTemperatureClass(currentEnvironment.value.temperature) === 'warning'
          ? '#f59e0b'
          : '#10b981'
    ctx.lineWidth = 2
    ctx.beginPath()

    temperatureHistory.value.forEach((temp, index) => {
      const x = (index / (temperatureHistory.value.length - 1)) * canvas.width
      const y = canvas.height - ((temp - 15) / 20) * canvas.height

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })

    ctx.stroke()
  }
}

const drawHumidityChart = () => {
  const canvas = humidityChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  if (humidityHistory.value.length > 1) {
    ctx.strokeStyle =
      getHumidityClass(currentEnvironment.value.humidity) === 'danger'
        ? '#ef4444'
        : getHumidityClass(currentEnvironment.value.humidity) === 'warning'
          ? '#f59e0b'
          : '#3b82f6'
    ctx.lineWidth = 2
    ctx.beginPath()

    humidityHistory.value.forEach((humidity, index) => {
      const x = (index / (humidityHistory.value.length - 1)) * canvas.width
      const y = canvas.height - (humidity / 100) * canvas.height

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })

    ctx.stroke()
  }
}

const drawPowerChart = () => {
  const canvas = powerChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  if (powerHistory.value.length > 1) {
    ctx.strokeStyle =
      getPowerClass(currentEnvironment.value.powerConsumption) === 'danger'
        ? '#ef4444'
        : getPowerClass(currentEnvironment.value.powerConsumption) === 'warning'
          ? '#f59e0b'
          : '#8b5cf6'
    ctx.lineWidth = 2
    ctx.beginPath()

    powerHistory.value.forEach((power, index) => {
      const x = (index / (powerHistory.value.length - 1)) * canvas.width
      const y = canvas.height - (power / 100) * canvas.height

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })

    ctx.stroke()
  }
}

const drawTrendChart = () => {
  const canvas = trendChart.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  if (!ctx) return

  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  if (temperatureHistory.value.length > 1) {
    // 根据选择的指标绘制不同的曲线
    const datasets = []

    if (selectedMetric.value === 'all' || selectedMetric.value === 'temperature') {
      datasets.push({
        data: temperatureHistory.value.map((t) => t / 35), // 归一化到0-1
        color: '#ef4444',
        label: '温度'
      })
    }

    if (selectedMetric.value === 'all' || selectedMetric.value === 'humidity') {
      datasets.push({
        data: humidityHistory.value.map((h) => h / 100), // 归一化到0-1
        color: '#3b82f6',
        label: '湿度'
      })
    }

    if (selectedMetric.value === 'all' || selectedMetric.value === 'power') {
      datasets.push({
        data: powerHistory.value.map((p) => p / 100), // 归一化到0-1
        color: '#8b5cf6',
        label: '功耗'
      })
    }

    // 绘制每个数据集
    datasets.forEach((dataset) => {
      ctx.strokeStyle = dataset.color
      ctx.lineWidth = 2
      ctx.beginPath()

      dataset.data.forEach((value: number, index: number) => {
        const x = (index / (dataset.data.length - 1)) * canvas.width
        const y = canvas.height - value * canvas.height

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()
    })
  }
}

// 初始化数据
const initializeData = () => {
  // 生成初始历史数据
  for (let i = 0; i < 20; i++) {
    const now = new Date(Date.now() - (20 - i) * 60000) // 每分钟一个数据点
    temperatureHistory.value.push(Math.floor(Math.random() * 10) + 20)
    humidityHistory.value.push(Math.floor(Math.random() * 30) + 40)
    powerHistory.value.push(Math.floor(Math.random() * 30) + 60)
    timeLabels.value.push(now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }))
  }
}

// 组件生命周期
onMounted(() => {
  initializeData()
  refreshData()

  // 初始化模块通信
  enterpriseResourceService.registerModuleCallback('room-realtime-monitor', 'roomEnvironmentUpdated', (data: any) => {
    console.log('收到机房环境更新:', data)
    // 可以根据需要更新本地数据
  })

  // 窗口大小变化时重新绘制图表
  const handleResize = () => {
    nextTick(() => {
      drawCharts()
    })
  }

  window.addEventListener('resize', handleResize)

  // 清理函数
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize)
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
    }
  })
})
</script>

<style lang="less" scoped>
.room-realtime-monitor {
  width: 100%;
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
  overflow-y: auto;

  /* 启用窗口拖拽 */
  -webkit-app-region: drag;

  /* 禁用子元素的拖拽，保持交互性 */
  * {
    -webkit-app-region: no-drag;
  }
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.title-icon {
  width: 32px;
  height: 32px;
  color: #3b82f6;
}

.page-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-back {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-back:hover {
  background: #e2e8f0;
  color: #334155;
}

.btn-secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #f1f5f9;
  color: #334155;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.btn-text {
  font-size: 14px;
}

/* 状态概览 */
.status-overview {
  margin-bottom: 32px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
}

.overview-card.warning {
  border-left-color: #f59e0b;
}

.overview-card.critical {
  border-left-color: #ef4444;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.status-icon {
  width: 32px;
  height: 32px;
}

.overview-card.normal .status-icon {
  color: #10b981;
}

.overview-card.warning .status-icon {
  color: #f59e0b;
}

.overview-card.critical .status-icon {
  color: #ef4444;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
}

.status-metrics {
  display: flex;
  gap: 32px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 14px;
  color: #6b7280;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

/* 监控区域 */
.monitoring-section {
  margin-bottom: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.time-range-selector {
  display: flex;
  gap: 8px;
}

.time-btn {
  padding: 8px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-btn:hover {
  background: #f8fafc;
  color: #475569;
}

.time-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* 图表网格 */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.chart-card-wide {
  grid-column: 1 / -1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.chart-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.chart-value {
  font-size: 24px;
  font-weight: 700;
  padding: 8px 16px;
  border-radius: 8px;
}

.chart-value.normal {
  background: #dcfce7;
  color: #166534;
}

.chart-value.warning {
  background: #fef3c7;
  color: #92400e;
}

.chart-value.danger {
  background: #fee2e2;
  color: #991b1b;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metric-selector {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
}

.chart-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-container {
  position: relative;
  height: 200px;
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
}

.chart-canvas {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.chart-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.threshold-info {
  display: flex;
  gap: 16px;
  align-items: center;
}

.threshold-label {
  font-size: 14px;
  color: #6b7280;
}

.threshold-status {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.threshold-status.normal {
  background: #dcfce7;
  color: #166534;
}

.threshold-status.warning {
  background: #fef3c7;
  color: #92400e;
}

.threshold-status.danger {
  background: #fee2e2;
  color: #991b1b;
}

/* 动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 告警面板样式 */
.alerts-section {
  margin-bottom: 32px;
}

.alert-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.alerts-container {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 48px 24px;
  color: #64748b;
}

.no-alerts-icon {
  width: 48px;
  height: 48px;
  color: #10b981;
}

.no-alerts-text {
  font-size: 16px;
  margin: 0;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid #64748b;
  background: #f8fafc;
}

.alert-item.low {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.alert-item.medium {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.alert-item.high {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.alert-icon {
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.alert-item.low .alert-icon {
  color: #3b82f6;
}

.alert-item.medium .alert-icon {
  color: #f59e0b;
}

.alert-item.high .alert-icon {
  color: #ef4444;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-type {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.alert-time {
  font-size: 12px;
  color: #6b7280;
}

.alert-message {
  font-size: 14px;
  color: #1f2937;
  line-height: 1.5;
}

.alert-room {
  font-size: 12px;
  color: #6b7280;
}

.alert-actions {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.alert-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.alert-action-btn:hover {
  background: #f1f5f9;
  color: #374151;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* 历史数据面板样式 */
.history-section {
  margin-bottom: 32px;
}

.history-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.history-range-selector {
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
}

.history-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.stat-icon {
  width: 24px;
  height: 24px;
}

.stat-icon.temperature {
  color: #ef4444;
}

.stat-icon.humidity {
  color: #3b82f6;
}

.stat-icon.power {
  color: #8b5cf6;
}

.stat-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.stat-values {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .room-realtime-monitor {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .status-metrics {
    flex-direction: column;
    gap: 16px;
  }

  .time-range-selector {
    flex-wrap: wrap;
  }

  .history-stats {
    grid-template-columns: 1fr;
  }

  .alert-item {
    flex-direction: column;
    gap: 12px;
  }

  .alert-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
