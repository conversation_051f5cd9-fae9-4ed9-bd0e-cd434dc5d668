/**
 * 布局修复测试脚本
 * 用于验证页面滚动和空间利用修复效果
 */

// 测试页面列表
const testPages = [
  {
    name: '监控仪表板',
    selector: '.monitor-tab',
    route: '/monitor'
  },
  {
    name: '权限管理',
    selector: '.permission-management',
    route: '/security/permission'
  },
  {
    name: '网络安全监控',
    selector: '.network-security-monitor',
    route: '/enterprise/network/security'
  },
  {
    name: '网络性能监控',
    selector: '.network-performance-monitor',
    route: '/enterprise/network/performance'
  },
  {
    name: '应用层监控',
    selector: '.application-layer-monitor',
    route: '/enterprise/network/application'
  },
  {
    name: '网络数据分析',
    selector: '.network-data-analytics',
    route: '/enterprise/network/analytics'
  },
  {
    name: '无线网络监控',
    selector: '.wifi-network-monitor',
    route: '/enterprise/network/wifi'
  },
  {
    name: '网络基础设施监控',
    selector: '.network-infrastructure-monitor',
    route: '/enterprise/network/infrastructure'
  }
];

/**
 * 测试页面滚动功能
 */
function testPageScrolling(pageSelector) {
  const element = document.querySelector(pageSelector);
  if (!element) {
    return {
      success: false,
      error: '页面元素未找到'
    };
  }

  // 检查是否可以滚动
  const isScrollable = element.scrollHeight > element.clientHeight;
  const hasOverflowY = window.getComputedStyle(element).overflowY;
  
  return {
    success: true,
    isScrollable,
    hasOverflowY,
    scrollHeight: element.scrollHeight,
    clientHeight: element.clientHeight,
    canScroll: isScrollable && (hasOverflowY === 'auto' || hasOverflowY === 'scroll')
  };
}

/**
 * 测试屏幕空间利用率
 */
function testSpaceUtilization(pageSelector) {
  const element = document.querySelector(pageSelector);
  if (!element) {
    return {
      success: false,
      error: '页面元素未找到'
    };
  }

  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  
  const heightUtilization = (rect.height / viewportHeight) * 100;
  const widthUtilization = (rect.width / viewportWidth) * 100;
  
  return {
    success: true,
    heightUtilization: Math.round(heightUtilization),
    widthUtilization: Math.round(widthUtilization),
    elementHeight: rect.height,
    elementWidth: rect.width,
    viewportHeight,
    viewportWidth
  };
}

/**
 * 测试响应式设计
 */
function testResponsiveDesign(pageSelector) {
  const element = document.querySelector(pageSelector);
  if (!element) {
    return {
      success: false,
      error: '页面元素未找到'
    };
  }

  // 模拟不同屏幕尺寸
  const breakpoints = [
    { name: '桌面端', width: 1920, height: 1080 },
    { name: '平板端', width: 1024, height: 768 },
    { name: '手机端', width: 375, height: 667 }
  ];

  const results = [];
  
  breakpoints.forEach(bp => {
    // 注意：实际测试中需要真实改变视窗大小
    // 这里只是模拟检查CSS媒体查询
    const mediaQuery = window.matchMedia(`(max-width: ${bp.width}px)`);
    results.push({
      breakpoint: bp.name,
      matches: mediaQuery.matches,
      width: bp.width,
      height: bp.height
    });
  });

  return {
    success: true,
    breakpoints: results
  };
}

/**
 * 运行完整测试套件
 */
function runLayoutTests() {
  console.log('🧪 开始布局修复测试...\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    details: []
  };

  testPages.forEach(page => {
    console.log(`📋 测试页面: ${page.name}`);
    
    const pageResult = {
      pageName: page.name,
      selector: page.selector,
      tests: {}
    };

    // 测试滚动功能
    console.log('  🔄 测试滚动功能...');
    const scrollTest = testPageScrolling(page.selector);
    pageResult.tests.scrolling = scrollTest;
    results.totalTests++;
    
    if (scrollTest.success && scrollTest.canScroll) {
      console.log('  ✅ 滚动功能正常');
      results.passedTests++;
    } else {
      console.log('  ❌ 滚动功能异常');
      results.failedTests++;
    }

    // 测试空间利用率
    console.log('  📏 测试空间利用率...');
    const spaceTest = testSpaceUtilization(page.selector);
    pageResult.tests.spaceUtilization = spaceTest;
    results.totalTests++;
    
    if (spaceTest.success && spaceTest.heightUtilization > 80) {
      console.log(`  ✅ 空间利用率良好 (${spaceTest.heightUtilization}%)`);
      results.passedTests++;
    } else {
      console.log(`  ⚠️ 空间利用率偏低 (${spaceTest.heightUtilization || 0}%)`);
      results.failedTests++;
    }

    // 测试响应式设计
    console.log('  📱 测试响应式设计...');
    const responsiveTest = testResponsiveDesign(page.selector);
    pageResult.tests.responsive = responsiveTest;
    results.totalTests++;
    
    if (responsiveTest.success) {
      console.log('  ✅ 响应式设计检查完成');
      results.passedTests++;
    } else {
      console.log('  ❌ 响应式设计检查失败');
      results.failedTests++;
    }

    results.details.push(pageResult);
    console.log('');
  });

  // 输出测试总结
  console.log('📊 测试总结:');
  console.log(`  总测试数: ${results.totalTests}`);
  console.log(`  通过测试: ${results.passedTests}`);
  console.log(`  失败测试: ${results.failedTests}`);
  console.log(`  成功率: ${Math.round((results.passedTests / results.totalTests) * 100)}%`);

  return results;
}

/**
 * 生成测试报告
 */
function generateTestReport(results) {
  const report = `
# 布局修复测试报告

## 测试概览
- 测试时间: ${results.timestamp}
- 总测试数: ${results.totalTests}
- 通过测试: ${results.passedTests}
- 失败测试: ${results.failedTests}
- 成功率: ${Math.round((results.passedTests / results.totalTests) * 100)}%

## 详细结果

${results.details.map(page => `
### ${page.pageName}

#### 滚动功能测试
- 状态: ${page.tests.scrolling.success ? '✅ 通过' : '❌ 失败'}
- 可滚动: ${page.tests.scrolling.canScroll ? '是' : '否'}
- 滚动高度: ${page.tests.scrolling.scrollHeight || 'N/A'}px
- 可视高度: ${page.tests.scrolling.clientHeight || 'N/A'}px

#### 空间利用率测试
- 状态: ${page.tests.spaceUtilization.success ? '✅ 通过' : '❌ 失败'}
- 高度利用率: ${page.tests.spaceUtilization.heightUtilization || 'N/A'}%
- 宽度利用率: ${page.tests.spaceUtilization.widthUtilization || 'N/A'}%

#### 响应式设计测试
- 状态: ${page.tests.responsive.success ? '✅ 通过' : '❌ 失败'}
- 断点检查: ${page.tests.responsive.breakpoints ? page.tests.responsive.breakpoints.length : 0} 个断点
`).join('')}

## 建议

${results.failedTests > 0 ? `
⚠️ 发现 ${results.failedTests} 个问题，建议：
1. 检查失败的页面元素选择器是否正确
2. 确认CSS样式是否正确应用
3. 验证页面是否已正确加载
` : `
🎉 所有测试通过！布局修复效果良好。
`}
`;

  return report;
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testPageScrolling,
    testSpaceUtilization,
    testResponsiveDesign,
    runLayoutTests,
    generateTestReport
  };
}

// 浏览器环境下自动运行测试
if (typeof window !== 'undefined') {
  // 等待页面加载完成后运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        const results = runLayoutTests();
        const report = generateTestReport(results);
        console.log(report);
      }, 1000);
    });
  } else {
    setTimeout(() => {
      const results = runLayoutTests();
      const report = generateTestReport(results);
      console.log(report);
    }, 1000);
  }
}
