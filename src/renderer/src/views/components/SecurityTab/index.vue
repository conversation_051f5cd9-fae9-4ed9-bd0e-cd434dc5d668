<template>
  <div class="security-tab">
    <!-- 安全状态概览 -->
    <div class="security-overview">
      <div class="overview-header">
        <div class="security-score">
          <div
            class="score-circle"
            :class="securityLevel.class"
          >
            <div class="score-value">{{ securityScore }}</div>
            <div class="score-label">安全评分</div>
          </div>
          <div class="score-info">
            <div class="score-title">{{ securityLevel.title }}</div>
            <div class="score-desc">{{ securityLevel.description }}</div>
          </div>
        </div>
        <a-button
          type="primary"
          @click="runSecurityScan"
        >
          <Shield class="btn-icon" />
          安全扫描
        </a-button>
      </div>
    </div>

    <!-- 安全指标 -->
    <div class="security-metrics">
      <div class="metric-card">
        <div class="metric-icon">
          <Lock class="icon" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.activeConnections }}</div>
          <div class="metric-label">活跃连接</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">
          <Key class="icon" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.sshKeys }}</div>
          <div class="metric-label">SSH密钥</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">
          <AlertTriangle class="icon" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.threats }}</div>
          <div class="metric-label">威胁检测</div>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-icon">
          <FileText class="icon" />
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ metrics.auditLogs }}</div>
          <div class="metric-label">审计日志</div>
        </div>
      </div>
    </div>

    <!-- 安全策略 -->
    <div class="security-policies">
      <div class="section-header">
        <h3>安全策略</h3>
        <a-button
          size="small"
          @click="navigateToPage('/security/permissions')"
        >
          <Settings class="btn-icon" />
          管理策略
        </a-button>
      </div>
      <div class="policies-grid">
        <div
          v-for="policy in securityPolicies"
          :key="policy.id"
          class="policy-card"
          :class="policy.status"
        >
          <div class="policy-header">
            <component
              :is="policy.icon"
              class="policy-icon"
            />
            <div class="policy-info">
              <div class="policy-name">{{ policy.name }}</div>
              <div class="policy-desc">{{ policy.description }}</div>
            </div>
            <div
              class="policy-status"
              :class="policy.status"
            >
              <div class="status-dot"></div>
              <span>{{ policy.statusText }}</span>
            </div>
          </div>
          <div class="policy-details">
            <div class="detail-item">
              <span class="detail-label">最后更新</span>
              <span class="detail-value">{{ policy.lastUpdated }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">影响范围</span>
              <span class="detail-value">{{ policy.scope }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近安全事件 -->
    <div class="security-events">
      <div class="section-header">
        <h3>最近安全事件</h3>
        <div class="event-controls">
          <a-select
            v-model:value="eventFilter"
            size="small"
            style="width: 120px"
          >
            <a-select-option value="all">全部事件</a-select-option>
            <a-select-option value="high">高风险</a-select-option>
            <a-select-option value="medium">中风险</a-select-option>
            <a-select-option value="low">低风险</a-select-option>
          </a-select>
          <a-button
            size="small"
            @click="navigateToPage('/security/audit')"
          >
            <Eye class="btn-icon" />
            查看全部
          </a-button>
        </div>
      </div>
      <div class="events-list">
        <div
          v-for="event in filteredEvents"
          :key="event.id"
          class="event-item"
          :class="event.severity"
        >
          <div
            class="event-icon"
            :class="event.severity"
          >
            <component
              :is="event.icon"
              class="icon"
            />
          </div>
          <div class="event-content">
            <div class="event-title">{{ event.title }}</div>
            <div class="event-desc">{{ event.description }}</div>
            <div class="event-meta">
              <span class="event-time">{{ event.time }}</span>
              <span class="event-source">来源: {{ event.source }}</span>
            </div>
          </div>
          <div
            class="event-severity"
            :class="event.severity"
          >
            {{ event.severityText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h3>快速操作</h3>
      </div>
      <div class="action-grid">
        <div
          class="action-card"
          @click="navigateToPage('/security/permissions')"
        >
          <Users class="action-icon" />
          <div class="action-title">权限管理</div>
          <div class="action-desc">管理用户权限和角色</div>
        </div>
        <div
          class="action-card"
          @click="navigateToPage('/security/audit')"
        >
          <FileText class="action-icon" />
          <div class="action-title">审计日志</div>
          <div class="action-desc">查看系统审计记录</div>
        </div>
        <div
          class="action-card"
          @click="generateSecurityReport"
        >
          <Download class="action-icon" />
          <div class="action-title">安全报告</div>
          <div class="action-desc">生成安全评估报告</div>
        </div>
        <div
          class="action-card"
          @click="navigateToPage('/settings/global')"
        >
          <Settings class="action-icon" />
          <div class="action-title">安全设置</div>
          <div class="action-desc">配置安全相关设置</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Shield, Lock, Key, AlertTriangle, FileText, Settings, Eye, Users, Download, CheckCircle, XCircle, Clock } from 'lucide-vue-next'

const router = useRouter()

// 安全评分
const securityScore = ref(85)

// 安全等级
const securityLevel = computed(() => {
  const score = securityScore.value
  if (score >= 90) {
    return {
      class: 'excellent',
      title: '安全状态优秀',
      description: '系统安全配置完善，风险较低'
    }
  } else if (score >= 75) {
    return {
      class: 'good',
      title: '安全状态良好',
      description: '系统安全配置基本完善，存在少量风险'
    }
  } else if (score >= 60) {
    return {
      class: 'warning',
      title: '安全状态一般',
      description: '系统存在一些安全风险，建议优化'
    }
  } else {
    return {
      class: 'danger',
      title: '安全状态较差',
      description: '系统存在较多安全风险，需要立即处理'
    }
  }
})

// 安全指标
const metrics = ref({
  activeConnections: 12,
  sshKeys: 8,
  threats: 2,
  auditLogs: 156
})

// 安全策略
const securityPolicies = ref([
  {
    id: 1,
    name: '密码策略',
    description: '强制使用复杂密码',
    icon: Lock,
    status: 'active',
    statusText: '已启用',
    lastUpdated: '2024-01-15',
    scope: '全局'
  },
  {
    id: 2,
    name: 'SSH密钥管理',
    description: '管理SSH密钥访问权限',
    icon: Key,
    status: 'active',
    statusText: '已启用',
    lastUpdated: '2024-01-14',
    scope: '服务器连接'
  },
  {
    id: 3,
    name: '访问控制',
    description: '基于角色的访问控制',
    icon: Users,
    status: 'warning',
    statusText: '需要更新',
    lastUpdated: '2024-01-10',
    scope: '用户权限'
  },
  {
    id: 4,
    name: '审计日志',
    description: '记录所有系统操作',
    icon: FileText,
    status: 'active',
    statusText: '已启用',
    lastUpdated: '2024-01-15',
    scope: '全局'
  }
])

// 事件过滤器
const eventFilter = ref('all')

// 安全事件
const securityEvents = ref([
  {
    id: 1,
    title: '异常登录尝试',
    description: '检测到来自未知IP的登录尝试',
    severity: 'high',
    severityText: '高风险',
    icon: AlertTriangle,
    time: '2小时前',
    source: '认证系统'
  },
  {
    id: 2,
    title: 'SSH密钥更新',
    description: '用户更新了SSH密钥',
    severity: 'low',
    severityText: '低风险',
    icon: Key,
    time: '4小时前',
    source: '密钥管理'
  },
  {
    id: 3,
    title: '权限变更',
    description: '管理员修改了用户权限',
    severity: 'medium',
    severityText: '中风险',
    icon: Users,
    time: '6小时前',
    source: '权限管理'
  },
  {
    id: 4,
    title: '安全扫描完成',
    description: '定期安全扫描已完成',
    severity: 'low',
    severityText: '低风险',
    icon: Shield,
    time: '8小时前',
    source: '安全扫描'
  }
])

// 过滤后的事件
const filteredEvents = computed(() => {
  if (eventFilter.value === 'all') {
    return securityEvents.value
  }
  return securityEvents.value.filter((event) => event.severity === eventFilter.value)
})

// 运行安全扫描
const runSecurityScan = async () => {
  try {
    console.log('Starting security scan...')
    // 这里可以调用API执行安全扫描
  } catch (error) {
    console.error('Failed to run security scan:', error)
  }
}

// 生成安全报告
const generateSecurityReport = async () => {
  try {
    console.log('Generating security report...')
    // 这里可以调用API生成安全报告
    const reportData = {
      securityScore: securityScore.value,
      metrics: metrics.value,
      policies: securityPolicies.value,
      events: securityEvents.value,
      generatedAt: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `security-report-${new Date().toISOString().split('T')[0]}.json`
    a.click()
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to generate security report:', error)
  }
}

// 导航到页面
const navigateToPage = (path: string) => {
  router.push(path)
}

// 加载数据
const loadData = async () => {
  try {
    console.log('Loading security data...')
    // 这里可以调用API获取实际数据
  } catch (error) {
    console.error('Failed to load security data:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.security-tab {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  background: var(--bg-color);
}

.security-overview {
  margin-bottom: 24px;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.security-score {
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 4px solid;

  &.excellent {
    border-color: #52c41a;
    background: #f6ffed;
  }

  &.good {
    border-color: #1890ff;
    background: #e6f7ff;
  }

  &.warning {
    border-color: #faad14;
    background: #fffbe6;
  }

  &.danger {
    border-color: #ff4d4f;
    background: #fff2f0;
  }
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-color);
}

.score-label {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin-top: 2px;
}

.score-info {
  .score-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .score-desc {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.security-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-color-light);
  border-radius: 8px;

  .icon {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
  }
}

.metric-content {
  .metric-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .metric-label {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
  }
}

.security-policies {
  margin-bottom: 24px;
}

.policies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.policy-card {
  padding: 16px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);

  &.active {
    border-left: 4px solid #52c41a;
  }

  &.warning {
    border-left: 4px solid #faad14;
  }

  &.inactive {
    border-left: 4px solid #d9d9d9;
  }
}

.policy-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.policy-icon {
  width: 24px;
  height: 24px;
  color: var(--primary-color);
}

.policy-info {
  flex: 1;

  .policy-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2px;
  }

  .policy-desc {
    font-size: 12px;
    color: var(--text-color-secondary);
  }
}

.policy-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &.active {
    color: #52c41a;

    .status-dot {
      background: #52c41a;
    }
  }

  &.warning {
    color: #faad14;

    .status-dot {
      background: #faad14;
    }
  }

  &.inactive {
    color: #d9d9d9;

    .status-dot {
      background: #d9d9d9;
    }
  }
}

.policy-details {
  display: flex;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .detail-label {
    font-size: 11px;
    color: var(--text-color-secondary);
  }

  .detail-value {
    font-size: 12px;
    color: var(--text-color);
  }
}

.security-events {
  margin-bottom: 24px;
}

.event-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.events-list {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: var(--hover-bg-color);
  }
}

.event-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 6px;

  &.high {
    background: #fff2f0;

    .icon {
      color: #ff4d4f;
    }
  }

  &.medium {
    background: #fffbe6;

    .icon {
      color: #faad14;
    }
  }

  &.low {
    background: #e6f7ff;

    .icon {
      color: #1890ff;
    }
  }

  .icon {
    width: 18px;
    height: 18px;
  }
}

.event-content {
  flex: 1;

  .event-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .event-desc {
    font-size: 13px;
    color: var(--text-color-secondary);
    margin-bottom: 6px;
  }

  .event-meta {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: var(--text-color-tertiary);
  }
}

.event-severity {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.high {
    background: #fff2f0;
    color: #ff4d4f;
  }

  &.medium {
    background: #fffbe6;
    color: #faad14;
  }

  &.low {
    background: #e6f7ff;
    color: #1890ff;
  }
}

.quick-actions {
  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

.action-card {
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .action-icon {
    width: 32px;
    height: 32px;
    color: var(--primary-color);
    margin: 0 auto 12px;
  }

  .action-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  .action-desc {
    font-size: 12px;
    color: var(--text-color-secondary);
    line-height: 1.4;
  }
}
</style>
