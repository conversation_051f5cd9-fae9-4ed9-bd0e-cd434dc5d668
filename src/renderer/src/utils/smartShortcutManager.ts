/**
 * 智能快捷键管理器
 * 功能：管理快捷键绑定、执行快捷命令、提供智能推荐
 * 作者：AI Assistant
 * 修改时间：2024-01-09
 */

import { ref, reactive } from 'vue'
import { inputManager } from '@/views/components/Ssh/termInputManager'
import { executeScript } from '@/views/components/Ssh/commandScript'

// 快捷键配置接口
interface ShortcutConfig {
  id: string
  name: string
  description: string
  shortcut: string // 如 'Ctrl+Shift+L'
  command: string
  category: string
  icon?: string
  enabled: boolean
  usage_count: number
  last_used?: Date
}

// 命令分类
export const COMMAND_CATEGORIES = {
  SYSTEM: 'system',
  FILE: 'file',
  NETWORK: 'network',
  DOCKER: 'docker',
  GIT: 'git',
  CUSTOM: 'custom'
} as const

// 预定义的智能快捷命令
const DEFAULT_SHORTCUTS: ShortcutConfig[] = [
  {
    id: 'quick_ls',
    name: '快速列表',
    description: '显示当前目录详细信息',
    shortcut: 'Ctrl+Shift+L',
    command: 'ls -la',
    category: COMMAND_CATEGORIES.FILE,
    icon: '📁',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_ps',
    name: '进程列表',
    description: '显示运行中的进程',
    shortcut: 'Ctrl+Shift+P',
    command: 'ps aux | head -20',
    category: COMMAND_CATEGORIES.SYSTEM,
    icon: '⚙️',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_df',
    name: '磁盘使用',
    description: '显示磁盘使用情况',
    shortcut: 'Ctrl+Shift+D',
    command: 'df -h',
    category: COMMAND_CATEGORIES.SYSTEM,
    icon: '💾',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_top',
    name: '系统监控',
    description: '显示系统资源使用情况',
    shortcut: 'Ctrl+Shift+T',
    command: 'top -n 1',
    category: COMMAND_CATEGORIES.SYSTEM,
    icon: '📊',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_git_status',
    name: 'Git状态',
    description: '显示Git仓库状态',
    shortcut: 'Ctrl+Shift+G',
    command: 'git status',
    category: COMMAND_CATEGORIES.GIT,
    icon: '🔀',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_docker_ps',
    name: 'Docker容器',
    description: '显示运行中的Docker容器',
    shortcut: 'Ctrl+Shift+C',
    command: 'docker ps',
    category: COMMAND_CATEGORIES.DOCKER,
    icon: '🐳',
    enabled: true,
    usage_count: 0
  },
  {
    id: 'quick_netstat',
    name: '网络连接',
    description: '显示网络连接状态',
    shortcut: 'Ctrl+Shift+N',
    command: 'netstat -tuln',
    category: COMMAND_CATEGORIES.NETWORK,
    icon: '🌐',
    enabled: true,
    usage_count: 0
  }
]

// 快捷键管理器状态
const shortcuts = ref<ShortcutConfig[]>([...DEFAULT_SHORTCUTS])
const activeShortcuts = ref<Map<string, ShortcutConfig>>(new Map())
const shortcutStats = reactive({
  totalUsage: 0,
  mostUsed: null as ShortcutConfig | null,
  recentlyUsed: [] as ShortcutConfig[]
})

// 快捷键解析函数
function parseShortcut(shortcut: string): { ctrl: boolean; shift: boolean; alt: boolean; key: string } {
  const parts = shortcut.toLowerCase().split('+')
  return {
    ctrl: parts.includes('ctrl'),
    shift: parts.includes('shift'),
    alt: parts.includes('alt'),
    key: parts[parts.length - 1]
  }
}

// 检查快捷键是否匹配
function isShortcutMatch(event: KeyboardEvent, shortcut: string): boolean {
  const parsed = parseShortcut(shortcut)
  return event.ctrlKey === parsed.ctrl && event.shiftKey === parsed.shift && event.altKey === parsed.alt && event.key.toLowerCase() === parsed.key
}

// 执行快捷命令
function executeShortcut(shortcutConfig: ShortcutConfig): void {
  try {
    // 更新使用统计
    shortcutConfig.usage_count++
    shortcutConfig.last_used = new Date()
    updateStats(shortcutConfig)

    // 创建终端接口
    const terminal = {
      write: (data: string) => {
        inputManager.sendToActiveTerm(data)
      }
    }

    // 执行命令
    executeScript(shortcutConfig.command, terminal)

    console.log(`执行快捷命令: ${shortcutConfig.name} (${shortcutConfig.command})`)
  } catch (error) {
    console.error('执行快捷命令失败:', error)
  }
}

// 更新使用统计
function updateStats(shortcut: ShortcutConfig): void {
  shortcutStats.totalUsage++

  // 更新最常用命令
  if (!shortcutStats.mostUsed || shortcut.usage_count > shortcutStats.mostUsed.usage_count) {
    shortcutStats.mostUsed = shortcut
  }

  // 更新最近使用列表
  const recentIndex = shortcutStats.recentlyUsed.findIndex((s) => s.id === shortcut.id)
  if (recentIndex > -1) {
    shortcutStats.recentlyUsed.splice(recentIndex, 1)
  }
  shortcutStats.recentlyUsed.unshift(shortcut)
  if (shortcutStats.recentlyUsed.length > 10) {
    shortcutStats.recentlyUsed.pop()
  }
}

// 智能快捷键管理器
export const smartShortcutManager = {
  // 获取所有快捷键
  getShortcuts: () => shortcuts.value,

  // 获取启用的快捷键
  getEnabledShortcuts: () => shortcuts.value.filter((s) => s.enabled),

  // 按分类获取快捷键
  getShortcutsByCategory: (category: string) => shortcuts.value.filter((s) => s.category === category),

  // 添加自定义快捷键
  addShortcut: (config: Omit<ShortcutConfig, 'id' | 'usage_count'>) => {
    const newShortcut: ShortcutConfig = {
      ...config,
      id: `custom_${Date.now()}`,
      usage_count: 0
    }
    shortcuts.value.push(newShortcut)
    if (newShortcut.enabled) {
      activeShortcuts.value.set(newShortcut.shortcut, newShortcut)
    }
    return newShortcut.id
  },

  // 更新快捷键
  updateShortcut: (id: string, updates: Partial<ShortcutConfig>) => {
    const index = shortcuts.value.findIndex((s) => s.id === id)
    if (index > -1) {
      const oldShortcut = shortcuts.value[index].shortcut
      shortcuts.value[index] = { ...shortcuts.value[index], ...updates }

      // 更新活动快捷键映射
      if (oldShortcut !== shortcuts.value[index].shortcut) {
        activeShortcuts.value.delete(oldShortcut)
      }
      if (shortcuts.value[index].enabled) {
        activeShortcuts.value.set(shortcuts.value[index].shortcut, shortcuts.value[index])
      } else {
        activeShortcuts.value.delete(shortcuts.value[index].shortcut)
      }
    }
  },

  // 删除快捷键
  removeShortcut: (id: string) => {
    const index = shortcuts.value.findIndex((s) => s.id === id)
    if (index > -1) {
      const shortcut = shortcuts.value[index]
      activeShortcuts.value.delete(shortcut.shortcut)
      shortcuts.value.splice(index, 1)
    }
  },

  // 切换快捷键启用状态
  toggleShortcut: (id: string) => {
    const shortcut = shortcuts.value.find((s) => s.id === id)
    if (shortcut) {
      shortcut.enabled = !shortcut.enabled
      if (shortcut.enabled) {
        activeShortcuts.value.set(shortcut.shortcut, shortcut)
      } else {
        activeShortcuts.value.delete(shortcut.shortcut)
      }
    }
  },

  // 处理键盘事件
  handleKeyEvent: (event: KeyboardEvent) => {
    for (const [shortcutKey, config] of activeShortcuts.value) {
      if (isShortcutMatch(event, shortcutKey)) {
        event.preventDefault()
        executeShortcut(config)
        return true
      }
    }
    return false
  },

  // 获取使用统计
  getStats: () => shortcutStats,

  // 获取推荐的快捷键（基于使用频率和上下文）
  getRecommendations: (context?: string) => {
    const recommendations = shortcuts.value
      .filter((s) => s.enabled)
      .sort((a, b) => {
        // 优先推荐最近使用的和使用频率高的
        const aScore = a.usage_count + (a.last_used ? 1 : 0)
        const bScore = b.usage_count + (b.last_used ? 1 : 0)
        return bScore - aScore
      })
      .slice(0, 5)

    return recommendations
  },

  // 初始化快捷键系统
  initialize: () => {
    // 构建活动快捷键映射
    shortcuts.value.forEach((shortcut) => {
      if (shortcut.enabled) {
        activeShortcuts.value.set(shortcut.shortcut, shortcut)
      }
    })

    // 绑定全局键盘事件
    document.addEventListener('keydown', smartShortcutManager.handleKeyEvent)

    console.log('智能快捷键管理器已初始化')
  },

  // 清理资源
  destroy: () => {
    document.removeEventListener('keydown', smartShortcutManager.handleKeyEvent)
    activeShortcuts.value.clear()
  }
}

// 导出类型和常量
export type { ShortcutConfig }
export { DEFAULT_SHORTCUTS }
