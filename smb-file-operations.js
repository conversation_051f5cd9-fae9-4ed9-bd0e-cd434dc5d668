/**
 * SMB文件操作工具 - Node.js版本
 * 功能：文件扫描、共享和远程拷贝
 * 依赖：@marsaud/smb2、fs、path、readline
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 *
 * 安装依赖：npm install @marsaud/smb2
 */

const SMB2 = require('@marsaud/smb2')
const fs = require('fs')
const path = require('path')
const readline = require('readline')
const os = require('os')

/**
 * 创建readline接口用于用户交互
 */
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

/**
 * 异步输入函数
 * @param {string} question 提示问题
 * @returns {Promise<string>} 用户输入
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim())
    })
  })
}

/**
 * 初始化SMB连接配置
 * @param {string} host SMB服务器地址
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} domain 域名
 * @param {string} share 共享名
 * @returns {object} SMB2客户端实例
 */
function initSMBProtocol(host, username, password, domain = 'brbiotech', share = 'public') {
  const smbConfig = {
    share: `\\\\${host}\\${share}`,
    domain: domain,
    username: username,
    password: password,
    autoCloseTimeout: 0
  }

  console.log(`🔧 初始化SMB连接: ${host}/${share}`)
  return new SMB2(smbConfig)
}

/**
 * 扫描本地文件夹，返回符合条件的文件
 * @param {string} targetFolder 目标文件夹路径
 * @param {Array} targetFiles 目标文件名列表
 * @param {Array} targetExt 目标文件扩展名列表
 * @returns {object} 按父目录分组的文件列表
 */
function scanFiles(targetFolder, targetFiles, targetExt) {
  const filesFound = {}

  console.log(`🔍 扫描文件夹: ${targetFolder}`)

  /**
   * 递归扫描目录
   * @param {string} dir 当前目录
   */
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir)

      for (const item of items) {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)

        if (stat.isDirectory()) {
          // 递归扫描子目录
          scanDirectory(fullPath)
        } else if (stat.isFile()) {
          // 检查文件是否符合条件
          const shouldInclude = targetFiles.includes(item) || targetExt.some((ext) => item.endsWith(ext))

          if (shouldInclude) {
            const parentDir = path.basename(path.dirname(fullPath))
            if (!filesFound[parentDir]) {
              filesFound[parentDir] = []
            }
            filesFound[parentDir].push(fullPath)
            console.log(`✅ 找到文件: ${fullPath}`)
          }
        }
      }
    } catch (error) {
      console.error(`❌ 扫描目录失败 ${dir}: ${error.message}`)
    }
  }

  scanDirectory(targetFolder)
  return filesFound
}

/**
 * 获取本机IP地址
 * @returns {string} IP地址
 */
function getLocalIP() {
  const interfaces = os.networkInterfaces()
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address
      }
    }
  }
  return '127.0.0.1'
}

/**
 * 旧电脑端：扫描文件并准备共享
 * @param {string} targetFolder 目标文件夹
 * @param {Array} targetFiles 目标文件列表
 * @param {Array} targetExt 目标扩展名列表
 */
async function scanFilesAndShare(targetFolder, targetFiles, targetExt) {
  const localIP = getLocalIP()
  console.log(`📡 本机IP地址: ${localIP}`)

  // 扫描文件
  const filesByParentDir = scanFiles(targetFolder, targetFiles, targetExt)

  if (Object.keys(filesByParentDir).length === 0) {
    console.log('⚠️ 未找到符合条件的文件')
    return
  }

  console.log('\n📊 扫描结果:')
  for (const [parentDir, files] of Object.entries(filesByParentDir)) {
    console.log(`📁 ${parentDir}: ${files.length} 个文件`)
    files.forEach((file) => {
      console.log(`   📄 ${path.basename(file)}`)
    })
  }

  console.log('\n💡 提示: 请确保SMB服务已启用，其他设备可通过以下方式访问:')
  console.log(`   Windows: \\\\${localIP}\\共享文件夹名`)
  console.log(`   macOS: smb://${localIP}/共享文件夹名`)
  console.log('\n📋 建议手动设置文件夹共享，或使用专门的文件共享工具。')
}

/**
 * 新电脑端：从远程SMB服务器拷贝文件
 * @param {string} oldIP 旧电脑IP地址
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {string} targetFolderOnNew 新电脑保存路径
 * @param {string} domain 域名
 * @param {string} share 共享名
 */
async function copySMBFilesFromOldToNew(oldIP, username, password, targetFolderOnNew, domain = 'brbiotech', share = 'public') {
  console.log(`🔗 连接到远程SMB服务器: ${oldIP}`)

  const smb2Client = initSMBProtocol(oldIP, username, password, domain, share)

  try {
    // 测试连接
    console.log('🔍 测试SMB连接...')

    // 列出根目录文件
    const files = await new Promise((resolve, reject) => {
      smb2Client.readdir('/', (err, files) => {
        if (err) {
          reject(err)
        } else {
          resolve(files)
        }
      })
    })

    console.log(`✅ 连接成功！找到 ${files.length} 个项目`)

    // 创建本地目标目录
    if (!fs.existsSync(targetFolderOnNew)) {
      fs.mkdirSync(targetFolderOnNew, { recursive: true })
      console.log(`📁 创建目录: ${targetFolderOnNew}`)
    }

    // 遍历并下载文件
    for (const file of files) {
      const remotePath = `/${file}`
      const localPath = path.join(targetFolderOnNew, file)

      try {
        // 检查是否为文件
        const stats = await new Promise((resolve, reject) => {
          smb2Client.stat(remotePath, (err, stats) => {
            if (err) {
              reject(err)
            } else {
              resolve(stats)
            }
          })
        })

        if (stats.isFile()) {
          console.log(`📥 下载文件: ${file}`)

          // 下载文件
          await new Promise((resolve, reject) => {
            const readStream = smb2Client.createReadStream(remotePath)
            const writeStream = fs.createWriteStream(localPath)

            readStream.on('error', reject)
            writeStream.on('error', reject)
            writeStream.on('finish', resolve)

            readStream.pipe(writeStream)
          })

          console.log(`✅ 文件已保存: ${localPath}`)
        } else if (stats.isDirectory()) {
          console.log(`📁 跳过目录: ${file}`)
        }
      } catch (error) {
        console.error(`❌ 处理文件失败 ${file}: ${error.message}`)
      }
    }

    console.log('🎉 文件拷贝完成！')
  } catch (error) {
    console.error(`❌ SMB操作失败: ${error.message}`)
    console.error('🔧 请检查:')
    console.error('   1. 网络连接是否正常')
    console.error('   2. SMB服务器地址是否正确')
    console.error('   3. 用户名和密码是否正确')
    console.error('   4. 共享文件夹是否存在')
    console.error('   5. 是否有访问权限')
  } finally {
    // 关闭连接
    smb2Client.disconnect()
  }
}

/**
 * 主函数 - 交互式操作界面
 */
async function main() {
  console.log('🚀 SMB文件操作工具')
  console.log('==================')
  console.log('1. 旧电脑：扫描文件并准备共享')
  console.log('2. 新电脑：从远程SMB服务器拷贝文件')
  console.log('')

  try {
    const choice = await askQuestion('请输入选择 (1 或 2): ')

    // 默认参数
    const defaultFiles = ['example.txt', 'default.docx']
    const defaultExt = ['.txt', '.pdf', '.docx']
    const defaultFolder = process.platform === 'win32' ? 'C:\\Users\\<USER>\\Documents' : `${os.homedir()}/Documents`

    if (choice === '1') {
      // 旧电脑端：扫描文件
      console.log('\n📂 配置文件扫描参数:')

      const targetFolder = (await askQuestion(`请输入需要扫描的文件夹路径（默认: ${defaultFolder}）: `)) || defaultFolder

      const targetFilesInput = await askQuestion(`请输入需要扫描的文件名（多个文件用逗号分隔，默认: ${defaultFiles.join(', ')}）: `)
      const targetFiles = targetFilesInput ? targetFilesInput.split(',').map((f) => f.trim()) : defaultFiles

      const targetExtInput = await askQuestion(`请输入需要扫描的文件扩展名（多个扩展名用逗号分隔，默认: ${defaultExt.join(', ')}）: `)
      const targetExt = targetExtInput ? targetExtInput.split(',').map((e) => e.trim()) : defaultExt

      await scanFilesAndShare(targetFolder, targetFiles, targetExt)
    } else if (choice === '2') {
      // 新电脑端：拷贝文件
      console.log('\n🔗 配置SMB连接参数:')

      const oldIP = await askQuestion('请输入远程SMB服务器IP地址: ')
      if (!oldIP) {
        console.error('❌ IP地址不能为空')
        return
      }

      const username = (await askQuestion('请输入用户名: ')) || os.userInfo().username
      const password = await askQuestion('请输入密码: ')
      const domain = (await askQuestion('请输入域名（默认: brbiotech）: ')) || 'brbiotech'
      const share = (await askQuestion('请输入共享名（默认: public）: ')) || 'public'
      const targetFolderOnNew = (await askQuestion('请输入新电脑文件保存路径: ')) || './downloaded_files'

      await copySMBFilesFromOldToNew(oldIP, username, password, targetFolderOnNew, domain, share)
    } else {
      console.log('❌ 无效选择，请选择 1 或 2')
    }
  } catch (error) {
    console.error(`💥 程序执行失败: ${error.message}`)
  } finally {
    rl.close()
  }
}

// 导出函数供其他模块使用
module.exports = {
  initSMBProtocol,
  scanFiles,
  scanFilesAndShare,
  copySMBFilesFromOldToNew,
  main
}

// 如果直接运行此文件，启动主程序
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 程序启动失败:', error.message)
    process.exit(1)
  })
}
