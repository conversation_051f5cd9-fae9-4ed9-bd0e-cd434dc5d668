/**
 * 网络资产服务
 * 提供网络资产数据的获取和管理功能
 */

export interface NetworkAsset {
  id: number
  name: string
  type: string
  ip: string
  serialNumber: string
  macAddress?: string
  model?: string
  firmwareVersion?: string
  location: string
  rackInfo?: string
  status: 'online' | 'offline' | 'maintenance'
  lifecycle: 'new' | 'active' | 'aging' | 'eol'
  purchaseDate?: string
  warrantyExpiry?: string
  owner?: string
  hasBackup: boolean
  canBackup: boolean
  description?: string
  username: string
  password: string
  sshPort: number
  snmpCommunity: string
  lastUpdate: string
}

export interface HostOption {
  value: string
  label: string
  ip: string
  type: string
  status: string
  asset: NetworkAsset
}

class NetworkAssetsService {
  private assets: NetworkAsset[] = []

  constructor() {
    this.initializeMockData()
  }

  /**
   * 初始化模拟数据
   */
  private initializeMockData() {
    this.assets = [
      {
        id: 1,
        name: 'Core-Switch-01',
        type: 'switch',
        ip: '************',
        serialNumber: 'SW001234567',
        macAddress: 'AA:BB:CC:DD:EE:01',
        model: 'Cisco Catalyst 2960',
        firmwareVersion: '15.2(4)E10',
        location: '机房A-机柜01',
        rackInfo: 'U1-U2',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-01-15',
        warrantyExpiry: '2026-01-15',
        owner: '网络管理员',
        hasBackup: true,
        canBackup: true,
        description: '核心交换机',
        username: 'admin',
        password: 'cisco123',
        sshPort: 22,
        snmpCommunity: 'public',
        lastUpdate: '2024-01-15 14:30:25'
      },
      {
        id: 2,
        name: 'Web-Server-01',
        type: 'server',
        ip: '************0',
        serialNumber: 'SV001234567',
        macAddress: 'AA:BB:CC:DD:EE:02',
        model: 'Dell PowerEdge R740',
        firmwareVersion: '2.8.2',
        location: '机房A-机柜03',
        rackInfo: 'U5-U7',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-03-20',
        warrantyExpiry: '2026-03-20',
        owner: '系统管理员',
        hasBackup: false,
        canBackup: false,
        description: 'Web服务器',
        username: 'root',
        password: 'server123',
        sshPort: 22,
        snmpCommunity: 'private',
        lastUpdate: '2024-01-15 14:25:10'
      },
      {
        id: 3,
        name: 'Router-Gateway-01',
        type: 'router',
        ip: '***********',
        serialNumber: 'RT001234567',
        macAddress: 'AA:BB:CC:DD:EE:03',
        model: 'Cisco ISR 4331',
        firmwareVersion: '16.09.04',
        location: '机房A-机柜01',
        rackInfo: 'U3-U4',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-02-10',
        warrantyExpiry: '2026-02-10',
        owner: '网络管理员',
        hasBackup: true,
        canBackup: true,
        description: '网关路由器',
        username: 'admin',
        password: 'router123',
        sshPort: 22,
        snmpCommunity: 'public',
        lastUpdate: '2024-01-15 14:28:45'
      },
      {
        id: 4,
        name: 'Firewall-01',
        type: 'firewall',
        ip: '*************',
        serialNumber: 'FW001234567',
        macAddress: 'AA:BB:CC:DD:EE:04',
        model: 'Fortinet FortiGate 100F',
        firmwareVersion: '7.0.8',
        location: '机房A-机柜02',
        rackInfo: 'U1-U2',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-04-05',
        warrantyExpiry: '2026-04-05',
        owner: '安全管理员',
        hasBackup: true,
        canBackup: true,
        description: '边界防火墙',
        username: 'admin',
        password: 'firewall123',
        sshPort: 22,
        snmpCommunity: 'secure',
        lastUpdate: '2024-01-15 14:32:15'
      },
      {
        id: 5,
        name: 'AP-Office-01',
        type: 'ap',
        ip: '************',
        serialNumber: 'AP001234567',
        macAddress: 'AA:BB:CC:DD:EE:05',
        model: 'Ubiquiti UniFi AP AC Pro',
        firmwareVersion: '5.43.35',
        location: '办公区域A',
        rackInfo: '天花板安装',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-05-12',
        warrantyExpiry: '2025-05-12',
        owner: '网络管理员',
        hasBackup: false,
        canBackup: false,
        description: '办公区无线接入点',
        username: 'ubnt',
        password: 'wireless123',
        sshPort: 22,
        snmpCommunity: 'public',
        lastUpdate: '2024-01-15 14:35:30'
      },
      {
        id: 6,
        name: 'DB-Server-01',
        type: 'server',
        ip: '*************',
        serialNumber: 'DB001234567',
        macAddress: 'AA:BB:CC:DD:EE:06',
        model: 'HP ProLiant DL380',
        firmwareVersion: '2.65',
        location: '机房B-机柜01',
        rackInfo: 'U10-U12',
        status: 'online',
        lifecycle: 'active',
        purchaseDate: '2023-06-18',
        warrantyExpiry: '2026-06-18',
        owner: '数据库管理员',
        hasBackup: false,
        canBackup: false,
        description: '数据库服务器',
        username: 'root',
        password: 'database123',
        sshPort: 22,
        snmpCommunity: 'private',
        lastUpdate: '2024-01-15 14:40:20'
      }
    ]
  }

  /**
   * 获取所有网络资产
   */
  async getAllAssets(): Promise<NetworkAsset[]> {
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 100))
    return [...this.assets]
  }

  /**
   * 根据类型获取资产
   */
  async getAssetsByType(type: string): Promise<NetworkAsset[]> {
    await new Promise(resolve => setTimeout(resolve, 100))
    return this.assets.filter(asset => asset.type === type)
  }

  /**
   * 根据状态获取资产
   */
  async getAssetsByStatus(status: string): Promise<NetworkAsset[]> {
    await new Promise(resolve => setTimeout(resolve, 100))
    return this.assets.filter(asset => asset.status === status)
  }

  /**
   * 获取可用于脚本执行的主机选项
   */
  async getHostOptions(): Promise<HostOption[]> {
    const assets = await this.getAllAssets()
    return assets
      .filter(asset => asset.status === 'online') // 只返回在线设备
      .map(asset => ({
        value: asset.ip,
        label: `${asset.ip} (${asset.name})`,
        ip: asset.ip,
        type: asset.type,
        status: asset.status,
        asset
      }))
  }

  /**
   * 根据设备类型获取主机选项
   */
  async getHostOptionsByType(type: string): Promise<HostOption[]> {
    const assets = await this.getAssetsByType(type)
    return assets
      .filter(asset => asset.status === 'online')
      .map(asset => ({
        value: asset.ip,
        label: `${asset.ip} (${asset.name})`,
        ip: asset.ip,
        type: asset.type,
        status: asset.status,
        asset
      }))
  }

  /**
   * 根据IP地址获取资产
   */
  async getAssetByIp(ip: string): Promise<NetworkAsset | null> {
    await new Promise(resolve => setTimeout(resolve, 50))
    return this.assets.find(asset => asset.ip === ip) || null
  }

  /**
   * 搜索资产
   */
  async searchAssets(query: string): Promise<NetworkAsset[]> {
    await new Promise(resolve => setTimeout(resolve, 100))
    const lowerQuery = query.toLowerCase()
    return this.assets.filter(asset => 
      asset.name.toLowerCase().includes(lowerQuery) ||
      asset.ip.includes(lowerQuery) ||
      asset.serialNumber.toLowerCase().includes(lowerQuery) ||
      asset.location.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * 添加资产
   */
  async addAsset(asset: Omit<NetworkAsset, 'id' | 'lastUpdate'>): Promise<NetworkAsset> {
    await new Promise(resolve => setTimeout(resolve, 200))
    const newAsset: NetworkAsset = {
      ...asset,
      id: Math.max(...this.assets.map(a => a.id)) + 1,
      lastUpdate: new Date().toLocaleString('zh-CN')
    }
    this.assets.push(newAsset)
    return newAsset
  }

  /**
   * 更新资产
   */
  async updateAsset(id: number, updates: Partial<NetworkAsset>): Promise<NetworkAsset | null> {
    await new Promise(resolve => setTimeout(resolve, 200))
    const index = this.assets.findIndex(asset => asset.id === id)
    if (index === -1) return null
    
    this.assets[index] = {
      ...this.assets[index],
      ...updates,
      lastUpdate: new Date().toLocaleString('zh-CN')
    }
    return this.assets[index]
  }

  /**
   * 删除资产
   */
  async deleteAsset(id: number): Promise<boolean> {
    await new Promise(resolve => setTimeout(resolve, 200))
    const index = this.assets.findIndex(asset => asset.id === id)
    if (index === -1) return false
    
    this.assets.splice(index, 1)
    return true
  }
}

// 创建单例实例
export const networkAssetsService = new NetworkAssetsService()

// 导出类型
export type { NetworkAsset, HostOption }
