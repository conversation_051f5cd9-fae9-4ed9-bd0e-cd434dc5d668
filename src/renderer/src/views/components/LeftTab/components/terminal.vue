<template>
  <div class="userInfo">
    <a-card
      :bordered="false"
      class="userInfo-container"
    >
      <a-form
        :colon="false"
        label-align="left"
        wrapper-align="right"
        :label-col="{ span: 7, offset: 0 }"
        :wrapper-col="{ span: 17, class: 'right-aligned-wrapper' }"
        class="custom-form"
      >
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.terminalSetting') }}</span>
          </template>
        </a-form-item>

        <!-- 终端类型 -->
        <a-form-item
          :label="$t('user.terminalType')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.terminalType"
            class="terminal-type-select"
          >
            <a-select-option value="xterm">xterm</a-select-option>
            <a-select-option value="xterm-256color">xterm-256color</a-select-option>
            <a-select-option value="vt100">vt100</a-select-option>
            <a-select-option value="vt220">vt220</a-select-option>
          </a-select>
        </a-form-item>

        <!-- 字体大小 -->
        <a-form-item
          :label="$t('user.fontSize')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.fontSize"
            :min="8"
            :max="32"
            :step="1"
            class="font-size-input"
          />
        </a-form-item>

        <!-- 字体系列 -->
        <a-form-item
          :label="$t('user.fontFamily')"
          class="user_my-ant-form-item"
        >
          <a-select
            v-model:value="userConfig.fontFamily"
            class="font-family-select"
          >
            <a-select-option
              v-for="font in fontFamilyOptions"
              :key="font.value"
              :value="font.value"
            >
              {{ font.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 滚动缓冲区 -->
        <a-form-item
          :label="$t('user.scrollBack')"
          class="user_my-ant-form-item"
        >
          <a-input-number
            v-model:value="userConfig.scrollBack"
            :min="100"
            :max="10000"
            :step="100"
            class="scrollback-input"
          />
        </a-form-item>

        <!-- 光标样式 -->
        <a-form-item
          :label="$t('user.cursorStyle')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.cursorStyle"
            class="cursor-style-group"
          >
            <a-radio value="block">{{ $t('user.cursorBlock') }}</a-radio>
            <a-radio value="bar">{{ $t('user.cursorBar') }}</a-radio>
            <a-radio value="underline">{{ $t('user.cursorUnderline') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 鼠标事件设置 -->
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.mouseEvents') }}</span>
          </template>
        </a-form-item>

        <!-- 右键事件 -->
        <a-form-item
          :label="$t('user.rightMouseEvent')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.rightMouseEvent"
            class="mouse-event-group"
          >
            <a-radio value="paste">{{ $t('user.paste') }}</a-radio>
            <a-radio value="contextMenu">{{ $t('user.contextMenu') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 中键事件 -->
        <a-form-item
          :label="$t('user.middleMouseEvent')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.middleMouseEvent"
            class="mouse-event-group"
          >
            <a-radio value="paste">{{ $t('user.paste') }}</a-radio>
            <a-radio value="contextMenu">{{ $t('user.contextMenu') }}</a-radio>
            <a-radio value="none">{{ $t('user.none') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- SSH 代理设置 -->
        <a-form-item>
          <template #label>
            <span class="label-text">{{ $t('user.sshSettings') }}</span>
          </template>
        </a-form-item>

        <!-- SSH 代理状态 -->
        <a-form-item
          :label="$t('user.sshAgentsStatus')"
          class="user_my-ant-form-item"
        >
          <a-radio-group
            v-model:value="userConfig.sshAgentsStatus"
            class="ssh-agents-group"
          >
            <a-radio :value="1">{{ $t('user.enabled') }}</a-radio>
            <a-radio :value="2">{{ $t('user.disabled') }}</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- SSH 代理配置 -->
        <a-form-item
          :label="$t('user.sshAgentConfig')"
          class="user_my-ant-form-item"
        >
          <a-button
            type="primary"
            class="config-button"
            @click="showAgentConfigModal"
          >
            {{ $t('user.configureAgent') }}
          </a-button>
        </a-form-item>

        <!-- 保存按钮 -->
        <a-form-item class="save-button-item">
          <a-button
            type="primary"
            :loading="saving"
            class="save-button"
            @click="saveTerminalConfig"
          >
            {{ $t('user.saveSettings') }}
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- SSH 代理配置模态框 -->
    <a-modal
      v-model:open="agentConfigModalVisible"
      :title="$t('user.sshAgentConfig')"
      width="600px"
      @ok="saveAgentConfig"
      @cancel="cancelAgentConfig"
    >
      <a-form
        :colon="false"
        label-align="left"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item :label="$t('user.agentPath')">
          <a-input
            v-model:value="agentConfig.path"
            :placeholder="$t('user.agentPathPlaceholder')"
          />
        </a-form-item>

        <a-form-item :label="$t('user.agentArgs')">
          <a-input
            v-model:value="agentConfig.args"
            :placeholder="$t('user.agentArgsPlaceholder')"
          />
        </a-form-item>

        <a-form-item :label="$t('user.agentEnv')">
          <a-textarea
            v-model:value="agentConfig.env"
            :rows="4"
            :placeholder="$t('user.agentEnvPlaceholder')"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- SSH Agent Keys 管理模态框 -->
    <a-modal
      v-model:open="agentConfigModalVisible"
      :title="$t('user.sshAgentSettings')"
      width="700px"
    >
      <a-table
        :row-key="(record) => record.fingerprint"
        :columns="columns"
        :data-source="agentKeys"
        size="small"
        :pagination="false"
        :locale="{ emptyText: $t('user.noKeyAdd') }"
        class="agent-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button
              type="link"
              @click="removeKey(record)"
              >{{ $t('user.remove') }}
            </a-button>
          </template>
        </template>
      </a-table>

      <a-form
        layout="inline"
        style="width: 100%; margin-top: 20px; margin-bottom: 10px"
      >
        <a-form-item
          :label="t('personal.key')"
          style="flex: 1"
        >
          <a-select
            v-model:value="keyChainData"
            :options="keyChainOptions"
            :field-names="{ value: 'key', label: 'label' }"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button
            type="primary"
            @click="addKey"
            >{{ $t('common.add') }}</a-button
          >
        </a-form-item>
      </a-form>
    </a-modal>

    <a-modal
      v-model:open="sshProxyConfigShowModalVisible"
      :title="$t('user.proxySettings')"
      width="700px"
    >
      <a-table
        :row-key="(record) => record.name"
        :columns="proxyConfigColumns"
        :data-source="userConfig.value.sshProxyConfigs"
        size="small"
        :pagination="false"
        :locale="{ emptyText: $t('user.noProxyAdd') }"
        class="agent-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-button
              type="link"
              @click="removeProxyConfig(record.name)"
              >{{ $t('user.remove') }}
            </a-button>
          </template>
        </template>
      </a-table>

      <template #footer>
        <a-button
          type="primary"
          @click="handleProxyConfigAdd"
          >{{ $t('common.add') }}</a-button
        >
        <a-button @click="handleProxyConfigClose">{{ $t('common.close') }}</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:open="sshProxyConfigAddModalVisible"
      :title="$t('user.proxySettings')"
      :ok-text="$t('common.confirm')"
      :cancel-text="$t('common.cancel')"
      @ok="handleAddSshProxyConfigConfirm"
      @cancel="handleAddSshProxyConfigClose"
    >
      <a-form
        ref="proxyForm"
        class="proxy-form"
        :model="proxyConfig"
        :rules="proxyConfigRules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item
          name="name"
          :label="$t('user.proxyName')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.name"
            :placeholder="$t('user.proxyHost')"
          />
        </a-form-item>
        <a-form-item
          name="proxyType"
          :label="$t('user.proxyType')"
          style="margin-bottom: 12px"
        >
          <a-select
            v-model:value="proxyConfig.type"
            class="proxy-form-select"
            :placeholder="$t('user.proxyType')"
          >
            <a-select-option value="HTTP">HTTP</a-select-option>
            <a-select-option value="HTTPS">HTTPS</a-select-option>
            <a-select-option value="SOCKS4">SOCKS4</a-select-option>
            <a-select-option value="SOCKS5">SOCKS5</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          name="host"
          :label="$t('user.proxyHost')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.host"
            :placeholder="$t('user.proxyHost')"
          />
        </a-form-item>
        <a-form-item
          name="port"
          :label="$t('user.proxyPort')"
          style="margin-bottom: 12px"
        >
          <a-input-number
            v-model:value="proxyConfig.port"
            :min="1"
            :max="65535"
            :placeholder="$t('user.proxyPort')"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item
          name="enableProxyIdentity"
          :label="$t('user.enableProxyIdentity')"
          style="margin-bottom: 12px"
        >
          <a-switch
            :checked="proxyConfig.enableProxyIdentity"
            class="user_my-ant-form-item-content"
            @click="handleSshProxyIdentityChange"
          />
        </a-form-item>
        <a-form-item
          v-if="proxyConfig.enableProxyIdentity"
          name="proxyUsername"
          :label="$t('user.proxyUsername')"
          style="margin-bottom: 12px"
        >
          <a-input
            v-model:value="proxyConfig.username"
            :placeholder="$t('user.proxyUsername')"
          />
        </a-form-item>
        <a-form-item
          v-if="proxyConfig.enableProxyIdentity"
          name="proxyPassword"
          :label="$t('user.proxyPassword')"
          style="margin-bottom: 12px"
        >
          <a-input-password
            v-model:value="proxyConfig.password"
            :placeholder="$t('user.proxyPassword')"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { notification } from 'ant-design-vue'
import { userConfigStore } from '@/services/userConfigStoreService'
import { useI18n } from 'vue-i18n'
import eventBus from '@/utils/eventBus'

const { t } = useI18n()

// 响应式数据
const userConfig = ref({
  value: {
    terminalType: 'xterm-256color',
    fontSize: 14,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    scrollBack: 1000,
    cursorStyle: 'block',
    rightMouseEvent: 'paste',
    middleMouseEvent: 'paste',
    sshAgentsStatus: 1,
    sshProxyConfigs: [],
    sshAgentsMap: '[]',
    terminalBackground: {
      type: 'default',
      color: 'transparent',
      imageUrl: '',
      opacity: 1.0
    },
    themeColors: {
      primaryColor: '#1890ff',
      accentColor: '#52c41a',
      backgroundColor: '#141414',
      surfaceColor: '#1f1f1f',
      textColor: '#ffffff',
      borderColor: '#303030'
    }
  }
})

const agentConfigModalVisible = ref(false)
const saving = ref(false)

const agentConfig = reactive({
  path: '',
  args: '',
  env: ''
})

// 字体选项
const fontFamilyOptions = ref([
  { label: 'Monaco', value: 'Monaco, Menlo, "Ubuntu Mono", monospace' },
  { label: 'Consolas', value: 'Consolas, "Courier New", monospace' },
  { label: 'Menlo', value: 'Menlo, Monaco, "Ubuntu Mono", monospace' },
  { label: 'Ubuntu Mono', value: '"Ubuntu Mono", Monaco, Menlo, monospace' },
  { label: 'Source Code Pro', value: '"Source Code Pro", Monaco, Menlo, monospace' },
  { label: 'Fira Code', value: '"Fira Code", Monaco, Menlo, monospace' },
  { label: 'JetBrains Mono', value: '"JetBrains Mono", Monaco, Menlo, monospace' }
])

// 方法
const showAgentConfigModal = () => {
  agentConfigModalVisible.value = true
}

const saveAgentConfig = () => {
  // 保存代理配置逻辑
  try {
    // 这里可以添加保存到配置文件的逻辑
    notification.success({
      message: t('common.success'),
      description: t('user.agentConfigSaved')
    })
    agentConfigModalVisible.value = false
  } catch (error) {
    notification.error({
      message: t('common.error'),
      description: t('user.agentConfigSaveFailed')
    })
  }
}

const cancelAgentConfig = () => {
  agentConfigModalVisible.value = false
}

const saveTerminalConfig = async () => {
  saving.value = true
  try {
    // 保存终端配置
    await userConfigStore.saveConfig(userConfig.value)

    // 发送事件通知其他组件配置已更新
    eventBus.emit('terminalConfigUpdated', userConfig.value)

    notification.success({
      message: t('common.success'),
      description: t('user.terminalConfigSaved')
    })
  } catch (error) {
    notification.error({
      message: t('common.error'),
      description: t('user.terminalConfigSaveFailed')
    })
  } finally {
    saving.value = false
  }
}

// 初始化配置
const loadTerminalConfig = async () => {
  try {
    const config = await userConfigStore.getConfig('terminal')
    if (config) {
      Object.assign(userConfig.value, config)
    }
  } catch (error) {
    console.error('Failed to load terminal config:', error)
  }
}

// 监听配置变化
watch(
  userConfig,
  (newConfig) => {
    // 实时应用配置变化
    eventBus.emit('terminalConfigChanged', newConfig)
  },
  { deep: true }
)

// 组件挂载时加载配置
onMounted(() => {
  loadTerminalConfig()
})

const columns = [
  {
    title: t('user.fingerprint'),
    dataIndex: 'fingerprint',
    key: 'fingerprint'
  },
  {
    title: t('user.comment'),
    dataIndex: 'comment',
    key: 'comment'
  },
  {
    title: t('user.type'),
    dataIndex: 'keyType',
    key: 'keyType'
  },
  {
    title: t('extensions.action'),
    dataIndex: 'action',
    key: 'action'
  }
]

const proxyConfigColumns = [
  {
    title: t('user.proxyName'),
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: t('user.proxyType'),
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: t('user.proxyHost'),
    dataIndex: 'host',
    key: 'host'
  },
  {
    title: t('user.proxyPort'),
    dataIndex: 'port',
    key: 'port'
  },
  {
    title: t('user.proxyUsername'),
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: t('extensions.action'),
    dataIndex: 'action',
    key: 'action'
  }
]

// Load saved configuration
const loadSavedConfig = async () => {
  try {
    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      userConfig.value = {
        ...userConfig.value,
        ...savedConfig,
        // 确保terminalBackground字段正确初始化
        terminalBackground: savedConfig.terminalBackground || {
          type: 'default',
          color: '#1a1a1a',
          imageUrl: '',
          opacity: 1.0
        },
        // 确保themeColors字段正确初始化
        themeColors: savedConfig.themeColors || {
          primaryColor: '#1890ff',
          accentColor: '#52c41a',
          backgroundColor: '#141414',
          surfaceColor: '#1f1f1f',
          textColor: '#ffffff',
          borderColor: '#303030'
        }
      }

      // 注释掉自动应用主题颜色，避免覆盖应用默认主题
      // if (savedConfig.themeColors) {
      //   applyThemeColors()
      // }
    }
  } catch (error) {
    console.error('Failed to load config:', error)
    notification.error({
      message: t('user.loadConfigFailed'),
      description: t('user.loadConfigFailedDescription')
    })
  }
}

// ssh代理
const sshProxyConfigAddModalVisible = ref(false)
const sshProxyConfigShowModalVisible = ref(false)

const handleProxyConfigClose = async () => {
  sshProxyConfigShowModalVisible.value = false
}
//
const proxyConfigRules = {
  name: [
    { required: true, message: t('user.pleaseInputProxyName'), trigger: 'blur' },
    {
      validator: (rule, value) => {
        if (!value) return Promise.resolve()

        const nameExists = userConfig.value.sshProxyConfigs.some((config) => config.name === value)

        if (nameExists) {
          return Promise.reject(new Error(t('user.pleaseInputOtherProxyName')))
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  host: [{ required: true, message: t('user.pleaseInputProxyHost'), trigger: 'blur' }],
  port: [{ type: 'number', min: 1, max: 65535, message: t('user.errorProxyPort'), trigger: 'blur' }]
}

const handleProxyConfigAdd = async () => {
  sshProxyConfigAddModalVisible.value = true
}

const handleSshProxyIdentityChange = async (checked) => {
  proxyConfig.value.enableProxyIdentity = checked
}

const proxyForm = ref()
const handleAddSshProxyConfigConfirm = async () => {
  await proxyForm.value.validateFields()
  userConfig.value.sshProxyConfigs.push(proxyConfig.value)
  sshProxyConfigAddModalVisible.value = false
  proxyConfig.value = defaultProxyConfig
}

const removeProxyConfig = (proxyName) => {
  const index = userConfig.value.sshProxyConfigs.findIndex((config) => config.name === proxyName)

  if (index !== -1) {
    userConfig.value.sshProxyConfigs.splice(index, 1)
    return true
  } else {
    return false
  }
}

const handleAddSshProxyConfigClose = async () => {
  sshProxyConfigAddModalVisible.value = false
}

const keyChainOptions = ref([])
const agentKeys = ref([])
const keyChainData = ref()

const removeKey = async (record) => {
  await api.removeKey({ keyId: record.id })
  const target = keyChainOptions.value.find((item) => item.label === record.comment)

  if (target) {
    const sshAgentsMap = JSON.parse(userConfig.value.sshAgentsMap)
    const index = sshAgentsMap.indexOf(target.key)
    if (index !== -1) {
      sshAgentsMap.splice(index, 1)
    }
    userConfig.value.sshAgentsMap = JSON.stringify(sshAgentsMap)
  }
  await getAgentKeys()
}

const addKey = async () => {
  if (keyChainData.value) {
    await api.getKeyChainInfo({ id: keyChainData.value }).then((res) => {
      api
        .addKey({
          keyData: res.private_key,
          comment: res.chain_name,
          passphrase: res.passphrase
        })
        .then(() => {
          notification.success({
            message: t('user.addSuccess')
          })
          let sshAgentKey = JSON.parse(userConfig.value.sshAgentsMap)
          sshAgentKey.push(keyChainData.value)
          sshAgentKey = Array.from(new Set(sshAgentKey))
          userConfig.value.sshAgentsMap = JSON.stringify(sshAgentKey)
          keyChainData.value = null
          getAgentKeys()
        })
        .catch(() => {
          notification.error({
            message: t('user.addFailed')
          })
          keyChainData.value = null
        })
    })
  }
}
const getAgentKeys = async () => {
  const res = await api.listKeys()
  agentKeys.value = res.keys
}

const saveConfig = async () => {
  try {
    const configToStore = {
      fontSize: userConfig.value.fontSize,
      fontFamily: userConfig.value.fontFamily,
      scrollBack: userConfig.value.scrollBack,
      cursorStyle: userConfig.value.cursorStyle,
      middleMouseEvent: userConfig.value.middleMouseEvent,
      rightMouseEvent: userConfig.value.rightMouseEvent,
      terminalType: userConfig.value.terminalType,
      sshAgentsStatus: userConfig.value.sshAgentsStatus,
      sshAgentsMap: userConfig.value.sshAgentsMap,
      sshProxyConfigs: userConfig.value.sshProxyConfigs,
      terminalBackground: userConfig.value.terminalBackground,
      themeColors: userConfig.value.themeColors
    }

    await userConfigStore.saveConfig(configToStore)

    // 发送事件通知终端组件更新背景
    eventBus.emit('updateTerminalBackground', userConfig.value.terminalBackground)
  } catch (error) {
    console.error('Failed to save config:', error)
    notification.error({
      message: t('user.error'),
      description: t('user.saveConfigFailedDescription')
    })
  }
}

watch(
  () => userConfig.value,
  async () => {
    await saveConfig()
  },
  { deep: true }
)

watch(
  () => userConfig.value.fontFamily,
  (newFontFamily) => {
    eventBus.emit('updateTerminalFont', newFontFamily)
  }
)

onMounted(async () => {
  console.log('Terminal component mounted')
  try {
    await loadSavedConfig()
    console.log('Terminal config loaded:', userConfig.value)
  } catch (error) {
    console.error('Terminal component mount error:', error)
  }
})
</script>

<style scoped>
.userInfo {
  width: 100%;
  height: 100%;
}

.userInfo-container {
  width: 100%;
  height: 100%;
  background-color: var(--bg-color) !important;
  border-radius: 6px;
  overflow: hidden;
  padding: 4px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: var(--text-color);
}

:deep(.ant-card) {
  height: 100%;
  background-color: var(--bg-color) !important;
}

:deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color);
}

.proxy-form :deep(.ant-form-item-label > label) {
  color: #000000 !important;
}

.custom-form {
  color: var(--text-color);
  align-content: center;
}

.custom-form :deep(.ant-form-item-label) {
  padding-right: 20px;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input),
.custom-form :deep(.ant-input-number),
.custom-form :deep(.ant-radio-wrapper) {
  color: var(--text-color);
}

.custom-form :deep(.ant-input-number) {
  background-color: var(--input-number-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.3s;
  width: 100px !important;
}

.custom-form :deep(.ant-input-number:hover),
.custom-form :deep(.ant-input-number:focus),
.custom-form :deep(.ant-input-number-focused) {
  background-color: var(--input-number-hover-bg);
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-form :deep(.ant-input-number-input) {
  height: 32px;
  padding: 4px 8px;
  background-color: transparent;
  color: var(--text-color);
}

.label-text {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3;
}

.user_my-ant-form-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.65);
  font-size: 30px;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  -webkit-font-feature-settings: 'tnum';
  font-feature-settings: 'tnum';
  margin-bottom: 14px;
  vertical-align: top;
  color: #ffffff;
}

.terminal-type-select {
  width: 180px !important;
  text-align: left;
}

.font-family-select {
  width: 180px !important;
  text-align: left;
}

.font-family-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.font-family-select :deep(.ant-select-selector:hover) {
  border-color: #1890ff;
  background-color: var(--select-hover-bg);
}

.font-family-select :deep(.ant-select-focused .ant-select-selector),
.font-family-select :deep(.ant-select-selector:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  background-color: var(--select-hover-bg);
}

.font-family-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.font-family-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

.divider-container {
  width: calc(65%);
  margin: -10px calc(16%);
}

:deep(.right-aligned-wrapper) {
  text-align: right;
  color: #ffffff;
}

.checkbox-md :deep(.ant-checkbox-inner) {
  width: 20px;
  height: 20px;
}

.telemetry-description-item {
  margin-top: -15px;
  margin-bottom: 14px;
}

.telemetry-description-item :deep(.ant-form-item-control) {
  margin-left: 0 !important;
  max-width: 100% !important;
}

.telemetry-description {
  font-size: 12px;
  color: var(--text-color-secondary);
  line-height: 1.4;
  opacity: 0.8;
  text-align: left;
  margin: 0;
  margin-left: 20px;
  padding: 0;
  word-wrap: break-word;
}

.telemetry-description a {
  color: #1890ff;
  text-decoration: none;
  transition: color 0.3s;
}

.telemetry-description a:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.mouse-event-row {
  margin-bottom: 10px;
  min-height: 32px;
}

.mouse-event-label {
  font-size: 14px;
  color: var(--text-color);
  min-width: 110px;
  text-align: left;
  opacity: 0.9;
  margin-right: 10px;
}

.mouse-event-select {
  width: 140px;
}

.mouse-event-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.mouse-event-select :deep(.ant-select-selector:hover),
.mouse-event-select :deep(.ant-select-focused .ant-select-selector) {
  background-color: var(--select-hover-bg);
  border-color: #1890ff;
}

.mouse-event-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.mouse-event-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

:deep(.ant-select .ant-select-selector) {
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

:deep(.ant-select.ant-select-focused .ant-select-selector) {
  background-color: var(--bg-color-secondary) !important;
  border-color: #1890ff !important;
}
::v-deep(.agent-table .ant-table-tbody > tr > td),
::v-deep(.agent-table .ant-table-thead > tr > th) {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  line-height: 1 !important;
  font-size: 12px !important;
}
.agent-table .ant-table-tbody > tr {
  height: 28px !important;
}

.proxy-form :deep(:where(.ant-form-item)) {
  margin-bottom: 12px !important;
}

/* Setting button styles - consistent with host management import button */
.setting-button {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s ease;
  /* Ensure button inherits right alignment from parent */
  margin-left: auto;
  margin-right: 0;
}

.setting-button:hover {
  background: var(--hover-bg-color);
  border-color: #1890ff;
  color: #1890ff;
}

.setting-button:active {
  background: var(--active-bg-color);
}

/* Override Ant Design default button styles for setting buttons */
.setting-button:deep(.ant-btn) {
  background: var(--bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

/* Ensure setting button form items inherit right alignment */
.user_my-ant-form-item:has(.setting-button) {
  text-align: right;
}

.user_my-ant-form-item:has(.setting-button) :deep(.ant-form-item-control) {
  text-align: right;
}

.setting-button:deep(.ant-btn:hover) {
  background: var(--hover-bg-color) !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
}

.setting-button:deep(.ant-btn:active) {
  background: var(--active-bg-color) !important;
}

/* Terminal background settings styles */
.terminal-background-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.background-type-row,
.background-color-row,
.background-image-row,
.background-opacity-row {
  display: flex;
  align-items: center;
  gap: 10px;
  min-height: 32px;
}

.background-label {
  font-size: 14px;
  color: var(--text-color);
  min-width: 120px;
  text-align: left;
  opacity: 0.9;
}

.background-type-select {
  width: 140px;
}

.background-type-select :deep(.ant-select-selector) {
  background-color: var(--select-bg);
  border: 1px solid var(--select-border);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
  height: 32px;
}

.background-type-select :deep(.ant-select-selector:hover),
.background-type-select :deep(.ant-select-focused .ant-select-selector) {
  background-color: var(--select-hover-bg);
  border-color: #1890ff;
}

.background-type-select :deep(.ant-select-selection-item) {
  color: var(--text-color);
  font-size: 14px;
  line-height: 32px;
}

.background-type-select :deep(.ant-select-arrow) {
  color: var(--text-color);
  opacity: 0.7;
}

.color-picker {
  width: 60px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s;
}

.color-picker:hover {
  border-color: #1890ff;
}

.image-input-container {
  display: flex;
  gap: 8px;
  align-items: center;
  flex: 1;
}

.image-url-input {
  flex: 1;
  max-width: 200px;
}

.image-url-input :deep(.ant-input) {
  background-color: var(--input-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-color);
  transition: all 0.3s;
}

.image-url-input :deep(.ant-input:hover),
.image-url-input :deep(.ant-input:focus) {
  border-color: #1890ff;
  background-color: var(--input-hover-bg);
}

.upload-button {
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: all 0.3s ease;
  font-size: 12px;
}

.upload-button:hover {
  background: var(--hover-bg-color);
  border-color: #1890ff;
  color: #1890ff;
}

.opacity-slider {
  width: 120px;
}

.opacity-slider :deep(.ant-slider-rail) {
  background-color: var(--border-color);
}

.opacity-slider :deep(.ant-slider-track) {
  background-color: #1890ff;
}

.opacity-slider :deep(.ant-slider-handle) {
  border-color: #1890ff;
  background-color: #1890ff;
}

.opacity-value {
  font-size: 12px;
  color: var(--text-color);
  min-width: 35px;
  text-align: center;
}

/* Preset backgrounds styles */
.background-presets-container {
  width: 100%;
  margin-top: 12px;
}

.background-presets-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.preset-backgrounds {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  width: 100%;
  padding: 0;
  margin: 0;
  justify-content: flex-start;
}

.preset-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 10px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bg-color-secondary);
  min-width: 80px;
  position: relative;
  z-index: 1;
}

.preset-item:hover {
  border-color: #1890ff;
  background: var(--hover-bg-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.preset-item.active {
  border-color: #1890ff;
  background: var(--active-bg-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
}

.preset-thumbnail {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid var(--border-color);
}

.preset-name {
  font-size: 11px;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
}

/* Theme colors styles */
.theme-colors-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.theme-color-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.color-label {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
  min-width: 100px;
}

.theme-color-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 8px;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.theme-color-actions .ant-btn {
  min-width: 80px;
}
</style>
