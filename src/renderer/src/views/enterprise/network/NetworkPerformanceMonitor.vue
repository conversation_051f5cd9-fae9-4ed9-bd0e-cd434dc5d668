<template>
  <div
    class="network-performance-monitor"
    style="height: 100%; max-height: calc(100vh - 60px); overflow-y: auto; padding: 20px"
  >
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <a-button
          type="text"
          class="back-button"
          @click="goBack"
        >
          <ArrowLeft class="back-icon" />
          返回
        </a-button>
        <div class="header-content">
          <h1 class="page-title">
            <Activity class="title-icon" />
            网络性能监控
          </h1>
          <p class="page-description">监控带宽使用率、网络延迟和连接质量</p>
        </div>
      </div>
      <div class="header-actions">
        <a-button @click="refreshData">
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新数据
        </a-button>
        <a-button
          type="primary"
          @click="addMonitorTarget"
        >
          <Plus class="btn-icon" />
          添加监控对象
        </a-button>
        <a-button
          type="primary"
          @click="exportReport"
        >
          <Download class="btn-icon" />
          导出报告
        </a-button>
      </div>
    </div>

    <!-- 性能概览 -->
    <div class="performance-overview">
      <div class="overview-grid">
        <div class="performance-card">
          <div class="card-header">
            <div class="card-title">
              <Zap class="card-icon" />
              总带宽使用率
            </div>
            <div class="card-value">{{ performanceData.totalBandwidthUsage }}%</div>
          </div>
          <div class="card-chart">
            <div class="bandwidth-bar">
              <div
                class="bandwidth-fill"
                :style="{ width: performanceData.totalBandwidthUsage + '%' }"
                :class="getBandwidthClass(performanceData.totalBandwidthUsage)"
              ></div>
            </div>
            <div class="bandwidth-details">
              <span>上行: {{ performanceData.uploadUsage }}%</span>
              <span>下行: {{ performanceData.downloadUsage }}%</span>
            </div>
          </div>
        </div>

        <div class="performance-card">
          <div class="card-header">
            <div class="card-title">
              <Clock class="card-icon" />
              平均延迟
            </div>
            <div class="card-value">{{ performanceData.avgLatency }}ms</div>
          </div>
          <div class="card-chart">
            <div
              class="latency-indicator"
              :class="getLatencyClass(performanceData.avgLatency)"
            >
              {{ getLatencyLabel(performanceData.avgLatency) }}
            </div>
            <div class="latency-details">
              <span>最小: {{ performanceData.minLatency }}ms</span>
              <span>最大: {{ performanceData.maxLatency }}ms</span>
            </div>
          </div>
        </div>

        <div class="performance-card">
          <div class="card-header">
            <div class="card-title">
              <TrendingUp class="card-icon" />
              吞吐量
            </div>
            <div class="card-value">{{ performanceData.throughput }}Mbps</div>
          </div>
          <div class="card-chart">
            <div class="throughput-trend">
              <div
                class="trend-indicator"
                :class="performanceData.throughputTrend"
              >
                {{ performanceData.throughputChange }}%
              </div>
            </div>
            <div class="throughput-details">
              <span>峰值: {{ performanceData.peakThroughput }}Mbps</span>
            </div>
          </div>
        </div>

        <div class="performance-card">
          <div class="card-header">
            <div class="card-title">
              <Wifi class="card-icon" />
              连接质量
            </div>
            <div class="card-value">{{ performanceData.connectionQuality }}%</div>
          </div>
          <div class="card-chart">
            <div
              class="quality-score"
              :class="getQualityClass(performanceData.connectionQuality)"
            >
              {{ getQualityLabel(performanceData.connectionQuality) }}
            </div>
            <div class="quality-details">
              <span>丢包率: {{ performanceData.packetLoss }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时监控图表 -->
    <div class="realtime-charts">
      <div class="charts-grid">
        <div class="chart-container">
          <div class="chart-header">
            <h3>带宽使用趋势</h3>
            <div class="chart-controls">
              <a-radio-group
                v-model:value="bandwidthTimeRange"
                size="small"
              >
                <a-radio-button value="1h">1小时</a-radio-button>
                <a-radio-button value="6h">6小时</a-radio-button>
                <a-radio-button value="24h">24小时</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div class="chart-content">
            <!-- 这里集成图表组件 -->
            <div class="chart-placeholder">
              <BarChart3 class="chart-icon" />
              <span>带宽使用趋势图</span>
            </div>
          </div>
        </div>

        <div class="chart-container">
          <div class="chart-header">
            <h3>延迟监控</h3>
            <div class="chart-controls">
              <a-radio-group
                v-model:value="latencyTimeRange"
                size="small"
              >
                <a-radio-button value="1h">1小时</a-radio-button>
                <a-radio-button value="6h">6小时</a-radio-button>
                <a-radio-button value="24h">24小时</a-radio-button>
              </a-radio-group>
            </div>
          </div>
          <div class="chart-content">
            <div class="chart-placeholder">
              <LineChart class="chart-icon" />
              <span>延迟趋势图</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网络接口监控 -->
    <div class="interface-monitoring">
      <div class="section-header">
        <h2 class="section-title">网络接口监控</h2>
        <div class="section-actions">
          <a-select
            v-model:value="selectedInterface"
            style="width: 150px"
          >
            <a-select-option value="all">全部接口</a-select-option>
            <a-select-option value="eth0">eth0</a-select-option>
            <a-select-option value="eth1">eth1</a-select-option>
            <a-select-option value="wlan0">wlan0</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="interface-grid">
        <div
          v-for="networkInterface in filteredInterfaces"
          :key="networkInterface.name"
          class="interface-card"
          :class="networkInterface.status"
        >
          <div class="interface-header">
            <div class="interface-info">
              <h3 class="interface-name">{{ networkInterface.name }}</h3>
              <p class="interface-type">{{ networkInterface.type }}</p>
            </div>
            <div
              class="interface-status"
              :class="networkInterface.status"
            >
              <div class="status-dot"></div>
              <span>{{ getInterfaceStatusLabel(networkInterface.status) }}</span>
            </div>
          </div>

          <div class="interface-metrics">
            <div class="metric-group">
              <div class="metric">
                <span class="metric-label">上行速率</span>
                <span class="metric-value">{{ networkInterface.uploadSpeed }} Mbps</span>
              </div>
              <div class="metric">
                <span class="metric-label">下行速率</span>
                <span class="metric-value">{{ networkInterface.downloadSpeed }} Mbps</span>
              </div>
            </div>
            <div class="metric-group">
              <div class="metric">
                <span class="metric-label">错误包</span>
                <span class="metric-value">{{ networkInterface.errorPackets }}</span>
              </div>
              <div class="metric">
                <span class="metric-label">丢包率</span>
                <span class="metric-value">{{ networkInterface.packetLoss }}%</span>
              </div>
            </div>
          </div>

          <div class="interface-utilization">
            <div class="utilization-header">
              <span>接口利用率</span>
              <span>{{ networkInterface.utilization }}%</span>
            </div>
            <div class="utilization-bar">
              <div
                class="utilization-fill"
                :style="{ width: networkInterface.utilization + '%' }"
                :class="getUtilizationClass(networkInterface.utilization)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能告警 -->
    <div class="performance-alerts">
      <div class="section-header">
        <h2 class="section-title">性能告警</h2>
        <div class="alert-summary">
          <span class="alert-count critical">{{ alertCounts.critical }} 严重</span>
          <span class="alert-count warning">{{ alertCounts.warning }} 警告</span>
          <span class="alert-count info">{{ alertCounts.info }} 信息</span>
        </div>
      </div>

      <div class="alerts-list">
        <div
          v-for="alert in recentAlerts"
          :key="alert.id"
          class="alert-item"
          :class="alert.level"
        >
          <div class="alert-icon">
            <AlertTriangle
              v-if="alert.level === 'critical'"
              class="icon"
            />
            <AlertCircle
              v-else-if="alert.level === 'warning'"
              class="icon"
            />
            <Info
              v-else
              class="icon"
            />
          </div>
          <div class="alert-content">
            <h4 class="alert-title">{{ alert.title }}</h4>
            <p class="alert-description">{{ alert.description }}</p>
            <span class="alert-time">{{ alert.timestamp }}</span>
          </div>
          <div class="alert-actions">
            <a-button
              size="small"
              @click="acknowledgeAlert(alert)"
            >
              确认
            </a-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时性能图表 -->
    <div class="performance-charts">
      <div class="section-header">
        <h2 class="section-title">实时性能图表</h2>
        <div class="chart-controls">
          <a-select
            v-model:value="chartTimeRange"
            style="width: 120px; margin-right: 8px"
          >
            <a-select-option value="5m">最近5分钟</a-select-option>
            <a-select-option value="15m">最近15分钟</a-select-option>
            <a-select-option value="1h">最近1小时</a-select-option>
            <a-select-option value="6h">最近6小时</a-select-option>
          </a-select>
          <a-button @click="refreshCharts">
            <RefreshCw class="btn-icon" />
            刷新图表
          </a-button>
        </div>
      </div>

      <div class="charts-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3>带宽使用趋势</h3>
            <div class="chart-legend">
              <span class="legend-item upload">上行</span>
              <span class="legend-item download">下行</span>
            </div>
          </div>
          <div class="chart-container">
            <div class="bandwidth-chart">
              <div
                v-for="(point, index) in bandwidthHistory"
                :key="index"
                class="chart-point"
                :style="{
                  left: `${(index / (bandwidthHistory.length - 1)) * 100}%`,
                  bottom: `${point.upload}%`
                }"
                :title="`上行: ${point.upload}%, 下行: ${point.download}%`"
              >
                <div
                  class="point-upload"
                  :style="{ height: `${point.upload}%` }"
                ></div>
                <div
                  class="point-download"
                  :style="{ height: `${point.download}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>延迟变化趋势</h3>
            <div class="chart-stats">
              <span>平均: {{ avgLatency }}ms</span>
              <span>峰值: {{ maxLatency }}ms</span>
            </div>
          </div>
          <div class="chart-container">
            <div class="latency-chart">
              <div
                v-for="(latency, index) in latencyHistory"
                :key="index"
                class="latency-bar"
                :style="{
                  height: `${Math.min(latency / 2, 100)}%`,
                  left: `${(index / (latencyHistory.length - 1)) * 100}%`
                }"
                :class="getLatencyClass(latency)"
                :title="`延迟: ${latency}ms`"
              ></div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>连接质量分析</h3>
            <div class="quality-score">
              <span
                class="score-value"
                :class="getQualityClass(connectionQuality)"
              >
                {{ connectionQuality }}
              </span>
              <span class="score-label">质量评分</span>
            </div>
          </div>
          <div class="chart-container">
            <div class="quality-metrics">
              <div class="quality-metric">
                <div class="metric-name">稳定性</div>
                <div class="metric-bar">
                  <div
                    class="metric-fill stability"
                    :style="{ width: `${qualityMetrics.stability}%` }"
                  ></div>
                </div>
                <div class="metric-value">{{ qualityMetrics.stability }}%</div>
              </div>
              <div class="quality-metric">
                <div class="metric-name">可靠性</div>
                <div class="metric-bar">
                  <div
                    class="metric-fill reliability"
                    :style="{ width: `${qualityMetrics.reliability}%` }"
                  ></div>
                </div>
                <div class="metric-value">{{ qualityMetrics.reliability }}%</div>
              </div>
              <div class="quality-metric">
                <div class="metric-name">响应性</div>
                <div class="metric-bar">
                  <div
                    class="metric-fill responsiveness"
                    :style="{ width: `${qualityMetrics.responsiveness}%` }"
                  ></div>
                </div>
                <div class="metric-value">{{ qualityMetrics.responsiveness }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加监控对象对话框 -->
    <a-modal
      v-model:open="showAddTargetModal"
      title="添加监控对象"
      width="600px"
      :footer="null"
    >
      <a-form
        :model="targetForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="saveMonitorTarget"
      >
        <a-form-item
          label="监控对象名称"
          name="name"
          :rules="[{ required: true, message: '请输入监控对象名称' }]"
        >
          <a-input
            v-model:value="targetForm.name"
            placeholder="请输入监控对象名称"
          />
        </a-form-item>

        <a-form-item
          label="监控类型"
          name="type"
          :rules="[{ required: true, message: '请选择监控类型' }]"
        >
          <a-select
            v-model:value="targetForm.type"
            placeholder="请选择监控类型"
          >
            <a-select-option value="bandwidth">带宽监控</a-select-option>
            <a-select-option value="latency">延迟监控</a-select-option>
            <a-select-option value="throughput">吞吐量监控</a-select-option>
            <a-select-option value="interface">网络接口监控</a-select-option>
            <a-select-option value="quality">连接质量监控</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="目标地址"
          name="target"
          :rules="[{ required: true, message: '请输入目标地址' }]"
        >
          <a-input
            v-model:value="targetForm.target"
            placeholder="IP地址、域名或网络接口名称"
          />
        </a-form-item>

        <a-form-item
          label="监控端口"
          name="port"
        >
          <a-input-number
            v-model:value="targetForm.port"
            :min="1"
            :max="65535"
            placeholder="端口号(可选)"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="监控协议"
          name="protocol"
        >
          <a-select
            v-model:value="targetForm.protocol"
            placeholder="请选择监控协议"
          >
            <a-select-option value="icmp">ICMP (Ping)</a-select-option>
            <a-select-option value="tcp">TCP</a-select-option>
            <a-select-option value="udp">UDP</a-select-option>
            <a-select-option value="snmp">SNMP</a-select-option>
            <a-select-option value="http">HTTP</a-select-option>
            <a-select-option value="https">HTTPS</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="监控间隔"
          name="interval"
        >
          <a-select
            v-model:value="targetForm.interval"
            placeholder="请选择监控间隔"
          >
            <a-select-option value="30">30秒</a-select-option>
            <a-select-option value="60">1分钟</a-select-option>
            <a-select-option value="300">5分钟</a-select-option>
            <a-select-option value="600">10分钟</a-select-option>
            <a-select-option value="1800">30分钟</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item
          label="告警阈值"
          name="threshold"
        >
          <a-input-number
            v-model:value="targetForm.threshold"
            :min="0"
            :max="100"
            placeholder="告警阈值(%或ms)"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item
          label="SNMP团体名"
          name="snmpCommunity"
          v-if="targetForm.protocol === 'snmp'"
        >
          <a-input
            v-model:value="targetForm.snmpCommunity"
            placeholder="SNMP Community"
          />
        </a-form-item>

        <a-form-item
          label="启用状态"
          name="enabled"
        >
          <a-switch
            v-model:checked="targetForm.enabled"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>

        <a-form-item
          label="描述"
          name="description"
        >
          <a-textarea
            v-model:value="targetForm.description"
            :rows="3"
            placeholder="监控对象描述信息"
          />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button @click="closeAddTargetModal">取消</a-button>
            <a-button
              type="primary"
              html-type="submit"
              :loading="saving"
            >
              {{ saving ? '保存中...' : '保存' }}
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Activity,
  RefreshCw,
  Download,
  Plus,
  Zap,
  Clock,
  TrendingUp,
  Wifi,
  BarChart3,
  LineChart,
  AlertTriangle,
  AlertCircle,
  Info
} from 'lucide-vue-next'
import eventBus from '@/utils/eventBus'

const router = useRouter()
const loading = ref(false)
const bandwidthTimeRange = ref('24h')
const latencyTimeRange = ref('24h')
const selectedInterface = ref('all')
const showAddTargetModal = ref(false)
const saving = ref(false)

// 新增状态变量
const chartTimeRange = ref('1h')
const connectionQuality = ref(92)
const avgLatency = computed(() => {
  return latencyHistory.value.reduce((sum, val) => sum + val, 0) / latencyHistory.value.length
})
const maxLatency = computed(() => {
  return Math.max(...latencyHistory.value)
})

// 历史数据
const bandwidthHistory = ref([
  { upload: 45, download: 72 },
  { upload: 48, download: 75 },
  { upload: 42, download: 68 },
  { upload: 50, download: 78 },
  { upload: 46, download: 70 },
  { upload: 52, download: 82 },
  { upload: 49, download: 76 },
  { upload: 47, download: 73 },
  { upload: 51, download: 79 },
  { upload: 45, download: 71 }
])

const latencyHistory = ref([12, 15, 11, 18, 14, 16, 13, 17, 12, 19, 15, 14, 16, 13, 12])

// 质量指标
const qualityMetrics = reactive({
  stability: 95,
  reliability: 88,
  responsiveness: 92
})

// 监控对象表单数据
const targetForm = ref({
  name: '',
  type: '',
  target: '',
  port: null,
  protocol: 'icmp',
  interval: '60',
  threshold: 80,
  snmpCommunity: 'public',
  enabled: true,
  description: ''
})

// 监控对象列表
const monitorTargets = ref([
  {
    id: 1,
    name: '主网关监控',
    type: 'latency',
    target: '***********',
    protocol: 'icmp',
    interval: 60,
    threshold: 50,
    enabled: true,
    status: 'active',
    lastCheck: '2024-01-15 14:30:25',
    currentValue: 12
  },
  {
    id: 2,
    name: '外网连接监控',
    type: 'latency',
    target: '*******',
    protocol: 'icmp',
    interval: 30,
    threshold: 100,
    enabled: true,
    status: 'active',
    lastCheck: '2024-01-15 14:30:20',
    currentValue: 25
  }
])

// 性能数据
const performanceData = reactive({
  totalBandwidthUsage: 68,
  uploadUsage: 45,
  downloadUsage: 72,
  avgLatency: 12,
  minLatency: 8,
  maxLatency: 25,
  throughput: 850,
  throughputTrend: 'up',
  throughputChange: 15,
  peakThroughput: 1200,
  connectionQuality: 92,
  packetLoss: 0.2
})

// 网络接口数据
const networkInterfaces = ref([
  {
    name: 'eth0',
    type: '千兆以太网',
    status: 'active',
    uploadSpeed: 45.2,
    downloadSpeed: 78.5,
    errorPackets: 12,
    packetLoss: 0.1,
    utilization: 68
  },
  {
    name: 'eth1',
    type: '千兆以太网',
    status: 'active',
    uploadSpeed: 32.1,
    downloadSpeed: 56.8,
    errorPackets: 8,
    packetLoss: 0.2,
    utilization: 45
  },
  {
    name: 'wlan0',
    type: '无线网络',
    status: 'warning',
    uploadSpeed: 12.5,
    downloadSpeed: 25.3,
    errorPackets: 45,
    packetLoss: 2.1,
    utilization: 78
  }
])

// 告警数据
const alertCounts = reactive({
  critical: 2,
  warning: 5,
  info: 8
})

const recentAlerts = ref([
  {
    id: 1,
    level: 'critical',
    title: '带宽使用率过高',
    description: 'eth0接口带宽使用率达到95%，可能影响网络性能',
    timestamp: '2024-01-15 14:30:25'
  },
  {
    id: 2,
    level: 'warning',
    title: '网络延迟增加',
    description: '平均延迟从8ms增加到25ms，建议检查网络连接',
    timestamp: '2024-01-15 14:25:18'
  },
  {
    id: 3,
    level: 'info',
    title: '接口状态变更',
    description: 'wlan0接口从离线状态恢复为在线',
    timestamp: '2024-01-15 14:20:12'
  }
])

// 过滤后的接口列表
const filteredInterfaces = computed(() => {
  if (selectedInterface.value === 'all') {
    return networkInterfaces.value
  }
  return networkInterfaces.value.filter((iface) => iface.name === selectedInterface.value)
})

// 方法
const goBack = () => {
  // 先跳转到主界面
  router.push('/')
  // 然后触发打开企业资源管理标签页
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

const exportReport = () => {
  console.log('导出性能报告')
}

const addMonitorTarget = () => {
  showAddTargetModal.value = true
  resetTargetForm()
}

const closeAddTargetModal = () => {
  showAddTargetModal.value = false
  resetTargetForm()
}

const resetTargetForm = () => {
  targetForm.value = {
    name: '',
    type: '',
    target: '',
    port: null,
    protocol: 'icmp',
    interval: '60',
    threshold: 80,
    snmpCommunity: 'public',
    enabled: true,
    description: ''
  }
}

const saveMonitorTarget = async () => {
  saving.value = true

  try {
    // 模拟保存监控对象
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 创建新监控对象
    const newTarget = {
      id: Date.now(),
      name: targetForm.value.name,
      type: targetForm.value.type,
      target: targetForm.value.target,
      port: targetForm.value.port,
      protocol: targetForm.value.protocol,
      interval: parseInt(targetForm.value.interval),
      threshold: targetForm.value.threshold,
      snmpCommunity: targetForm.value.snmpCommunity,
      enabled: targetForm.value.enabled,
      description: targetForm.value.description,
      status: 'active',
      lastCheck: new Date().toLocaleString('zh-CN'),
      currentValue: Math.floor(Math.random() * 50) + 10
    }

    // 添加到监控对象列表
    monitorTargets.value.push(newTarget)

    // 关闭对话框
    closeAddTargetModal()

    // 显示成功消息
    console.log('监控对象添加成功:', newTarget)
  } catch (error) {
    console.error('保存监控对象失败:', error)
  } finally {
    saving.value = false
  }
}

const acknowledgeAlert = (alert: any) => {
  console.log('确认告警:', alert)
}

const getBandwidthClass = (usage: number) => {
  if (usage >= 90) return 'critical'
  if (usage >= 70) return 'warning'
  return 'normal'
}

const getLatencyClass = (latency: number) => {
  if (latency >= 50) return 'high'
  if (latency >= 20) return 'medium'
  return 'low'
}

const getLatencyLabel = (latency: number) => {
  if (latency >= 50) return '较高'
  if (latency >= 20) return '中等'
  return '良好'
}

const getQualityClass = (quality: number) => {
  if (quality >= 90) return 'excellent'
  if (quality >= 70) return 'good'
  return 'poor'
}

const getQualityLabel = (quality: number) => {
  if (quality >= 90) return '优秀'
  if (quality >= 70) return '良好'
  return '较差'
}

const getInterfaceStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    active: '活动',
    inactive: '非活动',
    warning: '警告',
    error: '错误'
  }
  return labels[status] || status
}

const getUtilizationClass = (utilization: number) => {
  if (utilization >= 80) return 'high'
  if (utilization >= 60) return 'medium'
  return 'low'
}

// 新增方法
const refreshCharts = () => {
  // 模拟刷新图表数据
  bandwidthHistory.value = Array.from({ length: 10 }, () => ({
    upload: Math.floor(Math.random() * 50) + 30,
    download: Math.floor(Math.random() * 50) + 50
  }))

  latencyHistory.value = Array.from({ length: 15 }, () => Math.floor(Math.random() * 20) + 10)

  // 更新质量指标
  qualityMetrics.stability = Math.floor(Math.random() * 20) + 80
  qualityMetrics.reliability = Math.floor(Math.random() * 20) + 75
  qualityMetrics.responsiveness = Math.floor(Math.random() * 20) + 85

  message.success('图表数据已刷新')
}

// 实时数据更新
let updateTimer: NodeJS.Timeout | null = null

const startRealTimeUpdates = () => {
  updateTimer = setInterval(() => {
    // 更新性能数据
    performanceData.totalBandwidthUsage = Math.floor(Math.random() * 30) + 50
    performanceData.uploadUsage = Math.floor(Math.random() * 40) + 30
    performanceData.downloadUsage = Math.floor(Math.random() * 40) + 50
    performanceData.avgLatency = Math.floor(Math.random() * 20) + 10

    // 更新历史数据
    bandwidthHistory.value.shift()
    bandwidthHistory.value.push({
      upload: performanceData.uploadUsage,
      download: performanceData.downloadUsage
    })

    latencyHistory.value.shift()
    latencyHistory.value.push(performanceData.avgLatency)

    // 更新网络接口数据
    networkInterfaces.value.forEach((netInterface) => {
      netInterface.uploadSpeed += (Math.random() - 0.5) * 5
      netInterface.downloadSpeed += (Math.random() - 0.5) * 8
      netInterface.utilization = Math.max(0, Math.min(100, netInterface.utilization + (Math.random() - 0.5) * 10))
    })
  }, 5000)
}

const stopRealTimeUpdates = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

onMounted(() => {
  refreshData()
  startRealTimeUpdates()
})

onUnmounted(() => {
  stopRealTimeUpdates()
})
</script>

<style scoped>
.network-performance-monitor {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.header-content h1 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  color: #1a1a1a;
}

.header-content p {
  margin: 4px 0 0 0;
  color: #666;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.header-actions button {
  -webkit-app-region: no-drag;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  -webkit-app-region: no-drag;
}

.performance-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.performance-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-chart {
  margin-top: 12px;
}

.bandwidth-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.bandwidth-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.bandwidth-fill.normal {
  background: #52c41a;
}

.bandwidth-fill.warning {
  background: #faad14;
}

.bandwidth-fill.critical {
  background: #ff4d4f;
}

.bandwidth-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.latency-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  display: inline-block;
}

.latency-indicator.low {
  background: #f6ffed;
  color: #52c41a;
}

.latency-indicator.medium {
  background: #fffbe6;
  color: #faad14;
}

.latency-indicator.high {
  background: #fff2f0;
  color: #ff4d4f;
}

.latency-details {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
}

.throughput-trend {
  margin-bottom: 8px;
}

.trend-indicator {
  font-size: 14px;
  font-weight: 600;
}

.trend-indicator.up {
  color: #52c41a;
}

.trend-indicator.down {
  color: #ff4d4f;
}

.throughput-details {
  font-size: 12px;
  color: #666;
}

.quality-score {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  display: inline-block;
}

.quality-score.excellent {
  background: #f6ffed;
  color: #52c41a;
}

.quality-score.good {
  background: #fffbe6;
  color: #faad14;
}

.quality-score.poor {
  background: #fff2f0;
  color: #ff4d4f;
}

.quality-details {
  font-size: 12px;
  color: #666;
}

.realtime-charts,
.interface-monitoring,
.performance-alerts {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  -webkit-app-region: no-drag;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 24px;
}

.chart-container {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  color: #1a1a1a;
}

.chart-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.section-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  -webkit-app-region: no-drag;
}

.interface-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.interface-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
}

.interface-card.active {
  border-left: 4px solid #52c41a;
}

.interface-card.warning {
  border-left: 4px solid #faad14;
}

.interface-card.error {
  border-left: 4px solid #ff4d4f;
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.interface-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #1a1a1a;
}

.interface-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.interface-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.interface-status.active .status-dot {
  background: #52c41a;
}

.interface-status.warning .status-dot {
  background: #faad14;
}

.interface-status.error .status-dot {
  background: #ff4d4f;
}

.interface-metrics {
  margin-bottom: 16px;
}

.metric-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.interface-utilization {
  margin-top: 16px;
}

.utilization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #666;
}

.utilization-bar {
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.utilization-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.utilization-fill.low {
  background: #52c41a;
}

.utilization-fill.medium {
  background: #faad14;
}

.utilization-fill.high {
  background: #ff4d4f;
}

.alert-summary {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.alert-count {
  font-weight: 600;
}

.alert-count.critical {
  color: #ff4d4f;
}

.alert-count.warning {
  color: #faad14;
}

.alert-count.info {
  color: #1890ff;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.alert-item.critical {
  border-left: 4px solid #ff4d4f;
  background: #fff2f0;
}

.alert-item.warning {
  border-left: 4px solid #faad14;
  background: #fffbe6;
}

.alert-item.info {
  border-left: 4px solid #1890ff;
  background: #e6f7ff;
}

.alert-icon {
  margin-top: 2px;
}

.alert-icon.critical {
  color: #ff4d4f;
}

.alert-icon.warning {
  color: #faad14;
}

.alert-icon.info {
  color: #1890ff;
}

.alert-content {
  flex: 1;
}

.alert-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.alert-description {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.alert-time {
  font-size: 12px;
  color: #999;
}

.alert-actions {
  margin-top: 2px;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.title-icon {
  width: 24px;
  height: 24px;
}

.card-icon {
  width: 16px;
  height: 16px;
}

.chart-icon {
  width: 32px;
  height: 32px;
}

.icon {
  width: 16px;
  height: 16px;
}

.back-icon {
  width: 16px;
  height: 16px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
