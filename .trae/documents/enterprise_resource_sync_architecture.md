# 企业资源同步系统技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户界面层] --> B[统一配置接口层]
    B --> C[资源分离管理层]
    C --> D[个人资源管理器]
    C --> E[企业资源管理器]
    
    D --> F[现有 data_sync 模块]
    E --> G[企业配置后端适配器层]
    
    G --> H[SMB适配器]
    G --> I[数据库适配器]
    G --> J[Git适配器]
    G --> K[Consul适配器]
    G --> L[对象存储适配器]
    
    F --> M[Supabase 个人数据库]
    H --> N[企业SMB共享]
    I --> O[企业PostgreSQL/MySQL]
    J --> P[企业Git仓库]
    K --> Q[Consul配置服务]
    L --> R[MinIO/S3对象存储]
    
    subgraph "前端层"
        A
    end
    
    subgraph "业务逻辑层"
        B
        C
        D
        E
    end
    
    subgraph "适配器层"
        F
        G
        H
        I
        J
        K
        L
    end
    
    subgraph "存储层"
        M
        N
        O
        P
        Q
        R
    end
```

## 2. 技术描述

* 前端：Vue\@3 + TypeScript + Ant Design Vue + Vite

* 后端：Electron Main Process + Node.js

* 个人数据存储：Supabase (现有data\_sync模块)

* 企业数据存储：多后端支持 (SMB/数据库/Git/Consul/对象存储)

* 加密：AES-256-GCM + 信封加密

* 认证：LDAP + JWT + 双因子认证

## 3. 路由定义

| 路由                    | 用途                   |
| --------------------- | -------------------- |
| /resources/personal   | 个人资源管理页面，显示和编辑个人主机配置 |
| /resources/enterprise | 企业资源浏览页面，只读访问企业共享资源  |
| /sync/config          | 同步配置页面，管理后端存储和同步策略   |
| /sync/monitor         | 监控仪表板，显示同步状态和性能指标    |
| /security/permissions | 权限管理页面，配置用户角色和访问权限   |
| /security/audit       | 审计日志页面，查看系统操作记录      |
| /settings/global      | 全局设置页面，系统级配置管理       |
| /settings/users       | 用户管理页面，账户和角色管理       |

## 4. API定义

### 4.1 核心API

**企业资源配置管理**

```
POST /api/enterprise/config/backends
```

请求参数：

| 参数名称     | 参数类型    | 是否必填 | 描述                              |
| -------- | ------- | ---- | ------------------------------- |
| type     | string  | true | 后端类型：smb/database/git/consul/s3 |
| name     | string  | true | 配置名称                            |
| priority | number  | true | 优先级，用于故障转移                      |
| enabled  | boolean | true | 是否启用                            |
| config   | object  | true | 后端特定配置                          |

响应参数：

| 参数名称     | 参数类型    | 描述     |
| -------- | ------- | ------ |
| success  | boolean | 操作是否成功 |
| configId | string  | 配置ID   |
| message  | string  | 响应消息   |

示例：

```json
{
  "type": "database",
  "name": "primary-db",
  "priority": 1,
  "enabled": true,
  "config": {
    "database_type": "postgresql",
    "connection_string": "host=config-db.corp.example.com user=admin dbname=host_config",
    "table_name": "host_configurations",
    "ssl_mode": "require"
  }
}
```

**企业资源同步**

```
POST /api/enterprise/sync/trigger
```

请求参数：

| 参数名称     | 参数类型    | 是否必填  | 描述             |
| -------- | ------- | ----- | -------------- |
| configId | string  | false | 指定配置ID，为空则同步所有 |
| force    | boolean | false | 是否强制同步         |

响应参数：

| 参数名称           | 参数类型    | 描述        |
| -------------- | ------- | --------- |
| success        | boolean | 同步是否成功    |
| syncedBackends | number  | 成功同步的后端数量 |
| failedBackends | number  | 失败的后端数量   |
| conflicts      | array   | 冲突信息列表    |

**权限验证**

```
GET /api/enterprise/auth/permissions
```

请求参数：

| 参数名称     | 参数类型   | 是否必填 | 描述   |
| -------- | ------ | ---- | ---- |
| resource | string | true | 资源类型 |
| action   | string | true | 操作类型 |

响应参数：

| 参数名称    | 参数类型    | 描述    |
| ------- | ------- | ----- |
| allowed | boolean | 是否有权限 |
| role    | string  | 用户角色  |
| reason  | string  | 权限说明  |

## 5. 服务架构图

```mermaid
graph TD
    A[Controller Layer] --> B[Service Layer]
    B --> C[Repository Layer]
    C --> D[Adapter Layer]
    D --> E[(Storage Backends)]
    
    subgraph "Controller Layer"
        A1[EnterpriseConfigController]
        A2[ResourceSyncController]
        A3[SecurityController]
    end
    
    subgraph "Service Layer"
        B1[ConfigManagerService]
        B2[SyncOrchestrationService]
        B3[PermissionService]
        B4[AuditService]
    end
    
    subgraph "Repository Layer"
        C1[ConfigRepository]
        C2[ResourceRepository]
        C3[UserRepository]
        C4[AuditRepository]
    end
    
    subgraph "Adapter Layer"
        D1[SMBAdapter]
        D2[DatabaseAdapter]
        D3[GitAdapter]
        D4[ConsulAdapter]
        D5[S3Adapter]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A3 --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C1 --> D2
    C2 --> D3
    C2 --> D4
    C2 --> D5
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    USER_ROLE ||--|| ROLE : references
    ROLE ||--o{ ROLE_PERMISSION : has
    ROLE_PERMISSION ||--|| PERMISSION : references
    
    USER ||--o{ PERSONAL_RESOURCE : owns
    ENTERPRISE_CONFIG ||--o{ ENTERPRISE_RESOURCE : contains
    
    CONFIG_BACKEND ||--o{ ENTERPRISE_CONFIG : stores
    CONFIG_BACKEND ||--o{ SYNC_LOG : generates
    
    USER {
        string id PK
        string username
        string email
        string ldap_dn
        string role_type
        timestamp created_at
        timestamp updated_at
    }
    
    ROLE {
        string id PK
        string name
        string description
        json permissions
        timestamp created_at
    }
    
    PERSONAL_RESOURCE {
        string id PK
        string user_id FK
        string resource_type
        json config_data
        string encryption_key_id
        timestamp created_at
        timestamp updated_at
    }
    
    ENTERPRISE_RESOURCE {
        string id PK
        string config_id FK
        string resource_type
        json config_data
        string environment
        json tags
        timestamp created_at
        timestamp updated_at
    }
    
    CONFIG_BACKEND {
        string id PK
        string type
        string name
        int priority
        boolean enabled
        json config
        timestamp created_at
        timestamp updated_at
    }
    
    SYNC_LOG {
        string id PK
        string backend_id FK
        string operation_type
        string status
        json result_data
        string error_message
        timestamp created_at
    }
```

### 6.2 数据定义语言

**用户表 (users)**

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    ldap_dn VARCHAR(500),
    role_type VARCHAR(50) DEFAULT 'personal' CHECK (role_type IN ('personal', 'enterprise_admin', 'system_admin')),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role_type ON users(role_type);
```

**配置后端表 (config\_backends)**

```sql
-- 创建配置后端表
CREATE TABLE config_backends (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('smb', 'database', 'git', 'consul', 's3')),
    name VARCHAR(100) NOT NULL,
    priority INTEGER NOT NULL DEFAULT 1,
    enabled BOOLEAN DEFAULT true,
    config JSONB NOT NULL,
    health_status VARCHAR(20) DEFAULT 'unknown' CHECK (health_status IN ('healthy', 'unhealthy', 'unknown')),
    last_health_check TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_config_backends_type ON config_backends(type);
CREATE INDEX idx_config_backends_priority ON config_backends(priority);
CREATE INDEX idx_config_backends_enabled ON config_backends(enabled);
CREATE UNIQUE INDEX idx_config_backends_name ON config_backends(name);
```

**企业资源表 (enterprise\_resources)**

```sql
-- 创建企业资源表
CREATE TABLE enterprise_resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backend_id UUID NOT NULL REFERENCES config_backends(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL CHECK (resource_type IN ('host', 'keychain', 'snippet')),
    name VARCHAR(200) NOT NULL,
    config_data JSONB NOT NULL,
    environment VARCHAR(50) DEFAULT 'production' CHECK (environment IN ('production', 'staging', 'development')),
    tags JSONB DEFAULT '[]'::jsonb,
    version INTEGER DEFAULT 1,
    checksum VARCHAR(64),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_enterprise_resources_backend_id ON enterprise_resources(backend_id);
CREATE INDEX idx_enterprise_resources_type ON enterprise_resources(resource_type);
CREATE INDEX idx_enterprise_resources_environment ON enterprise_resources(environment);
CREATE INDEX idx_enterprise_resources_tags ON enterprise_resources USING GIN(tags);
```

**同步日志表 (sync\_logs)**

```sql
-- 创建同步日志表
CREATE TABLE sync_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    backend_id UUID NOT NULL REFERENCES config_backends(id) ON DELETE CASCADE,
    operation_type VARCHAR(50) NOT NULL CHECK (operation_type IN ('sync', 'health_check', 'failover')),
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failed', 'partial')),
    result_data JSONB,
    error_message TEXT,
    duration_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_sync_logs_backend_id ON sync_logs(backend_id);
CREATE INDEX idx_sync_logs_status ON sync_logs(status);
CREATE INDEX idx_sync_logs_created_at ON sync_logs(created_at DESC);
```

**初始化数据**

```sql
-- 插入默认系统管理员
INSERT INTO users (username, email, role_type) VALUES 
('admin', '<EMAIL>', 'system_admin');

-- 插入示例配置后端
INSERT INTO config_backends (type, name, priority, config) VALUES 
('database', 'primary-db', 1, '{
  "database_type": "postgresql",
  "connection_string": "host=localhost port=5432 dbname=enterprise_config",
  "table_name": "host_configurations",
  "ssl_mode": "prefer"
}'),
('smb', 'legacy-smb', 2, '{
  "host": "fileserver.company.com",
  "share": "it_resources",
  "path": "/configs/hosts.yaml",
  "username": "service_account",
  "domain": "COMPANY"
}');
```

