// 搜索框组件样式
.search-box {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100%;
  background: var(--bg-color-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  transition: all 0.2s ease;
  
  // 禁用拖拽
  -webkit-app-region: no-drag;

  // 基础尺寸 - 默认
  &--default {
    height: 40px;
    font-size: 14px;
    
    .search-box__input {
      padding: 0 12px;
    }
    
    .search-box__icon {
      width: 16px;
      height: 16px;
      margin: 0 12px;
    }
  }

  // 小尺寸
  &--small {
    height: 32px;
    font-size: 12px;
    
    .search-box__input {
      padding: 0 10px;
    }
    
    .search-box__icon {
      width: 14px;
      height: 14px;
      margin: 0 10px;
    }
  }

  // 大尺寸
  &--large {
    height: 48px;
    font-size: 16px;
    
    .search-box__input {
      padding: 0 16px;
    }
    
    .search-box__icon {
      width: 18px;
      height: 18px;
      margin: 0 16px;
    }
  }

  // 紧凑型变体
  &--compact {
    height: 28px;
    font-size: 12px;
    border-radius: 4px;
    
    .search-box__input {
      padding: 0 8px;
    }
    
    .search-box__icon {
      width: 12px;
      height: 12px;
      margin: 0 8px;
    }
  }

  // 悬停状态
  &:hover:not(&--disabled) {
    border-color: var(--border-color-light);
    background: var(--bg-color-tertiary);
  }

  // 聚焦状态
  &--focused {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    background: var(--bg-color-secondary);
  }

  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--bg-color-quaternary);
    
    .search-box__input {
      cursor: not-allowed;
    }
  }

  // 有值状态
  &--has-value {
    .search-box__icon--clear {
      opacity: 1;
      visibility: visible;
    }
  }
}

// 输入框样式
.search-box__input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-color);
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
  
  &::placeholder {
    color: var(--text-color-tertiary);
    opacity: 1;
  }
  
  &:disabled {
    cursor: not-allowed;
    color: var(--text-color-quaternary);
  }
  
  // 移除自动填充样式
  &:-webkit-autofill,
  &:-webkit-autofill:hover,
  &:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px var(--bg-color-secondary) inset;
    -webkit-text-fill-color: var(--text-color);
    transition: background-color 5000s ease-in-out 0s;
  }
}

// 图标样式
.search-box__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: var(--text-color-tertiary);
  transition: all 0.2s ease;
  
  svg {
    width: 100%;
    height: 100%;
  }

  // 搜索图标
  &--search {
    margin-left: 12px;
    margin-right: 0;
  }

  // 清除按钮
  &--clear {
    margin-left: 0;
    margin-right: 12px;
    padding: 2px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 2px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--hover-bg-color);
      color: var(--text-color-secondary);
      transform: scale(1.1);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }

  // 后缀图标
  &--suffix {
    margin-left: 0;
    margin-right: 12px;
  }
}

// 建议下拉样式
.search-box__suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: var(--bg-color-quinary);
  border: 1px solid var(--border-color-light);
  border-radius: 6px;
  box-shadow: var(--box-shadow);
  max-height: 200px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--border-color-light);
    border-radius: 3px;
    
    &:hover {
      background: var(--text-color-tertiary);
    }
  }
}

.search-box__suggestion {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.15s ease;
  border-bottom: 1px solid var(--border-color);
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover,
  &--active {
    background: var(--hover-bg-color);
    color: var(--text-color);
  }
  
  &--active {
    background: var(--active-bg-color);
  }
}

.search-box__suggestion-text {
  display: block;
  color: var(--text-color-secondary);
  font-size: inherit;
  line-height: 1.4;
  word-break: break-word;
}

// 主题适配
.theme-dark {
  .search-box {
    &:hover:not(.search-box--disabled) {
      background: rgba(255, 255, 255, 0.05);
    }
    
    &--focused {
      background: var(--bg-color-secondary);
    }
  }
  
  .search-box__icon--clear:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.theme-light {
  .search-box {
    &:hover:not(.search-box--disabled) {
      background: rgba(0, 0, 0, 0.02);
    }
    
    &--focused {
      background: #ffffff;
    }
  }
  
  .search-box__icon--clear:hover {
    background: rgba(0, 0, 0, 0.05);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .search-box {
    // 移动端使用紧凑型样式
    &--default {
      height: 36px;
      font-size: 14px;
    }
    
    &--large {
      height: 44px;
      font-size: 16px;
    }
  }
  
  .search-box__suggestions {
    max-height: 150px;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .search-box,
  .search-box__icon,
  .search-box__suggestion {
    transition: none;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .search-box {
    border-width: 2px;
    
    &--focused {
      border-width: 3px;
    }
  }
}

// 打印样式
@media print {
  .search-box__suggestions {
    display: none;
  }
}
