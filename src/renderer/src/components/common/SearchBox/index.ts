import SearchBox from './SearchBox.vue'
import type { App } from 'vue'

// 组件安装函数
SearchBox.install = (app: App) => {
  app.component('SearchBox', SearchBox)
  app.component('CSearchBox', SearchBox) // 别名
}

// 导出组件
export default SearchBox
export { SearchBox }

// 导出类型定义
export interface SearchBoxSuggestion {
  text?: string
  value?: string
  label?: string
  [key: string]: any
}

export interface SearchBoxProps {
  modelValue?: string
  type?: string
  size?: 'small' | 'default' | 'large'
  variant?: 'basic' | 'clearable' | 'suggestions' | 'compact'
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  clearable?: boolean
  maxlength?: number
  autocomplete?: string
  ariaLabel?: string
  clearAriaLabel?: string
  suggestions?: SearchBoxSuggestion[]
  showSuggestions?: boolean
  suggestionTextKey?: string
  suggestionValueKey?: string
  debounceDelay?: number
}

export interface SearchBoxEmits {
  'update:modelValue': [value: string]
  'input': [value: string, event: Event]
  'change': [value: string, event: Event]
  'focus': [event: FocusEvent]
  'blur': [event: FocusEvent]
  'clear': []
  'search': [value: string]
  'suggestion-select': [suggestion: SearchBoxSuggestion, index: number]
  'keydown': [event: KeyboardEvent]
  'keyup': [event: KeyboardEvent]
}

// 使用示例和文档
export const SearchBoxExamples = {
  // 基础用法
  basic: `
<SearchBox 
  v-model="searchValue" 
  placeholder="搜索..." 
  @search="handleSearch"
/>`,

  // 带清除按钮
  clearable: `
<SearchBox 
  v-model="searchValue" 
  placeholder="搜索资产..." 
  clearable
  @search="handleSearch"
  @clear="handleClear"
/>`,

  // 带建议下拉
  suggestions: `
<SearchBox 
  v-model="searchValue" 
  placeholder="搜索命令..." 
  :suggestions="suggestions"
  show-suggestions
  @search="handleSearch"
  @suggestion-select="handleSuggestionSelect"
/>`,

  // 不同尺寸
  sizes: `
<SearchBox size="small" placeholder="小尺寸" />
<SearchBox size="default" placeholder="默认尺寸" />
<SearchBox size="large" placeholder="大尺寸" />`,

  // 紧凑型
  compact: `
<SearchBox 
  variant="compact" 
  placeholder="紧凑型搜索框" 
/>`,

  // 自定义图标
  customIcons: `
<SearchBox placeholder="自定义图标">
  <template #prefix-icon>
    <CustomSearchIcon />
  </template>
  <template #clear-icon>
    <CustomClearIcon />
  </template>
</SearchBox>`,

  // 自定义建议项
  customSuggestions: `
<SearchBox 
  :suggestions="suggestions"
  show-suggestions
>
  <template #suggestion="{ suggestion, index }">
    <div class="custom-suggestion">
      <span class="suggestion-title">{{ suggestion.title }}</span>
      <span class="suggestion-desc">{{ suggestion.description }}</span>
    </div>
  </template>
</SearchBox>`
}

// 最佳实践指南
export const SearchBoxBestPractices = {
  accessibility: [
    '始终提供有意义的 aria-label',
    '确保键盘导航正常工作',
    '为清除按钮提供 aria-label',
    '支持屏幕阅读器'
  ],
  
  performance: [
    '使用防抖来优化搜索性能',
    '限制建议项数量（建议不超过10项）',
    '对于大数据集使用虚拟滚动',
    '合理设置 debounceDelay'
  ],
  
  ux: [
    '提供清晰的占位符文本',
    '支持键盘快捷键（如 Escape 清除）',
    '显示搜索状态和结果数量',
    '保持搜索框在页面中的可见性'
  ],
  
  styling: [
    '遵循设计系统的颜色规范',
    '确保在不同主题下的可读性',
    '适配移动端和响应式布局',
    '提供合适的视觉反馈'
  ]
}
