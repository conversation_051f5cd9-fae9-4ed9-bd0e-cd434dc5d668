/**
 * 全局选择框背景透明度修复
 * 解决所有选择框、下拉框背景透明导致字体不清楚的问题
 */

/* 原生HTML select元素 */
select {
  background-color: #ffffff !important;
  color: #1e293b !important;
  border: 1px solid #d1d5db !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

select:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

select option {
  background-color: #ffffff !important;
  color: #1e293b !important;
  padding: 8px 12px !important;
}

/* Ant Design Select组件 */
.ant-select-selector {
  background-color: var(--select-bg, #ffffff) !important;
  border: 1px solid var(--select-border, #d1d5db) !important;
  color: var(--select-text-color, #1e293b) !important;
}

.ant-select-focused .ant-select-selector {
  background-color: var(--select-bg, #ffffff) !important;
  border-color: var(--input-focus-border, #3b82f6) !important;
  box-shadow: var(--input-focus-shadow, 0 0 0 2px rgba(59, 130, 246, 0.2)) !important;
}

.ant-select-selector:hover {
  background-color: var(--select-hover-bg, #f8fafc) !important;
  border-color: #9ca3af !important;
}

/* Ant Design Select下拉菜单 */
.ant-select-dropdown {
  background-color: var(--select-option-bg, #ffffff) !important;
  border: 1px solid var(--select-border, #e5e7eb) !important;
  border-radius: 6px !important;
  box-shadow: var(--card-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)) !important;
}

.ant-select-item {
  background-color: var(--select-option-bg, #ffffff) !important;
  color: var(--select-text-color, #1e293b) !important;
}

.ant-select-item-option {
  background-color: var(--select-option-bg, #ffffff) !important;
  color: var(--select-text-color, #1e293b) !important;
  padding: 8px 12px !important;
}

.ant-select-item-option:hover {
  background-color: var(--select-option-hover-bg, #f1f5f9) !important;
  color: var(--select-text-color, #1e293b) !important;
}

.ant-select-item-option-selected {
  background-color: var(--primary-bg-light, #eff6ff) !important;
  color: var(--primary-color, #1e40af) !important;
  font-weight: 500 !important;
}

.ant-select-item-option-active {
  background-color: var(--select-option-hover-bg, #f1f5f9) !important;
  color: var(--select-text-color, #1e293b) !important;
}

/* Select选中项文字 */
.ant-select-selection-item {
  color: #1e293b !important;
  font-weight: 500 !important;
}

.ant-select-selection-placeholder {
  color: #6b7280 !important;
}

/* 下拉箭头 */
.ant-select-arrow {
  color: #6b7280 !important;
}

.ant-select-focused .ant-select-arrow {
  color: #3b82f6 !important;
}

/* 多选模式 */
.ant-select-multiple .ant-select-selection-item {
  background-color: #f3f4f6 !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

.ant-select-multiple .ant-select-selection-item-remove {
  color: #6b7280 !important;
}

.ant-select-multiple .ant-select-selection-item-remove:hover {
  color: #ef4444 !important;
}

/* 禁用状态 */
.ant-select-disabled .ant-select-selector {
  background-color: #f9fafb !important;
  color: #9ca3af !important;
  border-color: #e5e7eb !important;
  cursor: not-allowed !important;
}

/* 输入框相关 */
.ant-input {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.ant-input:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-input:hover {
  background-color: #ffffff !important;
  border-color: #9ca3af !important;
}

.ant-input-password {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
}

.ant-input-password:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 文本域 */
.ant-input {
  background-color: #ffffff !important;
  color: #1e293b !important;
}

textarea.ant-input {
  background-color: #ffffff !important;
  color: #1e293b !important;
  border: 1px solid #d1d5db !important;
}

textarea.ant-input:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 数字输入框 */
.ant-input-number {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
}

.ant-input-number:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-input-number-input {
  background-color: transparent !important;
  color: #1e293b !important;
}

/* 日期选择器 */
.ant-picker {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.ant-picker:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-picker-input > input {
  background-color: transparent !important;
  color: #1e293b !important;
}

/* 下拉面板 */
.ant-picker-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* 级联选择器 */
.ant-cascader-picker {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
}

.ant-cascader-picker:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 树选择器 */
.ant-tree-select-selector {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.ant-tree-select-focused .ant-tree-select-selector {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 自动完成 */
.ant-auto-complete .ant-select-selector {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

/* 提及组件 */
.ant-mentions {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.ant-mentions:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 暗色主题适配 */
[data-theme='dark'] select,
[data-theme='dark'] .ant-select-selector {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
  color: #f9fafb !important;
}

[data-theme='dark'] .ant-select-dropdown {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
}

[data-theme='dark'] .ant-select-item-option {
  background-color: #1f2937 !important;
  color: #f9fafb !important;
}

[data-theme='dark'] .ant-select-item-option:hover {
  background-color: #374151 !important;
}

[data-theme='dark'] .ant-select-item-option-selected {
  background-color: #1e40af !important;
  color: #ffffff !important;
}

[data-theme='dark'] .ant-input,
[data-theme='dark'] textarea.ant-input {
  background-color: #1f2937 !important;
  border-color: #374151 !important;
  color: #f9fafb !important;
}

/* 确保所有表单控件在任何情况下都有足够的对比度 */
.form-control,
.form-select,
input[type='text'],
input[type='email'],
input[type='password'],
input[type='number'],
input[type='search'],
input[type='tel'],
input[type='url'],
textarea {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
}

.form-control:focus,
.form-select:focus,
input[type='text']:focus,
input[type='email']:focus,
input[type='password']:focus,
input[type='number']:focus,
input[type='search']:focus,
input[type='tel']:focus,
input[type='url']:focus,
textarea:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* 确保下拉选项在所有情况下都可见 */
option {
  background-color: #ffffff !important;
  color: #1e293b !important;
  padding: 8px !important;
}

/* 修复可能的z-index问题 */
.ant-select-dropdown,
.ant-picker-dropdown,
.ant-cascader-menus,
.ant-tree-select-dropdown {
  z-index: 9999 !important;
}

/* 特定页面的选择框修复 */
/* 自动化运维管理页面的主机选择框 */
.automation-manager .ant-select-selector,
.automation-manager .ant-form-item .ant-select-selector {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.automation-manager .ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.automation-manager .ant-select-item-option {
  background-color: #ffffff !important;
  color: #1e293b !important;
}

.automation-manager .ant-select-item-option:hover {
  background-color: #f1f5f9 !important;
  color: #1e293b !important;
}

/* 主机类型和状态标签 */
.automation-manager .host-type-tag,
.automation-manager .host-status-tag {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  margin-left: 8px !important;
}

.automation-manager .host-status-tag.online {
  background-color: #dcfce7 !important;
  color: #166534 !important;
}

.automation-manager .host-status-tag.offline {
  background-color: #fee2e2 !important;
  color: #dc2626 !important;
}

/* 统一资源选择器的原生select */
.unified-resource-selector select {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
}

.unified-resource-selector select:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.unified-resource-selector select option {
  background-color: #ffffff !important;
  color: #1e293b !important;
  padding: 8px 12px !important;
}

/* AI聊天页面的主机选择 */
.host-select-popup {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.host-select-list {
  background-color: #ffffff !important;
}

.host-select-item {
  background-color: #ffffff !important;
  color: #1e293b !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid #f3f4f6 !important;
}

.host-select-item:hover,
.host-select-item.hovered,
.host-select-item.keyboard-selected {
  background-color: #f1f5f9 !important;
  color: #1e293b !important;
}

.mini-host-search-input {
  background-color: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #1e293b !important;
}

.mini-host-search-input:focus {
  background-color: #ffffff !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 企业资源管理页面的功能模块选择 */
.enterprise-resource-management .function-nav-card {
  background-color: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.enterprise-resource-management .function-nav-card:hover {
  background-color: #f8fafc !important;
  border-color: #3b82f6 !important;
}

.enterprise-resource-management .nav-card-title {
  color: #1e293b !important;
}

.enterprise-resource-management .nav-card-desc {
  color: #475569 !important;
}

/* 终端设置页面的选择框修复 */
.font-family-select .ant-select-selector,
.mouse-event-select .ant-select-selector,
.background-type-select .ant-select-selector {
  background-color: var(--select-bg, #ffffff) !important;
  border: 1px solid var(--select-border, #d1d5db) !important;
  color: var(--select-text-color, #1e293b) !important;
}

.font-family-select .ant-select-selector:hover,
.font-family-select .ant-select-focused .ant-select-selector,
.mouse-event-select .ant-select-selector:hover,
.mouse-event-select .ant-select-focused .ant-select-selector,
.background-type-select .ant-select-selector:hover,
.background-type-select .ant-select-focused .ant-select-selector {
  background-color: var(--select-hover-bg, #f8fafc) !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.font-family-select .ant-select-selection-item,
.mouse-event-select .ant-select-selection-item,
.background-type-select .ant-select-selection-item {
  color: var(--select-text-color, #1e293b) !important;
  font-size: 14px !important;
}

.font-family-select .ant-select-arrow,
.mouse-event-select .ant-select-arrow,
.background-type-select .ant-select-arrow {
  color: var(--select-text-color, #6b7280) !important;
  opacity: 0.7 !important;
}

/* 左侧标签页的通用选择框修复 */
.left-tab-container .ant-select-selector {
  background-color: var(--bg-color-secondary, #ffffff) !important;
  border: 1px solid var(--border-color, #d1d5db) !important;
  color: var(--text-color, #1e293b) !important;
}

.left-tab-container .ant-select-focused .ant-select-selector {
  background-color: var(--bg-color-secondary, #ffffff) !important;
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

.left-tab-container .ant-select-dropdown {
  background-color: var(--bg-color, #ffffff) !important;
  border: 1px solid var(--border-color, #e5e7eb) !important;
}

.left-tab-container .ant-select-item-option {
  background-color: var(--bg-color, #ffffff) !important;
  color: var(--text-color, #1e293b) !important;
}

.left-tab-container .ant-select-item-option:hover,
.left-tab-container .ant-select-item-option-active,
.left-tab-container .ant-select-item-option-selected {
  background-color: var(--hover-bg-color, #f1f5f9) !important;
  color: var(--text-color, #1e293b) !important;
}

/* 工作区页面的选择框修复 */
.workspace-container .ant-select-selector {
  background-color: var(--bg-color-secondary, #ffffff) !important;
  border: 1px solid var(--border-color, #d1d5db) !important;
  color: var(--text-color-secondary, #1e293b) !important;
}

.workspace-container .ant-select-dropdown {
  background-color: var(--bg-color-secondary, #ffffff) !important;
  border: 1px solid var(--border-color, #e5e7eb) !important;
}

.workspace-container .ant-select-item-option {
  background-color: var(--bg-color-secondary, #ffffff) !important;
  color: var(--text-color-secondary, #1e293b) !important;
}

.workspace-container .ant-select-item-option:hover,
.workspace-container .ant-select-item-option-selected,
.workspace-container .ant-select-item-option-active {
  background-color: var(--hover-bg-color, #f1f5f9) !important;
  color: var(--text-color, #1e293b) !important;
}
