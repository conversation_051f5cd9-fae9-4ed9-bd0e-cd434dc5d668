<template>
  <div class="enterprise-asset-form-container">
    <div class="form-header">
      <div class="form-title">
        <h3>{{ isEditMode ? '编辑企业资产' : '添加企业资产' }}</h3>
      </div>
      <CloseOutlined
        class="close-icon"
        @click="handleClose"
      />
    </div>

    <div class="form-content">
      <a-form
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        layout="vertical"
        class="custom-form"
        @submit="handleSubmit"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <div class="title-indicator"></div>
            基本信息
          </div>

          <a-form-item
            label="资产名称"
            required
          >
            <a-input
              v-model:value="formData.name"
              placeholder="请输入资产名称"
            />
          </a-form-item>

          <a-form-item
            v-if="formData.type !== 'console'"
            label="主机地址"
            required
          >
            <a-input
              v-model:value="formData.host"
              placeholder="请输入IP地址或域名"
            />
          </a-form-item>

          <a-form-item
            v-if="formData.type !== 'console'"
            label="端口"
            required
          >
            <a-input-number
              v-model:value="formData.port"
              :min="1"
              :max="65535"
              style="width: 100%"
              placeholder="请输入端口号"
            />
          </a-form-item>

          <a-form-item
            label="资产类型"
            required
          >
            <a-select
              v-model:value="formData.type"
              placeholder="请选择资产类型"
              @change="handleTypeChange"
            >
              <a-select-option value="server">服务器</a-select-option>
              <a-select-option value="database">数据库</a-select-option>
              <a-select-option value="network">网络设备</a-select-option>
              <a-select-option value="ssh">SSH</a-select-option>
              <a-select-option value="console">Console（串口）</a-select-option>
              <a-select-option value="telnet">Telnet</a-select-option>
              <a-select-option value="rdp">RDP</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="所属组织">
            <a-input
              v-model:value="formData.organization"
              placeholder="请输入所属组织"
            />
          </a-form-item>
        </div>

        <!-- Console连接配置（串口） -->
        <div
          v-if="formData.type === 'console'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            Console连接配置（串口）
          </div>

          <!-- 串口配置 -->
          <a-form-item label="设备路径">
            <div style="display: flex; gap: 8px">
              <a-select
                v-model:value="formData.serial_config.device"
                placeholder="选择或输入设备路径"
                style="flex: 1"
                show-search
                allow-clear
                :options="availableSerialDevices"
                :loading="detectingDevices"
                @dropdown-visible-change="onDeviceDropdownChange"
              >
                <template #notFoundContent>
                  <div style="text-align: center; padding: 8px">
                    <div v-if="detectingDevices">正在检测设备...</div>
                    <div v-else>未找到可用设备</div>
                  </div>
                </template>
              </a-select>
              <a-button
                type="default"
                :loading="detectingDevices"
                @click="detectSerialDevices"
              >
                刷新
              </a-button>
            </div>
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="波特率">
                <a-select
                  v-model:value="formData.serial_config.baudRate"
                  placeholder="选择波特率"
                >
                  <a-select-option :value="1200">1200</a-select-option>
                  <a-select-option :value="2400">2400</a-select-option>
                  <a-select-option :value="4800">4800</a-select-option>
                  <a-select-option :value="9600">9600</a-select-option>
                  <a-select-option :value="19200">19200</a-select-option>
                  <a-select-option :value="38400">38400</a-select-option>
                  <a-select-option :value="57600">57600</a-select-option>
                  <a-select-option :value="115200">115200</a-select-option>
                  <a-select-option :value="230400">230400</a-select-option>
                  <a-select-option :value="460800">460800</a-select-option>
                  <a-select-option :value="921600">921600</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数据位">
                <a-select
                  v-model:value="formData.serial_config.dataBits"
                  placeholder="数据位"
                >
                  <a-select-option :value="5">5</a-select-option>
                  <a-select-option :value="6">6</a-select-option>
                  <a-select-option :value="7">7</a-select-option>
                  <a-select-option :value="8">8</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="停止位">
                <a-select
                  v-model:value="formData.serial_config.stopBits"
                  placeholder="停止位"
                >
                  <a-select-option :value="1">1</a-select-option>
                  <a-select-option :value="1.5">1.5</a-select-option>
                  <a-select-option :value="2">2</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="校验位">
                <a-select
                  v-model:value="formData.serial_config.parity"
                  placeholder="校验位"
                >
                  <a-select-option value="none">无 (None)</a-select-option>
                  <a-select-option value="even">偶校验 (Even)</a-select-option>
                  <a-select-option value="odd">奇校验 (Odd)</a-select-option>
                  <a-select-option value="mark">标记 (Mark)</a-select-option>
                  <a-select-option value="space">空格 (Space)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="流控制">
                <a-select
                  v-model:value="formData.serial_config.flowControl"
                  placeholder="流控制"
                >
                  <a-select-option value="none">无 (None)</a-select-option>
                  <a-select-option value="rts_cts">RTS/CTS</a-select-option>
                  <a-select-option value="xon_xoff">XON/XOFF</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- Telnet连接配置 -->
        <div
          v-if="formData.type === 'telnet'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            Telnet连接配置
          </div>

          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="formData.telnet_config.timeout"
              :min="1"
              :max="300"
              placeholder="连接超时时间"
              style="width: 100%"
            />
          </a-form-item>
        </div>

        <!-- RDP连接配置 -->
        <div
          v-if="formData.type === 'rdp'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            RDP连接配置
          </div>

          <a-form-item label="域名">
            <a-input
              v-model:value="formData.rdp_config.domain"
              placeholder="例如: WORKGROUP 或域名"
            />
          </a-form-item>

          <div
            class="section-title"
            style="margin-top: 16px"
          >
            <div class="title-indicator"></div>
            显示设置
          </div>

          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="宽度">
                <a-input-number
                  v-model:value="formData.rdp_config.display_settings.width"
                  :min="800"
                  :max="3840"
                  placeholder="1920"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="高度">
                <a-input-number
                  v-model:value="formData.rdp_config.display_settings.height"
                  :min="600"
                  :max="2160"
                  placeholder="1080"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="色深">
                <a-select
                  v-model:value="formData.rdp_config.display_settings.colorDepth"
                  placeholder="色深"
                >
                  <a-select-option :value="16">16位</a-select-option>
                  <a-select-option :value="24">24位</a-select-option>
                  <a-select-option :value="32">32位</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <div
            class="section-title"
            style="margin-top: 16px"
          >
            <div class="title-indicator"></div>
            安全设置
          </div>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="启用加密">
                <a-switch v-model:checked="formData.rdp_config.security_settings.encryption" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="认证方式">
                <a-select
                  v-model:value="formData.rdp_config.security_settings.authentication"
                  placeholder="认证方式"
                >
                  <a-select-option value="ntlm">NTLM</a-select-option>
                  <a-select-option value="basic">基本认证</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <!-- 认证信息（除Console串口连接外的其他连接类型） -->
        <div
          v-if="formData.type !== 'console'"
          class="form-section"
        >
          <div class="section-title">
            <div class="title-indicator"></div>
            连接信息
          </div>

          <a-form-item label="用户名">
            <a-input
              v-model:value="formData.username"
              placeholder="请输入用户名"
            />
          </a-form-item>

          <a-form-item label="密码">
            <a-input-password
              v-model:value="formData.password"
              placeholder="请输入密码"
            />
          </a-form-item>

          <a-form-item label="描述">
            <a-textarea
              v-model:value="formData.description"
              :rows="3"
              placeholder="请输入资产描述"
            />
          </a-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-button @click="handleClose"> 取消 </a-button>
          <a-button
            type="primary"
            @click="handleSubmit"
          >
            {{ isEditMode ? '更新' : '创建' }}
          </a-button>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface EnterpriseAssetFormData {
  id?: string
  name: string
  host: string
  port: number
  type: 'server' | 'database' | 'network' | 'ssh' | 'console' | 'telnet' | 'rdp'
  organization: string
  username: string
  password: string
  description: string
  auth_type?: 'password' | 'keyBased'
  keyChain?: string
  // Console和RDP连接类型支持
  serial_config?: {
    baudRate?: number
    dataBits?: number
    stopBits?: number
    parity?: 'none' | 'even' | 'odd' | 'mark' | 'space'
    device?: string
    flowControl?: 'none' | 'rts_cts' | 'xon_xoff'
  }
  telnet_config?: {
    timeout?: number
  }
  ssh_config?: {
    timeout?: number
  }
  rdp_config?: {
    domain?: string
    display_settings?: {
      width?: number
      height?: number
      colorDepth?: number
    }
    security_settings?: {
      encryption?: boolean
      authentication?: 'ntlm' | 'basic'
    }
  }
}

interface Props {
  isEditMode: boolean
  initialData: EnterpriseAssetFormData
}

const props = defineProps<Props>()

const emit = defineEmits<{
  submit: [data: EnterpriseAssetFormData]
  close: []
}>()

const formData = reactive<EnterpriseAssetFormData>({
  name: '',
  host: '',
  port: 22,
  type: 'server',
  organization: '',
  username: '',
  password: '',
  description: '',
  auth_type: 'password',
  keyChain: undefined,
  // Console和RDP连接类型支持
  serial_config: {
    baudRate: 9600,
    dataBits: 8,
    stopBits: 1,
    parity: 'none',
    device: '',
    flowControl: 'none'
  },
  telnet_config: {
    timeout: 30
  },
  ssh_config: {
    timeout: 30
  },
  rdp_config: {
    domain: 'WORKGROUP',
    display_settings: {
      width: 1920,
      height: 1080,
      colorDepth: 32
    },
    security_settings: {
      encryption: true,
      authentication: 'ntlm'
    }
  }
})

// 设备检测相关状态
const detectingDevices = ref(false)
const availableSerialDevices = ref < Array<{ label: string; value: string }>([])

// 监听初始数据变化
watch(
  () => props.initialData,
  (newData) => {
    Object.assign(formData, newData)
  },
  { immediate: true, deep: true }
)

const handleSubmit = () => {
  // 简单验证
  if (!formData.name.trim()) {
    return
  }
  if (!formData.host.trim()) {
    return
  }
  if (!formData.port || formData.port < 1 || formData.port > 65535) {
    return
  }

  emit('submit', { ...formData })
}

const handleClose = () => {
  emit('close')
}

// 设备检测相关方法
const detectSerialDevices = async () => {
  detectingDevices.value = true
  try {
    // 根据操作系统检测可用的串口设备
    const devices: Array<{ label: string; value: string }> = []

    // macOS/Linux 常见串口设备
    const unixDevices = [
      '/dev/ttyUSB0',
      '/dev/ttyUSB1',
      '/dev/ttyUSB2',
      '/dev/ttyUSB3',
      '/dev/ttyACM0',
      '/dev/ttyACM1',
      '/dev/ttyACM2',
      '/dev/ttyACM3',
      '/dev/ttyS0',
      '/dev/ttyS1',
      '/dev/ttyS2',
      '/dev/ttyS3',
      '/dev/cu.usbserial',
      '/dev/cu.usbmodem'
    ]

    // Windows 常见串口设备
    const windowsDevices = ['COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8']

    // 检测当前操作系统
    const isWindows = navigator.platform.toLowerCase().includes('win')
    const deviceList = isWindows ? windowsDevices : unixDevices

    // 模拟设备检测（实际项目中可以通过API调用系统命令检测）
    for (const device of deviceList) {
      devices.push({
        label: `${device} (串口设备)`,
        value: device
      })
    }

    // 添加一些常用的网络设备端口
    if (!isWindows) {
      devices.push({ label: '/dev/ttyUSB0 (USB转串口)', value: '/dev/ttyUSB0' }, { label: '/dev/ttyACM0 (Arduino设备)', value: '/dev/ttyACM0' })
    }

    availableSerialDevices.value = devices
  } catch (error) {
    console.error('检测串口设备失败:', error)
    message.error('检测串口设备失败')
  } finally {
    detectingDevices.value = false
  }
}

// 处理设备下拉框显示事件
const onDeviceDropdownChange = (visible: boolean) => {
  if (visible && availableSerialDevices.value.length === 0) {
    detectSerialDevices()
  }
}

// 处理资产类型变化
const handleTypeChange = (value: string) => {
  // 根据资产类型设置默认端口
  switch (value) {
    case 'server':
    case 'database':
    case 'network':
    case 'ssh':
      formData.port = 22
      break
    case 'console':
      formData.port = 0 // 串口不需要端口
      break
    case 'telnet':
      formData.port = 23
      break
    case 'rdp':
      formData.port = 3389
      break
  }
}
</script>

<style lang="less" scoped>
.enterprise-asset-form-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color);
  border-left: 1px solid var(--border-color);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-color-secondary);
}

.form-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.close-icon {
  font-size: 16px;
  color: var(--text-color-tertiary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    color: var(--text-color);
    background: var(--hover-bg-color);
  }
}

.form-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.title-indicator {
  width: 3px;
  height: 14px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.custom-form {
  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }

  :deep(.ant-form-item-label) {
    padding-bottom: 4px;

    label {
      color: var(--text-color);
      font-size: 13px;
    }
  }

  :deep(.ant-input),
  :deep(.ant-input-number),
  :deep(.ant-select-selector),
  :deep(.ant-input-password) {
    background: var(--component-bg-color, #ffffff);
    border-color: var(--border-color);
    color: var(--text-color);

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus,
    &.ant-input-focused {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  /* 特别针对Console设备路径选择框的背景色 */
  :deep(.ant-select-selector) {
    background: var(--component-bg-color, #ffffff) !important;
    opacity: 1 !important;
  }

  :global(.dark-theme) & :deep(.ant-select-selector) {
    background: var(--component-bg-color, #1f1f1f) !important;
  }

  :deep(.ant-select-dropdown) {
    background: var(--bg-color);
    border-color: var(--border-color);
  }

  :deep(.ant-select-selection-item) {
    color: var(--text-color) !important;
    background-color: transparent !important;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  margin-top: 20px;
}
</style>
