/* 全局布局修复 - 解决页面滚动和空间利用问题 */

/* 主应用容器 */
#app {
  height: 100vh !important;
  width: 100% !important;
  overflow: auto !important;
  margin: 0 !important;
}

/* 终端布局容器 */
.terminal-layout {
  height: 100vh !important;
  width: 100vw !important;
  display: flex !important;
  flex-direction: column !important;
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
  margin: 0 !important;
  overflow: hidden !important;
}

/* 主要内容区域 */
.term_content {
  width: calc(100% - 40px) !important;
  height: 100% !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* 分割面板容器 */
.splitpanes__pane {
  overflow: hidden !important;
}

/* 监控页面通用样式 */
.monitor-tab,
.permission-management,
.network-security-monitor,
.network-performance-monitor,
.application-layer-monitor,
.network-data-analytics,
.wifi-network-monitor,
.network-infrastructure-monitor,
.network-assets-management,
.network-devices-monitor,
.asset-management,
.room-data-analysis,
.room-realtime-monitor {
  height: 100% !important;
  max-height: calc(100vh - 60px) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 20px !important;
  box-sizing: border-box !important;
  background: var(--bg-color, #f5f5f5) !important;
}

/* 企业资源管理页面特殊处理 - 移除全局padding */
.enterprise-resource-management {
  height: 100% !important;
  max-height: calc(100vh - 60px) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  background: var(--bg-color, #f5f5f5) !important;
}

/* 页面头部样式优化 */
.page-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: 24px !important;
  padding: 24px !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  -webkit-app-region: no-drag !important;
}

/* 主要内容区域样式 */
.main-content,
.security-overview,
.performance-overview,
.application-overview,
.analytics-overview,
.wifi-overview,
.infrastructure-overview,
.assets-overview,
.devices-overview {
  margin-bottom: 24px !important;
}

/* 卡片容器样式优化 */
.users-card,
.roles-card,
.security-card,
.performance-card,
.application-card,
.analytics-card,
.wifi-card,
.infrastructure-card,
.assets-card,
.devices-card {
  height: auto !important;
  max-height: 600px !important;
  overflow: visible !important;
}

/* 列表容器样式优化 */
.users-list,
.role-permissions,
.security-events,
.performance-metrics,
.application-list,
.analytics-data,
.wifi-networks,
.infrastructure-devices,
.assets-list,
.devices-list {
  max-height: 520px !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* 日志容器样式优化 */
.logs-container,
.real-time-logs .logs-container {
  max-height: 300px !important;
  overflow-y: auto !important;
  background: var(--bg-color-secondary) !important;
  border-radius: 8px !important;
  border: 1px solid var(--border-color) !important;
}

/* 网格布局优化 */
.overview-grid,
.services-grid,
.permissions-grid,
.metrics-grid,
.analytics-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 16px !important;
  margin-bottom: 24px !important;
}

/* 状态指标优化 */
.status-metrics,
.performance-stats,
.security-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
  gap: 16px !important;
}

/* 滚动条样式优化 */
.monitor-tab::-webkit-scrollbar,
.permission-management::-webkit-scrollbar,
.network-security-monitor::-webkit-scrollbar,
.network-performance-monitor::-webkit-scrollbar,
.application-layer-monitor::-webkit-scrollbar,
.network-data-analytics::-webkit-scrollbar,
.wifi-network-monitor::-webkit-scrollbar,
.network-infrastructure-monitor::-webkit-scrollbar,
.network-assets-management::-webkit-scrollbar,
.network-devices-monitor::-webkit-scrollbar,
.enterprise-resource-management::-webkit-scrollbar,
.asset-management::-webkit-scrollbar,
.users-list::-webkit-scrollbar,
.role-permissions::-webkit-scrollbar,
.logs-container::-webkit-scrollbar {
  width: 6px !important;
}

.monitor-tab::-webkit-scrollbar-track,
.permission-management::-webkit-scrollbar-track,
.network-security-monitor::-webkit-scrollbar-track,
.network-performance-monitor::-webkit-scrollbar-track,
.application-layer-monitor::-webkit-scrollbar-track,
.network-data-analytics::-webkit-scrollbar-track,
.wifi-network-monitor::-webkit-scrollbar-track,
.network-infrastructure-monitor::-webkit-scrollbar-track,
.network-assets-management::-webkit-scrollbar-track,
.network-devices-monitor::-webkit-scrollbar-track,
.enterprise-resource-management::-webkit-scrollbar-track,
.asset-management::-webkit-scrollbar-track,
.users-list::-webkit-scrollbar-track,
.role-permissions::-webkit-scrollbar-track,
.logs-container::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1) !important;
  border-radius: 3px !important;
}

.monitor-tab::-webkit-scrollbar-thumb,
.permission-management::-webkit-scrollbar-thumb,
.network-security-monitor::-webkit-scrollbar-thumb,
.network-performance-monitor::-webkit-scrollbar-thumb,
.application-layer-monitor::-webkit-scrollbar-thumb,
.network-data-analytics::-webkit-scrollbar-thumb,
.wifi-network-monitor::-webkit-scrollbar-thumb,
.network-infrastructure-monitor::-webkit-scrollbar-thumb,
.network-assets-management::-webkit-scrollbar-thumb,
.network-devices-monitor::-webkit-scrollbar-thumb,
.enterprise-resource-management::-webkit-scrollbar-thumb,
.asset-management::-webkit-scrollbar-thumb,
.users-list::-webkit-scrollbar-thumb,
.role-permissions::-webkit-scrollbar-thumb,
.logs-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-radius: 3px !important;
}

.monitor-tab::-webkit-scrollbar-thumb:hover,
.permission-management::-webkit-scrollbar-thumb:hover,
.network-security-monitor::-webkit-scrollbar-thumb:hover,
.network-performance-monitor::-webkit-scrollbar-thumb:hover,
.application-layer-monitor::-webkit-scrollbar-thumb:hover,
.network-data-analytics::-webkit-scrollbar-thumb:hover,
.wifi-network-monitor::-webkit-scrollbar-thumb:hover,
.network-infrastructure-monitor::-webkit-scrollbar-thumb:hover,
.network-assets-management::-webkit-scrollbar-thumb:hover,
.network-devices-monitor::-webkit-scrollbar-thumb:hover,
.enterprise-resource-management::-webkit-scrollbar-thumb:hover,
.asset-management::-webkit-scrollbar-thumb:hover,
.users-list::-webkit-scrollbar-thumb:hover,
.role-permissions::-webkit-scrollbar-thumb:hover,
.logs-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .overview-grid,
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  }
}

@media (max-width: 768px) {
  .monitor-tab,
  .permission-management,
  .network-security-monitor,
  .network-performance-monitor,
  .application-layer-monitor,
  .network-data-analytics,
  .wifi-network-monitor,
  .network-infrastructure-monitor,
  .network-assets-management,
  .network-devices-monitor,
  .enterprise-resource-management,
  .asset-management {
    padding: 10px !important;
  }

  .page-header {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 16px !important;
  }

  .header-actions {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
  }

  .overview-grid,
  .services-grid {
    grid-template-columns: 1fr !important;
  }
}

/* 确保内容不被遮挡 */
.main-terminal-area,
.rigth-sidebar,
.bottom-sidebar {
  position: relative !important;
  overflow: hidden !important;
}

/* 修复分割面板的滚动问题 */
.splitpanes--vertical > .splitpanes__pane,
.splitpanes--horizontal > .splitpanes__pane {
  overflow: hidden !important;
}

/* 确保标签页内容可以滚动 */
.ant-tabs-content-holder {
  overflow: hidden !important;
}

.ant-tabs-tabpane {
  height: 100% !important;
  overflow: hidden !important;
}

/* 企业资源管理页面特殊优化 - 已在上面处理，这里移除重复定义 */

/* 企业资源管理响应式网格优化 */
.enterprise-resource-management .resource-stats-grid,
.enterprise-resource-management .function-nav-grid,
.enterprise-resource-management .quick-actions-grid {
  width: 100% !important;
}

/* 自适应容器优化 */
@media (max-width: 1400px) {
  .enterprise-resource-management .function-nav-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)) !important;
  }
}

@media (max-width: 1200px) {
  .enterprise-resource-management .resource-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)) !important;
  }
}

@media (max-width: 1024px) {
  .enterprise-resource-management .function-nav-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)) !important;
  }
}

@media (max-width: 768px) {
  .enterprise-resource-management .page-header {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 16px !important;
  }

  .enterprise-resource-management .header-actions {
    display: flex !important;
    gap: 8px !important;
    flex-wrap: wrap !important;
  }
}

@media (max-width: 480px) {
  .enterprise-resource-management .resource-stats-grid,
  .enterprise-resource-management .function-nav-grid {
    grid-template-columns: 1fr !important;
  }
}
