<!--
 * 企业资源管理主布局组件
 * 功能：提供统一的企业功能模块布局，包含左侧菜单栏和右侧内容区域
 * 作者：AI Assistant
 * 修改时间：2024-01-15
-->
<template>
  <div class="enterprise-layout">
    <!-- 左侧菜单栏 -->
    <aside
      class="enterprise-sidebar"
      :class="{ collapsed: !sidebarExpanded }"
    >
      <!-- 侧边栏头部 -->
      <header class="sidebar-header">
        <div class="brand">
          <div class="brand-logo">
            <Building2 class="logo-icon" />
          </div>
          <div
            v-show="sidebarExpanded"
            class="brand-text"
          >
            <h1 class="brand-title">企业管理</h1>
            <span class="brand-subtitle">Enterprise Hub</span>
          </div>
        </div>
        <button
          class="sidebar-toggle"
          :aria-label="sidebarExpanded ? '收起侧边栏' : '展开侧边栏'"
          @click="toggleSidebar"
        >
          <ChevronLeft
            v-if="sidebarExpanded"
            class="toggle-icon"
          />
          <ChevronRight
            v-else
            class="toggle-icon"
          />
        </button>
      </header>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <h3 class="nav-section-title">核心功能</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <router-link
                to="/enterprise/resources"
                class="nav-link"
                active-class="active"
              >
                <Database class="nav-icon" />
                <span class="nav-text">资源管理</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/sync"
                class="nav-link"
                active-class="active"
              >
                <RefreshCw class="nav-icon" />
                <span class="nav-text">同步设置</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/monitor"
                class="nav-link"
                active-class="active"
              >
                <Activity class="nav-icon" />
                <span class="nav-text">监控仪表板</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/security"
                class="nav-link"
                active-class="active"
              >
                <Shield class="nav-icon" />
                <span class="nav-text">安全管理</span>
              </router-link>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3 class="nav-section-title">资产管理</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <router-link
                to="/enterprise/asset-management"
                class="nav-link"
                active-class="active"
              >
                <Package class="nav-icon" />
                <span class="nav-text">固定资产管理</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/room-management"
                class="nav-link"
                active-class="active"
              >
                <Building class="nav-icon" />
                <span class="nav-text">机房管理</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/smb"
                class="nav-link"
                active-class="active"
              >
                <HardDrive class="nav-icon" />
                <span class="nav-text">SMB管理工具</span>
              </router-link>
            </li>
          </ul>
        </div>

        <div class="nav-section">
          <h3 class="nav-section-title">网络监控</h3>
          <ul class="nav-list">
            <li class="nav-item">
              <router-link
                to="/enterprise/network/infrastructure"
                class="nav-link"
                active-class="active"
              >
                <Network class="nav-icon" />
                <span class="nav-text">基础设施监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/devices"
                class="nav-link"
                active-class="active"
              >
                <Router class="nav-icon" />
                <span class="nav-text">设备监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/wifi"
                class="nav-link"
                active-class="active"
              >
                <Wifi class="nav-icon" />
                <span class="nav-text">无线网络监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/performance"
                class="nav-link"
                active-class="active"
              >
                <Activity class="nav-icon" />
                <span class="nav-text">性能监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/security"
                class="nav-link"
                active-class="active"
              >
                <Shield class="nav-icon" />
                <span class="nav-text">安全监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/applications"
                class="nav-link"
                active-class="active"
              >
                <Globe class="nav-icon" />
                <span class="nav-text">应用层监控</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/assets"
                class="nav-link"
                active-class="active"
              >
                <Server class="nav-icon" />
                <span class="nav-text">资产管理</span>
              </router-link>
            </li>
            <li class="nav-item">
              <router-link
                to="/enterprise/network/analytics"
                class="nav-link"
                active-class="active"
              >
                <Monitor class="nav-icon" />
                <span class="nav-text">数据分析</span>
              </router-link>
            </li>
          </ul>
        </div>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main
      class="enterprise-main"
      :class="{ 'sidebar-collapsed': !sidebarExpanded }"
    >
      <!-- 顶部导航栏 -->
      <header class="main-header">
        <div class="header-left">
          <button
            class="back-button"
            @click="goBack"
          >
            <ArrowLeft class="back-icon" />
            <span>返回主页</span>
          </button>
        </div>
        <div class="header-center">
          <h1 class="page-title">{{ currentPageTitle }}</h1>
        </div>
        <div class="header-right">
          <!-- 预留空间用于页面特定的操作按钮 -->
        </div>
      </header>

      <!-- 路由内容区域 -->
      <div class="main-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  Building2,
  ChevronLeft,
  ChevronRight,
  Database,
  RefreshCw,
  Activity,
  Shield,
  Package,
  Building,
  HardDrive,
  Network,
  Router,
  Wifi,
  Globe,
  Server,
  Monitor,
  ArrowLeft
} from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()

// 响应式数据
const sidebarExpanded = ref(true)

// 计算属性
const currentPageTitle = computed(() => {
  return route.meta?.title || '企业资源管理'
})

// 方法
/**
 * 切换侧边栏展开/收起状态
 */
const toggleSidebar = () => {
  sidebarExpanded.value = !sidebarExpanded.value
}

/**
 * 返回主页
 * 如果当前在企业资源管理主页，返回应用主页
 * 如果在子页面，返回企业资源管理主页
 */
const goBack = () => {
  const currentPath = route.path

  // 如果当前在企业资源管理主页，返回应用主页
  if (currentPath === '/enterprise' || currentPath === '/enterprise/resources') {
    router.push('/')
  } else {
    // 如果在子页面，返回企业资源管理主页
    router.push('/enterprise/resources')
  }
}
</script>

<style scoped>
/* 基础布局 */
.enterprise-layout {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  position: relative;
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.enterprise-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 侧边栏样式 */
.enterprise-sidebar {
  width: 280px;
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  color: white;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 20;
  box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  flex: 0 0 280px;
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

.enterprise-sidebar.collapsed {
  width: 80px;
  flex-basis: 80px;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.1) 100%);
}

.brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.logo-icon {
  width: 20px;
  height: 20px;
  color: white;
}

.brand-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.brand-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 11px;
  opacity: 0.7;
  margin: 0;
}

.sidebar-toggle {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.toggle-icon {
  width: 14px;
  height: 14px;
}

/* 导航样式 */
.sidebar-nav {
  padding: 16px 0;
  flex: 1;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 16px 8px;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 1px 8px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 12px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 13px;
  font-weight: 500;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.nav-text {
  transition: opacity 0.2s ease;
}

.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 主内容区域 */
.enterprise-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1;
}

.main-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* 启用拖拽 */
  -webkit-app-region: drag;
}

.header-left,
.header-right {
  flex: 1;
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

.header-center {
  flex: 2;
  text-align: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.back-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #374151;
}

.back-icon {
  width: 14px;
  height: 14px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  /* 禁用拖拽 */
  -webkit-app-region: no-drag;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .enterprise-sidebar {
    width: 240px;
    flex-basis: 240px;
  }

  .enterprise-sidebar.collapsed {
    width: 60px;
    flex-basis: 60px;
  }
}

@media (max-width: 768px) {
  .enterprise-layout {
    flex-direction: column;
  }

  .enterprise-sidebar {
    width: 100%;
    height: auto;
    flex-basis: auto;
  }

  .enterprise-sidebar.collapsed {
    height: 60px;
    overflow: hidden;
  }

  .main-header {
    padding: 12px 16px;
  }

  .main-content {
    padding: 16px;
  }
}
</style>
