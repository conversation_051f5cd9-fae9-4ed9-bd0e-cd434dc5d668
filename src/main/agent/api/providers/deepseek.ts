import { Anthropic } from '@anthropic-ai/sdk'
import OpenAI from 'openai'
import { ApiHandler } from '../'
import { ApiHandlerOptions, DeepSeekModelId, ModelInfo, deepSeekDefaultModelId, deepSeekModels } from '@shared/api'
import { calculateApiCostOpenAI } from '../../utils/cost'
import { convertToOpenAiMessages } from '../transform/openai-format'
import { ApiStream } from '../transform/stream'
import { convertToR1Format } from '../transform/r1-format'
import { Agent } from 'http'
import { checkProxyConnectivity, createProxyAgent } from './proxy'
import { secureLog } from '../../utils/secure-logging'

export class DeepSeekHandler implements ApiHandler {
  private options: ApiHandlerOptions
  private client: OpenAI

  constructor(options: ApiHandlerOptions) {
    this.options = options

    // Determine if a proxy is needed
    let httpAgent: Agent | undefined = undefined
    if (this.options.needProxy !== false) {
      const proxyConfig = this.options.proxyConfig
      httpAgent = createProxyAgent(proxyConfig)
    }
    // Log configuration without sensitive information using secure logging
    secureLog('Using DeepSeekHandler with options:', this.options)
    this.client = new OpenAI({
      baseURL: 'https://api.deepseek.com/v1',
      apiKey: this.options.deepSeekApiKey,
      httpAgent: httpAgent
    })
  }

  // Add API validation method
  async validateApiKey(): Promise<{ isValid: boolean; error?: string }> {
    try {
      console.log('[DeepSeek] Starting API key validation')

      // Validate required configuration
      if (!this.options.deepSeekApiKey) {
        throw new Error('DeepSeek API key is required')
      }

      const modelId = this.options.deepSeekModelId || this.options.apiModelId || 'deepseek-chat'
      console.log(`[DeepSeek] Using model: ${modelId}`)

      // Validate proxy if needed
      if (this.options.needProxy) {
        console.log('[DeepSeek] Validating proxy connectivity')
        try {
          await checkProxyConnectivity(this.options.proxyConfig!)
          console.log('[DeepSeek] Proxy connectivity validated successfully')
        } catch (proxyError) {
          console.error('[DeepSeek] Proxy validation failed:', proxyError)
          throw new Error(`Proxy validation failed: ${proxyError instanceof Error ? proxyError.message : String(proxyError)}`)
        }
      }

      console.log('[DeepSeek] Testing API connection')

      const response = await this.client.chat.completions.create({
        model: modelId,
        messages: [{ role: 'user', content: 'test' }],
        max_tokens: 1
      })

      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('Invalid response format: no choices returned')
      }

      console.log('[DeepSeek] API key validation successful')
      return { isValid: true }
    } catch (error: any) {
      console.error('[DeepSeek] Configuration validation failed:', {
        message: error?.message,
        status: error?.status,
        code: error?.code,
        type: error?.type,
        stack: error?.stack
      })

      // Categorize error types for better user feedback
      let errorMessage = 'Validation failed'

      if (error?.status === 401) {
        errorMessage = 'Invalid API key or unauthorized access'
      } else if (error?.status === 403) {
        errorMessage = 'Access forbidden - check API key permissions'
      } else if (error?.status === 404) {
        errorMessage = 'Model not found or invalid model ID'
      } else if (error?.status === 429) {
        errorMessage = 'Rate limit exceeded - please try again later'
      } else if (error?.status >= 500) {
        errorMessage = 'Server error - please try again later'
      } else if (error?.code === 'ECONNREFUSED' || error?.code === 'ENOTFOUND') {
        errorMessage = 'Connection failed - check network connectivity'
      } else if (error?.code === 'ETIMEDOUT') {
        errorMessage = 'Connection timeout - check network connectivity'
      } else if (error?.message) {
        errorMessage = error.message
      }

      return {
        isValid: false,
        error: errorMessage
      }
    }
  }

  private async *yieldUsage(info: ModelInfo, usage: OpenAI.Completions.CompletionUsage | undefined): ApiStream {
    // Deepseek reports total input AND cache reads/writes,
    // see context caching: https://api-docs.deepseek.com/guides/kv_cache)
    // where the input tokens is the sum of the cache hits/misses, just like OpenAI.
    // This affects:
    // 1) context management truncation algorithm, and
    // 2) cost calculation

    // Deepseek usage includes extra fields.
    // Safely cast the prompt token details section to the appropriate structure.
    interface DeepSeekUsage extends OpenAI.CompletionUsage {
      prompt_cache_hit_tokens?: number
      prompt_cache_miss_tokens?: number
    }

    const deepUsage = usage as DeepSeekUsage

    const inputTokens = deepUsage?.prompt_tokens || 0 // sum of cache hits and misses
    const outputTokens = deepUsage?.completion_tokens || 0
    const cacheReadTokens = deepUsage?.prompt_cache_hit_tokens || 0
    const cacheWriteTokens = deepUsage?.prompt_cache_miss_tokens || 0
    const totalCost = calculateApiCostOpenAI(info, inputTokens, outputTokens, cacheWriteTokens, cacheReadTokens)
    const nonCachedInputTokens = Math.max(0, inputTokens - cacheReadTokens - cacheWriteTokens) // this will always be 0
    yield {
      type: 'usage',
      inputTokens: nonCachedInputTokens,
      outputTokens: outputTokens,
      cacheWriteTokens: cacheWriteTokens,
      cacheReadTokens: cacheReadTokens,
      totalCost: totalCost
    }
  }

  async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
    const model = this.getModel()

    const isDeepseekReasoner = model.id.includes('deepseek-reasoner')

    let openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [{ role: 'system', content: systemPrompt }, ...convertToOpenAiMessages(messages)]

    if (isDeepseekReasoner) {
      openAiMessages = convertToR1Format([{ role: 'user', content: systemPrompt }, ...messages])
    }

    const stream = await this.client.chat.completions.create({
      model: model.id,
      max_completion_tokens: model.info.maxTokens,
      messages: openAiMessages,
      stream: true,
      stream_options: { include_usage: true },
      // Only set temperature for non-reasoner models
      ...(model.id === 'deepseek-reasoner' ? {} : { temperature: 0 })
    })

    for await (const chunk of stream) {
      const delta = chunk.choices[0]?.delta
      if (delta?.content) {
        yield {
          type: 'text',
          text: delta.content
        }
      }

      if (delta && 'reasoning_content' in delta && delta.reasoning_content) {
        yield {
          type: 'reasoning',
          reasoning: (delta.reasoning_content as string | undefined) || ''
        }
      }

      if (chunk.usage) {
        yield* this.yieldUsage(model.info, chunk.usage)
      }
    }
  }

  getModel(): { id: DeepSeekModelId; info: ModelInfo } {
    const modelId = this.options.deepSeekModelId || this.options.apiModelId
    if (!modelId) {
      throw new Error('DeepSeek model ID is not configured')
    }

    if (!(modelId in deepSeekModels)) {
      const availableModels = Object.keys(deepSeekModels).join(', ')
      throw new Error(`Invalid DeepSeek model ID: "${modelId}". Available model IDs: ${availableModels}`)
    }

    const id = modelId as DeepSeekModelId
    return { id, info: deepSeekModels[id] }
  }
}
