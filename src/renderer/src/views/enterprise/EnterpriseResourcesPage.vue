<template>
  <div class="enterprise-resources-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="btn btn-back"
          title="返回企业资源管理"
          @click="goBack"
        >
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Database class="title-icon" />
            企业资源管理
          </h1>
          <p class="page-description">管理企业共享的主机配置、密钥链和代码片段</p>
        </div>
      </div>
      <div class="header-actions">
        <button
          class="btn btn-secondary"
          :disabled="loading"
          @click="refreshResources"
        >
          <RefreshCw
            class="btn-icon"
            :class="{ 'animate-spin': loading }"
          />
          刷新
        </button>
        <button
          class="btn btn-secondary"
          @click="exportResources"
        >
          <Download class="btn-icon" />
          导出
        </button>
        <button
          class="btn btn-secondary"
          @click="showBatchOperations = !showBatchOperations"
          :class="{ active: showBatchOperations }"
        >
          <CheckSquare class="btn-icon" />
          批量操作
        </button>
        <button
          class="btn btn-primary"
          @click="showAddResourceModal = true"
        >
          <Plus class="btn-icon" />
          添加资源
        </button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <div class="filter-container">
        <div class="filter-row">
          <div class="search-input-wrapper">
            <Search class="search-icon" />
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索资源名称、IP地址或标签"
              class="search-input"
              @input="handleSearch"
            />
          </div>
          <select
            v-model="selectedType"
            class="filter-select"
            @change="handleTypeFilter"
          >
            <option value="">全部类型</option>
            <option value="host">主机配置</option>
            <option value="keychain">密钥链</option>
            <option value="snippet">代码片段</option>
          </select>
          <select
            v-model="selectedEnvironment"
            class="filter-select"
            @change="handleEnvironmentFilter"
          >
            <option value="">全部环境</option>
            <option value="production">生产环境</option>
            <option value="staging">测试环境</option>
            <option value="development">开发环境</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 资源统计 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <Server class="icon" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ hostCount }}</div>
              <div class="stat-label">主机配置</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <Key class="icon" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ keychainCount }}</div>
              <div class="stat-label">密钥链</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <Code class="icon" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ snippetCount }}</div>
              <div class="stat-label">代码片段</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <Users class="icon" />
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalResources }}</div>
              <div class="stat-label">总资源数</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源列表 -->
      <div class="resource-list">
        <div class="list-header">
          <span class="resource-count">共 {{ filteredResources.length }} 个资源</span>
          <div class="view-controls">
            <button
              :class="['view-btn', { active: viewMode === 'card' }]"
              @click="viewMode = 'card'"
            >
              <Grid class="btn-icon" />
              卡片视图
            </button>
            <button
              :class="['view-btn', { active: viewMode === 'table' }]"
              @click="viewMode = 'table'"
            >
              <List class="btn-icon" />
              表格视图
            </button>
          </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div
          v-if="showBatchOperations"
          class="batch-toolbar"
        >
          <div class="batch-selection">
            <label class="checkbox-wrapper">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="toggleSelectAll"
              />
              <span class="checkmark"></span>
              <span class="checkbox-label">
                {{ selectedResources.length > 0 ? `已选择 ${selectedResources.length} 项` : '全选' }}
              </span>
            </label>
          </div>
          <div
            v-if="selectedResources.length > 0"
            class="batch-actions"
          >
            <button
              class="batch-btn"
              @click="batchDelete"
            >
              <Trash2 class="btn-icon" />
              批量删除
            </button>
            <button
              class="batch-btn"
              @click="batchExport"
            >
              <Download class="btn-icon" />
              批量导出
            </button>
            <button
              class="batch-btn"
              @click="batchChangeEnvironment"
            >
              <Edit class="btn-icon" />
              批量修改环境
            </button>
          </div>
        </div>

        <!-- 卡片视图 -->
        <div
          v-if="viewMode === 'card'"
          class="card-view"
        >
          <div class="resource-grid">
            <div
              v-for="resource in filteredResources"
              :key="resource.id"
              class="resource-card"
              :class="{ selected: selectedResources.includes(resource.id) }"
              @click="selectResource(resource)"
            >
              <div class="card-header">
                <div class="resource-info">
                  <div
                    v-if="showBatchOperations"
                    class="resource-checkbox"
                  >
                    <input
                      type="checkbox"
                      :checked="selectedResources.includes(resource.id)"
                      @click.stop
                      @change="toggleResourceSelection(resource.id)"
                    />
                  </div>
                  <div class="resource-icon">
                    <Server v-if="resource.type === 'host'" />
                    <Key v-else-if="resource.type === 'keychain'" />
                    <Code v-else />
                  </div>
                  <div class="resource-details">
                    <h3 class="resource-name">{{ resource.name }}</h3>
                    <p class="resource-description">{{ resource.description || '暂无描述' }}</p>
                  </div>
                </div>
                <div class="card-actions">
                  <button
                    class="action-btn"
                    @click.stop="editResource(resource)"
                  >
                    <Edit class="btn-icon" />
                  </button>
                  <button
                    class="action-btn"
                    @click.stop="deleteResource(resource)"
                  >
                    <Trash2 class="btn-icon" />
                  </button>
                </div>
              </div>
              <div class="card-content">
                <div class="resource-tags">
                  <span
                    v-for="tag in resource.tags"
                    :key="tag"
                    class="tag"
                    >{{ tag }}</span
                  >
                </div>
                <div class="resource-meta">
                  <span class="meta-item">
                    <span class="meta-label">类型:</span>
                    <span class="meta-value">{{ getTypeLabel(resource.type) }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">环境:</span>
                    <span class="meta-value">{{ getEnvironmentLabel(resource.environment) }}</span>
                  </span>
                  <span class="meta-item">
                    <span class="meta-label">更新时间:</span>
                    <span class="meta-value">{{ formatDate(resource.updated_at) }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 表格视图 -->
        <div
          v-else
          class="table-view"
        >
          <table class="resource-table">
            <thead>
              <tr>
                <th>名称</th>
                <th>类型</th>
                <th>环境</th>
                <th>标签</th>
                <th>更新时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="resource in filteredResources"
                :key="resource.id"
                class="resource-row"
                @click="selectResource(resource)"
              >
                <td class="name-cell">
                  <div class="resource-icon">
                    <Server v-if="resource.type === 'host'" />
                    <Key v-else-if="resource.type === 'keychain'" />
                    <Code v-else />
                  </div>
                  <div class="name-info">
                    <span class="name-text">{{ resource.name }}</span>
                    <span class="description-text">{{ resource.description || '暂无描述' }}</span>
                  </div>
                </td>
                <td>
                  <span class="type-badge">{{ getTypeLabel(resource.type) }}</span>
                </td>
                <td>
                  <span class="env-badge">{{ getEnvironmentLabel(resource.environment) }}</span>
                </td>
                <td>
                  <div class="tags-cell">
                    <span
                      v-for="tag in resource.tags.slice(0, 2)"
                      :key="tag"
                      class="tag"
                      >{{ tag }}</span
                    >
                    <span
                      v-if="resource.tags.length > 2"
                      class="tag-more"
                      >+{{ resource.tags.length - 2 }}</span
                    >
                  </div>
                </td>
                <td>{{ formatDate(resource.updated_at) }}</td>
                <td>
                  <div class="action-buttons">
                    <button
                      class="action-btn"
                      @click.stop="editResource(resource)"
                    >
                      <Edit class="btn-icon" />
                    </button>
                    <button
                      class="action-btn"
                      @click.stop="deleteResource(resource)"
                    >
                      <Trash2 class="btn-icon" />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- 添加/编辑资源弹窗 -->
    <div
      v-if="showAddResourceModal"
      class="modal-overlay"
      @click="closeAddResourceModal"
    >
      <div
        class="modal-content"
        @click.stop
      >
        <div class="modal-header">
          <h3>{{ editingResource ? '编辑资源' : '添加资源' }}</h3>
          <button
            class="close-btn"
            @click="closeAddResourceModal"
          >
            <X class="btn-icon" />
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveResource">
            <!-- 基本信息 -->
            <div class="form-section">
              <h4 class="section-title">基本信息</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">资源名称 *</label>
                  <input
                    v-model="resourceForm.name"
                    type="text"
                    class="form-input"
                    placeholder="请输入资源名称"
                    required
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">资源类型 *</label>
                  <select
                    v-model="resourceForm.type"
                    class="form-select"
                    required
                  >
                    <option value="">请选择资源类型</option>
                    <option value="server">服务器</option>
                    <option value="database">数据库</option>
                    <option value="application">应用程序</option>
                    <option value="network">网络设备</option>
                    <option value="storage">存储设备</option>
                    <option value="other">其他</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">描述</label>
                <textarea
                  v-model="resourceForm.description"
                  class="form-textarea"
                  placeholder="请输入资源描述"
                  rows="3"
                ></textarea>
              </div>
            </div>

            <!-- 连接信息 -->
            <div class="form-section">
              <h4 class="section-title">连接信息</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">主机地址 *</label>
                  <input
                    v-model="resourceForm.host"
                    type="text"
                    class="form-input"
                    placeholder="IP地址或域名"
                    required
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">端口</label>
                  <input
                    v-model="resourceForm.port"
                    type="number"
                    class="form-input"
                    placeholder="端口号"
                    min="1"
                    max="65535"
                  />
                </div>
              </div>
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">用户名</label>
                  <input
                    v-model="resourceForm.username"
                    type="text"
                    class="form-input"
                    placeholder="登录用户名"
                  />
                </div>
                <div class="form-group">
                  <label class="form-label">密码</label>
                  <input
                    v-model="resourceForm.password"
                    type="password"
                    class="form-input"
                    placeholder="登录密码"
                  />
                </div>
              </div>
            </div>

            <!-- 环境和标签 -->
            <div class="form-section">
              <h4 class="section-title">分类信息</h4>
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">环境</label>
                  <select
                    v-model="resourceForm.environment"
                    class="form-select"
                  >
                    <option value="">请选择环境</option>
                    <option value="production">生产环境</option>
                    <option value="staging">预发布环境</option>
                    <option value="testing">测试环境</option>
                    <option value="development">开发环境</option>
                  </select>
                </div>
                <div class="form-group">
                  <label class="form-label">状态</label>
                  <select
                    v-model="resourceForm.status"
                    class="form-select"
                  >
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                    <option value="maintenance">维护中</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">标签</label>
                <input
                  v-model="resourceForm.tags"
                  type="text"
                  class="form-input"
                  placeholder="用逗号分隔多个标签，如：web,api,mysql"
                />
              </div>
            </div>

            <!-- 表单按钮 -->
            <div class="form-actions">
              <button
                type="button"
                class="btn btn-secondary"
                @click="closeAddResourceModal"
              >
                取消
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="!isFormValid"
              >
                {{ editingResource ? '更新资源' : '添加资源' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import eventBus from '@/utils/eventBus'
import {
  ArrowLeft,
  Database,
  RefreshCw,
  Download,
  Plus,
  Search,
  Server,
  Key,
  Code,
  Users,
  Grid,
  List,
  Edit,
  Trash2,
  X,
  CheckSquare
} from 'lucide-vue-next'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedType = ref('')
const selectedEnvironment = ref('')
const viewMode = ref<'card' | 'table'>('card')
const showAddResourceModal = ref(false)
const editingResource = ref<any>(null)
const showBatchOperations = ref(false)
const selectedResources = ref<string[]>([])
const showResourceDetail = ref(false)
const selectedResource = ref<any>(null)

// 资源表单数据
const resourceForm = ref({
  name: '',
  type: '',
  description: '',
  host: '',
  port: '',
  username: '',
  password: '',
  environment: '',
  status: 'active',
  tags: ''
})

// 模拟数据
const resources = ref([
  {
    id: '1',
    name: '生产服务器-01',
    type: 'host',
    environment: 'production',
    description: '主要生产环境服务器',
    tags: ['生产', 'Web服务器', 'Linux'],
    config_data: { ip: '*************', port: 22 },
    updated_at: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    name: '开发环境密钥',
    type: 'keychain',
    environment: 'development',
    description: '开发环境SSH密钥',
    tags: ['开发', 'SSH'],
    config_data: { keyType: 'rsa' },
    updated_at: '2024-01-14T15:20:00Z'
  },
  {
    id: '3',
    name: '部署脚本',
    type: 'snippet',
    environment: 'production',
    description: '自动化部署脚本',
    tags: ['部署', '自动化', 'Shell'],
    config_data: { language: 'bash' },
    updated_at: '2024-01-13T09:15:00Z'
  }
])

// 计算属性
const filteredResources = computed(() => {
  let filtered = resources.value

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (resource) =>
        resource.name.toLowerCase().includes(keyword) ||
        resource.description?.toLowerCase().includes(keyword) ||
        resource.tags.some((tag) => tag.toLowerCase().includes(keyword))
    )
  }

  if (selectedType.value) {
    filtered = filtered.filter((resource) => resource.type === selectedType.value)
  }

  if (selectedEnvironment.value) {
    filtered = filtered.filter((resource) => resource.environment === selectedEnvironment.value)
  }

  return filtered
})

const hostCount = computed(() => resources.value.filter((r) => r.type === 'host').length)
const keychainCount = computed(() => resources.value.filter((r) => r.type === 'keychain').length)
const snippetCount = computed(() => resources.value.filter((r) => r.type === 'snippet').length)
const totalResources = computed(() => resources.value.length)

// 批量操作相关计算属性
const isAllSelected = computed(() => {
  return filteredResources.value.length > 0 && selectedResources.value.length === filteredResources.value.length
})

// 方法
const goBack = () => {
  router.push('/')
  setTimeout(() => {
    eventBus.emit('openUserTab', 'enterpriseResourceManagement')
  }, 100)
}

const refreshResources = async () => {
  loading.value = true
  try {
    // TODO: 调用API刷新资源数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('刷新资源失败:', error)
  } finally {
    loading.value = false
  }
}

const exportResources = () => {
  // TODO: 实现导出功能
  console.log('导出资源')
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleTypeFilter = () => {
  // 筛选逻辑已在计算属性中实现
}

const handleEnvironmentFilter = () => {
  // 筛选逻辑已在计算属性中实现
}

const selectResource = (resource: any) => {
  if (showBatchOperations.value) {
    toggleResourceSelection(resource.id)
  } else {
    selectedResource.value = resource
    showResourceDetail.value = true
  }
}

// 批量操作方法
const toggleResourceSelection = (resourceId: string) => {
  const index = selectedResources.value.indexOf(resourceId)
  if (index > -1) {
    selectedResources.value.splice(index, 1)
  } else {
    selectedResources.value.push(resourceId)
  }
}

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedResources.value = []
  } else {
    selectedResources.value = filteredResources.value.map((r) => r.id)
  }
}

const batchDelete = () => {
  if (selectedResources.value.length === 0) return

  if (confirm(`确定要删除选中的 ${selectedResources.value.length} 个资源吗？`)) {
    resources.value = resources.value.filter((r) => !selectedResources.value.includes(r.id))
    selectedResources.value = []
    alert('批量删除成功！')
  }
}

const batchExport = () => {
  if (selectedResources.value.length === 0) return

  const selectedData = resources.value.filter((r) => selectedResources.value.includes(r.id))
  const dataStr = JSON.stringify(selectedData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `resources_export_${new Date().toISOString().split('T')[0]}.json`
  link.click()

  alert('导出成功！')
}

const batchChangeEnvironment = () => {
  if (selectedResources.value.length === 0) return

  const newEnv = prompt('请输入新的环境类型 (production/staging/development):')
  if (newEnv && ['production', 'staging', 'development'].includes(newEnv)) {
    resources.value.forEach((resource) => {
      if (selectedResources.value.includes(resource.id)) {
        resource.environment = newEnv
        resource.updated_at = new Date().toISOString()
      }
    })
    selectedResources.value = []
    alert('批量修改环境成功！')
  }
}

const editResource = (resource: any) => {
  editingResource.value = resource
  // 填充表单数据
  resourceForm.value = {
    name: resource.name,
    type: resource.type,
    description: resource.description,
    host: resource.config_data?.ip || resource.config_data?.host || '',
    port: resource.config_data?.port || '',
    username: resource.config_data?.username || '',
    password: '', // 出于安全考虑，不显示密码
    environment: resource.environment,
    status: resource.status || 'active',
    tags: Array.isArray(resource.tags) ? resource.tags.join(', ') : ''
  }
  showAddResourceModal.value = true
}

const deleteResource = (resource: any) => {
  if (confirm(`确定要删除资源 "${resource.name}" 吗？`)) {
    const index = resources.value.findIndex((r) => r.id === resource.id)
    if (index > -1) {
      resources.value.splice(index, 1)
    }
  }
}

const closeAddResourceModal = () => {
  showAddResourceModal.value = false
  editingResource.value = null
  // 重置表单
  resourceForm.value = {
    name: '',
    type: '',
    description: '',
    host: '',
    port: '',
    username: '',
    password: '',
    environment: '',
    status: 'active',
    tags: ''
  }
}

// 表单验证
const isFormValid = computed(() => {
  return resourceForm.value.name.trim() !== '' && resourceForm.value.type !== '' && resourceForm.value.host.trim() !== ''
})

// 保存资源
const saveResource = async () => {
  if (!isFormValid.value) {
    alert('请填写必填字段')
    return
  }

  try {
    const resourceData = {
      id: editingResource.value?.id || Date.now().toString(),
      name: resourceForm.value.name.trim(),
      type: resourceForm.value.type,
      description: resourceForm.value.description.trim(),
      environment: resourceForm.value.environment,
      status: resourceForm.value.status,
      tags: resourceForm.value.tags
        .split(',')
        .map((tag) => tag.trim())
        .filter((tag) => tag),
      config_data: {
        host: resourceForm.value.host.trim(),
        ip: resourceForm.value.host.trim(), // 兼容性
        port: resourceForm.value.port ? parseInt(resourceForm.value.port) : undefined,
        username: resourceForm.value.username.trim() || undefined,
        password: resourceForm.value.password || undefined
      },
      updated_at: new Date().toISOString()
    }

    if (editingResource.value) {
      // 更新现有资源
      const index = resources.value.findIndex((r) => r.id === editingResource.value.id)
      if (index !== -1) {
        resources.value[index] = { ...resources.value[index], ...resourceData }
      }
    } else {
      // 添加新资源
      resources.value.unshift(resourceData)
    }

    closeAddResourceModal()
    alert(editingResource.value ? '资源更新成功！' : '资源添加成功！')
  } catch (error) {
    console.error('保存资源失败:', error)
    alert('保存失败，请重试')
  }
}

const getTypeLabel = (type: string): string => {
  const labels = {
    host: '主机配置',
    keychain: '密钥链',
    snippet: '代码片段'
  }
  return labels[type as keyof typeof labels] || type
}

const getEnvironmentLabel = (env: string): string => {
  const labels = {
    production: '生产环境',
    staging: '测试环境',
    development: '开发环境'
  }
  return labels[env as keyof typeof labels] || env
}

const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return dateString
  }
}

onMounted(() => {
  // 组件挂载时的初始化逻辑
})
</script>

<style lang="less" scoped>
.enterprise-resources-page {
  width: 100%;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #333333);
  -webkit-app-region: drag;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.page-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid var(--border-color-light, #e5e7eb);
  background: var(--bg-color-secondary, #f8fafc);
  -webkit-app-region: no-drag;
  box-sizing: border-box;
  margin: 0;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.btn-back {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #6b7280;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  white-space: nowrap;
  -webkit-app-region: no-drag;
}

.btn-back:hover {
  background: #f3f4f6;
  color: #1f2937;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.btn-text {
  font-weight: 500;
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary, #1f2937);
}

.title-icon {
  width: 28px;
  height: 28px;
  margin-right: 12px;
  color: #3b82f6;
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary, #6b7280);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background: var(--bg-color-hover, #f9fafb);
  border-color: var(--border-color-hover, #9ca3af);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.filter-section {
  width: 100%;
  background: var(--bg-color-secondary, #f8fafc);
  -webkit-app-region: no-drag;
  box-sizing: border-box;
  margin: 0;
}

.filter-container {
  width: 100%;
  padding: 0 24px 24px 24px;
  box-sizing: border-box;
}

.main-content {
  width: 100%;
  padding: 0 24px;
  -webkit-app-region: no-drag;
  box-sizing: border-box;
  margin: 0;
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-light, #e5e7eb);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #eff6ff;
  border-radius: 8px;
  margin-right: 16px;
}

.stat-icon .icon {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-primary, #1f2937);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-color-secondary, #6b7280);
}

.resource-list {
  margin-bottom: 24px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.resource-count {
  font-size: 14px;
  color: var(--text-color-secondary, #6b7280);
}

.view-controls {
  display: flex;
  gap: 8px;
}

.view-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #d1d5db);
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background: var(--bg-color-hover, #f9fafb);
  border-color: var(--border-color-hover, #9ca3af);
}

.view-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.resource-card {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-light, #e5e7eb);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resource-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.resource-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.resource-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #eff6ff;
  border-radius: 6px;
  flex-shrink: 0;
}

.resource-icon svg {
  width: 20px;
  height: 20px;
  color: #3b82f6;
}

.resource-details {
  flex: 1;
  min-width: 0;
}

.resource-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color-primary, #1f2937);
  word-break: break-word;
}

.resource-description {
  margin: 0;
  font-size: 14px;
  color: var(--text-color-secondary, #6b7280);
  line-height: 1.4;
  word-break: break-word;
}

.card-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--bg-color-hover, #f3f4f6);
}

.action-btn svg {
  width: 16px;
  height: 16px;
  color: var(--text-color-secondary, #6b7280);
}

.card-content {
  margin-top: 12px;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.tag {
  display: inline-block;
  padding: 2px 8px;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.resource-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.meta-label {
  color: var(--text-color-secondary, #6b7280);
  font-weight: 500;
}

.meta-value {
  color: var(--text-color, #374151);
}

.table-view {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color-light, #e5e7eb);
  border-radius: 8px;
  overflow: hidden;
}

.resource-table {
  width: 100%;
  border-collapse: collapse;
}

.resource-table th {
  background: var(--bg-color-secondary, #f8fafc);
  padding: 12px 16px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-color-primary, #1f2937);
  border-bottom: 1px solid var(--border-color-light, #e5e7eb);
}

.resource-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color-light, #e5e7eb);
  font-size: 14px;
  color: var(--text-color, #374151);
}

.resource-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.resource-row:hover {
  background: var(--bg-color-hover, #f9fafb);
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.name-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.name-text {
  font-weight: 500;
  color: var(--text-color-primary, #1f2937);
}

.description-text {
  font-size: 12px;
  color: var(--text-color-secondary, #6b7280);
}

.type-badge,
.env-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge {
  background: #eff6ff;
  color: #1d4ed8;
}

.env-badge {
  background: #f0fdf4;
  color: #166534;
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-more {
  display: inline-block;
  padding: 2px 6px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75) !important;
  backdrop-filter: blur(8px) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  -webkit-app-region: no-drag;
}

.modal-content {
  background: #ffffff !important;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.2) !important;
  border: 2px solid #e5e7eb !important;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 2px solid #e5e7eb !important;
  background: #ffffff !important;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937 !important;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f3f4f6 !important;
}

.close-btn svg {
  width: 20px;
  height: 20px;
  color: #6b7280 !important;
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(80vh - 120px);
  background: #ffffff !important;
  color: #1f2937 !important;
}

/* 表单样式 */
.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937 !important;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151 !important;
  margin-bottom: 6px;
}

.form-input,
.form-select,
.form-textarea {
  padding: 10px 12px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #1f2937 !important;
  background: #ffffff !important;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 2px solid #e5e7eb;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3b82f6 !important;
  color: white !important;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb !important;
}

.btn-primary:disabled {
  background: #9ca3af !important;
  cursor: not-allowed;
}

.btn-secondary {
  background: #f8fafc !important;
  color: #374151 !important;
  border: 2px solid #d1d5db !important;
}

.btn-secondary:hover {
  background: #f1f5f9 !important;
  border-color: #9ca3af !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* 批量操作样式 */
.batch-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 16px;
}

.batch-selection {
  display: flex;
  align-items: center;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-wrapper input[type='checkbox'] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.batch-btn svg {
  width: 14px;
  height: 14px;
}

.resource-card.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.resource-checkbox {
  margin-right: 12px;
}

.resource-checkbox input[type='checkbox'] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}
</style>
