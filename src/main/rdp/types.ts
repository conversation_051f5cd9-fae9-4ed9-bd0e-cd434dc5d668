/**
 * RDP连接服务类型定义
 * 定义RDP连接相关的接口、类型和配置
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { EventEmitter } from 'events'

/**
 * RDP连接状态枚举
 */
export enum RdpConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting',
  AUTHENTICATING = 'authenticating'
}

/**
 * RDP安全协议枚举
 */
export enum RdpSecurityProtocol {
  RDP = 'rdp',
  TLS = 'tls',
  NLA = 'nla',
  AUTO = 'auto'
}

/**
 * RDP颜色深度枚举
 */
export enum RdpColorDepth {
  COLOR_8 = 8,
  COLOR_15 = 15,
  COLOR_16 = 16,
  COLOR_24 = 24,
  COLOR_32 = 32
}

/**
 * RDP音频模式枚举
 */
export enum RdpAudioMode {
  NONE = 'none',
  LOCAL = 'local',
  REMOTE = 'remote'
}

/**
 * RDP显示设置接口
 */
export interface RdpDisplaySettings {
  /** 屏幕宽度 */
  width: number
  /** 屏幕高度 */
  height: number
  /** 颜色深度 */
  colorDepth: RdpColorDepth
  /** 是否全屏 */
  fullscreen?: boolean
  /** DPI设置 */
  dpi?: number
  /** 是否启用桌面合成 */
  desktopComposition?: boolean
  /** 是否启用字体平滑 */
  fontSmoothing?: boolean
  /** 是否启用主题 */
  themes?: boolean
  /** 是否启用壁纸 */
  wallpaper?: boolean
  /** 是否启用菜单动画 */
  menuAnimations?: boolean
}

/**
 * RDP安全设置接口
 */
export interface RdpSecuritySettings {
  /** 安全协议 */
  protocol: RdpSecurityProtocol
  /** 是否启用加密 */
  encryption: boolean
  /** 认证方式 */
  authentication: 'ntlm' | 'kerberos' | 'basic'
  /** 是否忽略证书错误 */
  ignoreCertErrors?: boolean
  /** 是否启用网络级别认证 */
  networkLevelAuth?: boolean
  /** 是否启用凭据安全支持提供程序 */
  credSSP?: boolean
}

/**
 * RDP性能设置接口
 */
export interface RdpPerformanceSettings {
  /** 连接类型 */
  connectionType: 'modem' | 'broadband-low' | 'satellite' | 'broadband-high' | 'wan' | 'lan'
  /** 是否启用位图缓存 */
  bitmapCache?: boolean
  /** 是否启用持久位图缓存 */
  persistentBitmapCache?: boolean
  /** 是否启用压缩 */
  compression?: boolean
  /** 压缩级别 */
  compressionLevel?: number
  /** 是否启用自动重连 */
  autoReconnect?: boolean
  /** 重连间隔（秒） */
  reconnectInterval?: number
}

/**
 * RDP重定向设置接口
 */
export interface RdpRedirectionSettings {
  /** 是否重定向剪贴板 */
  clipboard?: boolean
  /** 是否重定向音频 */
  audio?: RdpAudioMode
  /** 是否重定向打印机 */
  printers?: boolean
  /** 是否重定向驱动器 */
  drives?: boolean
  /** 重定向的驱动器列表 */
  driveList?: string[]
  /** 是否重定向串口 */
  serialPorts?: boolean
  /** 是否重定向智能卡 */
  smartCards?: boolean
  /** 是否重定向USB设备 */
  usbDevices?: boolean
}

/**
 * RDP连接配置接口
 */
export interface RdpConnectionConfig {
  /** 连接ID */
  id: string
  /** 资产ID */
  assetId: string
  /** 连接名称 */
  name: string
  /** 服务器地址 */
  host: string
  /** 端口号 */
  port: number
  /** 用户名 */
  username: string
  /** 密码（加密存储） */
  password?: string
  /** 域名 */
  domain?: string
  /** 显示设置 */
  displaySettings: RdpDisplaySettings
  /** 安全设置 */
  securitySettings: RdpSecuritySettings
  /** 性能设置 */
  performanceSettings?: RdpPerformanceSettings
  /** 重定向设置 */
  redirectionSettings?: RdpRedirectionSettings
  /** 连接超时时间（毫秒） */
  timeout?: number
  /** 是否自动重连 */
  autoReconnect?: boolean
  /** 重连间隔（毫秒） */
  reconnectInterval?: number
  /** 最大重连次数 */
  maxReconnectAttempts?: number
  /** 创建时间 */
  createdAt?: Date
  /** 更新时间 */
  updatedAt?: Date
}

/**
 * RDP连接结果接口
 */
export interface RdpConnectionResult {
  /** 连接ID */
  connectionId: string
  /** 连接状态 */
  status: RdpConnectionStatus
  /** 状态消息 */
  message: string
  /** 会话信息 */
  sessionInfo?: RdpSessionInfo
  /** 错误信息 */
  error?: Error
  /** 连接时间 */
  connectedAt?: Date
}

/**
 * RDP会话信息接口
 */
export interface RdpSessionInfo {
  /** 会话ID */
  sessionId: string
  /** 服务器版本 */
  serverVersion?: string
  /** 客户端版本 */
  clientVersion?: string
  /** 实际显示设置 */
  actualDisplaySettings?: RdpDisplaySettings
  /** 支持的功能 */
  supportedFeatures?: string[]
  /** 会话开始时间 */
  startTime: Date
  /** 最后活动时间 */
  lastActivity: Date
  /** 传输统计 */
  statistics?: RdpStatistics
}

/**
 * RDP统计信息接口
 */
export interface RdpStatistics {
  /** 发送字节数 */
  bytesSent: number
  /** 接收字节数 */
  bytesReceived: number
  /** 发送包数 */
  packetsSent: number
  /** 接收包数 */
  packetsReceived: number
  /** 平均延迟（毫秒） */
  averageLatency: number
  /** 丢包率 */
  packetLoss: number
  /** 带宽使用率 */
  bandwidthUsage: number
}

/**
 * RDP连接信息接口
 */
export interface RdpConnectionInfo {
  /** 连接ID */
  id: string
  /** 资产ID */
  assetId: string
  /** 连接名称 */
  name: string
  /** 连接状态 */
  status: RdpConnectionStatus
  /** 连接配置 */
  config: RdpConnectionConfig
  /** 会话信息 */
  sessionInfo?: RdpSessionInfo
  /** 连接开始时间 */
  startedAt: Date
  /** 最后活动时间 */
  lastActivityAt: Date
  /** 重连次数 */
  reconnectCount: number
  /** 错误信息 */
  lastError?: string
}

/**
 * RDP管理器选项接口
 */
export interface RdpManagerOptions {
  /** 最大连接数 */
  maxConnections?: number
  /** 默认超时时间（毫秒） */
  defaultTimeout?: number
  /** 是否启用日志 */
  enableLogging?: boolean
  /** 日志级别 */
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  /** 连接池清理间隔（毫秒） */
  cleanupInterval?: number
  /** 默认显示设置 */
  defaultDisplaySettings?: Partial<RdpDisplaySettings>
  /** 默认安全设置 */
  defaultSecuritySettings?: Partial<RdpSecuritySettings>
  /** 默认性能设置 */
  defaultPerformanceSettings?: Partial<RdpPerformanceSettings>
}

/**
 * RDP连接事件接口
 */
export interface RdpConnectionEvents {
  /** 连接建立事件 */
  connect: (connectionId: string, sessionInfo: RdpSessionInfo) => void
  /** 连接断开事件 */
  disconnect: (connectionId: string, reason?: string) => void
  /** 错误事件 */
  error: (connectionId: string, error: Error) => void
  /** 状态变化事件 */
  statusChange: (connectionId: string, status: RdpConnectionStatus) => void
  /** 重连事件 */
  reconnect: (connectionId: string, attempt: number) => void
  /** 屏幕更新事件 */
  screenUpdate: (connectionId: string, updateData: RdpScreenUpdate) => void
  /** 鼠标事件 */
  mouseEvent: (connectionId: string, event: RdpMouseEvent) => void
  /** 键盘事件 */
  keyboardEvent: (connectionId: string, event: RdpKeyboardEvent) => void
  /** 剪贴板事件 */
  clipboardEvent: (connectionId: string, data: string) => void
}

/**
 * RDP屏幕更新数据接口
 */
export interface RdpScreenUpdate {
  /** 更新区域 */
  region: {
    x: number
    y: number
    width: number
    height: number
  }
  /** 图像数据 */
  imageData: Buffer
  /** 图像格式 */
  format: 'bitmap' | 'jpeg' | 'png'
  /** 时间戳 */
  timestamp: Date
}

/**
 * RDP鼠标事件接口
 */
export interface RdpMouseEvent {
  /** 事件类型 */
  type: 'move' | 'click' | 'dblclick' | 'wheel'
  /** X坐标 */
  x: number
  /** Y坐标 */
  y: number
  /** 按钮状态 */
  buttons: {
    left: boolean
    right: boolean
    middle: boolean
  }
  /** 滚轮增量 */
  wheelDelta?: number
  /** 时间戳 */
  timestamp: Date
}

/**
 * RDP键盘事件接口
 */
export interface RdpKeyboardEvent {
  /** 事件类型 */
  type: 'keydown' | 'keyup' | 'keypress'
  /** 键码 */
  keyCode: number
  /** 字符码 */
  charCode?: number
  /** 修饰键状态 */
  modifiers: {
    ctrl: boolean
    alt: boolean
    shift: boolean
    meta: boolean
  }
  /** 时间戳 */
  timestamp: Date
}

/**
 * RDP连接抽象类
 */
export abstract class RdpConnection extends EventEmitter {
  /** 连接ID */
  public readonly id: string
  /** 连接配置 */
  public readonly config: RdpConnectionConfig
  /** 连接状态 */
  public status: RdpConnectionStatus
  /** 会话信息 */
  public sessionInfo?: RdpSessionInfo
  /** 连接开始时间 */
  public startedAt?: Date
  /** 最后活动时间 */
  public lastActivityAt?: Date
  /** 重连次数 */
  public reconnectCount: number = 0

  constructor(id: string, config: RdpConnectionConfig) {
    super()
    this.id = id
    this.config = config
    this.status = RdpConnectionStatus.DISCONNECTED
  }

  /**
   * 建立RDP连接
   */
  abstract connect(): Promise<void>

  /**
   * 断开RDP连接
   */
  abstract disconnect(): Promise<void>

  /**
   * 发送鼠标事件
   * @param event 鼠标事件
   */
  abstract sendMouseEvent(event: RdpMouseEvent): Promise<void>

  /**
   * 发送键盘事件
   * @param event 键盘事件
   */
  abstract sendKeyboardEvent(event: RdpKeyboardEvent): Promise<void>

  /**
   * 发送剪贴板数据
   * @param data 剪贴板数据
   */
  abstract sendClipboardData(data: string): Promise<void>

  /**
   * 获取屏幕截图
   * @param region 截图区域（可选）
   */
  abstract getScreenshot(region?: { x: number; y: number; width: number; height: number }): Promise<Buffer>

  /**
   * 获取连接信息
   */
  abstract getConnectionInfo(): RdpConnectionInfo

  /**
   * 检查连接是否活跃
   */
  abstract isAlive(): boolean

  /**
   * 重新调整显示设置
   * @param settings 新的显示设置
   */
  abstract resizeDisplay(settings: Partial<RdpDisplaySettings>): Promise<void>
}

/**
 * RDP服务接口
 */
export interface IRdpService {
  /**
   * 创建RDP连接
   * @param config 连接配置
   */
  createConnection(config: RdpConnectionConfig): Promise<RdpConnection>

  /**
   * 获取RDP连接
   * @param connectionId 连接ID
   */
  getConnection(connectionId: string): RdpConnection | undefined

  /**
   * 关闭RDP连接
   * @param connectionId 连接ID
   */
  closeConnection(connectionId: string): Promise<void>

  /**
   * 获取所有RDP连接
   */
  getAllConnections(): RdpConnection[]

  /**
   * 清理无效连接
   */
  cleanup(): Promise<void>

  /**
   * 测试RDP连接
   * @param config 连接配置
   */
  testConnection(config: RdpConnectionConfig): Promise<boolean>
}

/**
 * RDP客户端配置接口
 */
export interface RdpClientConfig {
  /** 客户端名称 */
  clientName?: string
  /** 客户端版本 */
  clientVersion?: string
  /** 键盘布局 */
  keyboardLayout?: string
  /** 时区 */
  timezone?: string
  /** 是否启用调试模式 */
  debug?: boolean
  /** 日志文件路径 */
  logFile?: string
}
