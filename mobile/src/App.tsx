/**
 * Chaterm Mobile App
 * 主应用入口
 */

import React, { useEffect, useState } from 'react'
import { View, StyleSheet, StatusBar, Alert, AppState, AppStateStatus } from 'react-native'
import { NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import AsyncStorage from '@react-native-async-storage/async-storage'
import NetInfo from '@react-native-community/netinfo'

// 导入屏幕组件
import ConnectionListScreen from './screens/ConnectionListScreen'
import TerminalScreen from './screens/TerminalScreen'
import SettingsScreen from './screens/SettingsScreen'
import AddConnectionScreen from './screens/AddConnectionScreen'
import FileManagerScreen from './screens/FileManagerScreen'
import LoadingScreen from './screens/LoadingScreen'

// 导入服务和工具
import { store, persistor } from './store'
import { sshService } from './services/SSHService'
import { AppSettings, UserProfile } from './types'

// 导入图标
import Icon from 'react-native-vector-icons/MaterialIcons'

const Stack = createStackNavigator()
const Tab = createBottomTabNavigator()

// 主标签导航
const MainTabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string

          switch (route.name) {
            case 'Connections':
              iconName = 'computer'
              break
            case 'Terminal':
              iconName = 'terminal'
              break
            case 'Files':
              iconName = 'folder'
              break
            case 'Settings':
              iconName = 'settings'
              break
            default:
              iconName = 'help'
          }

          return (
            <Icon
              name={iconName}
              size={size}
              color={color}
            />
          )
        },
        tabBarActiveTintColor: '#007aff',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: '#ffffff',
          borderTopColor: '#e0e0e0'
        },
        headerShown: false
      })}
    >
      <Tab.Screen
        name="Connections"
        component={ConnectionListScreen}
        options={{ title: '连接' }}
      />
      <Tab.Screen
        name="Terminal"
        component={TerminalScreen}
        options={{ title: '终端' }}
      />
      <Tab.Screen
        name="Files"
        component={FileManagerScreen}
        options={{ title: '文件' }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ title: '设置' }}
      />
    </Tab.Navigator>
  )
}

// 主应用组件
const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [appSettings, setAppSettings] = useState<AppSettings | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [networkStatus, setNetworkStatus] = useState<boolean>(true)

  // 应用初始化
  useEffect(() => {
    initializeApp()
  }, [])

  // 监听应用状态变化
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background') {
        // 应用进入后台
        handleAppBackground()
      } else if (nextAppState === 'active') {
        // 应用回到前台
        handleAppForeground()
      }
    }

    const subscription = AppState.addEventListener('change', handleAppStateChange)
    return () => subscription?.remove()
  }, [])

  // 监听网络状态
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setNetworkStatus(state.isConnected ?? false)

      if (!state.isConnected) {
        Alert.alert('网络连接丢失', '请检查您的网络连接。SSH连接可能会受到影响。', [{ text: '确定' }])
      }
    })

    return unsubscribe
  }, [])

  /**
   * 初始化应用
   */
  const initializeApp = async () => {
    try {
      // 加载应用设置
      const settings = await loadAppSettings()
      setAppSettings(settings)

      // 加载用户配置
      const profile = await loadUserProfile()
      setUserProfile(profile)

      // 初始化服务
      await initializeServices()

      // 检查生物识别认证
      if (settings.biometricAuth) {
        await authenticateWithBiometrics()
      }

      setIsLoading(false)
    } catch (error) {
      console.error('应用初始化失败:', error)
      Alert.alert('初始化失败', '应用初始化过程中发生错误，请重试。', [
        { text: '重试', onPress: initializeApp },
        { text: '跳过', onPress: () => setIsLoading(false) }
      ])
    }
  }

  /**
   * 加载应用设置
   */
  const loadAppSettings = async (): Promise<AppSettings> => {
    try {
      const settingsJson = await AsyncStorage.getItem('app_settings')
      if (settingsJson) {
        return JSON.parse(settingsJson)
      }
    } catch (error) {
      console.error('加载应用设置失败:', error)
    }

    // 返回默认设置
    return {
      theme: 'auto',
      language: 'zh-CN',
      defaultTimeout: 30000,
      maxConnections: 10,
      autoSave: true,
      biometricAuth: false,
      sessionTimeout: 3600000,
      encryptStorage: true,
      backgroundSync: true,
      lowPowerMode: false,
      dataUsageLimit: 100 * 1024 * 1024, // 100MB
      connectionNotifications: true,
      errorNotifications: true,
      soundEnabled: true,
      vibrationEnabled: true,
      gestureConfig: {
        swipeLeft: 'switchSession',
        swipeRight: 'switchSession',
        swipeUp: 'showKeyboard',
        swipeDown: 'hideKeyboard',
        doubleTap: 'showKeyboard',
        longPress: 'showShortcuts',
        pinchZoom: true,
        hapticFeedback: true
      },
      keyboardConfig: {
        layout: 'qwerty',
        showFunctionKeys: true,
        showArrowKeys: true,
        showNumberRow: true,
        customKeys: [],
        keyHeight: 40,
        keySpacing: 5,
        backgroundColor: '#f2f2f7',
        keyColor: '#ffffff',
        textColor: '#000000'
      }
    }
  }

  /**
   * 加载用户配置
   */
  const loadUserProfile = async (): Promise<UserProfile | null> => {
    try {
      const profileJson = await AsyncStorage.getItem('user_profile')
      if (profileJson) {
        return JSON.parse(profileJson)
      }
    } catch (error) {
      console.error('加载用户配置失败:', error)
    }
    return null
  }

  /**
   * 初始化服务
   */
  const initializeServices = async (): Promise<void> => {
    // 初始化SSH服务
    // sshService已经是单例，无需额外初始化

    // 设置全局错误处理
    const originalConsoleError = console.error
    console.error = (...args) => {
      originalConsoleError(...args)
      // 可以在这里添加错误上报逻辑
    }
  }

  /**
   * 生物识别认证
   */
  const authenticateWithBiometrics = async (): Promise<void> => {
    try {
      // 这里应该集成生物识别库
      // 例如 react-native-biometrics 或 react-native-touch-id
      console.log('生物识别认证')
    } catch (error) {
      console.error('生物识别认证失败:', error)
      Alert.alert('认证失败', '生物识别认证失败，请使用其他方式登录。', [{ text: '确定' }])
    }
  }

  /**
   * 应用进入后台处理
   */
  const handleAppBackground = async (): Promise<void> => {
    try {
      // 保存应用状态
      if (appSettings) {
        await AsyncStorage.setItem('app_settings', JSON.stringify(appSettings))
      }

      // 处理后台连接
      if (appSettings?.backgroundSync) {
        // 保持重要连接
        const connections = sshService.getAllConnections()
        connections.forEach((connection) => {
          if (connection.config.backgroundConnection) {
            // 保持连接活跃
            console.log(`保持连接活跃: ${connection.config.name}`)
          }
        })
      } else {
        // 断开所有连接
        await sshService.cleanup()
      }
    } catch (error) {
      console.error('应用后台处理失败:', error)
    }
  }

  /**
   * 应用回到前台处理
   */
  const handleAppForeground = async (): Promise<void> => {
    try {
      // 检查会话超时
      if (appSettings?.sessionTimeout) {
        const lastActiveTime = await AsyncStorage.getItem('last_active_time')
        if (lastActiveTime) {
          const timeDiff = Date.now() - parseInt(lastActiveTime)
          if (timeDiff > appSettings.sessionTimeout) {
            // 会话超时，需要重新认证
            if (appSettings.biometricAuth) {
              await authenticateWithBiometrics()
            }
          }
        }
      }

      // 更新最后活跃时间
      await AsyncStorage.setItem('last_active_time', Date.now().toString())

      // 恢复连接
      // 这里可以添加自动重连逻辑
    } catch (error) {
      console.error('应用前台处理失败:', error)
    }
  }

  // 显示加载屏幕
  if (isLoading) {
    return <LoadingScreen />
  }

  return (
    <Provider store={store}>
      <PersistGate
        loading={<LoadingScreen />}
        persistor={persistor}
      >
        <GestureHandlerRootView style={styles.container}>
          <StatusBar
            barStyle={appSettings?.theme === 'dark' ? 'light-content' : 'dark-content'}
            backgroundColor={appSettings?.theme === 'dark' ? '#000000' : '#ffffff'}
          />

          <NavigationContainer>
            <Stack.Navigator
              screenOptions={{
                headerShown: false,
                gestureEnabled: true,
                cardStyleInterpolator: ({ current, layouts }) => {
                  return {
                    cardStyle: {
                      transform: [
                        {
                          translateX: current.progress.interpolate({
                            inputRange: [0, 1],
                            outputRange: [layouts.screen.width, 0]
                          })
                        }
                      ]
                    }
                  }
                }
              }}
            >
              <Stack.Screen
                name="Main"
                component={MainTabNavigator}
              />
              <Stack.Screen
                name="AddConnection"
                component={AddConnectionScreen}
                options={{
                  headerShown: true,
                  title: '添加连接',
                  headerBackTitle: '返回'
                }}
              />
            </Stack.Navigator>
          </NavigationContainer>
        </GestureHandlerRootView>
      </PersistGate>
    </Provider>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1
  }
})

export default App
