#!/bin/bash

# macOS SMB 连接诊断脚本
# 功能：全面诊断 macOS 系统的 SMB 连接问题
# 作者：SOLO Coding
# 修改时间：2025-01-15

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# SMB 配置信息
SMB_HOST="************"
SMB_SHARE="it部"
SMB_USERNAME="<EMAIL>"
SMB_DOMAIN="brbiotech.com"
SMB_PORT="445"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_separator() {
    echo -e "${BLUE}$(printf '=%.0s' {1..50})${NC}"
}

# 检查命令是否存在
check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        log_success "$1 命令可用"
        return 0
    else
        log_error "$1 命令不可用"
        return 1
    fi
}

# 网络连通性测试
test_network_connectivity() {
    print_header "网络连通性测试"
    
    log_info "测试主机连通性: $SMB_HOST"
    if ping -c 3 -W 3000 "$SMB_HOST" >/dev/null 2>&1; then
        log_success "主机 $SMB_HOST 可达"
    else
        log_error "主机 $SMB_HOST 不可达"
        return 1
    fi
    
    log_info "测试 SMB 端口连通性: $SMB_HOST:$SMB_PORT"
    if nc -z -w 5 "$SMB_HOST" "$SMB_PORT" 2>/dev/null; then
        log_success "SMB 端口 $SMB_PORT 开放"
    else
        log_error "SMB 端口 $SMB_PORT 不可达"
        return 1
    fi
    
    # 使用 nmap 检查 SMB 服务详情（如果可用）
    if command -v nmap >/dev/null 2>&1; then
        log_info "使用 nmap 检查 SMB 服务详情"
        nmap -p 445 --script smb-protocols "$SMB_HOST" 2>/dev/null || true
    fi
}

# macOS SMB 系统配置检查
check_macos_smb_config() {
    print_header "macOS SMB 系统配置检查"
    
    log_info "检查 macOS SMB 客户端配置"
    
    # 检查 SMB 协议版本设置
    if [ -f "/Library/Preferences/SystemConfiguration/com.apple.smb.server.plist" ]; then
        log_info "SMB 服务器配置文件存在"
    fi
    
    # 检查 SMB 客户端协议版本
    log_info "当前 SMB 协议版本设置:"
    defaults read /Library/Preferences/SystemConfiguration/com.apple.smb.server 2>/dev/null || log_warning "无法读取 SMB 服务器配置"
    
    # 检查网络接口
    log_info "当前网络接口:"
    ifconfig | grep -E "inet |flags" | head -10
}

# 使用 smbutil 测试连接
test_smbutil_connection() {
    print_header "使用 smbutil 测试 SMB 连接"
    
    if ! check_command "smbutil"; then
        log_error "smbutil 不可用，跳过此测试"
        return 1
    fi
    
    # 测试基本连接
    log_info "测试基本 SMB 连接到 $SMB_HOST"
    echo "smbutil view //$SMB_HOST"
    if timeout 10 smbutil view "//$SMB_HOST" 2>&1; then
        log_success "基本 SMB 连接成功"
    else
        log_warning "基本 SMB 连接失败，可能需要认证"
    fi
    
    # 测试带用户名的连接
    log_info "测试带用户名的 SMB 连接"
    echo "smbutil view //$SMB_USERNAME@$SMB_HOST"
    if timeout 10 smbutil view "//$SMB_USERNAME@$SMB_HOST" 2>&1; then
        log_success "带用户名的 SMB 连接成功"
    else
        log_warning "带用户名的 SMB 连接失败"
    fi
    
    # 测试特定共享
    log_info "测试特定共享: $SMB_SHARE"
    # 对中文共享名进行 URL 编码
    ENCODED_SHARE=$(python3 -c "import urllib.parse; print(urllib.parse.quote('$SMB_SHARE'))")
    echo "编码后的共享名: $ENCODED_SHARE"
    echo "smbutil view //$SMB_USERNAME@$SMB_HOST/$ENCODED_SHARE"
    if timeout 10 smbutil view "//$SMB_USERNAME@$SMB_HOST/$ENCODED_SHARE" 2>&1; then
        log_success "特定共享连接成功"
    else
        log_warning "特定共享连接失败"
    fi
}

# 使用 mount_smbfs 测试挂载
test_mount_smbfs() {
    print_header "使用 mount_smbfs 测试 SMB 挂载"
    
    if ! check_command "mount_smbfs"; then
        log_error "mount_smbfs 不可用，跳过此测试"
        return 1
    fi
    
    # 创建临时挂载点
    MOUNT_POINT="/tmp/smb_test_mount_$$"
    mkdir -p "$MOUNT_POINT"
    
    log_info "尝试挂载 SMB 共享到 $MOUNT_POINT"
    
    # 对中文共享名进行处理
    ENCODED_SHARE=$(python3 -c "import urllib.parse; print(urllib.parse.quote('$SMB_SHARE'))")
    
    # 尝试挂载（非交互式，会失败但能提供诊断信息）
    echo "mount_smbfs //$SMB_USERNAME@$SMB_HOST/$ENCODED_SHARE $MOUNT_POINT"
    if timeout 10 mount_smbfs "//$SMB_USERNAME@$SMB_HOST/$ENCODED_SHARE" "$MOUNT_POINT" 2>&1; then
        log_success "SMB 挂载成功"
        # 列出挂载内容
        ls -la "$MOUNT_POINT" 2>/dev/null || true
        # 卸载
        umount "$MOUNT_POINT" 2>/dev/null || true
    else
        log_warning "SMB 挂载失败（预期，因为需要密码）"
    fi
    
    # 清理临时挂载点
    rmdir "$MOUNT_POINT" 2>/dev/null || true
}

# 检查 DNS 解析
test_dns_resolution() {
    print_header "DNS 解析测试"
    
    log_info "测试主机名解析: $SMB_HOST"
    if nslookup "$SMB_HOST" >/dev/null 2>&1; then
        log_success "DNS 解析成功"
        nslookup "$SMB_HOST"
    else
        log_warning "DNS 解析失败，使用 IP 地址"
    fi
    
    # 测试反向 DNS
    log_info "测试反向 DNS 解析"
    if nslookup "$SMB_HOST" | grep -q "name ="; then
        log_success "反向 DNS 解析成功"
    else
        log_warning "反向 DNS 解析失败"
    fi
}

# 检查防火墙设置
check_firewall() {
    print_header "防火墙设置检查"
    
    log_info "检查 macOS 防火墙状态"
    FIREWALL_STATUS=$(sudo /usr/libexec/ApplicationFirewall/socketfilterfw --getglobalstate 2>/dev/null || echo "unknown")
    echo "防火墙状态: $FIREWALL_STATUS"
    
    if [[ "$FIREWALL_STATUS" == *"enabled"* ]]; then
        log_warning "防火墙已启用，可能影响 SMB 连接"
    else
        log_info "防火墙已禁用或状态未知"
    fi
}

# 生成诊断报告
generate_diagnosis_report() {
    print_header "诊断报告生成"
    
    REPORT_FILE="smb_diagnosis_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "macOS SMB 连接诊断报告"
        echo "生成时间: $(date)"
        echo "系统信息: $(sw_vers -productName) $(sw_vers -productVersion)"
        echo "目标主机: $SMB_HOST"
        echo "共享名称: $SMB_SHARE"
        echo "用户名: $SMB_USERNAME"
        echo "域: $SMB_DOMAIN"
        echo ""
        echo "=== 网络信息 ==="
        ifconfig | grep -E "inet |flags"
        echo ""
        echo "=== 路由信息 ==="
        netstat -rn | head -10
        echo ""
        echo "=== DNS 配置 ==="
        cat /etc/resolv.conf 2>/dev/null || echo "无法读取 DNS 配置"
    } > "$REPORT_FILE"
    
    log_success "诊断报告已生成: $REPORT_FILE"
}

# 提供修复建议
provide_fix_suggestions() {
    print_header "修复建议"
    
    echo -e "${YELLOW}基于诊断结果，以下是可能的修复建议：${NC}"
    echo ""
    echo "1. 网络连接问题："
    echo "   - 检查网络连接是否正常"
    echo "   - 确认防火墙设置不阻止 SMB 连接"
    echo "   - 验证 VPN 连接（如果需要）"
    echo ""
    echo "2. SMB 服务器问题："
    echo "   - 确认 SMB 服务器正在运行"
    echo "   - 检查服务器防火墙设置"
    echo "   - 验证共享 '$SMB_SHARE' 是否存在"
    echo ""
    echo "3. 认证问题："
    echo "   - 验证用户名和密码是否正确"
    echo "   - 检查域设置是否正确"
    echo "   - 尝试不同的用户名格式："
    echo "     * $SMB_USERNAME"
    echo "     * ${SMB_DOMAIN}\\${SMB_USERNAME%%@*}"
    echo "     * ${SMB_USERNAME%%@*}"
    echo ""
    echo "4. macOS 特定问题："
    echo "   - 尝试在 Finder 中手动连接 SMB 共享"
    echo "   - 检查 SMB 协议版本兼容性"
    echo "   - 清除 SMB 缓存：sudo dscacheutil -flushcache"
    echo ""
    echo "5. 中文共享名问题："
    echo "   - 中文共享名可能需要特殊处理"
    echo "   - 尝试使用 URL 编码的共享名"
    echo "   - 联系管理员创建英文别名"
}

# 主函数
main() {
    clear
    echo -e "${BLUE}"
    echo "██╗   ██╗ █████╗  ██████╗ ██████╗ ███████╗    ███████╗███╗   ███╗██████╗ "
    echo "██║   ██║██╔══██╗██╔════╝██╔═══██╗██╔════╝    ██╔════╝████╗ ████║██╔══██╗"
    echo "██║   ██║███████║██║     ██║   ██║███████╗    ███████╗██╔████╔██║██████╔╝"
    echo "██║   ██║██╔══██║██║     ██║   ██║╚════██║    ╚════██║██║╚██╔╝██║██╔══██╗"
    echo "╚██████╔╝██║  ██║╚██████╗╚██████╔╝███████║    ███████║██║ ╚═╝ ██║██████╔╝"
    echo " ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚═════╝ ╚══════╝    ╚══════╝╚═╝     ╚═╝╚═════╝ "
    echo -e "${NC}"
    echo -e "${BLUE}macOS SMB 连接诊断工具${NC}"
    print_separator
    
    log_info "开始 SMB 连接诊断..."
    log_info "目标: $SMB_HOST 共享: $SMB_SHARE"
    
    # 执行各项测试
    test_network_connectivity || true
    check_macos_smb_config || true
    test_dns_resolution || true
    check_firewall || true
    test_smbutil_connection || true
    test_mount_smbfs || true
    
    # 生成报告和建议
    generate_diagnosis_report
    provide_fix_suggestions
    
    print_separator
    log_info "诊断完成！"
    echo -e "${YELLOW}请查看上述输出和生成的报告文件以了解详细信息。${NC}"
}

# 检查是否以 root 权限运行（某些命令需要）
if [[ $EUID -eq 0 ]]; then
    log_warning "以 root 权限运行，某些测试可能受到影响"
fi

# 运行主函数
main "$@"