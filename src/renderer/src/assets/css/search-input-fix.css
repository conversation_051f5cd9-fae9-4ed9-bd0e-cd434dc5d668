/* 搜索框样式修复 - 全局强制覆盖 */

/* 禁用拖拽 */
.custom-search-input {
  -webkit-app-region: no-drag;
}

/* 最高优先级的搜索框样式覆盖 - 使用更具体的选择器 */
.custom-search-input.ant-input-search,
.custom-search-input .ant-input-search,
.custom-search-input .ant-input-search .ant-input-group,
.custom-search-input .ant-input-search .ant-input-group .ant-input-affix-wrapper,
.custom-search-input .ant-input-search .ant-input-affix-wrapper,
.custom-search-input .ant-input-affix-wrapper,
.custom-search-input .ant-input,
.list-filters .custom-search-input .ant-input,
.list-filters .custom-search-input .ant-input-affix-wrapper,
.ant-input-search.custom-search-input,
.ant-input-search.custom-search-input .ant-input-affix-wrapper,
.ant-input-search.custom-search-input .ant-input {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  color: #262626 !important;
  box-shadow: none !important;
}

/* 暗色主题下的样式 */
.theme-dark .custom-search-input.ant-input-search,
.theme-dark .custom-search-input .ant-input-search,
.theme-dark .custom-search-input .ant-input-search .ant-input-group,
.theme-dark .custom-search-input .ant-input-search .ant-input-group .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input-search .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input,
.theme-dark .list-filters .custom-search-input .ant-input,
.theme-dark .list-filters .custom-search-input .ant-input-affix-wrapper,
.theme-dark .ant-input-search.custom-search-input,
.theme-dark .ant-input-search.custom-search-input .ant-input-affix-wrapper,
.theme-dark .ant-input-search.custom-search-input .ant-input {
  background-color: #1f1f1f !important;
  background: #1f1f1f !important;
  border: 1px solid #4b5563 !important;
  color: #e2e8f0 !important;
  box-shadow: none !important;
}

/* 输入框内部元素 */
.custom-search-input input,
.custom-search-input .ant-input-affix-wrapper input,
.custom-search-input .ant-input-search input {
  background-color: transparent !important;
  background: transparent !important;
  color: inherit !important;
  padding-left: 11px !important;
  box-shadow: none !important;
}

/* 占位符样式 */
.custom-search-input .ant-input::placeholder,
.custom-search-input input::placeholder {
  color: #bfbfbf !important;
}

.theme-dark .custom-search-input .ant-input::placeholder,
.theme-dark .custom-search-input input::placeholder {
  color: #666666 !important;
}

/* 搜索按钮样式 */
.custom-search-input .ant-input-search-button {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #ffffff !important;
}

.custom-search-input .ant-input-search-button:hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

/* 焦点状态 */
.custom-search-input .ant-input-affix-wrapper:focus,
.custom-search-input .ant-input-affix-wrapper-focused,
.custom-search-input .ant-input:focus,
.list-filters .custom-search-input .ant-input-affix-wrapper-focused {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
}

/* 悬停状态 */
.custom-search-input .ant-input-affix-wrapper:hover,
.custom-search-input .ant-input:hover,
.list-filters .custom-search-input .ant-input-affix-wrapper:hover {
  border-color: #40a9ff !important;
}

/* 强制覆盖任何可能的全局样式 */
.custom-search-input .ant-input-affix-wrapper,
.custom-search-input .ant-input-group-wrapper .ant-input-affix-wrapper {
  background-color: #ffffff !important;
  border-color: #d9d9d9 !important;
}

.theme-dark .custom-search-input .ant-input-affix-wrapper,
.theme-dark .custom-search-input .ant-input-group-wrapper .ant-input-affix-wrapper {
  background-color: #1f1f1f !important;
  border-color: #4b5563 !important;
}

/* 修复图标和文字重叠问题 */
.custom-search-input .ant-input-affix-wrapper .ant-input {
  padding-left: 11px !important;
  padding-right: 11px !important;
}

.custom-search-input .ant-input-search .ant-input-affix-wrapper .ant-input {
  padding-right: 32px !important;
}

/* 确保搜索图标不会与文字重叠 */
.custom-search-input .ant-input-search-button {
  right: 0 !important;
  width: 32px !important;
  height: 30px !important;
}

/* 额外的强制覆盖 - 使用更高的特异性 */
body .custom-search-input.ant-input-search,
body .custom-search-input .ant-input-search,
body .custom-search-input .ant-input-affix-wrapper,
body .custom-search-input .ant-input {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  color: #262626 !important;
}

body .theme-dark .custom-search-input.ant-input-search,
body .theme-dark .custom-search-input .ant-input-search,
body .theme-dark .custom-search-input .ant-input-affix-wrapper,
body .theme-dark .custom-search-input .ant-input {
  background-color: #1f1f1f !important;
  background: #1f1f1f !important;
  border: 1px solid #4b5563 !important;
  color: #e2e8f0 !important;
}

/* 最终的强制覆盖 - 覆盖任何可能的内联样式 */
.custom-search-input[style] .ant-input-affix-wrapper,
.custom-search-input[style] .ant-input,
.custom-search-input.ant-input-search[style],
.custom-search-input .ant-input-search[style] {
  background-color: #ffffff !important;
  background: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  color: #262626 !important;
}

.theme-dark .custom-search-input[style] .ant-input-affix-wrapper,
.theme-dark .custom-search-input[style] .ant-input,
.theme-dark .custom-search-input.ant-input-search[style],
.theme-dark .custom-search-input .ant-input-search[style] {
  background-color: #1f1f1f !important;
  background: #1f1f1f !important;
  border: 1px solid #4b5563 !important;
  color: #e2e8f0 !important;
}
