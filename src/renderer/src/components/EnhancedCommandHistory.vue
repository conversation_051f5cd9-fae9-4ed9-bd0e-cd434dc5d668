<template>
  <div class="enhanced-command-history">
    <!-- 头部工具栏 -->
    <div class="history-header">
      <div class="search-section">
        <div class="search-input-wrapper">
          <i class="search-icon">🔍</i>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索命令或描述..."
            class="search-input"
            @input="handleSearch"
          />
          <button
            v-if="searchQuery"
            class="clear-btn"
            @click="clearSearch"
            >✕</button
          >
        </div>

        <div class="filter-controls">
          <!-- 分类筛选 -->
          <select
            v-model="selectedCategory"
            class="category-select"
            @change="handleSearch"
          >
            <option value="">所有分类</option>
            <option
              v-for="category in categories"
              :key="category.value"
              :value="category.value"
            >
              {{ category.label }}
            </option>
          </select>

          <!-- 标签筛选 -->
          <div class="tag-filter">
            <button
              class="tag-filter-btn"
              @click="showTagSelector = !showTagSelector"
            >
              标签筛选 <span v-if="selectedTags.length > 0">({{ selectedTags.length }})</span>
            </button>
            <div
              v-if="showTagSelector"
              class="tag-selector"
            >
              <div class="tag-list">
                <label
                  v-for="tag in availableTags"
                  :key="tag"
                  class="tag-checkbox"
                >
                  <input
                    v-model="selectedTags"
                    type="checkbox"
                    :value="tag"
                    @change="handleSearch"
                  />
                  <span class="tag-name">{{ tag }}</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 其他筛选选项 -->
          <label class="favorites-filter">
            <input
              v-model="onlyFavorites"
              type="checkbox"
              @change="handleSearch"
            />
            <span>仅收藏</span>
          </label>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <div class="stat-item">
          <span class="stat-label">总命令数:</span>
          <span class="stat-value">{{ stats.totalCommands }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">当前显示:</span>
          <span class="stat-value">{{ filteredCommands.length }}</span>
        </div>
      </div>
    </div>

    <!-- 命令列表 -->
    <div
      ref="commandList"
      class="command-list"
    >
      <div
        v-for="command in paginatedCommands"
        :key="command.id"
        class="command-item"
        :class="{ 'is-favorite': command.isFavorite }"
      >
        <!-- 命令头部 -->
        <div class="command-header">
          <div class="command-info">
            <span
              class="command-text"
              @click="copyCommand(command.command)"
              >{{ command.command }}</span
            >
            <span
              class="category-badge"
              :class="`category-${command.category}`"
            >
              {{ getCategoryLabel(command.category) }}
            </span>
          </div>

          <div class="command-actions">
            <button
              class="action-btn favorite-btn"
              :class="{ active: command.isFavorite }"
              title="收藏/取消收藏"
              @click="toggleFavorite(command)"
            >
              {{ command.isFavorite ? '★' : '☆' }}
            </button>

            <button
              class="action-btn edit-btn"
              title="编辑"
              @click="editCommand(command)"
            >
              ✏️
            </button>

            <button
              class="action-btn delete-btn"
              title="删除"
              @click="deleteCommand(command)"
            >
              🗑️
            </button>
          </div>
        </div>

        <!-- 命令详情 -->
        <div class="command-details">
          <div class="command-meta">
            <span class="meta-item">
              <i class="meta-icon">🖥️</i>
              {{ command.ip }}
            </span>
            <span class="meta-item">
              <i class="meta-icon">📊</i>
              使用 {{ command.count }} 次
            </span>
            <span class="meta-item">
              <i class="meta-icon">🕒</i>
              {{ formatDate(command.lastUsed) }}
            </span>
          </div>

          <!-- 描述 -->
          <div
            v-if="command.description"
            class="command-description"
          >
            {{ command.description }}
          </div>

          <!-- 标签 -->
          <div
            v-if="command.tags.length > 0"
            class="command-tags"
          >
            <span
              v-for="tag in command.tags"
              :key="tag"
              class="tag"
              @click="addTagFilter(tag)"
            >
              #{{ tag }}
            </span>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div
        v-if="hasMoreCommands"
        class="load-more"
      >
        <button
          class="load-more-btn"
          :disabled="loading"
          @click="loadMore"
        >
          {{ loading ? '加载中...' : '加载更多' }}
        </button>
      </div>

      <!-- 空状态 -->
      <div
        v-if="filteredCommands.length === 0"
        class="empty-state"
      >
        <div class="empty-icon">📝</div>
        <div class="empty-text">
          {{ searchQuery ? '未找到匹配的命令' : '暂无命令历史' }}
        </div>
      </div>
    </div>

    <!-- 编辑命令对话框 -->
    <div
      v-if="editingCommand"
      class="modal-overlay"
      @click="closeEditDialog"
    >
      <div
        class="edit-dialog"
        @click.stop
      >
        <div class="dialog-header">
          <h3>编辑命令</h3>
          <button
            class="close-btn"
            @click="closeEditDialog"
            >✕</button
          >
        </div>

        <div class="dialog-content">
          <div class="form-group">
            <label>命令:</label>
            <input
              v-model="editForm.command"
              type="text"
              class="form-input"
              readonly
            />
          </div>

          <div class="form-group">
            <label>分类:</label>
            <select
              v-model="editForm.category"
              class="form-select"
            >
              <option
                v-for="category in categories"
                :key="category.value"
                :value="category.value"
              >
                {{ category.label }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>描述:</label>
            <textarea
              v-model="editForm.description"
              class="form-textarea"
              placeholder="为这个命令添加描述..."
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <label>标签:</label>
            <div class="tag-input-wrapper">
              <input
                v-model="newTag"
                type="text"
                class="tag-input"
                placeholder="输入标签后按回车添加"
                @keyup.enter="addTag"
              />
              <button
                class="add-tag-btn"
                @click="addTag"
                >添加</button
              >
            </div>
            <div class="current-tags">
              <span
                v-for="tag in editForm.tags"
                :key="tag"
                class="edit-tag"
              >
                #{{ tag }}
                <button
                  class="remove-tag-btn"
                  @click="removeTag(tag)"
                  >✕</button
                >
              </span>
            </div>
          </div>
        </div>

        <div class="dialog-actions">
          <button
            class="cancel-btn"
            @click="closeEditDialog"
            >取消</button
          >
          <button
            class="save-btn"
            @click="saveCommand"
            >保存</button
          >
        </div>
      </div>
    </div>

    <!-- 统计面板 -->
    <div
      v-if="showStats"
      class="stats-panel"
    >
      <div class="stats-header">
        <h3>命令统计</h3>
        <button
          class="close-btn"
          @click="showStats = false"
          >✕</button
        >
      </div>

      <div class="stats-content">
        <!-- 分类统计 -->
        <div class="stats-section">
          <h4>分类分布</h4>
          <div class="category-stats">
            <div
              v-for="(count, category) in stats.categoryCounts"
              :key="category"
              class="category-stat"
            >
              <span class="category-name">{{ getCategoryLabel(category) }}</span>
              <span class="category-count">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- 热门标签 -->
        <div class="stats-section">
          <h4>热门标签</h4>
          <div class="tag-stats">
            <span
              v-for="tagStat in stats.topTags"
              :key="tagStat.tag"
              class="tag-stat"
            >
              #{{ tagStat.tag }} ({{ tagStat.count }})
            </span>
          </div>
        </div>

        <!-- 最常用命令 -->
        <div class="stats-section">
          <h4>最常用命令</h4>
          <div class="command-stats">
            <div
              v-for="cmdStat in stats.mostUsedCommands"
              :key="cmdStat.command"
              class="command-stat"
            >
              <span class="command-name">{{ cmdStat.command }}</span>
              <span class="command-count">{{ cmdStat.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <button
        class="fab stats-fab"
        title="统计信息"
        @click="showStats = !showStats"
      >
        📊
      </button>
      <button
        class="fab export-fab"
        title="导出"
        @click="exportCommands"
      >
        📤
      </button>
      <button
        class="fab import-fab"
        title="导入"
        @click="importCommands"
      >
        📥
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import {
  EnhancedCommandHistoryService,
  EnhancedCommandItem,
  CommandCategory,
  SearchOptions,
  CommandStats
} from '../../../main/storage/db/enhanced-command-history.service'

// 响应式数据
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedTags = ref<string[]>([])
const onlyFavorites = ref(false)
const showTagSelector = ref(false)
const showStats = ref(false)
const loading = ref(false)
const editingCommand = ref<EnhancedCommandItem | null>(null)
const newTag = ref('')

// 分页相关
const pageSize = 50
const currentPage = ref(1)

// 数据
const allCommands = ref<EnhancedCommandItem[]>([])
const availableTags = ref<string[]>([])
const stats = ref<CommandStats>({
  totalCommands: 0,
  categoryCounts: {} as Record<CommandCategory, number>,
  topTags: [],
  mostUsedCommands: [],
  recentActivity: []
})

// 编辑表单
const editForm = reactive({
  command: '',
  category: CommandCategory.OTHER,
  description: '',
  tags: [] as string[]
})

// 服务实例
let historyService: EnhancedCommandHistoryService

// 分类配置
const categories = [
  { value: CommandCategory.FILE_OPERATION, label: '文件操作' },
  { value: CommandCategory.NETWORK, label: '网络' },
  { value: CommandCategory.SYSTEM, label: '系统' },
  { value: CommandCategory.DEVELOPMENT, label: '开发' },
  { value: CommandCategory.DATABASE, label: '数据库' },
  { value: CommandCategory.DOCKER, label: 'Docker' },
  { value: CommandCategory.GIT, label: 'Git' },
  { value: CommandCategory.PROCESS, label: '进程' },
  { value: CommandCategory.SEARCH, label: '搜索' },
  { value: CommandCategory.ARCHIVE, label: '压缩' },
  { value: CommandCategory.OTHER, label: '其他' }
]

// 计算属性
const filteredCommands = computed(() => {
  const options: SearchOptions = {
    query: searchQuery.value || undefined,
    category: (selectedCategory.value as CommandCategory) || undefined,
    tags: selectedTags.value.length > 0 ? selectedTags.value : undefined,
    onlyFavorites: onlyFavorites.value || undefined,
    limit: pageSize * currentPage.value
  }

  return historyService ? historyService.searchCommands(options) : []
})

const paginatedCommands = computed(() => {
  return filteredCommands.value.slice(0, pageSize * currentPage.value)
})

const hasMoreCommands = computed(() => {
  return filteredCommands.value.length > pageSize * currentPage.value
})

// 方法
const initializeService = async () => {
  try {
    historyService = await EnhancedCommandHistoryService.getInstance()
    await loadData()
  } catch (error) {
    console.error('Failed to initialize enhanced command history service:', error)
  }
}

const loadData = async () => {
  if (!historyService) return

  try {
    loading.value = true

    // 加载命令历史
    allCommands.value = historyService.searchCommands({ limit: 1000 })

    // 加载可用标签
    availableTags.value = historyService.getAllTags()

    // 加载统计信息
    stats.value = historyService.getCommandStats()
  } catch (error) {
    console.error('Failed to load command history data:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  // 搜索会通过计算属性自动触发
}

const clearSearch = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedTags.value = []
  onlyFavorites.value = false
  handleSearch()
}

const loadMore = () => {
  currentPage.value++
}

const copyCommand = async (command: string) => {
  try {
    await navigator.clipboard.writeText(command)
    // 可以添加提示
  } catch (error) {
    console.error('Failed to copy command:', error)
  }
}

const toggleFavorite = async (command: EnhancedCommandItem) => {
  if (!historyService) return

  try {
    const isFavorite = historyService.toggleFavorite(command.id)
    command.isFavorite = isFavorite

    // 更新统计信息
    stats.value = historyService.getCommandStats()
  } catch (error) {
    console.error('Failed to toggle favorite:', error)
  }
}

const editCommand = (command: EnhancedCommandItem) => {
  editingCommand.value = command
  editForm.command = command.command
  editForm.category = command.category
  editForm.description = command.description || ''
  editForm.tags = [...command.tags]
}

const closeEditDialog = () => {
  editingCommand.value = null
  newTag.value = ''
}

const addTag = () => {
  const tag = newTag.value.trim().toLowerCase()
  if (tag && !editForm.tags.includes(tag)) {
    editForm.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (tag: string) => {
  const index = editForm.tags.indexOf(tag)
  if (index > -1) {
    editForm.tags.splice(index, 1)
  }
}

const saveCommand = async () => {
  if (!historyService || !editingCommand.value) return

  try {
    const command = editingCommand.value

    // 更新分类
    if (editForm.category !== command.category) {
      historyService.updateCategory(command.id, editForm.category)
      command.category = editForm.category
    }

    // 更新描述
    if (editForm.description !== command.description) {
      historyService.updateDescription(command.id, editForm.description)
      command.description = editForm.description
    }

    // 更新标签
    const currentTags = new Set(command.tags)
    const newTags = new Set(editForm.tags)

    // 添加新标签
    for (const tag of newTags) {
      if (!currentTags.has(tag)) {
        historyService.addTag(command.id, tag)
      }
    }

    // 移除旧标签
    for (const tag of currentTags) {
      if (!newTags.has(tag)) {
        historyService.removeTag(command.id, tag)
      }
    }

    command.tags = [...editForm.tags]

    // 重新加载数据
    await loadData()

    closeEditDialog()
  } catch (error) {
    console.error('Failed to save command:', error)
  }
}

const deleteCommand = async (command: EnhancedCommandItem) => {
  if (!historyService) return

  if (confirm(`确定要删除命令 "${command.command}" 吗？`)) {
    try {
      historyService.deleteCommand(command.id)
      await loadData()
    } catch (error) {
      console.error('Failed to delete command:', error)
    }
  }
}

const addTagFilter = (tag: string) => {
  if (!selectedTags.value.includes(tag)) {
    selectedTags.value.push(tag)
    handleSearch()
  }
}

const getCategoryLabel = (category: string): string => {
  const found = categories.find((c) => c.value === category)
  return found ? found.label : '其他'
}

const formatDate = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const exportCommands = async () => {
  if (!historyService) return

  try {
    const commands = historyService.exportCommands()
    const data = JSON.stringify(commands, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `command-history-${new Date().toISOString().split('T')[0]}.json`
    a.click()

    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export commands:', error)
  }
}

const importCommands = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'

  input.onchange = async (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file || !historyService) return

    try {
      const text = await file.text()
      const commands = JSON.parse(text)

      if (Array.isArray(commands)) {
        historyService.batchImportCommands(commands)
        await loadData()
      }
    } catch (error) {
      console.error('Failed to import commands:', error)
      alert('导入失败，请检查文件格式')
    }
  }

  input.click()
}

// 生命周期
onMounted(() => {
  initializeService()
})
</script>

<style scoped>
.enhanced-command-history {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #1a1a1a;
  color: #e0e0e0;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 头部工具栏 */
.history-header {
  padding: 16px;
  border-bottom: 1px solid #333;
  background: #2a2a2a;
}

.search-section {
  margin-bottom: 12px;
}

.search-input-wrapper {
  position: relative;
  margin-bottom: 12px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 40px;
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  color: #e0e0e0;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #007acc;
}

.clear-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  font-size: 16px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.category-select {
  padding: 6px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 12px;
}

.tag-filter {
  position: relative;
}

.tag-filter-btn {
  padding: 6px 12px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 12px;
}

.tag-selector {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  background: #2a2a2a;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 8px;
  min-width: 200px;
  max-height: 200px;
  overflow-y: auto;
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tag-checkbox {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 12px;
}

.favorites-filter {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
}

.stats-section {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #888;
}

.stat-item {
  display: flex;
  gap: 4px;
}

.stat-value {
  color: #007acc;
  font-weight: bold;
}

/* 命令列表 */
.command-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.command-item {
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 6px;
  margin-bottom: 12px;
  padding: 12px;
  transition: all 0.2s ease;
}

.command-item:hover {
  border-color: #555;
  background: #2d2d2d;
}

.command-item.is-favorite {
  border-color: #ffd700;
}

.command-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.command-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.command-text {
  font-family: 'SF Mono', Monaco, monospace;
  background: #1a1a1a;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  word-break: break-all;
}

.command-text:hover {
  background: #333;
}

.category-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.category-file_operation {
  background: #4caf50;
  color: white;
}
.category-network {
  background: #2196f3;
  color: white;
}
.category-system {
  background: #ff9800;
  color: white;
}
.category-development {
  background: #9c27b0;
  color: white;
}
.category-database {
  background: #607d8b;
  color: white;
}
.category-docker {
  background: #0db7ed;
  color: white;
}
.category-git {
  background: #f05032;
  color: white;
}
.category-process {
  background: #795548;
  color: white;
}
.category-search {
  background: #e91e63;
  color: white;
}
.category-archive {
  background: #3f51b5;
  color: white;
}
.category-other {
  background: #757575;
  color: white;
}

.command-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  background: none;
  border: none;
  padding: 4px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
}

.action-btn:hover {
  background: #333;
}

.favorite-btn.active {
  color: #ffd700;
}

.command-details {
  font-size: 12px;
}

.command-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 6px;
  color: #888;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-icon {
  font-size: 10px;
}

.command-description {
  color: #ccc;
  margin-bottom: 6px;
  font-style: italic;
}

.command-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  background: #007acc;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  cursor: pointer;
}

.tag:hover {
  background: #005a9e;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 16px;
}

.load-more-btn {
  padding: 8px 16px;
  background: #007acc;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.load-more-btn:disabled {
  background: #555;
  cursor: not-allowed;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px;
  color: #888;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-dialog {
  background: #2a2a2a;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #333;
}

.dialog-header h3 {
  margin: 0;
  color: #e0e0e0;
}

.close-btn {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  font-size: 18px;
}

.dialog-content {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  color: #ccc;
  font-size: 12px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px;
  background: #333;
  border: 1px solid #555;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 14px;
}

.form-textarea {
  resize: vertical;
  min-height: 60px;
}

.tag-input-wrapper {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.tag-input {
  flex: 1;
}

.add-tag-btn {
  padding: 8px 12px;
  background: #007acc;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 12px;
}

.current-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.edit-tag {
  background: #007acc;
  color: white;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.remove-tag-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 10px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #333;
}

.cancel-btn,
.save-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.cancel-btn {
  background: #555;
  color: #e0e0e0;
}

.save-btn {
  background: #007acc;
  color: white;
}

/* 统计面板 */
.stats-panel {
  position: fixed;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  background: #2a2a2a;
  border: 1px solid #333;
  border-radius: 8px;
  width: 300px;
  max-height: 70vh;
  overflow-y: auto;
  z-index: 100;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #333;
}

.stats-header h3 {
  margin: 0;
  color: #e0e0e0;
  font-size: 14px;
}

.stats-content {
  padding: 16px;
}

.stats-section {
  margin-bottom: 16px;
}

.stats-section h4 {
  margin: 0 0 8px 0;
  color: #ccc;
  font-size: 12px;
}

.category-stats,
.command-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-stat,
.command-stat {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  padding: 2px 0;
}

.tag-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag-stat {
  background: #007acc;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

/* 浮动操作按钮 */
.floating-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 50;
}

.fab {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: #007acc;
  color: white;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.fab:hover {
  background: #005a9e;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .command-header {
    flex-direction: column;
    gap: 8px;
  }

  .command-meta {
    flex-direction: column;
    gap: 4px;
  }

  .stats-panel {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    transform: none;
    border-radius: 0;
  }
}
</style>
