macOS SMB 连接诊断报告
生成时间: 2025年 9月10日 星期三 14时47分16秒 CST
系统信息: macOS 15.6.1
目标主机: 10.4.121.250
共享名称: it部
用户名: <EMAIL>
域: brbiotech.com

=== 网络信息 ===
lo0: flags=8049<UP,LOOPBACK,RUNNING,MULTICAST> mtu 16384
	inet 127.0.0.1 netmask 0xff000000
gif0: flags=8010<POINTOPOINT,MULTICAST> mtu 1280
stf0: flags=0<> mtu 1280
anpi0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
anpi1: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
en3: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
en4: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
en1: flags=8963<UP,BROADCAST,SMART,RUNNING,PROMISC,SIMPLEX,MULTICAST> mtu 1500
en2: flags=8963<UP,BROADCAST,SMART,RUNNING,PROMISC,SIMPLEX,MULTICAST> mtu 1500
bridge0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
		ipfilter disabled flags 0x0
	member: en1 flags=3<LEARNING,DISCOVER>
	member: en2 flags=3<LEARNING,DISCOVER>
utun0: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
ap1: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
en0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
	inet ************ netmask 0xffffff00 broadcast ************
awdl0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
llw0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
utun1: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1500
utun2: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 2000
utun3: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1000
utun4: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun5: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun6: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun7: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun9: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun10: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
utun11: flags=8051<UP,POINTOPOINT,RUNNING,MULTICAST> mtu 1380
en7: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500

=== 路由信息 ===
Routing tables

Internet:
Destination        Gateway            Flags               Netif Expire
default            **********         UGScg                 en0       
10.4.131/24        link#11            UCS                   en0      !
**********/32      link#11            UCS                   en0      !
**********         fc:73:fb:72:64:c   UHLWIir               en0   1197
***********        26:34:19:1e:74:83  UHLWI                 en0   1180
***********        4e:36:1f:39:b6:76  UHLWI                 en0    893

=== DNS 配置 ===
#
# macOS Notice
#
# This file is not consulted for DNS hostname resolution, address
# resolution, or the DNS query routing mechanism used by most
# processes on this system.
#
# To view the DNS configuration used by this system, use:
#   scutil --dns
#
# SEE ALSO
#   dns-sd(1), scutil(8)
#
# This file is automatically generated.
#
nameserver ************
nameserver ************
