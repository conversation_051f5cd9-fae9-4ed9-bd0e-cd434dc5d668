<!--
 * RDP连接配置组件
 * 功能：提供RDP连接的配置界面和连接管理
 * 作者：SOLO Coding
 * 修改时间：2025-01-10
-->

<template>
  <div class="rdp-connector">
    <div class="connector-header">
      <div class="header-title">
        <WindowsOutlined class="title-icon" />
        <span>RDP远程桌面连接</span>
      </div>
      <div class="header-actions">
        <a-button
          v-if="!isConnected"
          type="primary"
          :loading="isConnecting"
          @click="handleConnect"
        >
          <PlayCircleOutlined />
          连接
        </a-button>
        <a-button
          v-else
          danger
          @click="handleDisconnect"
        >
          <StopOutlined />
          断开
        </a-button>
        <a-button
          v-if="isConnected"
          @click="handleFullscreen"
        >
          <FullscreenOutlined />
          全屏
        </a-button>
      </div>
    </div>

    <div class="connector-content">
      <!-- 基本连接信息 -->
      <div class="config-section">
        <div class="section-title">
          <div class="title-indicator"></div>
          连接信息
        </div>

        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item label="服务器地址">
                <a-input
                  v-model:value="config.host"
                  placeholder="请输入IP地址或域名"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="端口">
                <a-input-number
                  v-model:value="config.port"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="用户名">
                <a-input
                  v-model:value="config.username"
                  placeholder="请输入用户名"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="域名">
                <a-input
                  v-model:value="config.domain"
                  placeholder="例如: WORKGROUP"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-form-item label="密码">
            <a-input-password
              v-model:value="config.password"
              placeholder="请输入密码"
              :disabled="isConnected"
            />
          </a-form-item>
        </a-form>
      </div>

      <!-- 显示设置 -->
      <div class="config-section">
        <div class="section-title">
          <div class="title-indicator"></div>
          显示设置
        </div>

        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item label="宽度">
                <a-input-number
                  v-model:value="config.display_settings.width"
                  :min="800"
                  :max="3840"
                  placeholder="1920"
                  style="width: 100%"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="高度">
                <a-input-number
                  v-model:value="config.display_settings.height"
                  :min="600"
                  :max="2160"
                  placeholder="1080"
                  style="width: 100%"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="色深">
                <a-select
                  v-model:value="config.display_settings.colorDepth"
                  placeholder="色深"
                  :disabled="isConnected"
                >
                  <a-select-option :value="16">16位</a-select-option>
                  <a-select-option :value="24">24位</a-select-option>
                  <a-select-option :value="32">32位</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>全屏模式</span>
                  <a-tooltip title="启用后将以全屏模式连接">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.display_settings.fullscreen"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>多显示器</span>
                  <a-tooltip title="使用所有可用显示器">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.display_settings.multiMonitor"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 安全设置 -->
      <div class="config-section">
        <div class="section-title">
          <div class="title-indicator"></div>
          安全设置
        </div>

        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="启用加密">
                <a-switch
                  v-model:checked="config.security_settings.encryption"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="认证方式">
                <a-select
                  v-model:value="config.security_settings.authentication"
                  placeholder="认证方式"
                  :disabled="isConnected"
                >
                  <a-select-option value="ntlm">NTLM</a-select-option>
                  <a-select-option value="basic">基本认证</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>忽略证书错误</span>
                  <a-tooltip title="忽略SSL证书验证错误">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.security_settings.ignoreCertErrors"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>网络级别认证</span>
                  <a-tooltip title="启用网络级别认证(NLA)">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.security_settings.nla"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 高级设置 -->
      <div class="config-section">
        <div class="section-title">
          <div class="title-indicator"></div>
          高级设置
        </div>

        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="连接超时(秒)">
                <a-input-number
                  v-model:value="config.advanced_settings.timeout"
                  :min="5"
                  :max="300"
                  placeholder="60"
                  style="width: 100%"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="音频重定向">
                <a-select
                  v-model:value="config.advanced_settings.audioRedirection"
                  placeholder="音频重定向"
                  :disabled="isConnected"
                >
                  <a-select-option value="local">播放到本地</a-select-option>
                  <a-select-option value="remote">播放到远程</a-select-option>
                  <a-select-option value="none">不播放</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>剪贴板共享</span>
                  <a-tooltip title="启用本地和远程剪贴板共享">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.advanced_settings.clipboardSharing"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item>
                <template #label>
                  <span>驱动器重定向</span>
                  <a-tooltip title="将本地驱动器映射到远程桌面">
                    <QuestionCircleOutlined style="margin-left: 4px; color: var(--text-color-tertiary)" />
                  </a-tooltip>
                </template>
                <a-switch
                  v-model:checked="config.advanced_settings.driveRedirection"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 连接状态 -->
      <div
        v-if="connectionStatus"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          连接状态
        </div>

        <div class="status-info">
          <div class="status-item">
            <span class="status-label">状态:</span>
            <a-tag :color="getStatusColor(connectionStatus.status)">
              {{ getStatusText(connectionStatus.status) }}
            </a-tag>
          </div>

          <div
            v-if="connectionStatus.connectedAt"
            class="status-item"
          >
            <span class="status-label">连接时间:</span>
            <span>{{ formatTime(connectionStatus.connectedAt) }}</span>
          </div>

          <div
            v-if="connectionStatus.sessionInfo"
            class="status-item"
          >
            <span class="status-label">会话ID:</span>
            <span>{{ connectionStatus.sessionInfo.sessionId }}</span>
          </div>

          <div
            v-if="connectionStatus.error"
            class="status-item"
          >
            <span class="status-label">错误信息:</span>
            <span class="error-text">{{ connectionStatus.error }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { WindowsOutlined, PlayCircleOutlined, StopOutlined, FullscreenOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 接口定义
interface RdpConfig {
  host: string
  port: number
  username: string
  password: string
  domain: string
  display_settings: {
    width: number
    height: number
    colorDepth: number
    fullscreen: boolean
    multiMonitor: boolean
  }
  security_settings: {
    encryption: boolean
    authentication: 'ntlm' | 'basic'
    ignoreCertErrors: boolean
    nla: boolean
  }
  advanced_settings: {
    timeout: number
    audioRedirection: 'local' | 'remote' | 'none'
    clipboardSharing: boolean
    driveRedirection: boolean
  }
}

interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  connectedAt?: Date
  sessionInfo?: {
    sessionId: string
    resolution: string
  }
  error?: string
}

interface Props {
  assetId?: string
  initialConfig?: Partial<RdpConfig>
}

const props = withDefaults(defineProps<Props>(), {
  initialConfig: () => ({})
})

const emit = defineEmits<{
  connect: [config: RdpConfig]
  disconnect: []
  fullscreen: []
  'config-change': [config: RdpConfig]
}>()

// 响应式数据
const isConnecting = ref(false)
const isConnected = ref(false)
const connectionStatus = ref<ConnectionStatus | null>(null)

const config = reactive<RdpConfig>({
  host: '',
  port: 3389,
  username: '',
  password: '',
  domain: 'WORKGROUP',
  display_settings: {
    width: 1920,
    height: 1080,
    colorDepth: 32,
    fullscreen: false,
    multiMonitor: false
  },
  security_settings: {
    encryption: true,
    authentication: 'ntlm',
    ignoreCertErrors: false,
    nla: true
  },
  advanced_settings: {
    timeout: 60,
    audioRedirection: 'local',
    clipboardSharing: true,
    driveRedirection: false
  },
  ...props.initialConfig
})

// 监听配置变化
watch(
  () => config,
  (newConfig) => {
    emit('config-change', { ...newConfig })
  },
  { deep: true }
)

// 方法
const validateConfig = (): boolean => {
  if (!config.host) {
    message.error('请输入服务器地址')
    return false
  }
  if (!config.port || config.port <= 0) {
    message.error('请输入有效的端口号')
    return false
  }
  if (!config.username) {
    message.error('请输入用户名')
    return false
  }
  if (!config.password) {
    message.error('请输入密码')
    return false
  }

  return true
}

const handleConnect = async () => {
  if (!validateConfig()) return

  isConnecting.value = true
  connectionStatus.value = {
    status: 'connecting'
  }

  try {
    // 调用后端API建立RDP连接
    const response = await window.electronAPI?.rdpConnect({
      assetId: props.assetId,
      config: config,
      displaySettings: config.display_settings
    })

    if (response?.success) {
      isConnected.value = true
      connectionStatus.value = {
        status: 'connected',
        connectedAt: new Date(),
        sessionInfo: {
          sessionId: response.connectionId,
          resolution: `${config.display_settings.width}x${config.display_settings.height}`
        }
      }
      message.success('RDP连接成功')
      emit('connect', config)
    } else {
      throw new Error(response?.message || '连接失败')
    }
  } catch (error: any) {
    connectionStatus.value = {
      status: 'error',
      error: error.message
    }
    message.error(`RDP连接失败: ${error.message}`)
  } finally {
    isConnecting.value = false
  }
}

const handleDisconnect = async () => {
  try {
    await window.electronAPI?.rdpDisconnect(props.assetId)

    isConnected.value = false
    connectionStatus.value = {
      status: 'disconnected'
    }
    message.success('RDP连接已断开')
    emit('disconnect')
  } catch (error: any) {
    message.error(`断开连接失败: ${error.message}`)
  }
}

const handleFullscreen = () => {
  emit('fullscreen')
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
      return 'green'
    case 'connecting':
      return 'blue'
    case 'error':
      return 'red'
    case 'disconnected':
      return 'default'
    default:
      return 'default'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'error':
      return '连接错误'
    case 'disconnected':
      return '已断开'
    default:
      return '未知状态'
  }
}

const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}
</script>

<style lang="less" scoped>
.rdp-connector {
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.connector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.title-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.connector-content {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.title-indicator {
  width: 3px;
  height: 14px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.status-info {
  background: var(--bg-color-secondary);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.status-label {
  min-width: 80px;
  color: var(--text-color-secondary);
  font-size: 13px;
}

.error-text {
  color: var(--error-color);
  font-size: 13px;
}

// 深色主题适配
:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-input-password) {
  background: var(--bg-color-secondary);
  border-color: var(--border-color);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &:focus,
  &.ant-input-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-form-item-label > label) {
  color: var(--text-color);
}

:deep(.ant-switch) {
  &.ant-switch-checked {
    background-color: var(--primary-color);
  }
}
</style>
