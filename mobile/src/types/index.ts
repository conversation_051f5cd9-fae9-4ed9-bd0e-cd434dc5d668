/**
 * 移动端类型定义
 */

// SSH连接配置
export interface MobileSSHConfig {
  id: string
  name: string
  host: string
  port: number
  username: string
  password?: string
  privateKey?: string
  passphrase?: string

  // 移动端特定配置
  keepAlive: boolean
  backgroundConnection: boolean
  autoReconnect: boolean
  touchOptimization: boolean

  // 显示配置
  fontSize: number
  fontFamily: string
  theme: 'dark' | 'light' | 'auto'

  // 快捷键配置
  shortcuts: ShortcutConfig[]
}

// 快捷键配置
export interface ShortcutConfig {
  id: string
  label: string
  command: string
  icon?: string
  color?: string
}

// SSH连接状态
export interface ConnectionState {
  id: string
  status: 'disconnected' | 'connecting' | 'connected' | 'error'
  lastConnected?: Date
  error?: string

  // 性能指标
  latency?: number
  bytesReceived: number
  bytesSent: number

  // 会话信息
  sessionDuration: number
  commandCount: number
}

// 终端会话
export interface TerminalSession {
  id: string
  connectionId: string
  title: string

  // 终端状态
  isActive: boolean
  currentDirectory: string
  lastCommand: string

  // 显示配置
  scrollPosition: number
  fontSize: number

  // 历史记录
  commandHistory: string[]
  output: TerminalOutput[]
}

// 终端输出
export interface TerminalOutput {
  id: string
  timestamp: Date
  type: 'input' | 'output' | 'error'
  content: string

  // 格式化信息
  formatting?: {
    color?: string
    backgroundColor?: string
    bold?: boolean
    italic?: boolean
  }
}

// 移动端手势配置
export interface GestureConfig {
  // 滑动手势
  swipeLeft: GestureAction
  swipeRight: GestureAction
  swipeUp: GestureAction
  swipeDown: GestureAction

  // 点击手势
  doubleTap: GestureAction
  longPress: GestureAction

  // 缩放手势
  pinchZoom: boolean

  // 触摸反馈
  hapticFeedback: boolean
}

// 手势动作
export type GestureAction =
  | 'none'
  | 'showKeyboard'
  | 'hideKeyboard'
  | 'showShortcuts'
  | 'switchSession'
  | 'newSession'
  | 'closeSession'
  | 'copy'
  | 'paste'
  | 'clear'

// 虚拟键盘配置
export interface VirtualKeyboardConfig {
  layout: 'qwerty' | 'compact' | 'custom'
  showFunctionKeys: boolean
  showArrowKeys: boolean
  showNumberRow: boolean

  // 自定义按键
  customKeys: CustomKey[]

  // 外观配置
  keyHeight: number
  keySpacing: number
  backgroundColor: string
  keyColor: string
  textColor: string
}

// 自定义按键
export interface CustomKey {
  id: string
  label: string
  value: string
  width?: number
  type: 'text' | 'command' | 'special'
  icon?: string
}

// 应用设置
export interface AppSettings {
  // 通用设置
  theme: 'dark' | 'light' | 'auto'
  language: string

  // 连接设置
  defaultTimeout: number
  maxConnections: number
  autoSave: boolean

  // 安全设置
  biometricAuth: boolean
  sessionTimeout: number
  encryptStorage: boolean

  // 性能设置
  backgroundSync: boolean
  lowPowerMode: boolean
  dataUsageLimit: number

  // 通知设置
  connectionNotifications: boolean
  errorNotifications: boolean
  soundEnabled: boolean
  vibrationEnabled: boolean

  // 手势和键盘
  gestureConfig: GestureConfig
  keyboardConfig: VirtualKeyboardConfig
}

// 用户数据
export interface UserProfile {
  id: string
  username: string
  email: string
  avatar?: string

  // 使用统计
  totalSessions: number
  totalCommands: number
  favoriteCommands: string[]

  // 同步设置
  cloudSync: boolean
  lastSyncTime?: Date

  // 订阅信息
  subscription?: {
    type: 'free' | 'pro' | 'enterprise'
    expiresAt?: Date
    features: string[]
  }
}

// 文件传输
export interface FileTransfer {
  id: string
  connectionId: string
  type: 'upload' | 'download'

  // 文件信息
  localPath: string
  remotePath: string
  fileName: string
  fileSize: number

  // 传输状态
  status: 'pending' | 'transferring' | 'completed' | 'error'
  progress: number
  speed: number

  // 时间信息
  startTime: Date
  endTime?: Date
  estimatedTime?: number

  // 错误信息
  error?: string
}

// 命令模板
export interface CommandTemplate {
  id: string
  name: string
  description: string
  command: string
  category: string

  // 参数配置
  parameters: CommandParameter[]

  // 使用统计
  useCount: number
  lastUsed?: Date

  // 标签和分类
  tags: string[]
  isFavorite: boolean
}

// 命令参数
export interface CommandParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'select'
  required: boolean
  defaultValue?: any
  options?: string[]
  description?: string
}

// 性能监控数据
export interface PerformanceMetrics {
  timestamp: Date

  // 连接性能
  connectionLatency: number
  throughput: number
  packetLoss: number

  // 应用性能
  memoryUsage: number
  cpuUsage: number
  batteryLevel: number

  // 网络状态
  networkType: 'wifi' | 'cellular' | 'none'
  signalStrength: number

  // 用户体验
  responseTime: number
  errorRate: number
  sessionDuration: number
}

// Redux状态类型
export interface RootState {
  connections: {
    configs: MobileSSHConfig[]
    states: Record<string, ConnectionState>
    activeConnectionId?: string
  }

  sessions: {
    sessions: Record<string, TerminalSession>
    activeSessionId?: string
  }

  settings: AppSettings

  user: {
    profile?: UserProfile
    isAuthenticated: boolean
  }

  fileTransfers: {
    transfers: FileTransfer[]
    activeTransfers: string[]
  }

  templates: {
    commands: CommandTemplate[]
    categories: string[]
  }

  performance: {
    metrics: PerformanceMetrics[]
    alerts: PerformanceAlert[]
  }
}

// 性能告警
export interface PerformanceAlert {
  id: string
  type: 'latency' | 'memory' | 'battery' | 'network'
  severity: 'low' | 'medium' | 'high'
  message: string
  timestamp: Date
  acknowledged: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 云同步数据
export interface CloudSyncData {
  connections: MobileSSHConfig[]
  settings: AppSettings
  templates: CommandTemplate[]
  lastSyncTime: Date
  version: string
}
