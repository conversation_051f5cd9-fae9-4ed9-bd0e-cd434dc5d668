<template>
  <div class="sync-tab">
    <!-- 头部状态 -->
    <div class="sync-header">
      <div class="sync-status">
        <div
          class="status-indicator"
          :class="syncStatus.type"
        >
          <component
            :is="syncStatus.icon"
            class="status-icon"
          />
        </div>
        <div class="status-content">
          <div class="status-title">{{ syncStatus.title }}</div>
          <div class="status-desc">{{ syncStatus.description }}</div>
        </div>
      </div>
      <a-button
        type="primary"
        @click="navigateToConfig"
      >
        <Settings class="btn-icon" />
        配置同步
      </a-button>
    </div>

    <!-- 同步统计 -->
    <div class="sync-stats">
      <div class="stat-card">
        <div class="stat-icon">
          <Upload class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.uploaded }}</div>
          <div class="stat-label">已上传</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <Download class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.downloaded }}</div>
          <div class="stat-label">已下载</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">
          <RefreshCw class="icon" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.synced }}</div>
          <div class="stat-label">已同步</div>
        </div>
      </div>
    </div>

    <!-- 最近同步活动 -->
    <div class="recent-activity">
      <div class="section-header">
        <h3>最近同步活动</h3>
        <a-button
          size="small"
          @click="refreshActivity"
        >
          <RefreshCw class="btn-icon" />
          刷新
        </a-button>
      </div>
      <div class="activity-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
        >
          <div
            class="activity-icon"
            :class="activity.type"
          >
            <component
              :is="activity.icon"
              class="icon"
            />
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-desc">{{ activity.description }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
          <div
            class="activity-status"
            :class="activity.status"
          >
            {{ activity.statusText }}
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <div class="section-header">
        <h3>快速操作</h3>
      </div>
      <div class="action-grid">
        <div
          class="action-card"
          @click="navigateToPage('/sync/configuration')"
        >
          <Settings class="action-icon" />
          <div class="action-title">同步配置</div>
          <div class="action-desc">管理同步设置和策略</div>
        </div>
        <div
          class="action-card"
          @click="navigateToPage('/sync/monitor')"
        >
          <Activity class="action-icon" />
          <div class="action-title">监控仪表板</div>
          <div class="action-desc">查看实时同步状态</div>
        </div>
        <div
          class="action-card"
          @click="manualSync"
        >
          <RefreshCw class="action-icon" />
          <div class="action-title">手动同步</div>
          <div class="action-desc">立即执行同步操作</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Settings, Upload, Download, RefreshCw, Activity, CheckCircle, AlertCircle, XCircle, Clock } from 'lucide-vue-next'

const router = useRouter()

// 同步状态
const syncStatus = ref({
  type: 'success',
  icon: CheckCircle,
  title: '同步正常',
  description: '最后同步时间：2024年1月15日 14:30'
})

// 统计数据
const stats = ref({
  uploaded: 156,
  downloaded: 89,
  synced: 245
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'upload',
    icon: Upload,
    title: '配置文件上传',
    description: 'SSH配置已同步到云端',
    time: '2分钟前',
    status: 'success',
    statusText: '成功'
  },
  {
    id: 2,
    type: 'download',
    icon: Download,
    title: '密钥下载',
    description: '从云端下载SSH密钥',
    time: '15分钟前',
    status: 'success',
    statusText: '成功'
  },
  {
    id: 3,
    type: 'sync',
    icon: RefreshCw,
    title: '自动同步',
    description: '定时同步任务执行',
    time: '1小时前',
    status: 'warning',
    statusText: '部分成功'
  }
])

// 导航到配置页面
const navigateToConfig = () => {
  router.push('/sync/configuration')
}

// 导航到指定页面
const navigateToPage = (path: string) => {
  router.push(path)
}

// 刷新活动
const refreshActivity = async () => {
  try {
    console.log('Refreshing sync activity...')
    // 这里可以调用API刷新数据
  } catch (error) {
    console.error('Failed to refresh activity:', error)
  }
}

// 手动同步
const manualSync = async () => {
  try {
    console.log('Starting manual sync...')
    // 这里可以调用API执行手动同步
  } catch (error) {
    console.error('Failed to start manual sync:', error)
  }
}

// 加载数据
const loadData = async () => {
  try {
    console.log('Loading sync data...')
    // 这里可以调用API获取实际数据
  } catch (error) {
    console.error('Failed to load sync data:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.sync-tab {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  background: var(--bg-color);
}

.sync-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;

  &.success {
    background: #f6ffed;

    .status-icon {
      color: #52c41a;
    }
  }

  &.warning {
    background: #fffbe6;

    .status-icon {
      color: #faad14;
    }
  }

  &.error {
    background: #fff2f0;

    .status-icon {
      color: #ff4d4f;
    }
  }
}

.status-icon {
  width: 24px;
  height: 24px;
}

.status-content {
  .status-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .status-desc {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.sync-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color-light);
  border-radius: 6px;

  .icon {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
  }
}

.stat-content {
  .stat-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
  }

  .stat-label {
    font-size: 12px;
    color: var(--text-color-secondary);
  }
}

.recent-activity {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
  }
}

.activity-list {
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);

  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 6px;

  &.upload {
    background: #e6f7ff;

    .icon {
      color: #1890ff;
    }
  }

  &.download {
    background: #f6ffed;

    .icon {
      color: #52c41a;
    }
  }

  &.sync {
    background: #fff7e6;

    .icon {
      color: #fa8c16;
    }
  }

  .icon {
    width: 18px;
    height: 18px;
  }
}

.activity-content {
  flex: 1;

  .activity-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 4px;
  }

  .activity-desc {
    font-size: 13px;
    color: var(--text-color-secondary);
    margin-bottom: 4px;
  }

  .activity-time {
    font-size: 12px;
    color: var(--text-color-tertiary);
  }
}

.activity-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.success {
    background: #f6ffed;
    color: #52c41a;
  }

  &.warning {
    background: #fffbe6;
    color: #faad14;
  }

  &.error {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

.quick-actions {
  .action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
}

.action-card {
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .action-icon {
    width: 32px;
    height: 32px;
    color: var(--primary-color);
    margin: 0 auto 12px;
  }

  .action-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  .action-desc {
    font-size: 12px;
    color: var(--text-color-secondary);
    line-height: 1.4;
  }
}
</style>
