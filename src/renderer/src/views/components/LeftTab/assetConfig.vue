<template>
  <div class="asset-config-container">
    <div class="split-layout">
      <div class="left-section">
        <AssetSearch
          v-model="searchValue"
          @search="handleSearch"
          @new-asset="openNewPanel"
          @import-assets="handleImportAssets"
          @export-assets="handleExportAssets"
        />
        <AssetList
          :asset-groups="assetGroups"
          :search-value="searchValue"
          :wide-layout="!isRightSectionVisible"
          @asset-click="handleAssetClick"
          @asset-double-click="handleAssetConnect"
          @asset-edit="handleAssetEdit"
          @asset-context-menu="handleAssetContextMenu"
        />
        <AssetContextMenu
          v-if="contextMenuVisible"
          :visible="contextMenuVisible"
          :position="contextMenuPosition"
          :asset="selectedAsset"
          @close="closeContextMenu"
          @connect="handleContextMenuConnect"
          @edit="handleContextMenuEdit"
          @refresh="handleContextMenuRefresh"
          @remove="handleContextMenuRemove"
        />
      </div>

      <div
        class="right-section"
        :class="{ collapsed: !isRightSectionVisible }"
      >
        <AssetForm
          v-if="isRightSectionVisible"
          :is-edit-mode="isEditMode"
          :initial-data="formData"
          :key-chain-options="keyChainOptions"
          :ssh-proxy-configs="sshProxyConfigs"
          :default-groups="defaultGroups"
          @close="closeForm"
          @submit="handleFormSubmit"
          @add-keychain="addKeychain"
          @auth-change="handleAuthChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Modal, message, notification } from 'ant-design-vue'
import { ref, onMounted, onBeforeUnmount, reactive, watch } from 'vue'
import AssetSearch from './components/AssetSearch.vue'
import AssetList from './components/AssetList.vue'
import AssetForm from './components/AssetForm.vue'
import AssetContextMenu from './components/AssetContextMenu.vue'
import eventBus from '@/utils/eventBus'
import i18n from '@/locales'
import { handleRefreshOrganizationAssets } from './components/refreshOrganizationAssets'
import type { AssetNode, AssetFormData, KeyChainItem, SshProxyConfigItem } from './types'

const { t } = i18n.global

const isEditMode = ref(false)
const editingAssetUUID = ref<string | null>(null)
const isRightSectionVisible = ref(false)
const searchValue = ref('')
const assetGroups = ref<AssetNode[]>([])
const keyChainOptions = ref<KeyChainItem[]>([])
const sshProxyConfigs = ref<SshProxyConfigItem[]>([])
const defaultGroups = ref(['development', 'production', 'staging', 'testing', 'database'])
const contextMenuVisible = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })
const selectedAsset = ref<AssetNode | null>(null)
import { userConfigStore } from '@/services/userConfigStoreService'

const formData = reactive<AssetFormData>({
  username: '',
  password: '',
  ip: '',
  label: '',
  group_name: t('personal.defaultGroup'),
  auth_type: 'password',
  keyChain: undefined,
  port: 22,
  asset_type: 'person',
  needProxy: false,
  proxyName: ''
})

const resetForm = () => {
  Object.assign(formData, {
    username: '',
    password: '',
    ip: '',
    label: '',
    group_name: [t('personal.defaultGroup')],
    auth_type: 'password',
    keyChain: undefined,
    port: 22,
    asset_type: 'person',
    needProxy: false,
    proxyName: ''
  })
}

const openNewPanel = () => {
  console.log('[Debug] 添加主机按钮被点击，openNewPanel函数被调用')
  console.log('[Debug] 当前isRightSectionVisible状态:', isRightSectionVisible.value)
  try {
    isEditMode.value = false
    editingAssetUUID.value = null
    resetForm()
    getAssetGroup()
    if (formData.auth_type === 'keyBased') {
      getkeyChainData()
    }
    isRightSectionVisible.value = true
    console.log('[Debug] 右侧面板已设置为可见:', isRightSectionVisible.value)
    console.log('[Debug] 表单数据已重置:', formData)
    // 强制触发DOM更新
    setTimeout(() => {
      console.log('[Debug] DOM更新后，isRightSectionVisible:', isRightSectionVisible.value)
    }, 100)
  } catch (error) {
    console.error('Error opening new panel:', error)
    // 即使出错也要显示面板，确保用户可以添加主机
    isRightSectionVisible.value = true
    message.warning(t('personal.openPanelWarning') || '打开添加主机面板时出现警告，但您仍可以继续添加主机')
  }
}

const closeForm = () => {
  isRightSectionVisible.value = false
}

const handleSearch = () => {
  // Search is handled by computed property in AssetList, so we don't need to do anything here
}

const handleAssetClick = (asset: AssetNode) => {
  console.log('Asset clicked:', asset)
}

const handleAssetConnect = (asset: AssetNode) => {
  console.log('Connecting to asset:', asset)
  eventBus.emit('currentClickServer', asset)
}

const handleAssetEdit = (asset: AssetNode) => {
  if (!asset) return
  isEditMode.value = true
  editingAssetUUID.value = asset.uuid || null

  let keyChain = asset.key_chain_id
  console.log('keyChain: ', keyChain)
  if (keyChain === 0) {
    keyChain = undefined
  }

  Object.assign(formData, {
    username: asset.username || '',
    password: asset.password || '',
    ip: asset.ip || '',
    label: asset.title || '',
    group_name: asset.group_name ? [asset.group_name] : ['Hosts'],
    auth_type: asset.auth_type || 'password',
    keyChain: keyChain,
    port: asset.port || 22,
    asset_type: asset.asset_type || 'person',
    needProxy: asset.needProxy || false,
    proxyName: asset.proxyName || ''
  })

  getAssetGroup()
  if (formData.auth_type === 'keyBased') {
    getkeyChainData()
  }
  isRightSectionVisible.value = true
}

const handleAssetRefresh = async (asset: AssetNode) => {
  if (!asset || asset.asset_type !== 'organization') return

  await handleRefreshOrganizationAssets(asset, () => {
    getAssetList()
  })
  closeContextMenu()
}

const handleAssetContextMenu = (event: MouseEvent, asset: AssetNode) => {
  event.preventDefault()
  contextMenuPosition.x = event.clientX
  contextMenuPosition.y = event.clientY
  selectedAsset.value = asset
  contextMenuVisible.value = true

  const closeMenu = () => {
    contextMenuVisible.value = false
    document.removeEventListener('click', closeMenu)
  }

  setTimeout(() => {
    document.addEventListener('click', closeMenu)
  }, 0)
}

const closeContextMenu = () => {
  contextMenuVisible.value = false
}

const handleContextMenuConnect = () => {
  if (selectedAsset.value) {
    handleAssetConnect(selectedAsset.value)
  }
  closeContextMenu()
}

const handleContextMenuEdit = () => {
  if (selectedAsset.value) {
    handleAssetEdit(selectedAsset.value)
  }
  closeContextMenu()
}

const handleContextMenuRefresh = () => {
  if (selectedAsset.value) {
    handleAssetRefresh(selectedAsset.value)
  }
}

const handleContextMenuRemove = () => {
  if (selectedAsset.value) {
    handleAssetRemove(selectedAsset.value)
  }
}

const handleAssetRemove = (asset: AssetNode) => {
  if (!asset || !asset.uuid) {
    console.error('删除资产失败：资产信息无效', asset)
    message.error(t('personal.missingAssetId'))
    return
  }

  console.log('准备删除资产:', asset)
  closeContextMenu()

  Modal.confirm({
    title: t('personal.deleteConfirm'),
    content: t('personal.deleteConfirmContent', { name: asset.title }),
    okText: t('common.delete'),
    okType: 'danger',
    cancelText: t('common.cancel'),
    maskClosable: true,
    onOk: async () => {
      try {
        console.log('开始删除资产，UUID:', asset.uuid)
        const api = window.api as any

        if (!api || !api.deleteAsset) {
          console.error('删除 API 不可用')
          message.error(t('personal.deleteError', { error: '删除 API 不可用' }))
          return
        }

        const res = await api.deleteAsset({ uuid: asset.uuid })
        console.log('删除资产 API 响应:', res)

        // 检查多种可能的成功响应格式
        const isSuccess = res?.success === true || res?.data?.message === 'success' || (res?.data && res.data.success === true)

        if (isSuccess) {
          console.log('资产删除成功')
          message.success(t('personal.deleteSuccess', { name: asset.title }))
          // 刷新资产列表
          getAssetList()
          // 通知工作空间刷新
          eventBus.emit('LocalAssetMenu')
        } else {
          console.error('删除失败，响应:', res)
          const errorMsg = res?.error || res?.data?.error || t('personal.deleteFailure')
          message.error(errorMsg)
        }
      } catch (err: any) {
        console.error('删除资产异常:', err)
        const errorMsg = err.message || err.toString() || t('ssh.unknownError')
        message.error(t('personal.deleteError', { error: errorMsg }))
      }
    }
  })
}

const handleImportAssets = async (assets: any[]) => {
  if (!assets || assets.length === 0) {
    message.warning(t('personal.importNoData'))
    return
  }

  try {
    const api = window.api as any
    let successCount = 0
    let errorCount = 0

    for (const asset of assets) {
      try {
        if (!asset.ip || !asset.username) {
          errorCount++
          continue
        }

        const cleanForm = {
          username: asset.username || '',
          password: asset.password || '',
          ip: asset.ip || '',
          label: asset.label || asset.ip,
          group_name: asset.group_name || t('personal.defaultGroup'),
          auth_type: asset.auth_type || 'password',
          keyChain: asset.keyChain,
          port: asset.port || 22,
          asset_type: asset.asset_type || 'person',
          needProxy: asset.needProxy || false,
          proxyName: asset.proxyName || ''
        }

        const result = await api.createAsset({ form: cleanForm })
        if (result && result.data && result.data.message === 'success') {
          successCount++
        } else {
          errorCount++
        }
      } catch (error) {
        console.error('Import asset error:', error)
        errorCount++
      }
    }

    if (successCount > 0) {
      message.success(t('personal.importSuccessCount', { count: successCount }))
      getAssetList()
      eventBus.emit('LocalAssetMenu')
    }

    if (errorCount > 0) {
      message.warning(t('personal.importErrorCount', { count: errorCount }))
    }
  } catch (error) {
    console.error('Batch import error:', error)
    message.error(t('personal.importError'))
  }
}

const handleExportAssets = () => {
  try {
    const allAssets: any[] = []

    const extractAssets = (nodes: AssetNode[]) => {
      nodes.forEach((node) => {
        if (node.children && node.children.length > 0) {
          extractAssets(node.children)
        } else {
          if (node.ip && node.username) {
            allAssets.push({
              username: node.username,
              password: node.password || '',
              ip: node.ip,
              label: node.label || node.title,
              group_name: node.group_name,
              auth_type: node.auth_type || 'password',
              keyChain: node.key_chain_id,
              port: node.port || 22,
              asset_type: node.asset_type || 'person',
              needProxy: node.needProxy || false,
              proxyName: node.proxyName || ''
            })
          }
        }
      })
    }

    extractAssets(assetGroups.value)

    if (allAssets.length === 0) {
      message.warning(t('personal.exportNoData'))
      return
    }

    const dataStr = JSON.stringify(allAssets, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement('a')
    link.href = url
    link.download = `chaterm-assets-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    message.success(t('personal.exportSuccess', { count: allAssets.length }))
  } catch (error) {
    console.error('Export assets error:', error)
    message.error(t('personal.exportError'))
  }
}

const handleAuthChange = (authType: string) => {
  if (authType === 'keyBased') {
    getkeyChainData()
  }
}

const getkeyChainData = () => {
  const api = window.api as any
  if (!api || !api.getKeyChainSelect) {
    console.warn('getKeyChainSelect API not available')
    keyChainOptions.value = []
    return
  }

  api
    .getKeyChainSelect()
    .then((res) => {
      if (res && res.data && res.data.keyChain) {
        keyChainOptions.value = res.data.keyChain
      } else {
        keyChainOptions.value = []
      }
    })
    .catch((error) => {
      console.error('Failed to get keychain data:', error)
      keyChainOptions.value = []
    })
}
const getProxyConfigData = async () => {
  try {
    const savedConfig = await userConfigStore.getConfig()
    if (savedConfig) {
      const savedConfigProxyConfig = savedConfig.sshProxyConfigs || []
      sshProxyConfigs.value = savedConfigProxyConfig.map((config) => ({
        key: config.name,
        label: config.name
      }))
    }
  } catch (error) {
    console.error('Failed to load config:', error)
    notification.error({
      message: t('user.loadConfigFailed'),
      description: t('user.loadConfigFailedDescription')
    })
  }
}

const getAssetGroup = () => {
  const api = window.api as any
  if (!api || !api.getAssetGroup) {
    console.warn('getAssetGroup API not available')
    // 设置默认分组，确保添加主机功能正常工作
    defaultGroups.value = ['development', 'production', 'staging', 'testing', 'database']
    return
  }

  api
    .getAssetGroup()
    .then((res) => {
      if (res && res.data && res.data.groups) {
        defaultGroups.value = res.data.groups
      } else {
        // 如果没有返回分组数据，使用默认分组
        defaultGroups.value = ['development', 'production', 'staging', 'testing', 'database']
      }
    })
    .catch((error) => {
      console.error('Failed to get asset groups:', error)
      // 出错时使用默认分组，确保添加主机功能正常工作
      defaultGroups.value = ['development', 'production', 'staging', 'testing', 'database']
    })
}

const addKeychain = () => {
  eventBus.emit('openUserTab', 'keyChainConfig')
}

const handleFormSubmit = async (data: AssetFormData) => {
  try {
    if (isEditMode.value) {
      await handleSaveAsset(data)
    } else {
      await handleCreateAsset(data)
    }
  } catch (error) {
    console.error('Form submission error:', error)
  }
}

const handleCreateAsset = async (data: AssetFormData) => {
  try {
    let groupName = data.group_name
    if (Array.isArray(groupName) && groupName.length > 0) {
      groupName = groupName[0]
    }

    const cleanForm = {
      username: data.username,
      password: data.password,
      ip: data.ip,
      label: data.label || data.ip,
      group_name: groupName,
      auth_type: data.auth_type,
      keyChain: data.keyChain,
      port: data.port,
      asset_type: data.asset_type,
      needProxy: data.needProxy,
      proxyName: data.proxyName
    }

    const api = window.api as any

    // 检查API是否可用
    if (!api || !api.createAsset) {
      console.error('createAsset API not available')
      message.error(t('personal.createError') || '创建主机失败：API不可用')
      return
    }

    console.log('[Debug] 准备创建资产:', cleanForm)
    const result = await api.createAsset({ form: cleanForm })
    console.log('[Debug] 创建资产结果:', result)

    // 检查主进程返回的格式: { success: true, data: ... } 或 { success: false, error: ... }
    if (result && result.success === true) {
      message.success(t('personal.createSuccess') || '创建主机成功')
      resetForm()
      isRightSectionVisible.value = false
      getAssetList()
      eventBus.emit('LocalAssetMenu')
    } else {
      console.error('Create asset failed, result:', result)
      const errorMessage = result?.error || 'Failed to create asset'
      throw new Error(errorMessage)
    }
  } catch (error) {
    console.error('Create asset error:', error)
    message.error(t('personal.createError'))
  }
}

const handleSaveAsset = async (data: AssetFormData) => {
  if (!editingAssetUUID.value) {
    message.error(t('personal.missingAssetId'))
    return
  }

  try {
    let groupName = data.group_name
    if (Array.isArray(groupName) && groupName.length > 0) {
      groupName = groupName[0]
    }

    const cleanForm = {
      uuid: editingAssetUUID.value,
      username: data.username,
      password: data.password,
      ip: data.ip,
      label: data.label || data.ip,
      group_name: groupName,
      auth_type: data.auth_type,
      keyChain: data.keyChain,
      port: data.port,
      asset_type: data.asset_type,
      needProxy: data.needProxy,
      proxyName: data.proxyName
    }

    const api = window.api as any

    // 检查API是否可用
    if (!api || !api.updateAsset) {
      console.error('updateAsset API not available')
      message.error(t('personal.saveError') || '保存主机失败：API不可用')
      return
    }

    console.log('[Debug] 准备更新资产:', cleanForm)
    const res = await api.updateAsset({ form: cleanForm })
    console.log('[Debug] 更新资产结果:', res)

    // 检查主进程返回的格式: { success: true, data: ... } 或 { success: false, error: ... }
    if (res && res.success === true) {
      message.success(t('personal.saveSuccess') || '保存主机成功')
      isRightSectionVisible.value = false
      getAssetList()
      eventBus.emit('LocalAssetMenu')
    } else {
      console.error('Update asset failed, result:', res)
      const errorMessage = res?.error || '保存失败'
      throw new Error(errorMessage)
    }
  } catch (e: any) {
    message.error(e.message || t('personal.saveError'))
  }
}

const getAssetList = () => {
  const api = window.api as any

  // 检查API是否可用
  if (!api || !api.getLocalAssetRoute) {
    console.warn('getLocalAssetRoute API not available, setting empty asset groups')
    assetGroups.value = []
    return
  }

  api
    .getLocalAssetRoute({ searchType: 'assetConfig', params: [] })
    .then((res) => {
      console.log('getAssetList response:', res)
      // 主进程返回格式: { success: true, data: { code: 200, data: { routers: [] } } }
      if (res && res.success && res.data && res.data.data) {
        const data = res.data.data.routers || []
        console.log('Asset groups data:', data)
        assetGroups.value = data as AssetNode[]
      } else {
        console.warn('Invalid response format:', res)
        assetGroups.value = []
      }
    })
    .catch((err) => {
      console.error('getAssetList error:', err)
      assetGroups.value = []
    })
}

onMounted(() => {
  getAssetList()
  getkeyChainData()
  getProxyConfigData()
  eventBus.on('keyChainUpdated', () => {
    getkeyChainData()
  })
  // 监听语言变更事件，重新加载资产数据
  eventBus.on('languageChanged', () => {
    console.log('Language changed in asset config, refreshing asset list...')
    getAssetList()
    eventBus.emit('LocalAssetMenu') // 通知工作空间组件也刷新
  })
})

onBeforeUnmount(() => {
  eventBus.off('keyChainUpdated')
  eventBus.off('languageChanged')
})

watch(isRightSectionVisible, (val) => {
  if (!val) {
    resetForm()
    isEditMode.value = false
    editingAssetUUID.value = null
  }
})
</script>

<style lang="less" scoped>
.asset-config-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.split-layout {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  flex: 1;
  overflow: hidden;
  justify-content: flex-start;
}

.left-section {
  flex: 1 1 auto;
  position: relative;
  transition: all 0.3s ease;
  padding: 10px;
  overflow-y: auto;
  background-color: var(--bg-color);
  width: 100%;
}

.right-section {
  flex: 0 0 30%;
  background: var(--bg-color);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 0;
  overflow: hidden;
  max-width: 30%;
  min-width: 300px;
  opacity: 1;
  visibility: visible;
}

.right-section.collapsed {
  flex: 0 0 0 !important;
  width: 0 !important;
  max-width: 0 !important;
  min-width: 0 !important;
  border-left: 0 !important;
  padding: 0;
  margin: 0;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  pointer-events: none;
}
</style>
