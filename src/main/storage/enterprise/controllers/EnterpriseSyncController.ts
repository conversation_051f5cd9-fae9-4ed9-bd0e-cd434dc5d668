/**
 * 企业资源同步系统 - 企业同步控制器
 * 功能：提供企业资源同步的API接口层，处理前端请求
 * 依赖：企业同步服务、权限服务、认证服务
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { ResourceType, Environment, EnterpriseConfigBackend, EnterpriseSyncResult, SyncStatus } from '../types/EnterpriseTypes'
import { EnterpriseSyncService } from '../services/EnterpriseSyncService'
import { EnterpriseConfigManager } from '../manager/EnterpriseConfigManager'
import { ResourceSeparationManager } from '../manager/ResourceSeparationManager'
import { UnifiedConfigInterface } from '../core/UnifiedConfigInterface'
import { AuthenticationService } from '../security/AuthenticationService'
import { PermissionService, PermissionAction } from '../security/PermissionService'

/**
 * API响应基础接口
 */
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
}

/**
 * 同步请求参数
 */
interface SyncRequest {
  type?: 'full' | 'incremental' | 'manual'
  target_backends?: string[]
  resource_filter?: {
    resource_type?: ResourceType
    environment?: Environment
    tags?: string[]
  }
  force?: boolean
}

/**
 * 后端配置请求参数
 */
interface BackendConfigRequest {
  type: string
  name: string
  priority: number
  enabled: boolean
  config: any
}

/**
 * 企业资源同步控制器
 * 提供RESTful API接口
 */
export class EnterpriseSyncController {
  private syncService: EnterpriseSyncService
  private configManager: EnterpriseConfigManager
  private separationManager: ResourceSeparationManager
  private unifiedInterface: UnifiedConfigInterface
  private authService: AuthenticationService
  private permissionService: PermissionService

  constructor() {
    this.syncService = EnterpriseSyncService.getInstance()
    this.configManager = EnterpriseConfigManager.getInstance()
    this.separationManager = ResourceSeparationManager.getInstance()
    this.unifiedInterface = UnifiedConfigInterface.getInstance()
    this.authService = AuthenticationService.getInstance()
    this.permissionService = PermissionService.getInstance()
  }

  /**
   * 启动企业资源同步
   * POST /api/enterprise/sync/start
   */
  async startSync(request: { token: string; body: SyncRequest }): Promise<ApiResponse<{ task_id: string }>> {
    try {
      const taskId = await this.syncService.startSync(request.token, request.body)

      return {
        success: true,
        data: { task_id: taskId },
        message: '同步任务已启动',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 取消同步任务
   * POST /api/enterprise/sync/cancel/:taskId
   */
  async cancelSync(request: { token: string; taskId: string }): Promise<ApiResponse<{ cancelled: boolean }>> {
    try {
      const cancelled = await this.syncService.cancelSync(request.token, request.taskId)

      return {
        success: true,
        data: { cancelled },
        message: cancelled ? '任务已取消' : '任务无法取消',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取同步任务状态
   * GET /api/enterprise/sync/status/:taskId
   */
  async getTaskStatus(request: { token: string; taskId: string }): Promise<ApiResponse> {
    try {
      const task = await this.syncService.getTaskStatus(request.token, request.taskId)

      if (!task) {
        return {
          success: false,
          error: '任务不存在',
          timestamp: new Date().toISOString()
        }
      }

      return {
        success: true,
        data: task,
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取用户同步任务列表
   * GET /api/enterprise/sync/tasks
   */
  async getUserTasks(request: {
    token: string
    query?: {
      status?: string
      type?: string
      limit?: string
      offset?: string
    }
  }): Promise<ApiResponse> {
    try {
      const filter = {
        status: request.query?.status as any,
        type: request.query?.type as any,
        limit: request.query?.limit ? parseInt(request.query.limit) : undefined,
        offset: request.query?.offset ? parseInt(request.query.offset) : undefined
      }

      const result = await this.syncService.getUserTasks(request.token, filter)

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取同步统计信息
   * GET /api/enterprise/sync/statistics
   */
  async getSyncStatistics(request: { token: string }): Promise<ApiResponse> {
    try {
      const statistics = await this.syncService.getSyncStatistics(request.token)

      return {
        success: true,
        data: statistics,
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 启用自动同步
   * POST /api/enterprise/sync/auto/enable
   */
  async enableAutoSync(request: { token: string; body: { interval_ms?: number } }): Promise<ApiResponse> {
    try {
      const enabled = await this.syncService.enableAutoSync(request.token, request.body.interval_ms)

      return {
        success: true,
        data: { enabled },
        message: '自动同步已启用',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 禁用自动同步
   * POST /api/enterprise/sync/auto/disable
   */
  async disableAutoSync(request: { token: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'enterprise_sync.auto', PermissionAction.ADMIN)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const disabled = this.syncService.disableAutoSync()

      return {
        success: true,
        data: { disabled },
        message: '自动同步已禁用',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取自动同步状态
   * GET /api/enterprise/sync/auto/status
   */
  async getAutoSyncStatus(request: { token: string }): Promise<ApiResponse> {
    try {
      // 验证用户令牌
      const user = await this.authService.validateToken(request.token)
      if (!user) {
        return {
          success: false,
          error: '无效的用户令牌',
          timestamp: new Date().toISOString()
        }
      }

      const isActive = this.syncService.isAutoSyncActive()

      return {
        success: true,
        data: { auto_sync_enabled: isActive },
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 添加配置后端
   * POST /api/enterprise/config/backends
   */
  async addBackend(request: { token: string; body: BackendConfigRequest }): Promise<ApiResponse<{ backend_id: string }>> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.WRITE)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const backendId = await this.configManager.addBackend(request.body as any)

      return {
        success: true,
        data: { backend_id: backendId },
        message: '后端配置已添加',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 更新配置后端
   * PUT /api/enterprise/config/backends/:backendId
   */
  async updateBackend(request: { token: string; backendId: string; body: Partial<BackendConfigRequest> }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.WRITE)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const updated = await this.configManager.updateBackend(request.backendId, request.body as any)

      return {
        success: true,
        data: { updated },
        message: updated ? '后端配置已更新' : '后端配置更新失败',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 删除配置后端
   * DELETE /api/enterprise/config/backends/:backendId
   */
  async removeBackend(request: { token: string; backendId: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.DELETE)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const removed = await this.configManager.removeBackend(request.backendId)

      return {
        success: true,
        data: { removed },
        message: removed ? '后端配置已删除' : '后端配置删除失败',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取所有配置后端
   * GET /api/enterprise/config/backends
   */
  async getAllBackends(request: { token: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.READ)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const backends = await this.configManager.getAllBackends()

      return {
        success: true,
        data: { backends },
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 测试后端连接
   * POST /api/enterprise/config/backends/:backendId/test
   */
  async testBackendConnection(request: { token: string; backendId: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.EXECUTE)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const result = await this.configManager.testBackendConnection(request.backendId)

      return {
        success: true,
        data: result,
        message: result.healthy ? '连接测试成功' : '连接测试失败',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取故障转移统计
   * GET /api/enterprise/config/failover/stats
   */
  async getFailoverStats(request: { token: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.READ)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const stats = this.unifiedInterface.getFailoverStats()

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 触发后端恢复
   * POST /api/enterprise/config/backends/:backendId/recover
   */
  async triggerBackendRecovery(request: { token: string; backendId: string }): Promise<ApiResponse> {
    try {
      // 验证权限
      const permissionCheck = await this.permissionService.checkPermission(request.token, 'config_backend', PermissionAction.ADMIN)

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: `权限不足: ${permissionCheck.reason}`,
          timestamp: new Date().toISOString()
        }
      }

      const recovered = await this.unifiedInterface.triggerBackendRecovery(request.backendId)

      return {
        success: true,
        data: { recovered },
        message: recovered ? '后端恢复成功' : '后端恢复失败',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取资源分离统计
   * GET /api/enterprise/separation/stats
   */
  async getSeparationStats(request: { token: string }): Promise<ApiResponse> {
    try {
      // 验证用户令牌
      const user = await this.authService.validateToken(request.token)
      if (!user) {
        return {
          success: false,
          error: '无效的用户令牌',
          timestamp: new Date().toISOString()
        }
      }

      const stats = this.separationManager.getSyncStats()

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 执行完整同步（个人+企业）
   * POST /api/enterprise/sync/full
   */
  async performFullSync(request: { token: string }): Promise<ApiResponse> {
    try {
      const result = await this.separationManager.performFullSync()

      return {
        success: true,
        data: result,
        message: result.overall_success ? '完整同步成功' : '完整同步部分失败',
        timestamp: new Date().toISOString()
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }
    }
  }
}
