/**
 * 增强命令历史服务
 * 功能：命令分类、标签管理、智能搜索
 * 作者：AI Assistant
 * 修改时间：2025-01-09
 */

import Database from 'better-sqlite3'
import { initDatabase, getCurrentUserId } from './connection'
import { autoCompleteDatabaseService } from './autocomplete.service'

// 命令分类枚举
export enum CommandCategory {
  FILE_OPERATION = 'file_operation',
  NETWORK = 'network',
  SYSTEM = 'system',
  DEVELOPMENT = 'development',
  DATABASE = 'database',
  DOCKER = 'docker',
  GIT = 'git',
  PROCESS = 'process',
  SEARCH = 'search',
  ARCHIVE = 'archive',
  OTHER = 'other'
}

// 命令历史项接口
export interface EnhancedCommandItem {
  id: number
  command: string
  category: CommandCategory
  tags: string[]
  description?: string
  ip: string
  count: number
  lastUsed: Date
  createdAt: Date
  isFavorite: boolean
}

// 搜索选项接口
export interface SearchOptions {
  query?: string
  category?: CommandCategory
  tags?: string[]
  ip?: string
  dateRange?: {
    start: Date
    end: Date
  }
  onlyFavorites?: boolean
  limit?: number
  offset?: number
}

// 统计信息接口
export interface CommandStats {
  totalCommands: number
  categoryCounts: Record<CommandCategory, number>
  topTags: Array<{ tag: string; count: number }>
  mostUsedCommands: Array<{ command: string; count: number }>
  recentActivity: Array<{ date: string; count: number }>
}

export class EnhancedCommandHistoryService {
  private static instances: Map<number, EnhancedCommandHistoryService> = new Map()
  private db: Database.Database
  private userId: number
  private autocompleteService: autoCompleteDatabaseService

  // 命令分类规则映射
  private categoryRules: Map<RegExp, CommandCategory> = new Map([
    // 文件操作
    [/^(ls|ll|la|dir|find|locate|which|whereis)\b/, CommandCategory.FILE_OPERATION],
    [/^(cp|mv|rm|mkdir|rmdir|touch|chmod|chown|ln)\b/, CommandCategory.FILE_OPERATION],
    [/^(cat|less|more|head|tail|grep|awk|sed|sort|uniq|wc)\b/, CommandCategory.FILE_OPERATION],

    // 网络相关
    [/^(ping|curl|wget|ssh|scp|rsync|netstat|ss|nmap)\b/, CommandCategory.NETWORK],
    [/^(telnet|ftp|sftp|nc|dig|nslookup|host|traceroute)\b/, CommandCategory.NETWORK],

    // 系统管理
    [/^(ps|top|htop|kill|killall|jobs|bg|fg|nohup)\b/, CommandCategory.PROCESS],
    [/^(df|du|free|mount|umount|fdisk|lsblk|iostat)\b/, CommandCategory.SYSTEM],
    [/^(systemctl|service|crontab|at|uptime|uname|whoami)\b/, CommandCategory.SYSTEM],

    // 开发相关
    [/^(npm|yarn|node|python|pip|java|javac|gcc|make)\b/, CommandCategory.DEVELOPMENT],
    [/^(vim|nano|emacs|code|subl)\b/, CommandCategory.DEVELOPMENT],

    // Git相关
    [/^git\b/, CommandCategory.GIT],

    // Docker相关
    [/^(docker|docker-compose)\b/, CommandCategory.DOCKER],

    // 数据库相关
    [/^(mysql|psql|sqlite3|mongo|redis-cli)\b/, CommandCategory.DATABASE],

    // 搜索相关
    [/^(grep|egrep|fgrep|rg|ag|ack)\b/, CommandCategory.SEARCH],

    // 压缩解压
    [/^(tar|zip|unzip|gzip|gunzip|7z)\b/, CommandCategory.ARCHIVE]
  ])

  private constructor(db: Database.Database, userId: number, autocompleteService: autoCompleteDatabaseService) {
    this.db = db
    this.userId = userId
    this.autocompleteService = autocompleteService
    this.initEnhancedTables()
  }

  /**
   * 初始化增强功能所需的数据表
   */
  private initEnhancedTables(): void {
    // 创建命令标签表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS command_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        command_id INTEGER NOT NULL,
        tag VARCHAR(50) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (command_id) REFERENCES linux_commands_history(id) ON DELETE CASCADE,
        UNIQUE(command_id, tag)
      )
    `)

    // 创建命令分类表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS command_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        command_id INTEGER NOT NULL,
        category VARCHAR(50) NOT NULL,
        auto_detected BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (command_id) REFERENCES linux_commands_history(id) ON DELETE CASCADE,
        UNIQUE(command_id)
      )
    `)

    // 创建收藏命令表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS command_favorites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        command_id INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (command_id) REFERENCES linux_commands_history(id) ON DELETE CASCADE,
        UNIQUE(command_id)
      )
    `)

    // 创建命令描述表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS command_descriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        command_id INTEGER NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (command_id) REFERENCES linux_commands_history(id) ON DELETE CASCADE,
        UNIQUE(command_id)
      )
    `)

    // 创建索引以提高查询性能
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_command_tags_tag ON command_tags(tag);
      CREATE INDEX IF NOT EXISTS idx_command_categories_category ON command_categories(category);
      CREATE INDEX IF NOT EXISTS idx_command_descriptions_command_id ON command_descriptions(command_id);
    `)
  }

  /**
   * 获取服务实例（单例模式）
   */
  public static async getInstance(userId?: number): Promise<EnhancedCommandHistoryService> {
    const targetUserId = userId || getCurrentUserId()
    if (!targetUserId) {
      throw new Error('User ID is required for EnhancedCommandHistoryService')
    }

    if (!EnhancedCommandHistoryService.instances.has(targetUserId)) {
      const db = await initDatabase(targetUserId)
      const autocompleteService = await autoCompleteDatabaseService.getInstance(targetUserId)
      const instance = new EnhancedCommandHistoryService(db, targetUserId, autocompleteService)
      EnhancedCommandHistoryService.instances.set(targetUserId, instance)
    }
    return EnhancedCommandHistoryService.instances.get(targetUserId)!
  }

  /**
   * 自动检测命令分类
   */
  private detectCommandCategory(command: string): CommandCategory {
    const normalizedCommand = command.toLowerCase().trim()

    for (const [pattern, category] of this.categoryRules) {
      if (pattern.test(normalizedCommand)) {
        return category
      }
    }

    return CommandCategory.OTHER
  }

  /**
   * 添加命令到历史记录（增强版）
   */
  public async addCommand(
    command: string,
    ip: string,
    options?: {
      category?: CommandCategory
      tags?: string[]
      description?: string
      isFavorite?: boolean
    }
  ): Promise<void> {
    // 首先使用原有服务添加命令
    this.autocompleteService.insertCommand(command, ip)

    // 获取命令ID
    const commandRecord = this.db.prepare('SELECT id FROM linux_commands_history WHERE command = ? AND ip = ?').get(command, ip) as
      | { id: number }
      | undefined

    if (!commandRecord) {
      throw new Error('Failed to insert command')
    }

    const commandId = commandRecord.id

    // 添加分类
    const category = options?.category || this.detectCommandCategory(command)
    this.db
      .prepare(
        `
      INSERT OR REPLACE INTO command_categories (command_id, category, auto_detected)
      VALUES (?, ?, ?)
    `
      )
      .run(commandId, category, !options?.category)

    // 添加标签
    if (options?.tags && options.tags.length > 0) {
      const tagStmt = this.db.prepare('INSERT OR IGNORE INTO command_tags (command_id, tag) VALUES (?, ?)')
      for (const tag of options.tags) {
        tagStmt.run(commandId, tag.toLowerCase().trim())
      }
    }

    // 添加描述
    if (options?.description) {
      this.db
        .prepare(
          `
        INSERT OR REPLACE INTO command_descriptions (command_id, description, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
      `
        )
        .run(commandId, options.description)
    }

    // 添加到收藏
    if (options?.isFavorite) {
      this.db.prepare('INSERT OR IGNORE INTO command_favorites (command_id) VALUES (?)').run(commandId)
    }
  }

  /**
   * 智能搜索命令历史
   */
  public searchCommands(options: SearchOptions = {}): EnhancedCommandItem[] {
    let query = `
      SELECT DISTINCT
        h.id,
        h.command,
        h.ip,
        h.count,
        h.update_time as lastUsed,
        h.create_time as createdAt,
        COALESCE(c.category, 'other') as category,
        COALESCE(d.description, '') as description,
        CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as isFavorite
      FROM linux_commands_history h
      LEFT JOIN command_categories c ON h.id = c.command_id
      LEFT JOIN command_descriptions d ON h.id = d.command_id
      LEFT JOIN command_favorites f ON h.id = f.command_id
    `

    const conditions: string[] = []
    const params: any[] = []

    // 文本搜索
    if (options.query) {
      conditions.push('(h.command LIKE ? OR d.description LIKE ?)')
      const searchPattern = `%${options.query}%`
      params.push(searchPattern, searchPattern)
    }

    // 分类筛选
    if (options.category) {
      conditions.push('c.category = ?')
      params.push(options.category)
    }

    // IP筛选
    if (options.ip) {
      conditions.push('h.ip = ?')
      params.push(options.ip)
    }

    // 只显示收藏
    if (options.onlyFavorites) {
      conditions.push('f.id IS NOT NULL')
    }

    // 日期范围筛选
    if (options.dateRange) {
      conditions.push('h.update_time BETWEEN ? AND ?')
      params.push(options.dateRange.start.toISOString(), options.dateRange.end.toISOString())
    }

    // 标签筛选
    if (options.tags && options.tags.length > 0) {
      const tagConditions = options.tags.map(() => '?').join(',')
      query += ` INNER JOIN command_tags t ON h.id = t.command_id AND t.tag IN (${tagConditions})`
      params.push(...options.tags)
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ')
    }

    query += ' ORDER BY h.count DESC, h.update_time DESC'

    if (options.limit) {
      query += ' LIMIT ?'
      params.push(options.limit)

      if (options.offset) {
        query += ' OFFSET ?'
        params.push(options.offset)
      }
    }

    const results = this.db.prepare(query).all(...params) as any[]

    return results.map((row) => {
      // 获取标签
      const tags = this.db
        .prepare('SELECT tag FROM command_tags WHERE command_id = ?')
        .all(row.id)
        .map((t: any) => t.tag)

      return {
        id: row.id,
        command: row.command,
        category: row.category as CommandCategory,
        tags,
        description: row.description || undefined,
        ip: row.ip,
        count: row.count,
        lastUsed: new Date(row.lastUsed),
        createdAt: new Date(row.createdAt),
        isFavorite: Boolean(row.isFavorite)
      }
    })
  }

  /**
   * 获取命令统计信息
   */
  public getCommandStats(): CommandStats {
    // 总命令数
    const totalCommands = this.db.prepare('SELECT COUNT(*) as count FROM linux_commands_history').get() as { count: number }

    // 分类统计
    const categoryStats = this.db
      .prepare(
        `
      SELECT 
        COALESCE(c.category, 'other') as category,
        COUNT(*) as count
      FROM linux_commands_history h
      LEFT JOIN command_categories c ON h.id = c.command_id
      GROUP BY category
    `
      )
      .all() as Array<{ category: string; count: number }>

    const categoryCounts = Object.values(CommandCategory).reduce(
      (acc, cat) => {
        acc[cat] = 0
        return acc
      },
      {} as Record<CommandCategory, number>
    )

    categoryStats.forEach((stat) => {
      categoryCounts[stat.category as CommandCategory] = stat.count
    })

    // 热门标签
    const topTags = this.db
      .prepare(
        `
      SELECT tag, COUNT(*) as count
      FROM command_tags
      GROUP BY tag
      ORDER BY count DESC
      LIMIT 10
    `
      )
      .all() as Array<{ tag: string; count: number }>

    // 最常用命令
    const mostUsedCommands = this.db
      .prepare(
        `
      SELECT command, count
      FROM linux_commands_history
      ORDER BY count DESC
      LIMIT 10
    `
      )
      .all() as Array<{ command: string; count: number }>

    // 最近活动（按天统计）
    const recentActivity = this.db
      .prepare(
        `
      SELECT 
        DATE(update_time) as date,
        COUNT(*) as count
      FROM linux_commands_history
      WHERE update_time >= DATE('now', '-30 days')
      GROUP BY DATE(update_time)
      ORDER BY date DESC
    `
      )
      .all() as Array<{ date: string; count: number }>

    return {
      totalCommands: totalCommands.count,
      categoryCounts,
      topTags,
      mostUsedCommands,
      recentActivity
    }
  }

  /**
   * 切换命令收藏状态
   */
  public toggleFavorite(commandId: number): boolean {
    const existing = this.db.prepare('SELECT id FROM command_favorites WHERE command_id = ?').get(commandId)

    if (existing) {
      this.db.prepare('DELETE FROM command_favorites WHERE command_id = ?').run(commandId)
      return false
    } else {
      this.db.prepare('INSERT INTO command_favorites (command_id) VALUES (?)').run(commandId)
      return true
    }
  }

  /**
   * 为命令添加标签
   */
  public addTag(commandId: number, tag: string): void {
    this.db.prepare('INSERT OR IGNORE INTO command_tags (command_id, tag) VALUES (?, ?)').run(commandId, tag.toLowerCase().trim())
  }

  /**
   * 移除命令标签
   */
  public removeTag(commandId: number, tag: string): void {
    this.db.prepare('DELETE FROM command_tags WHERE command_id = ? AND tag = ?').run(commandId, tag.toLowerCase().trim())
  }

  /**
   * 更新命令描述
   */
  public updateDescription(commandId: number, description: string): void {
    this.db
      .prepare(
        `
      INSERT OR REPLACE INTO command_descriptions (command_id, description, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `
      )
      .run(commandId, description)
  }

  /**
   * 更新命令分类
   */
  public updateCategory(commandId: number, category: CommandCategory): void {
    this.db
      .prepare(
        `
      INSERT OR REPLACE INTO command_categories (command_id, category, auto_detected)
      VALUES (?, ?, 0)
    `
      )
      .run(commandId, category)
  }

  /**
   * 获取所有可用标签
   */
  public getAllTags(): string[] {
    const tags = this.db.prepare('SELECT DISTINCT tag FROM command_tags ORDER BY tag').all() as Array<{ tag: string }>

    return tags.map((t) => t.tag)
  }

  /**
   * 删除命令（同时删除相关的增强数据）
   */
  public deleteCommand(commandId: number): void {
    this.db.transaction(() => {
      // 删除相关的增强数据（外键约束会自动处理）
      this.db.prepare('DELETE FROM linux_commands_history WHERE id = ?').run(commandId)
    })()
  }

  /**
   * 批量导入命令历史
   */
  public batchImportCommands(
    commands: Array<{
      command: string
      ip: string
      category?: CommandCategory
      tags?: string[]
      description?: string
    }>
  ): void {
    this.db.transaction(() => {
      for (const cmd of commands) {
        this.addCommand(cmd.command, cmd.ip, {
          category: cmd.category,
          tags: cmd.tags,
          description: cmd.description
        })
      }
    })()
  }

  /**
   * 导出命令历史
   */
  public exportCommands(): EnhancedCommandItem[] {
    return this.searchCommands({ limit: 10000 })
  }
}
