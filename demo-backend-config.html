<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业资源管理 - 后端配置演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1890ff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .feature-section {
            margin-bottom: 40px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            overflow: hidden;
        }
        .feature-header {
            background: #fafafa;
            padding: 15px 20px;
            border-bottom: 1px solid #e8e8e8;
            font-weight: 600;
            color: #333;
        }
        .feature-content {
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .checkbox {
            margin: 0;
        }
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
        .row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .col {
            flex: 1;
        }
        .auth-section {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .divider {
            border: none;
            border-top: 1px solid #e8e8e8;
            margin: 20px 0;
        }
        .highlight {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 15px;
        }
        .highlight-icon {
            color: #fa8c16;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>企业资源管理 - 后端配置演示</h1>
            <p>展示数据库初始化和GitHub认证配置的新功能</p>
        </div>
        
        <div class="content">
            <!-- 数据库配置演示 -->
            <div class="feature-section">
                <div class="feature-header">
                    📊 数据库后端配置 - 新增初始化选项
                </div>
                <div class="feature-content">
                    <div class="highlight">
                        <span class="highlight-icon">✨</span>
                        <strong>新功能：</strong> 添加了数据库初始化选项，适用于新部署的数据库环境
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">后端名称</label>
                                <input type="text" class="form-input" value="主数据库" placeholder="请输入后端名称">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">后端类型</label>
                                <select class="form-select">
                                    <option value="database" selected>数据库</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">数据库类型</label>
                                <select class="form-select">
                                    <option value="postgresql" selected>PostgreSQL</option>
                                    <option value="mysql">MySQL</option>
                                </select>
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">表名</label>
                                <input type="text" class="form-input" value="host_configurations" placeholder="host_configurations">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">主机地址</label>
                                <input type="text" class="form-input" value="***************" placeholder="***************">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">端口</label>
                                <input type="number" class="form-input" value="5432" placeholder="5432">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">数据库名</label>
                                <input type="text" class="form-input" value="postgres" placeholder="postgres">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" value="postgres" placeholder="postgres">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-input" placeholder="请输入数据库密码">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">SSL模式</label>
                                <select class="form-select">
                                    <option value="disable" selected>禁用</option>
                                    <option value="prefer">首选</option>
                                    <option value="require">必需</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 新增的数据库初始化选项 -->
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="init-db" class="checkbox" checked>
                            <label for="init-db" class="form-label" style="margin-bottom: 0;">初始化数据库</label>
                        </div>
                        <div class="help-text">适用于新部署的数据库，将创建所有必要的表结构和初始数据</div>
                    </div>
                </div>
            </div>
            
            <!-- Git配置演示 -->
            <div class="feature-section">
                <div class="feature-header">
                    🔗 Git仓库配置 - 新增认证选项
                </div>
                <div class="feature-content">
                    <div class="highlight">
                        <span class="highlight-icon">🔐</span>
                        <strong>新功能：</strong> 添加了完整的Git认证配置，支持访问令牌、用户名密码和SSH密钥三种认证方式
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">后端名称</label>
                                <input type="text" class="form-input" value="配置仓库" placeholder="请输入后端名称">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">后端类型</label>
                                <select class="form-select">
                                    <option value="git" selected>Git仓库</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">仓库地址</label>
                                <input type="text" class="form-input" value="**************:company/config.git" placeholder="**************:company/config.git">
                            </div>
                        </div>
                        <div class="col">
                            <div class="form-group">
                                <label class="form-label">分支</label>
                                <input type="text" class="form-input" value="main" placeholder="main">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">文件路径</label>
                        <input type="text" class="form-input" value="hosts.yaml" placeholder="hosts.yaml">
                    </div>
                    
                    <hr class="divider">
                    
                    <!-- 认证配置部分 -->
                    <h4 style="margin-bottom: 15px; color: #333;">认证配置</h4>
                    
                    <div class="form-group">
                        <label class="form-label">认证方式</label>
                        <select class="form-select" id="auth-type" onchange="toggleAuthFields()">
                            <option value="token" selected>访问令牌</option>
                            <option value="username_password">用户名密码</option>
                            <option value="ssh_key">SSH密钥</option>
                        </select>
                    </div>
                    
                    <!-- 访问令牌认证 -->
                    <div id="token-auth" class="auth-section">
                        <div class="form-group">
                            <label class="form-label">访问令牌</label>
                            <input type="password" class="form-input" placeholder="ghp_xxxxxxxxxxxxxxxxxxxx">
                            <div class="help-text">GitHub Personal Access Token 或其他Git服务的访问令牌</div>
                        </div>
                    </div>
                    
                    <!-- 用户名密码认证 -->
                    <div id="password-auth" class="auth-section" style="display: none;">
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label class="form-label">用户名</label>
                                    <input type="text" class="form-input" placeholder="请输入Git用户名">
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label class="form-label">密码</label>
                                    <input type="password" class="form-input" placeholder="请输入Git密码">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SSH密钥认证 -->
                    <div id="ssh-auth" class="auth-section" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">SSH私钥路径</label>
                            <input type="text" class="form-input" placeholder="~/.ssh/id_rsa">
                            <div class="help-text">SSH私钥文件的完整路径</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">SSH密钥密码（可选）</label>
                            <input type="password" class="form-input" placeholder="如果SSH密钥有密码保护，请输入">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleAuthFields() {
            const authType = document.getElementById('auth-type').value;
            const tokenAuth = document.getElementById('token-auth');
            const passwordAuth = document.getElementById('password-auth');
            const sshAuth = document.getElementById('ssh-auth');
            
            // 隐藏所有认证区域
            tokenAuth.style.display = 'none';
            passwordAuth.style.display = 'none';
            sshAuth.style.display = 'none';
            
            // 显示选中的认证区域
            switch(authType) {
                case 'token':
                    tokenAuth.style.display = 'block';
                    break;
                case 'username_password':
                    passwordAuth.style.display = 'block';
                    break;
                case 'ssh_key':
                    sshAuth.style.display = 'block';
                    break;
            }
        }
    </script>
</body>
</html>
