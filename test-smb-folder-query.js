/**
 * SMB 文件夹查询测试脚本
 * 功能：专门测试SMB服务器的文件夹查询和目录结构
 * 依赖：@marsaud/smb2、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

// 由于这是一个JavaScript文件测试TypeScript模块，我们需要使用编译后的版本
// 或者直接使用ts-node来运行
const { SMBAdapter } = require('./src/main/storage/multi_sync/adapters/SMBAdapter.ts')
const fs = require('fs')
const path = require('path')

// SMB配置信息
const smbConfig = {
  host: '************',
  share: 'it部',
  username: '<EMAIL>',
  password: 'RSgz@lbq0925!',
  domain: 'brbiotech.com',
  port: 445
}

// 不同的认证配置变体
const authVariants = [
  {
    name: '完整邮箱格式 + 域',
    username: '<EMAIL>',
    domain: 'brbiotech.com'
  },
  {
    name: '用户名 + 域',
    username: 'buqiu.li',
    domain: 'brbiotech.com'
  },
  {
    name: '域\\用户名格式',
    username: 'brbiotech\\buqiu.li',
    domain: ''
  },
  {
    name: '仅用户名',
    username: 'buqiu.li',
    domain: ''
  }
]

/**
 * 安全日志输出（隐藏密码）
 * @param {string} message 日志消息
 * @param {object} data 数据对象
 */
function safeLog(message, data = null) {
  if (data && typeof data === 'object') {
    const safeData = { ...data }
    if (safeData.password) safeData.password = '***'
    console.log(message, safeData)
  } else {
    console.log(message, data)
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化的文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化日期时间
 * @param {Date} date 日期对象
 * @returns {string} 格式化的日期时间
 */
function formatDateTime(date) {
  if (!date) return 'N/A'
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 显示目录树结构
 * @param {Array} files 文件列表
 * @param {string} prefix 前缀字符
 * @param {number} maxDepth 最大深度
 */
function displayDirectoryTree(files, prefix = '', maxDepth = 3) {
  if (maxDepth <= 0) return

  files.forEach((file, index) => {
    const isLast = index === files.length - 1
    const connector = isLast ? '└── ' : '├── '
    const icon = file.isDirectory ? '📁' : '📄'
    const size = file.isDirectory ? '' : ` (${formatFileSize(file.size)})`
    const modified = formatDateTime(new Date(file.lastModified))

    console.log(`${prefix}${connector}${icon} ${file.name}${size}`)
    console.log(`${prefix}${isLast ? '    ' : '│   '}   📅 ${modified}`)

    if (file.isDirectory && file.children && file.children.length > 0) {
      const newPrefix = prefix + (isLast ? '    ' : '│   ')
      displayDirectoryTree(file.children, newPrefix, maxDepth - 1)
    }
  })
}

/**
 * 递归查询目录结构
 * @param {SMBAdapter} adapter SMB适配器实例
 * @param {string} dirPath 目录路径
 * @param {number} maxDepth 最大递归深度
 * @returns {Promise<Array>} 文件列表
 */
async function queryDirectoryRecursive(adapter, dirPath, maxDepth = 2) {
  if (maxDepth <= 0) return []

  try {
    console.log(`🔍 查询目录: ${dirPath}`)
    const files = await adapter.listFiles(dirPath)

    // 为每个子目录递归查询
    for (const file of files) {
      if (file.isDirectory && maxDepth > 1) {
        const subDirPath = path.posix.join(dirPath, file.name)
        try {
          file.children = await queryDirectoryRecursive(adapter, subDirPath, maxDepth - 1)
        } catch (error) {
          console.log(`⚠️ 无法访问子目录 ${subDirPath}: ${error.message}`)
          file.children = []
        }
      }
    }

    return files
  } catch (error) {
    console.log(`❌ 查询目录失败 ${dirPath}: ${error.message}`)
    return []
  }
}

/**
 * 测试单个认证配置的文件夹查询
 * @param {object} variant 认证配置变体
 * @param {number} index 索引
 * @returns {Promise<object>} 测试结果
 */
async function testFolderQueryWithAuth(variant, index) {
  console.log(`\n${index + 1}️⃣ 测试认证配置: ${variant.name}`)
  console.log('='.repeat(50))

  const config = {
    ...smbConfig,
    username: variant.username,
    domain: variant.domain
  }

  safeLog('🔧 使用配置:', config)

  let adapter = null
  try {
    adapter = new SMBAdapter()

    // 尝试连接
    console.log('🔗 建立SMB连接...')
    const connectResult = await adapter.connect(config)

    if (!connectResult) {
      console.log('❌ 连接失败')
      return { success: false, variant, error: '连接失败' }
    }

    console.log('✅ 连接成功！')

    // 测试根目录查询
    console.log('\n📂 查询根目录内容...')
    try {
      const rootFiles = await adapter.listFiles('/')
      console.log(`✅ 根目录查询成功，找到 ${rootFiles.length} 个项目:`)

      rootFiles.forEach((file) => {
        const icon = file.isDirectory ? '📁' : '📄'
        const size = file.isDirectory ? '' : ` (${formatFileSize(file.size)})`
        console.log(`   ${icon} ${file.name}${size}`)
      })

      // 查询特定目录的详细结构
      console.log('\n🔍 查询详细目录结构...')
      const detailedStructure = await queryDirectoryRecursive(adapter, '/', 3)

      console.log('\n🌳 目录树结构:')
      displayDirectoryTree(detailedStructure)

      // 统计信息
      const totalFiles = detailedStructure.filter((f) => !f.isDirectory).length
      const totalDirs = detailedStructure.filter((f) => f.isDirectory).length
      const totalSize = detailedStructure.filter((f) => !f.isDirectory).reduce((sum, f) => sum + (f.size || 0), 0)

      console.log('\n📊 统计信息:')
      console.log(`   📁 目录数量: ${totalDirs}`)
      console.log(`   📄 文件数量: ${totalFiles}`)
      console.log(`   💾 总大小: ${formatFileSize(totalSize)}`)

      return {
        success: true,
        variant,
        stats: { totalFiles, totalDirs, totalSize },
        structure: detailedStructure
      }
    } catch (error) {
      console.log(`❌ 文件夹查询失败: ${error.message}`)
      return { success: false, variant, error: error.message }
    }
  } catch (error) {
    console.log(`❌ 连接异常: ${error.message}`)
    return { success: false, variant, error: error.message }
  } finally {
    if (adapter) {
      try {
        await adapter.disconnect()
        console.log('🔌 连接已断开')
      } catch (error) {
        console.log(`⚠️ 断开连接时出错: ${error.message}`)
      }
    }
  }
}

/**
 * 测试特定路径的文件夹查询
 * @param {SMBAdapter} adapter SMB适配器实例
 * @param {Array} testPaths 测试路径列表
 */
async function testSpecificPaths(adapter, testPaths) {
  console.log('\n🎯 测试特定路径查询...')

  for (const testPath of testPaths) {
    console.log(`\n📍 测试路径: ${testPath}`)
    try {
      const files = await adapter.listFiles(testPath)
      console.log(`✅ 查询成功，找到 ${files.length} 个项目:`)

      files.slice(0, 10).forEach((file) => {
        // 只显示前10个
        const icon = file.isDirectory ? '📁' : '📄'
        const size = file.isDirectory ? '' : ` (${formatFileSize(file.size)})`
        const modified = formatDateTime(new Date(file.lastModified))
        console.log(`   ${icon} ${file.name}${size} - ${modified}`)
      })

      if (files.length > 10) {
        console.log(`   ... 还有 ${files.length - 10} 个项目`)
      }
    } catch (error) {
      console.log(`❌ 查询失败: ${error.message}`)
    }
  }
}

/**
 * 主测试函数
 */
async function runFolderQueryTests() {
  console.log('🚀 开始SMB文件夹查询测试')
  console.log('目标服务器: ************')
  console.log('目标共享: it部')
  console.log('='.repeat(60))

  const results = []

  // 测试不同的认证配置
  for (let i = 0; i < authVariants.length; i++) {
    const result = await testFolderQueryWithAuth(authVariants[i], i)
    results.push(result)

    // 如果找到成功的配置，进行更详细的测试
    if (result.success) {
      console.log('\n🎉 找到有效的认证配置！进行详细测试...')

      // 重新连接进行详细测试
      const adapter = new SMBAdapter()
      try {
        const config = {
          ...smbConfig,
          username: result.variant.username,
          domain: result.variant.domain
        }

        await adapter.connect(config)

        // 测试常见路径
        const commonPaths = ['/', '/configs', '/documents', '/shared', '/public', '/temp']

        await testSpecificPaths(adapter, commonPaths)
      } catch (error) {
        console.log(`⚠️ 详细测试失败: ${error.message}`)
      } finally {
        await adapter.disconnect()
      }

      break // 找到成功的配置后停止测试其他配置
    }

    // 在测试之间稍作延迟
    if (i < authVariants.length - 1) {
      console.log('\n⏳ 等待2秒后继续下一个测试...')
      await new Promise((resolve) => setTimeout(resolve, 2000))
    }
  }

  // 输出测试总结
  console.log('\n' + '='.repeat(60))
  console.log('📊 SMB文件夹查询测试总结:')

  const successfulResults = results.filter((r) => r.success)
  const failedResults = results.filter((r) => !r.success)

  console.log(`✅ 成功: ${successfulResults.length}/${results.length}`)
  console.log(`❌ 失败: ${failedResults.length}/${results.length}`)

  if (successfulResults.length > 0) {
    console.log('\n🎯 成功的配置:')
    successfulResults.forEach((result) => {
      console.log(`   ✅ ${result.variant.name}`)
      if (result.stats) {
        console.log(`      📁 目录: ${result.stats.totalDirs}, 📄 文件: ${result.stats.totalFiles}`)
        console.log(`      💾 总大小: ${formatFileSize(result.stats.totalSize)}`)
      }
    })

    console.log('\n💡 建议:')
    console.log('   - SMB连接功能正常，可以进行文件操作')
    console.log('   - 建议使用成功的认证配置进行后续开发')
    console.log('   - 可以开始实现文件上传、下载等功能')
  } else {
    console.log('\n⚠️ 所有认证配置都失败了。')
    console.log('\n🔧 建议检查:')
    console.log('   1. 网络连接是否正常')
    console.log('   2. SMB服务器是否运行')
    console.log('   3. 用户名和密码是否正确')
    console.log('   4. 域设置是否正确')
    console.log('   5. 防火墙是否阻止了连接')
    console.log('   6. 使用macOS原生工具测试: smbutil view //************')
  }

  console.log('\n🏁 测试完成！')
}

// 运行测试
if (require.main === module) {
  runFolderQueryTests().catch((error) => {
    console.error('💥 测试运行失败:', error.message)
    console.error('错误堆栈:', error.stack)
    process.exit(1)
  })
}

module.exports = { runFolderQueryTests }
