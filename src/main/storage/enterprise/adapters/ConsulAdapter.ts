/**
 * 企业资源同步系统 - Consul适配器
 * 功能：实现企业级Consul配置服务适配器，支持分布式配置管理
 * 依赖：企业类型定义、基础存储适配器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { ConsulConfig, EnterpriseResource } from '../types/EnterpriseTypes'
import { IStorageAdapter, FileMetadata, SyncResult, ConflictInfo } from '../../multi_sync/types/StorageTypes'

/**
 * Consul键值对
 */
interface ConsulKV {
  key: string
  value: string
  flags?: number
  session?: string
  create_index?: number
  modify_index?: number
  lock_index?: number
}

/**
 * Consul查询选项
 */
interface ConsulQueryOptions {
  datacenter?: string
  consistency?: 'default' | 'consistent' | 'stale'
  wait?: string
  index?: number
  token?: string
}

/**
 * 企业级Consul适配器
 * 支持分布式配置管理和服务发现
 */
export class ConsulAdapter implements IStorageAdapter {
  private config: ConsulConfig | null = null
  private baseUrl: string = ''
  private isConnected: boolean = false
  private headers: Record<string, string> = {}

  /**
   * 连接到Consul服务
   * @param config Consul配置
   * @returns 连接是否成功
   */
  async connect(config: ConsulConfig): Promise<boolean> {
    try {
      this.config = config

      // 构建基础URL
      const scheme = config.scheme || 'http'
      const port = config.port || 8500
      this.baseUrl = `${scheme}://${config.host}:${port}/v1`

      // 设置请求头
      this.headers = {
        'Content-Type': 'application/json'
      }

      if (config.token) {
        this.headers['X-Consul-Token'] = config.token
      }

      // 测试连接
      const isHealthy = await this.testConnection()
      this.isConnected = isHealthy

      return isHealthy
    } catch (error: any) {
      console.error('Consul连接失败:', error)
      this.isConnected = false
      return false
    }
  }

  /**
   * 断开Consul连接
   */
  async disconnect(): Promise<void> {
    this.isConnected = false
    this.config = null
    this.baseUrl = ''
    this.headers = {}
  }

  /**
   * 测试Consul连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    if (!this.config) {
      return false
    }

    try {
      const startTime = Date.now()

      // 查询Consul状态
      const response = await this.makeRequest('GET', '/status/leader')

      if (response.ok) {
        const latency = Date.now() - startTime
        console.log(`Consul连接测试成功，延迟: ${latency}ms`)
        return true
      } else {
        console.warn('Consul连接测试失败:', response.status, response.statusText)
        return false
      }
    } catch (error) {
      console.warn('Consul连接测试异常:', error)
      return false
    }
  }

  /**
   * 上传文件（在Consul中存储企业资源）
   * @param localPath 本地文件路径（这里是资源数据）
   * @param remotePath 远程路径（这里是Consul键）
   * @param metadata 文件元数据
   * @returns 是否成功
   */
  async uploadFile(localPath: string, remotePath: string, metadata?: FileMetadata): Promise<boolean> {
    if (!this.isConnected || !this.config) {
      throw new Error('Consul未连接')
    }

    try {
      // 解析资源数据
      const resourceData: EnterpriseResource = JSON.parse(localPath)

      // 构建Consul键
      const consulKey = this.buildConsulKey(remotePath, resourceData)

      // 准备存储的数据
      const storeData = {
        resource: resourceData,
        metadata: {
          ...metadata,
          stored_at: new Date().toISOString(),
          adapter_type: 'consul'
        }
      }

      // 存储到Consul
      const response = await this.makeRequest('PUT', `/kv/${consulKey}`, JSON.stringify(storeData))

      if (response.ok) {
        console.log(`企业资源已存储到Consul: ${consulKey}`)
        return true
      } else {
        throw new Error(`Consul存储失败: ${response.status} ${response.statusText}`)
      }
    } catch (error: any) {
      console.error('上传企业资源到Consul失败:', error)
      throw error
    }
  }

  /**
   * 下载文件（从Consul获取企业资源）
   * @param remotePath 远程路径（Consul键或查询模式）
   * @param localPath 本地路径（输出路径）
   * @returns 是否成功
   */
  async downloadFile(remotePath: string, localPath: string): Promise<boolean> {
    if (!this.isConnected || !this.config) {
      throw new Error('Consul未连接')
    }

    try {
      // 构建查询键
      const consulKey = this.buildConsulKey(remotePath)

      // 从Consul获取数据
      const response = await this.makeRequest('GET', `/kv/${consulKey}?raw=false`)

      if (!response.ok) {
        if (response.status === 404) {
          return false // 键不存在
        }
        throw new Error(`Consul查询失败: ${response.status} ${response.statusText}`)
      }

      const kvPairs: ConsulKV[] = await response.json()

      if (kvPairs.length === 0) {
        return false
      }

      // 解析并处理数据
      const resources: EnterpriseResource[] = []

      for (const kv of kvPairs) {
        try {
          const decodedValue = Buffer.from(kv.value, 'base64').toString('utf-8')
          const storeData = JSON.parse(decodedValue)

          if (storeData.resource) {
            resources.push(storeData.resource)
          }
        } catch (parseError) {
          console.warn(`解析Consul数据失败 [${kv.key}]:`, parseError)
        }
      }

      // 输出结果
      const jsonData = JSON.stringify(resources, null, 2)
      console.log('从Consul下载的企业资源数据:', jsonData)

      return true
    } catch (error: any) {
      console.error('从Consul下载企业资源失败:', error)
      throw error
    }
  }

  /**
   * 删除文件（从Consul删除企业资源）
   * @param remotePath 远程路径（Consul键）
   * @returns 是否成功
   */
  async deleteFile(remotePath: string): Promise<boolean> {
    if (!this.isConnected || !this.config) {
      throw new Error('Consul未连接')
    }

    try {
      // 构建删除键
      const consulKey = this.buildConsulKey(remotePath)

      // 从Consul删除
      const response = await this.makeRequest('DELETE', `/kv/${consulKey}`)

      if (response.ok) {
        const result = await response.text()
        return result === 'true' // Consul返回字符串"true"表示成功
      } else {
        throw new Error(`Consul删除失败: ${response.status} ${response.statusText}`)
      }
    } catch (error: any) {
      console.error('从Consul删除企业资源失败:', error)
      throw error
    }
  }

  /**
   * 列出文件（查询Consul中的企业资源）
   * @param remotePath 远程路径（查询前缀）
   * @returns 文件元数据列表
   */
  async listFiles(remotePath: string): Promise<FileMetadata[]> {
    if (!this.isConnected || !this.config) {
      throw new Error('Consul未连接')
    }

    try {
      // 构建查询前缀
      const consulPrefix = this.buildConsulKey(remotePath)

      // 查询Consul键列表
      const response = await this.makeRequest('GET', `/kv/${consulPrefix}?keys=true`)

      if (!response.ok) {
        if (response.status === 404) {
          return [] // 没有匹配的键
        }
        throw new Error(`Consul查询失败: ${response.status} ${response.statusText}`)
      }

      const keys: string[] = await response.json()

      // 获取每个键的详细信息
      const metadataList: FileMetadata[] = []

      for (const key of keys) {
        try {
          const kvResponse = await this.makeRequest('GET', `/kv/${key}?raw=false`)

          if (kvResponse.ok) {
            const kvPairs: ConsulKV[] = await kvResponse.json()

            if (kvPairs.length > 0) {
              const kv = kvPairs[0]
              const metadata = this.consulKVToFileMetadata(kv)
              metadataList.push(metadata)
            }
          }
        } catch (error) {
          console.warn(`获取Consul键详情失败 [${key}]:`, error)
        }
      }

      return metadataList
    } catch (error: any) {
      console.error('查询Consul企业资源列表失败:', error)
      throw error
    }
  }

  /**
   * 同步文件
   * @param localFiles 本地文件列表
   * @param remoteFiles 远程文件列表
   * @returns 同步结果
   */
  async syncFiles(localFiles: FileMetadata[], remoteFiles: FileMetadata[]): Promise<SyncResult> {
    const result: SyncResult = {
      success: true,
      message: 'Consul同步完成',
      filesUploaded: 0,
      filesDownloaded: 0,
      filesDeleted: 0,
      errors: [],
      conflicts: []
    }

    try {
      // 计算同步操作
      const operations = this.calculateSyncOperations(localFiles, remoteFiles)

      // 执行上传操作
      for (const upload of operations.uploads) {
        try {
          await this.uploadFile(upload.localPath, upload.remotePath, upload.metadata)
          result.filesUploaded++
        } catch (error: any) {
          result.errors.push(`上传失败 ${upload.remotePath}: ${error.message}`)
        }
      }

      // 执行下载操作
      for (const download of operations.downloads) {
        try {
          await this.downloadFile(download.remotePath, download.localPath)
          result.filesDownloaded++
        } catch (error: any) {
          result.errors.push(`下载失败 ${download.remotePath}: ${error.message}`)
        }
      }

      // 执行删除操作
      for (const deletion of operations.deletions) {
        try {
          await this.deleteFile(deletion.remotePath)
          result.filesDeleted++
        } catch (error: any) {
          result.errors.push(`删除失败 ${deletion.remotePath}: ${error.message}`)
        }
      }

      // 处理冲突
      result.conflicts = operations.conflicts

      result.success = result.errors.length === 0
      if (!result.success) {
        result.message = `同步完成，但有 ${result.errors.length} 个错误`
      }
    } catch (error: any) {
      result.success = false
      result.message = `同步失败: ${error.message}`
      result.errors.push(error.message)
    }

    return result
  }

  /**
   * 监听Consul键变化
   * @param keyPrefix 键前缀
   * @param callback 变化回调
   * @returns 取消监听函数
   */
  async watchKeys(
    keyPrefix: string,
    callback: (changes: Array<{ key: string; action: 'create' | 'update' | 'delete'; value?: any }>) => void
  ): Promise<() => void> {
    if (!this.isConnected || !this.config) {
      throw new Error('Consul未连接')
    }

    let watching = true
    let lastIndex = 0

    const watch = async () => {
      while (watching) {
        try {
          // 使用阻塞查询监听变化
          const response = await this.makeRequest('GET', `/kv/${keyPrefix}?wait=30s&index=${lastIndex}&recurse=true`)

          if (response.ok) {
            const consulIndex = response.headers.get('X-Consul-Index')
            if (consulIndex) {
              const newIndex = parseInt(consulIndex, 10)
              if (newIndex > lastIndex) {
                lastIndex = newIndex

                // 获取当前数据并比较变化
                const kvPairs: ConsulKV[] = await response.json()
                const changes = this.detectChanges(kvPairs)

                if (changes.length > 0) {
                  callback(changes)
                }
              }
            }
          } else if (response.status !== 404) {
            console.warn('Consul监听失败:', response.status, response.statusText)
            await new Promise((resolve) => setTimeout(resolve, 5000)) // 等待5秒后重试
          }
        } catch (error) {
          console.warn('Consul监听异常:', error)
          await new Promise((resolve) => setTimeout(resolve, 5000)) // 等待5秒后重试
        }
      }
    }

    // 启动监听
    watch()

    // 返回取消监听函数
    return () => {
      watching = false
    }
  }

  /**
   * 构建Consul键
   * @param path 路径
   * @param resource 企业资源（可选）
   * @returns Consul键
   */
  private buildConsulKey(path: string, resource?: EnterpriseResource): string {
    const prefix = this.config!.key_prefix.replace(/\/$/, '') // 移除末尾斜杠

    if (resource) {
      // 为资源构建层次化键
      return `${prefix}/${resource.resource_type}/${resource.environment}/${resource.id}`
    } else {
      // 直接使用路径
      const cleanPath = path.replace(/^\//, '') // 移除开头斜杠
      return cleanPath ? `${prefix}/${cleanPath}` : prefix
    }
  }

  /**
   * 发起HTTP请求
   * @param method HTTP方法
   * @param path 请求路径
   * @param body 请求体
   * @param options 请求选项
   * @returns 响应对象
   */
  private async makeRequest(method: string, path: string, body?: string, options?: ConsulQueryOptions): Promise<Response> {
    const url = new URL(this.baseUrl + path)

    // 添加查询参数
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined) {
          url.searchParams.set(key, value.toString())
        }
      })
    }

    const requestOptions: RequestInit = {
      method,
      headers: this.headers
    }

    if (body) {
      requestOptions.body = body
    }

    // TODO: 在实际项目中，这里应该使用node-fetch或其他HTTP客户端
    // 这里使用模拟的fetch实现
    return this.mockFetch(url.toString(), requestOptions)
  }

  /**
   * 模拟fetch请求（用于演示）
   * @param url 请求URL
   * @param options 请求选项
   * @returns 模拟响应
   */
  private async mockFetch(url: string, options: RequestInit): Promise<Response> {
    console.log(`模拟Consul请求: ${options.method} ${url}`)

    // 模拟延迟
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 模拟成功响应
    return {
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['X-Consul-Index', '1000']]),
      json: async () => [],
      text: async () => 'true'
    } as any
  }

  /**
   * 将Consul KV转换为文件元数据
   * @param kv Consul键值对
   * @returns 文件元数据
   */
  private consulKVToFileMetadata(kv: ConsulKV): FileMetadata {
    const keyParts = kv.key.split('/')
    const name = keyParts[keyParts.length - 1]

    return {
      name,
      path: kv.key,
      size: kv.value ? Buffer.from(kv.value, 'base64').length : 0,
      lastModified: new Date(), // Consul不提供修改时间，使用当前时间
      contentType: 'application/json',
      etag: kv.modify_index?.toString(),
      version: kv.modify_index?.toString()
    }
  }

  /**
   * 计算同步操作
   * @param localFiles 本地文件列表
   * @param remoteFiles 远程文件列表
   * @returns 同步操作列表
   */
  private calculateSyncOperations(
    localFiles: FileMetadata[],
    remoteFiles: FileMetadata[]
  ): {
    uploads: Array<{ localPath: string; remotePath: string; metadata: FileMetadata }>
    downloads: Array<{ remotePath: string; localPath: string }>
    deletions: Array<{ remotePath: string }>
    conflicts: ConflictInfo[]
  } {
    const operations = {
      uploads: [] as Array<{ localPath: string; remotePath: string; metadata: FileMetadata }>,
      downloads: [] as Array<{ remotePath: string; localPath: string }>,
      deletions: [] as Array<{ remotePath: string }>,
      conflicts: [] as ConflictInfo[]
    }

    // TODO: 实现同步操作计算逻辑
    // 比较本地和远程文件，确定需要上传、下载或删除的文件

    return operations
  }

  /**
   * 检测变化
   * @param kvPairs 当前键值对列表
   * @returns 变化列表
   */
  private detectChanges(kvPairs: ConsulKV[]): Array<{ key: string; action: 'create' | 'update' | 'delete'; value?: any }> {
    // TODO: 实现变化检测逻辑
    // 比较当前状态和之前状态，检测创建、更新、删除操作

    return []
  }
}
