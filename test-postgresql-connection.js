/**
 * PostgreSQL数据库连接测试脚本
 * 功能：测试primary-db数据库连接并初始化表结构
 * 使用方法：node test-postgresql-connection.js
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const { Pool } = require('pg')

// 数据库配置
const dbConfig = {
  host: '***************',
  port: 5432,
  database: 'postgres',
  user: 'postgres',
  password: 'E5z`+wk%YjtO:@zE^YI<',
  ssl: false,
  max: 10,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 60000
}

console.log('=== PostgreSQL数据库连接测试 ===')
console.log('📋 数据库配置信息:')
console.log(`   主机: ${dbConfig.host}`)
console.log(`   端口: ${dbConfig.port}`)
console.log(`   数据库: ${dbConfig.database}`)
console.log(`   用户名: ${dbConfig.user}`)
console.log(`   SSL: ${dbConfig.ssl ? '启用' : '禁用'}`)
console.log(`   连接池大小: ${dbConfig.max}`)

async function testConnection() {
  let pool = null

  try {
    console.log('\n🔄 正在连接数据库...')
    pool = new Pool(dbConfig)

    // 测试连接
    const client = await pool.connect()
    const result = await client.query('SELECT NOW() as current_time, version() as version, current_database() as database, current_user as user')
    console.log('✅ 数据库连接成功!')
    console.log(`   服务器时间: ${result.rows[0].current_time}`)
    console.log(`   PostgreSQL版本: ${result.rows[0].version}`)
    console.log(`   当前数据库: ${result.rows[0].database}`)
    console.log(`   当前用户: ${result.rows[0].user}`)
    client.release()

    // 检查表是否存在
    console.log('\n🔄 检查表结构...')
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('t_assets', 't_organizations', 't_organization_assets', 't_custom_folders', 't_asset_folder_mapping', 't_asset_chains')
      ORDER BY table_name
    `)

    const existingTables = tablesResult.rows.map((row) => row.table_name)
    console.log(`✅ 已存在的表 (${existingTables.length}个):`, existingTables)

    // 如果没有表，创建表结构
    if (existingTables.length === 0) {
      console.log('\n🔄 数据库中没有表，开始创建表结构...')
      await createTables(pool)
    } else {
      // 检查每个表的记录数
      for (const tableName of existingTables) {
        try {
          const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${tableName}`)
          console.log(`   ${tableName}: ${countResult.rows[0].count} 条记录`)
        } catch (countError) {
          console.log(`   ${tableName}: 无法获取记录数 (${countError.message})`)
        }
      }
    }

    console.log('\n✅ 数据库测试完成!')
  } catch (error) {
    console.error('❌ 连接失败:', error.message)
    if (error.code) {
      console.error(`   错误代码: ${error.code}`)
    }
    process.exit(1)
  } finally {
    if (pool) {
      console.log('\n🔄 断开数据库连接...')
      await pool.end()
      console.log('✅ 连接已断开')
    }
  }
}

async function createTables(pool) {
  try {
    // 创建资产表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_assets (
        id SERIAL PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        label TEXT,
        asset_ip TEXT,
        group_name TEXT,
        uuid TEXT UNIQUE,
        auth_type TEXT,
        port INTEGER,
        username TEXT,
        password TEXT,
        key_chain_id INTEGER,
        favorite INTEGER DEFAULT 2,
        asset_type TEXT DEFAULT 'person',
        need_proxy INTEGER DEFAULT 0,
        proxy_name TEXT,
        version INTEGER DEFAULT 1
      )
    `)
    console.log('✅ 资产表创建成功')

    // 创建组织表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_organizations (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ 组织表创建成功')

    // 创建组织资产表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_organization_assets (
        id SERIAL PRIMARY KEY,
        organization_id INTEGER,
        asset_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ 组织资产表创建成功')

    // 创建自定义文件夹表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_custom_folders (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT,
        parent_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ 自定义文件夹表创建成功')

    // 创建资产文件夹映射表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_asset_folder_mapping (
        id SERIAL PRIMARY KEY,
        asset_id INTEGER,
        folder_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ 资产文件夹映射表创建成功')

    // 创建密钥链表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS t_asset_chains (
        id SERIAL PRIMARY KEY,
        uuid TEXT UNIQUE,
        name TEXT,
        private_key TEXT,
        passphrase TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✅ 密钥链表创建成功')

    // 插入一些示例数据
    await insertSampleData(pool)
  } catch (error) {
    console.error('❌ 创建表失败:', error.message)
    throw error
  }
}

async function insertSampleData(pool) {
  try {
    console.log('\n🔄 插入示例数据...')

    // 插入示例资产
    await pool.query(`
      INSERT INTO t_assets (label, asset_ip, group_name, uuid, auth_type, port, username, asset_type)
      VALUES 
        ('测试服务器1', '*************', '开发环境', 'asset-001', 'password', 22, 'root', 'server'),
        ('测试服务器2', '*************', '测试环境', 'asset-002', 'key', 22, 'admin', 'server')
      ON CONFLICT (uuid) DO NOTHING
    `)

    // 插入示例组织
    await pool.query(`
      INSERT INTO t_organizations (uuid, name, description)
      VALUES 
        ('org-001', '开发部门', '负责系统开发和维护'),
        ('org-002', '运维部门', '负责系统运维和监控')
      ON CONFLICT (uuid) DO NOTHING
    `)

    console.log('✅ 示例数据插入成功')
  } catch (error) {
    console.error('❌ 插入示例数据失败:', error.message)
  }
}

// 运行测试
testConnection()
