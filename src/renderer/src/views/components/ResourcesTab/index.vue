<template>
  <div class="resources-tab">
    <!-- 个人资源面板 -->
    <div class="content-panel">
      <div class="panel-header">
        <h3>个人资源概览</h3>
        <button
          class="btn btn-primary"
          @click="navigateToPage('/resources/personal')"
        >
          <Plus class="btn-icon" />
          管理个人资源
        </button>
      </div>

      <div class="resource-summary">
        <div class="summary-card">
          <div class="card-icon">
            <Server class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">服务器</div>
            <div class="card-value">{{ personalStats.servers }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <Database class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">数据库</div>
            <div class="card-value">{{ personalStats.databases }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <Cloud class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">云服务</div>
            <div class="card-value">{{ personalStats.cloudServices }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 企业资源面板 -->
    <div class="content-panel">
      <div class="panel-header">
        <h3>企业资源概览</h3>
        <button
          class="btn btn-primary"
          @click="openEnterpriseResourceManagement"
        >
          <Building class="btn-icon" />
          浏览企业资源
        </button>
      </div>

      <div class="resource-summary">
        <div class="summary-card">
          <div class="card-icon">
            <Server class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">共享主机</div>
            <div class="card-value">{{ enterpriseStats.hosts }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <Database class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">密钥链</div>
            <div class="card-value">{{ enterpriseStats.keychains }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <Cloud class="icon" />
          </div>
          <div class="card-content">
            <div class="card-title">代码片段</div>
            <div class="card-value">{{ enterpriseStats.snippets }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div class="content-panel">
      <div class="panel-header">
        <h3>快速操作</h3>
      </div>

      <div class="action-grid">
        <div
          class="action-card"
          @click="navigateToPage('/sync/config')"
        >
          <div class="action-icon">
            <Settings class="icon" />
          </div>
          <div class="action-content">
            <div class="action-title">同步配置</div>
            <div class="action-desc">管理数据同步设置</div>
          </div>
        </div>
        <div
          class="action-card"
          @click="navigateToPage('/sync/monitor')"
        >
          <div class="action-icon">
            <Activity class="icon" />
          </div>
          <div class="action-content">
            <div class="action-title">监控仪表板</div>
            <div class="action-desc">查看同步状态</div>
          </div>
        </div>
        <div
          class="action-card"
          @click="navigateToPage('/security/permissions')"
        >
          <div class="action-icon">
            <Shield class="icon" />
          </div>
          <div class="action-content">
            <div class="action-title">权限管理</div>
            <div class="action-desc">配置用户权限</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { User, Plus, Server, Database, Cloud, Building, Settings, Activity, Shield } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()

// Props定义，与其他Tab组件保持一致
const props = defineProps({
  toggleSidebar: {
    type: Function,
    required: false
  }
})

// Emits定义
const emit = defineEmits(['open-user-tab'])

// 个人资源统计数据
const personalStats = ref({
  servers: 5,
  databases: 3,
  cloudServices: 8
})

// 企业资源统计数据
const enterpriseStats = ref({
  hosts: 12,
  keychains: 8,
  snippets: 15
})

// 导航到页面
const navigateToPage = (path: string) => {
  router.push(path)
}

// 打开企业资源管理页面
const openEnterpriseResourceManagement = () => {
  emit('open-user-tab', 'enterpriseResourceManagement')
}

// 加载数据
const loadData = async () => {
  try {
    // 这里可以调用API获取个人资源数据
    console.log('Loading personal resources data...')
    // 这里可以调用API获取企业资源数据
    console.log('Loading enterprise resources data...')
  } catch (error) {
    console.error('Failed to load resources data:', error)
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="less" scoped>
.resources-tab {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #333333);
}

.content-panel {
  padding: 20px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.resource-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summary-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-color-light);
  border-radius: 8px;

  .icon {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
  }
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin-bottom: 4px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

/* 快速操作网格样式 */
.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-color-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--primary-color-light);
  border-radius: 8px;

  .icon {
    width: 24px;
    height: 24px;
    color: var(--primary-color);
  }
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
}

.action-desc {
  font-size: 14px;
  color: var(--text-color-secondary);
}

/* 企业资源面板样式 */
.enterprise-panel {
  padding: 0;

  .page-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 20px 24px;
    background: var(--bg-color-secondary, #f9fafb);
    border-bottom: 1px solid var(--border-color, #e5e7eb);
    flex-shrink: 0;
  }
}

.header-content {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
}

.title-icon {
  width: 28px;
  height: 28px;
  color: var(--primary-color);
}

.page-description {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

/* 企业面板内容区域 */
.enterprise-panel .filters {
  margin: 20px 24px;
  margin-bottom: 16px;
}

.enterprise-panel .resource-list {
  flex: 1;
  overflow: auto;
  padding: 0 24px 24px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color, #3b82f6) 0%, #2563eb 100%);
  color: white;

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }
}

.btn-secondary {
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  border: 1px solid var(--border-color, #d1d5db);

  &:hover:not(:disabled) {
    background: var(--hover-bg-color, #f9fafb);
    border-color: var(--primary-color, #3b82f6);
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 筛选器样式 */
.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--bg-color-secondary, #f9fafb);
  border-radius: 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  flex-wrap: wrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 160px;
  flex: 1;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color-secondary, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.filter-input,
.filter-select {
  padding: 10px 14px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 8px;
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  font-size: 14px;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary-color, #3b82f6);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &:hover {
    border-color: var(--primary-color, #3b82f6);
  }
}

/* 资源列表样式 */
.resource-list {
  margin-bottom: 24px;
}

.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 16px;
  text-align: center;
}

.loading-icon,
.error-icon,
.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  color: var(--text-color-secondary);
}

.loading-message,
.error-message,
.empty-message {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: var(--text-color-secondary);
  margin: 0;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 20px;
}

.resource-card {
  background: var(--bg-color, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover {
    border-color: var(--primary-color, #3b82f6);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 4px 0;
}

.resource-type {
  font-size: 14px;
  color: var(--text-color-secondary);
}

.resource-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: var(--success-color-light);
  color: var(--success-color);
}

.resource-content {
  margin-bottom: 12px;
}

.resource-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.tag {
  padding: 2px 6px;
  background: var(--primary-color-light);
  color: var(--primary-color);
  font-size: 12px;
  border-radius: 3px;
}

.resource-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.meta-item {
  display: flex;
  gap: 4px;
}

.meta-label {
  color: var(--text-color-secondary);
}

.meta-value {
  color: var(--text-color);
  font-weight: 500;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--text-color-secondary);
}

.update-time {
  flex: 1;
}

.card-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-color-secondary);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--hover-bg-color);
  color: var(--text-color);
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* 分页样式 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 20px;
  margin-top: auto;
}

.pagination-btn {
  padding: 10px 16px;
  border: 1px solid var(--border-color, #d1d5db);
  background: var(--bg-color, #ffffff);
  color: var(--text-color, #374151);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:hover:not(:disabled) {
    background: var(--hover-bg-color, #f9fafb);
    border-color: var(--primary-color, #3b82f6);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.pagination-info {
  margin: 0 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color-secondary, #6b7280);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #ffffff !important;
  border-radius: 12px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.4),
    0 10px 20px rgba(0, 0, 0, 0.2) !important;
  border: 2px solid #e5e7eb !important;
  max-width: 600px;
  width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 2px solid #e5e7eb !important;
  background: #ffffff !important;
}

.modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937 !important;
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: #6b7280 !important;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--hover-bg-color);
  color: var(--text-color);
}

.close-icon {
  width: 18px;
  height: 18px;
}

.modal-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

/* 同步选项样式 */
.sync-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-item,
.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.radio-text,
.checkbox-text {
  flex: 1;
  font-size: 14px;
  color: var(--text-color);
}

.backend-status {
  padding: 2px 6px;
  font-size: 11px;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.backend-status.healthy {
  background: var(--success-color-light);
  color: var(--success-color);
}

/* 详情网格样式 */
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-value {
  font-size: 14px;
  color: var(--text-color);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.config-data {
  background: var(--bg-color-secondary);
  padding: 16px;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  overflow-x: auto;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
