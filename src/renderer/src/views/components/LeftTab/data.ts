const menuTabsData = [
  {
    name: '主机',
    key: 'workspace',
    icon: new URL('@/assets/menu/host.svg', import.meta.url).href
  },
  {
    name: '密钥链',
    key: 'keychain',
    icon: new URL('@/assets/menu/key.svg', import.meta.url).href
  },
  {
    name: '文件',
    key: 'files',
    icon: new URL('@/assets/menu/files.svg', import.meta.url).href
  },
  {
    name: '扩展',
    key: 'extensions',
    icon: new URL('@/assets/menu/extensions.svg', import.meta.url).href
  },
  {
    name: 'AI',
    key: 'ai',
    icon: new URL('@/assets/menu/ai.svg', import.meta.url).href
  },
  {
    name: '用户',
    key: 'user',
    icon: new URL('@/assets/menu/user.svg', import.meta.url).href
  },
  {
    name: '设置',
    key: 'setting',
    icon: new URL('@/assets/menu/setting.svg', import.meta.url).href
  },
  {
    name: '通知',
    key: 'notice',
    icon: new URL('@/assets/menu/notice.svg', import.meta.url).href
  }
]
export { menuTabsData }
