<template>
  <div class="analytics-wrapper">
    <div
      v-if="showPanel"
      class="analytics-panel"
    >
      <div class="panel-header">
        <h3>数据分析</h3>
        <div class="header-controls">
          <select
            v-model="selectedTimeRange"
            class="time-range-select"
            @change="updateMetrics"
          >
            <option value="1h">最近1小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="all">全部时间</option>
          </select>
          <button
            class="close-btn"
            @click="closePanel"
            >×</button
          >
        </div>
      </div>

      <div class="panel-content">
        <!-- 概览指标 -->
        <div class="overview-section">
          <h4>概览</h4>
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">👥</div>
              <div class="metric-info">
                <div class="metric-value">{{ metrics.totalSessions }}</div>
                <div class="metric-label">总会话数</div>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">⌨️</div>
              <div class="metric-info">
                <div class="metric-value">{{ metrics.totalCommands }}</div>
                <div class="metric-label">总命令数</div>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">🔗</div>
              <div class="metric-info">
                <div class="metric-value">{{ metrics.totalConnections }}</div>
                <div class="metric-label">总连接数</div>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">⏱️</div>
              <div class="metric-info">
                <div class="metric-value">{{ formatDuration(metrics.averageSessionDuration) }}</div>
                <div class="metric-label">平均会话时长</div>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">✅</div>
              <div class="metric-info">
                <div class="metric-value">{{ connectionSuccessRate }}%</div>
                <div class="metric-label">连接成功率</div>
              </div>
            </div>
            <div class="metric-card">
              <div class="metric-icon">⚠️</div>
              <div class="metric-info">
                <div class="metric-value">{{ (metrics.errorRate * 100).toFixed(1) }}%</div>
                <div class="metric-label">错误率</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最常用命令 -->
        <div class="commands-section">
          <h4>最常用命令</h4>
          <div class="commands-list">
            <div
              v-for="(cmd, index) in metrics.mostUsedCommands.slice(0, 8)"
              :key="cmd.command"
              class="command-item"
            >
              <div class="command-rank">{{ index + 1 }}</div>
              <div class="command-name">{{ cmd.command }}</div>
              <div class="command-count">{{ cmd.count }}</div>
              <div class="command-bar">
                <div
                  class="command-bar-fill"
                  :style="{ width: (cmd.count / metrics.mostUsedCommands[0]?.count) * 100 + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统洞察 -->
        <div class="insights-section">
          <div class="section-header">
            <h4>系统洞察</h4>
            <div class="insight-filters">
              <button
                v-for="type in insightTypes"
                :key="type"
                class="filter-btn"
                :class="{ active: selectedInsightType === type }"
                @click="selectedInsightType = type"
              >
                {{ getInsightTypeName(type) }}
              </button>
            </div>
          </div>

          <div class="insights-list">
            <div
              v-for="insight in filteredInsights"
              :key="insight.createdAt.getTime()"
              class="insight-item"
              :class="insight.severity"
            >
              <div class="insight-header">
                <div class="insight-title">{{ insight.title }}</div>
                <div class="insight-time">{{ formatTime(insight.createdAt) }}</div>
              </div>
              <div class="insight-description">{{ insight.description }}</div>
              <div
                v-if="insight.recommendations.length > 0"
                class="insight-recommendations"
              >
                <div class="recommendations-title">建议:</div>
                <ul>
                  <li
                    v-for="rec in insight.recommendations"
                    :key="rec"
                    >{{ rec }}</li
                  >
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户行为分析 -->
        <div
          v-if="selectedUser"
          class="behavior-section"
        >
          <h4>用户行为分析</h4>
          <div class="user-selector">
            <select
              v-model="selectedUser"
              @change="updateUserBehavior"
            >
              <option value="">选择用户</option>
              <option
                v-for="user in availableUsers"
                :key="user"
                :value="user"
                >{{ user }}</option
              >
            </select>
          </div>

          <div
            v-if="userBehavior"
            class="behavior-analysis"
          >
            <div class="behavior-grid">
              <div class="behavior-card">
                <h5>偏好命令</h5>
                <div class="preferred-commands">
                  <span
                    v-for="cmd in userBehavior.patterns.preferredCommands.slice(0, 5)"
                    :key="cmd"
                    class="command-tag"
                  >
                    {{ cmd }}
                  </span>
                </div>
              </div>

              <div class="behavior-card">
                <h5>工作时间</h5>
                <div class="working-hours"> {{ userBehavior.patterns.workingHours.start }}:00 - {{ userBehavior.patterns.workingHours.end }}:00 </div>
              </div>

              <div class="behavior-card">
                <h5>效率指标</h5>
                <div class="efficiency-metrics">
                  <div>命令/分钟: {{ userBehavior.patterns.efficiency.commandsPerMinute.toFixed(2) }}</div>
                  <div>错误率: {{ (userBehavior.patterns.efficiency.errorRate * 100).toFixed(1) }}%</div>
                </div>
              </div>
            </div>

            <div
              v-if="userBehavior.recommendations.length > 0"
              class="recommendations-section"
            >
              <h5>个性化建议</h5>
              <ul class="user-recommendations">
                <li
                  v-for="rec in userBehavior.recommendations"
                  :key="rec"
                  >{{ rec }}</li
                >
              </ul>
            </div>
          </div>
        </div>

        <!-- 数据控制 -->
        <div class="controls-section">
          <h4>数据控制</h4>
          <div class="control-options">
            <label class="control-option">
              <input
                v-model="dataCollectionEnabled"
                type="checkbox"
                @change="toggleDataCollection"
              />
              启用数据收集
            </label>
            <label class="control-option">
              <input
                v-model="privacyMode"
                type="checkbox"
                @change="togglePrivacyMode"
              />
              隐私模式
            </label>
          </div>
          <div class="control-actions">
            <button
              class="action-btn"
              @click="exportData"
              >导出数据</button
            >
            <button
              class="action-btn danger"
              @click="clearData"
              >清空数据</button
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 浮动按钮 -->
    <div
      v-if="!showPanel"
      class="analytics-toggle"
      @click="togglePanel"
    >
      <span class="analytics-icon">📊</span>
      <span class="analytics-text">分析</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { dataAnalytics, type AnalyticsMetrics, type SystemInsight, type UserBehaviorPattern } from '@/utils/dataAnalytics'

const emit = defineEmits<{
  close: []
}>()

const showPanel = ref(true) // 默认显示，因为是通过工具栏打开的
const selectedTimeRange = ref('24h')
const selectedInsightType = ref('all')
const selectedUser = ref('')
const dataCollectionEnabled = ref(true)
const privacyMode = ref(false)

const metrics = ref<AnalyticsMetrics>({
  totalSessions: 0,
  totalCommands: 0,
  totalConnections: 0,
  averageSessionDuration: 0,
  mostUsedCommands: [],
  connectionSuccess: 0,
  connectionFailure: 0,
  errorRate: 0,
  peakUsageHours: [],
  userGrowth: []
})

const insights = ref<SystemInsight[]>([])
const userBehavior = ref<UserBehaviorPattern | null>(null)
const availableUsers = ref<string[]>([])

const insightTypes = ['all', 'performance', 'security', 'usage', 'optimization']

// 计算属性
const connectionSuccessRate = computed(() => {
  const total = metrics.value.connectionSuccess + metrics.value.connectionFailure
  return total > 0 ? ((metrics.value.connectionSuccess / total) * 100).toFixed(1) : '0'
})

const filteredInsights = computed(() => {
  if (selectedInsightType.value === 'all') {
    return insights.value
  }
  return insights.value.filter((insight) => insight.type === selectedInsightType.value)
})

onMounted(() => {
  updateMetrics()
  loadInsights()
  setupEventListeners()
})

const updateMetrics = () => {
  const timeRange = getTimeRange()
  metrics.value = dataAnalytics.getAnalyticsMetrics(timeRange)
}

const getTimeRange = () => {
  const now = new Date()
  const ranges = {
    '1h': new Date(now.getTime() - 60 * 60 * 1000),
    '24h': new Date(now.getTime() - 24 * 60 * 60 * 1000),
    '7d': new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
    '30d': new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
    all: new Date(0)
  }

  const start = ranges[selectedTimeRange.value] || ranges['24h']
  return { start, end: now }
}

const loadInsights = () => {
  insights.value = dataAnalytics.getInsights()
}

const setupEventListeners = () => {
  dataAnalytics.on('insightGenerated', (insight: SystemInsight) => {
    insights.value.unshift(insight)
    if (insights.value.length > 100) {
      insights.value = insights.value.slice(0, 50)
    }
  })

  dataAnalytics.on('actionTracked', () => {
    // 定期更新指标
    if (Math.random() < 0.1) {
      // 10%的概率更新
      updateMetrics()
    }
  })
}

const updateUserBehavior = () => {
  if (selectedUser.value) {
    userBehavior.value = dataAnalytics.getUserBehaviorPattern(selectedUser.value)
  } else {
    userBehavior.value = null
  }
}

const togglePanel = () => {
  if (showPanel.value) {
    emit('close')
  } else {
    showPanel.value = true
    updateMetrics()
    loadInsights()
  }
}

const closePanel = () => {
  emit('close')
}

const toggleDataCollection = () => {
  dataAnalytics.setCollectionEnabled(dataCollectionEnabled.value)
}

const togglePrivacyMode = () => {
  dataAnalytics.setPrivacyMode(privacyMode.value)
}

const exportData = () => {
  const data = dataAnalytics.exportData()
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `analytics-data-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const clearData = () => {
  if (confirm('确定要清空所有分析数据吗？此操作不可撤销。')) {
    dataAnalytics.clearData()
    updateMetrics()
    loadInsights()
    userBehavior.value = null
  }
}

const getInsightTypeName = (type: string): string => {
  const names = {
    all: '全部',
    performance: '性能',
    security: '安全',
    usage: '使用',
    optimization: '优化'
  }
  return names[type] || type
}

const formatDuration = (ms: number): string => {
  if (ms < 60000) return `${Math.round(ms / 1000)}秒`
  if (ms < 3600000) return `${Math.round(ms / 60000)}分钟`
  return `${Math.round(ms / 3600000)}小时`
}

const formatTime = (date: Date): string => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.round(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.round(diff / 3600000)}小时前`
  return `${Math.round(diff / 86400000)}天前`
}
</script>

<style scoped lang="less">
.analytics-wrapper {
  position: relative;
}

.analytics-panel {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 12px;

    .time-range-select {
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 11px;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 18px;
      cursor: pointer;
      color: #999;

      &:hover {
        color: #666;
      }
    }
  }
}

.panel-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.overview-section,
.commands-section,
.insights-section,
.behavior-section,
.controls-section {
  margin-bottom: 20px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  gap: 12px;

  .metric-icon {
    font-size: 24px;
  }

  .metric-info {
    .metric-value {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      line-height: 1;
    }

    .metric-label {
      font-size: 11px;
      color: #666;
      margin-top: 2px;
    }
  }
}

.commands-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.command-item {
  display: grid;
  grid-template-columns: 30px 1fr 60px 100px;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;

  .command-rank {
    font-weight: 600;
    color: #666;
    text-align: center;
  }

  .command-name {
    font-family: monospace;
    font-weight: 500;
  }

  .command-count {
    text-align: right;
    font-weight: 600;
    color: #1890ff;
  }

  .command-bar {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;

    .command-bar-fill {
      height: 100%;
      background: #1890ff;
      transition: width 0.3s ease;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .insight-filters {
    display: flex;
    gap: 4px;

    .filter-btn {
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      cursor: pointer;
      font-size: 10px;
      transition: all 0.2s;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &.active {
        background: #1890ff;
        border-color: #1890ff;
        color: white;
      }
    }
  }
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.insight-item {
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #d9d9d9;

  &.low {
    border-left-color: #52c41a;
    background: #f6ffed;
  }

  &.medium {
    border-left-color: #faad14;
    background: #fffbe6;
  }

  &.high {
    border-left-color: #ff7a45;
    background: #fff2e8;
  }

  &.critical {
    border-left-color: #ff4d4f;
    background: #fff1f0;
  }

  .insight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;

    .insight-title {
      font-weight: 600;
      font-size: 13px;
    }

    .insight-time {
      font-size: 10px;
      color: #999;
    }
  }

  .insight-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
  }

  .insight-recommendations {
    .recommendations-title {
      font-size: 11px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    ul {
      margin: 0;
      padding-left: 16px;
      font-size: 11px;
      color: #666;

      li {
        margin-bottom: 2px;
      }
    }
  }
}

.user-selector {
  margin-bottom: 16px;

  select {
    padding: 6px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    min-width: 200px;
  }
}

.behavior-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.behavior-card {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;

  h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #333;
  }

  .preferred-commands {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .command-tag {
      padding: 2px 6px;
      background: #1890ff;
      color: white;
      border-radius: 3px;
      font-size: 10px;
      font-family: monospace;
    }
  }

  .working-hours {
    font-size: 14px;
    font-weight: 600;
    color: #1890ff;
  }

  .efficiency-metrics {
    font-size: 11px;
    color: #666;

    div {
      margin-bottom: 4px;
    }
  }
}

.recommendations-section {
  h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    font-weight: 600;
    color: #333;
  }

  .user-recommendations {
    margin: 0;
    padding-left: 16px;
    font-size: 11px;
    color: #666;

    li {
      margin-bottom: 4px;
    }
  }
}

.control-options {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;

  .control-option {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    cursor: pointer;

    input[type='checkbox'] {
      margin: 0;
    }
  }
}

.control-actions {
  display: flex;
  gap: 8px;

  .action-btn {
    padding: 6px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s;

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }

    &.danger {
      &:hover {
        border-color: #ff4d4f;
        color: #ff4d4f;
      }
    }
  }
}

.analytics-toggle {
  position: fixed;
  bottom: 160px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #722ed1;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .analytics-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .analytics-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }
}
</style>
