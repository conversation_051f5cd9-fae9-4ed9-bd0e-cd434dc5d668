/**
 * 真实SMB连接和文件操作测试脚本
 * 测试SMB适配器的实际功能
 */

const { SMBAdapter } = require('./src/main/storage/multi_sync/adapters/SMBAdapter')
const fs = require('fs')
const path = require('path')

// SMB配置信息（从用户截图获取）
const smbConfig = {
  host: '************',
  share: 'it部',
  username: '<EMAIL>',
  password: 'RSgz@lbq0925!',
  domain: 'brbiotech.com',
  port: 445
}

// 测试文件配置
const testFileName = 'smb-test-file.txt'
const testFileContent = `SMB测试文件\n创建时间: ${new Date().toISOString()}\n测试内容: Hello SMB World!`
const localTestFile = path.join(__dirname, testFileName)
const remoteTestPath = '/configs/test-files'

/**
 * 创建本地测试文件
 */
function createLocalTestFile() {
  console.log('📝 创建本地测试文件...')
  fs.writeFileSync(localTestFile, testFileContent)
  console.log(`✅ 本地测试文件已创建: ${localTestFile}`)
}

/**
 * 清理本地测试文件
 */
function cleanupLocalTestFile() {
  if (fs.existsSync(localTestFile)) {
    fs.unlinkSync(localTestFile)
    console.log('🧹 本地测试文件已清理')
  }
}

/**
 * 安全日志输出（隐藏敏感信息）
 */
function safeLog(message, data = null) {
  if (data && typeof data === 'object') {
    const safeData = { ...data }
    if (safeData.password) safeData.password = '***'
    console.log(message, safeData)
  } else {
    console.log(message, data)
  }
}

/**
 * 主测试函数
 */
async function runSMBTests() {
  console.log('🚀 开始SMB连接和文件操作测试')
  console.log('='.repeat(50))

  let adapter = null
  let testsPassed = 0
  let testsTotal = 0

  try {
    // 1. 创建SMB适配器实例
    console.log('\n1️⃣ 创建SMB适配器实例...')
    testsTotal++
    adapter = new SMBAdapter()
    console.log('✅ SMB适配器创建成功')
    testsPassed++

    // 2. 建立SMB连接
    console.log('\n2️⃣ 建立SMB连接...')
    testsTotal++
    const connectResult = await adapter.connect(smbConfig)
    if (connectResult) {
      console.log('✅ SMB连接建立成功')
      testsPassed++
    } else {
      console.log('❌ SMB连接建立失败')
      return // 连接失败则退出测试
    }

    // 3. 测试SMB连接
    console.log('\n3️⃣ 测试SMB连接...')
    testsTotal++
    const connectionResult = await adapter.testConnection()
    if (connectionResult) {
      console.log('✅ SMB连接测试成功')
      testsPassed++
    } else {
      console.log('❌ SMB连接测试失败')
    }

    // 4. 测试目录创建
    console.log('\n4️⃣ 测试远程目录创建...')
    testsTotal++
    try {
      await adapter.createDirectory(remoteTestPath)
      console.log(`✅ 远程目录创建成功: ${remoteTestPath}`)
      testsPassed++
    } catch (error) {
      console.log(`⚠️ 远程目录创建失败（可能已存在）: ${error.message}`)
      testsPassed++ // 目录已存在也算成功
    }

    // 5. 创建本地测试文件
    console.log('\n5️⃣ 创建本地测试文件...')
    testsTotal++
    createLocalTestFile()
    testsPassed++

    // 6. 测试文件上传
    console.log('\n6️⃣ 测试文件上传...')
    testsTotal++
    const remoteFilePath = `${remoteTestPath}/${testFileName}`
    try {
      await adapter.uploadFile(localTestFile, remoteFilePath)
      console.log(`✅ 文件上传成功: ${remoteFilePath}`)
      testsPassed++
    } catch (error) {
      console.log(`❌ 文件上传失败:`, error.message)
    }

    // 7. 测试文件列表
    console.log('\n7️⃣ 测试文件列表...')
    testsTotal++
    try {
      const files = await adapter.listFiles(remoteTestPath)
      console.log(`✅ 文件列表获取成功，找到 ${files.length} 个文件:`)
      files.forEach((file) => {
        console.log(`   📄 ${file.name} (${file.size} bytes, ${file.lastModified})`)
      })
      testsPassed++
    } catch (error) {
      console.log(`❌ 文件列表获取失败:`, error.message)
    }

    // 8. 测试文件下载
    console.log('\n8️⃣ 测试文件下载...')
    testsTotal++
    const downloadPath = path.join(__dirname, `downloaded-${testFileName}`)
    try {
      await adapter.downloadFile(remoteFilePath, downloadPath)
      console.log(`✅ 文件下载成功: ${downloadPath}`)
      // 验证下载的文件内容
      const downloadedContent = fs.readFileSync(downloadPath, 'utf8')
      if (downloadedContent === testFileContent) {
        console.log('✅ 下载文件内容验证成功')
      } else {
        console.log('⚠️ 下载文件内容与原文件不匹配')
      }
      // 清理下载的文件
      fs.unlinkSync(downloadPath)
      testsPassed++
    } catch (error) {
      console.log(`❌ 文件下载失败:`, error.message)
    }

    // 9. 测试文件元数据获取
    console.log('\n9️⃣ 测试文件元数据获取...')
    testsTotal++
    try {
      const metadata = await adapter.getFileMetadata(remoteFilePath)
      console.log('✅ 文件元数据获取成功:')
      console.log(`   📊 大小: ${metadata.size} bytes`)
      console.log(`   📅 修改时间: ${metadata.lastModified}`)
      console.log(`   📁 类型: ${metadata.isDirectory ? '目录' : '文件'}`)
      testsPassed++
    } catch (error) {
      console.log(`❌ 文件元数据获取失败:`, error.message)
    }

    // 🔟 测试文件删除
    console.log('\n🔟 测试文件删除...')
    testsTotal++
    try {
      await adapter.deleteFile(remoteFilePath)
      console.log(`✅ 文件删除成功: ${remoteFilePath}`)
      testsPassed++
    } catch (error) {
      console.log(`❌ 文件删除失败:`, error.message)
    }
  } catch (error) {
    console.error('💥 测试过程中发生未捕获的错误:', error.message)
    console.error('错误堆栈:', error.stack)
  } finally {
    // 清理资源
    console.log('\n🧹 清理测试资源...')

    // 断开SMB连接
    if (adapter) {
      try {
        await adapter.disconnect()
        console.log('✅ SMB连接已断开')
      } catch (error) {
        console.log('⚠️ SMB连接断开时出错:', error.message)
      }
    }

    // 清理本地测试文件
    cleanupLocalTestFile()

    // 输出测试结果摘要
    console.log('\n' + '='.repeat(50))
    console.log('📊 测试结果摘要:')
    console.log(`✅ 通过: ${testsPassed}/${testsTotal}`)
    console.log(`❌ 失败: ${testsTotal - testsPassed}/${testsTotal}`)
    console.log(`📈 成功率: ${((testsPassed / testsTotal) * 100).toFixed(1)}%`)

    if (testsPassed === testsTotal) {
      console.log('🎉 所有测试通过！SMB功能正常工作。')
    } else {
      console.log('⚠️ 部分测试失败，请检查SMB配置和网络连接。')
    }
  }
}

// 运行测试
if (require.main === module) {
  runSMBTests().catch((error) => {
    console.error('💥 测试运行失败:', error.message)
    process.exit(1)
  })
}

module.exports = { runSMBTests }
