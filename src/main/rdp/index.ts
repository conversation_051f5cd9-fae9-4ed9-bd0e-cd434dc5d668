/**
 * RDP连接服务主入口
 * 提供统一的RDP连接管理接口
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { RdpService } from './rdpService'
import { RdpApiController } from './rdpApi'
import type { RdpConnectionConfig, RdpConnectionResult, RdpManagerOptions, RdpConnectionInfo } from './types'

/**
 * RDP连接服务类
 * 单例模式，提供全局的RDP连接管理
 */
class RdpConnectionService {
  private static instance: RdpConnectionService
  private connections: Map<string, RdpService> = new Map()
  private apiController?: RdpApiController
  private isInitialized: boolean = false
  private options: RdpManagerOptions

  private constructor(options: RdpManagerOptions = {}) {
    this.options = {
      maxConnections: options.maxConnections ?? 20,
      defaultTimeout: options.defaultTimeout ?? 30000,
      enableLogging: options.enableLogging ?? true,
      logLevel: options.logLevel ?? 'info',
      cleanupInterval: options.cleanupInterval ?? 60000,
      ...options
    }
  }

  /**
   * 获取RdpConnectionService单例实例
   */
  public static getInstance(options?: RdpManagerOptions): RdpConnectionService {
    if (!RdpConnectionService.instance) {
      RdpConnectionService.instance = new RdpConnectionService(options)
    }
    return RdpConnectionService.instance
  }

  /**
   * 初始化RDP服务
   */
  public initialize(): void {
    if (this.isInitialized) {
      return
    }

    // 初始化RDP API控制器
    this.apiController = new RdpApiController(this.options)
    this.apiController.initialize()

    this.isInitialized = true
    console.log('[RdpService] RDP服务已初始化')
  }

  /**
   * 清理RDP服务
   */
  public async cleanup(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    // 清理API控制器
    if (this.apiController) {
      await this.apiController.cleanup()
      this.apiController = undefined
    }

    // 关闭所有连接
    const connectionIds = Array.from(this.connections.keys())
    for (const id of connectionIds) {
      try {
        await this.disconnect(id)
      } catch (error) {
        console.warn(`[RdpService] 关闭连接失败: ${id}`, error)
      }
    }

    this.isInitialized = false
    console.log('[RdpService] RDP服务已清理')
  }

  /**
   * 建立RDP连接
   */
  public async connect(config: RdpConnectionConfig): Promise<RdpConnectionResult> {
    try {
      // 检查连接数限制
      if (this.connections.size >= this.options.maxConnections!) {
        throw new Error(`RDP连接数已达到最大限制: ${this.options.maxConnections}`)
      }

      // 检查是否已存在相同ID的连接
      if (this.connections.has(config.id)) {
        throw new Error(`RDP连接ID已存在: ${config.id}`)
      }

      console.log(`[RdpService] 开始建立RDP连接: ${config.id}`)

      // 创建RDP连接实例
      const rdpService = new RdpService(config.id, config)

      // 建立连接
      await rdpService.connect()

      // 存储连接
      this.connections.set(config.id, rdpService)

      const result: RdpConnectionResult = {
        connectionId: config.id,
        status: rdpService.status,
        message: '连接建立成功',
        sessionInfo: rdpService.sessionInfo,
        connectedAt: rdpService.startedAt
      }

      console.log(`[RdpService] RDP连接建立成功: ${config.id}`)

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error(`[RdpService] RDP连接建立失败: ${config.id}`, error)

      return {
        connectionId: config.id,
        status: 'error',
        message: `连接失败: ${errorMessage}`,
        error: error instanceof Error ? error : new Error(errorMessage)
      }
    }
  }

  /**
   * 断开RDP连接
   */
  public async disconnect(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      throw new Error(`RDP连接不存在: ${connectionId}`)
    }

    try {
      console.log(`[RdpService] 断开RDP连接: ${connectionId}`)
      await connection.disconnect()
      this.connections.delete(connectionId)
      console.log(`[RdpService] RDP连接已断开: ${connectionId}`)
    } catch (error) {
      console.error(`[RdpService] RDP连接断开失败: ${connectionId}`, error)
      throw error
    }
  }

  /**
   * 获取连接状态
   */
  public async getStatus(connectionId: string) {
    const connection = this.connections.get(connectionId)
    if (!connection) {
      return {
        connectionId,
        status: 'disconnected',
        message: 'RDP连接不存在'
      }
    }

    return {
      connectionId,
      status: connection.status,
      info: connection.getConnectionInfo(),
      isAlive: connection.isAlive()
    }
  }

  /**
   * 获取所有连接
   */
  public async listConnections() {
    const connections = Array.from(this.connections.values()).map((conn) => ({
      id: conn.id,
      status: conn.status,
      info: conn.getConnectionInfo()
    }))

    return {
      total: connections.length,
      connections
    }
  }

  /**
   * 获取连接实例
   */
  public getConnection(connectionId: string): RdpService | undefined {
    return this.connections.get(connectionId)
  }

  /**
   * 清理无效连接
   */
  public async cleanupDeadConnections(): Promise<void> {
    const deadConnections: string[] = []

    for (const [id, connection] of this.connections) {
      if (!connection.isAlive()) {
        deadConnections.push(id)
      }
    }

    if (deadConnections.length > 0) {
      console.log(`[RdpService] 清理无效RDP连接: ${deadConnections.length}个`)

      for (const id of deadConnections) {
        try {
          await this.disconnect(id)
        } catch (error) {
          console.warn(`[RdpService] 清理RDP连接时发生错误: ${id}`, error)
        }
      }
    }
  }
}

// 导出服务实例获取函数
export function getRdpService(options?: RdpManagerOptions): RdpConnectionService {
  return RdpConnectionService.getInstance(options)
}

// 导出初始化函数
export function initializeRdpService(options?: RdpManagerOptions): void {
  const service = getRdpService(options)
  service.initialize()
}

// 导出清理函数
export async function cleanupRdpService(): Promise<void> {
  const service = RdpConnectionService.getInstance()
  await service.cleanup()
}

// 导出类型和服务
export { RdpService, RdpConnectionService }
export * from './types'
