<template>
  <div 
    v-if="visible"
    class="terminal-search-box"
    :class="{ 'terminal-search-box--floating': floating }"
  >
    <div class="terminal-search-box__container">
      <!-- 搜索输入区域 -->
      <div class="terminal-search-box__input-section">
        <SearchBox
          ref="searchBoxRef"
          v-model="searchValue"
          :placeholder="placeholder"
          size="small"
          variant="compact"
          clearable
          class="terminal-search-box__input"
          @search="handleSearch"
          @input="handleInput"
          @clear="handleClear"
          @keydown="handleKeydown"
        >
          <template #prefix-icon>
            <svg 
              width="14" 
              height="14" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </template>
        </SearchBox>
        
        <!-- 搜索结果计数 -->
        <div 
          v-if="searchValue && searchResultsCount > 0"
          class="terminal-search-box__results"
        >
          {{ currentResultIndex }}/{{ searchResultsCount }}
        </div>
      </div>
      
      <!-- 控制按钮区域 -->
      <div class="terminal-search-box__controls">
        <!-- 上一个结果 -->
        <button
          type="button"
          class="terminal-search-box__button"
          :disabled="!searchValue || searchResultsCount === 0"
          :title="t('term.findPrevious')"
          @click="findPrevious"
        >
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M15 18L9 12L15 6"></path>
          </svg>
        </button>
        
        <!-- 下一个结果 -->
        <button
          type="button"
          class="terminal-search-box__button"
          :disabled="!searchValue || searchResultsCount === 0"
          :title="t('term.findNext')"
          @click="findNext"
        >
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 18L15 12L9 6"></path>
          </svg>
        </button>
        
        <!-- 搜索选项 -->
        <button
          v-if="showOptions"
          type="button"
          class="terminal-search-box__button terminal-search-box__button--options"
          :class="{ 'terminal-search-box__button--active': showOptionsPanel }"
          :title="t('term.searchOptions')"
          @click="toggleOptions"
        >
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M12 1V3"></path>
            <path d="M12 21V23"></path>
            <path d="M4.22 4.22L5.64 5.64"></path>
            <path d="M18.36 18.36L19.78 19.78"></path>
            <path d="M1 12H3"></path>
            <path d="M21 12H23"></path>
            <path d="M4.22 19.78L5.64 18.36"></path>
            <path d="M18.36 5.64L19.78 4.22"></path>
          </svg>
        </button>
        
        <!-- 关闭按钮 -->
        <button
          type="button"
          class="terminal-search-box__button terminal-search-box__button--close"
          :title="t('common.close')"
          @click="handleClose"
        >
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- 搜索选项面板 -->
    <div 
      v-if="showOptionsPanel"
      class="terminal-search-box__options-panel"
    >
      <label class="terminal-search-box__option">
        <input 
          v-model="caseSensitive"
          type="checkbox"
          @change="handleOptionsChange"
        />
        <span>{{ t('term.caseSensitive') }}</span>
      </label>
      
      <label class="terminal-search-box__option">
        <input 
          v-model="wholeWord"
          type="checkbox"
          @change="handleOptionsChange"
        />
        <span>{{ t('term.wholeWord') }}</span>
      </label>
      
      <label class="terminal-search-box__option">
        <input 
          v-model="useRegex"
          type="checkbox"
          @change="handleOptionsChange"
        />
        <span>{{ t('term.useRegex') }}</span>
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import SearchBox from './SearchBox.vue'

const { t } = useI18n()

interface Props {
  visible?: boolean
  floating?: boolean
  placeholder?: string
  showOptions?: boolean
  searchAddon?: any
  terminal?: any
}

interface Emits {
  'close': []
  'search': [term: string, options: SearchOptions]
  'find-next': []
  'find-previous': []
}

interface SearchOptions {
  caseSensitive: boolean
  wholeWord: boolean
  useRegex: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  floating: true,
  placeholder: '在终端中搜索...',
  showOptions: true
})

const emit = defineEmits<Emits>()

// 响应式数据
const searchBoxRef = ref()
const searchValue = ref('')
const searchResultsCount = ref(0)
const currentResultIndex = ref(0)
const showOptionsPanel = ref(false)
const caseSensitive = ref(false)
const wholeWord = ref(false)
const useRegex = ref(false)

// 搜索防抖
let searchTimeout: NodeJS.Timeout | null = null

// 监听搜索值变化
watch(searchValue, (newValue) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    if (newValue) {
      performSearch(newValue)
    } else {
      clearSearch()
    }
  }, 200)
})

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible) {
    nextTick(() => {
      searchBoxRef.value?.focus()
    })
  } else {
    clearSearch()
    showOptionsPanel.value = false
  }
})

// 执行搜索
const performSearch = (term: string) => {
  if (!props.searchAddon || !term) return
  
  const options = {
    caseSensitive: caseSensitive.value,
    wholeWord: wholeWord.value,
    regex: useRegex.value
  }
  
  try {
    const result = props.searchAddon.findNext(term, options)
    if (result) {
      // 计算匹配数量（这里需要根据实际的 searchAddon API 调整）
      calculateMatches(term, options)
    }
    
    emit('search', term, {
      caseSensitive: caseSensitive.value,
      wholeWord: wholeWord.value,
      useRegex: useRegex.value
    })
  } catch (error) {
    console.warn('Search failed:', error)
    searchResultsCount.value = 0
    currentResultIndex.value = 0
  }
}

// 计算匹配数量
const calculateMatches = (term: string, options: any) => {
  // 这里需要根据实际的终端内容和搜索插件来实现
  // 暂时使用模拟数据
  searchResultsCount.value = 5 // 模拟值
  currentResultIndex.value = 1 // 模拟值
}

// 清除搜索
const clearSearch = () => {
  if (props.searchAddon) {
    props.searchAddon.clearDecorations()
  }
  searchResultsCount.value = 0
  currentResultIndex.value = 0
}

// 事件处理
const handleSearch = (value: string) => {
  // 搜索逻辑在 watch 中处理
}

const handleInput = () => {
  // 输入处理逻辑
}

const handleClear = () => {
  clearSearch()
}

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      if (event.shiftKey) {
        findPrevious()
      } else {
        findNext()
      }
      break
    case 'Escape':
      event.preventDefault()
      handleClose()
      break
    case 'F3':
      event.preventDefault()
      if (event.shiftKey) {
        findPrevious()
      } else {
        findNext()
      }
      break
  }
}

const findNext = () => {
  if (!props.searchAddon || !searchValue.value) return
  
  const options = {
    caseSensitive: caseSensitive.value,
    wholeWord: wholeWord.value,
    regex: useRegex.value
  }
  
  const result = props.searchAddon.findNext(searchValue.value, options)
  if (result && currentResultIndex.value < searchResultsCount.value) {
    currentResultIndex.value++
  }
  
  emit('find-next')
}

const findPrevious = () => {
  if (!props.searchAddon || !searchValue.value) return
  
  const options = {
    caseSensitive: caseSensitive.value,
    wholeWord: wholeWord.value,
    regex: useRegex.value
  }
  
  const result = props.searchAddon.findPrevious(searchValue.value, options)
  if (result && currentResultIndex.value > 1) {
    currentResultIndex.value--
  }
  
  emit('find-previous')
}

const toggleOptions = () => {
  showOptionsPanel.value = !showOptionsPanel.value
}

const handleOptionsChange = () => {
  if (searchValue.value) {
    performSearch(searchValue.value)
  }
}

const handleClose = () => {
  emit('close')
}

// 组件挂载时聚焦
onMounted(() => {
  if (props.visible) {
    nextTick(() => {
      searchBoxRef.value?.focus()
    })
  }
})
</script>

<style lang="less" scoped>
.terminal-search-box {
  position: relative;
  background: var(--bg-color-quinary);
  border: 1px solid var(--border-color-light);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  backdrop-filter: blur(10px);
  z-index: 1000;
  
  &--floating {
    position: absolute;
    top: 10px;
    right: 10px;
    min-width: 300px;
  }
  
  &__container {
    display: flex;
    align-items: center;
    padding: 8px;
    gap: 8px;
  }
  
  &__input-section {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  &__input {
    flex: 1;
  }
  
  &__results {
    font-size: 11px;
    color: var(--text-color-tertiary);
    white-space: nowrap;
    padding: 0 4px;
  }
  
  &__controls {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  
  &__button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--text-color-tertiary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.15s ease;
    
    &:hover:not(:disabled) {
      background: var(--hover-bg-color);
      color: var(--text-color-secondary);
    }
    
    &:active:not(:disabled) {
      transform: scale(0.95);
    }
    
    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }
    
    &--active {
      background: var(--active-bg-color);
      color: var(--text-color);
    }
    
    &--close {
      color: var(--error-color);
      
      &:hover {
        background: rgba(239, 68, 68, 0.1);
        color: var(--error-color);
      }
    }
  }
  
  &__options-panel {
    border-top: 1px solid var(--border-color);
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  &__option {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-color-secondary);
    cursor: pointer;
    
    input[type="checkbox"] {
      margin: 0;
    }
    
    &:hover {
      color: var(--text-color);
    }
  }
}

// 主题适配
.theme-dark {
  .terminal-search-box {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(65, 65, 65, 0.8);
  }
}

.theme-light {
  .terminal-search-box {
    background: rgba(245, 245, 245, 0.95);
    border-color: rgba(232, 232, 232, 0.8);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .terminal-search-box {
    &--floating {
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      min-width: auto;
    }
    
    &__container {
      flex-wrap: wrap;
    }
    
    &__input-section {
      flex: 1;
      min-width: 200px;
    }
  }
}
</style>
