/**
 * PostgreSQL数据库连接测试脚本
 * 功能：测试primary-db数据库连接并初始化表结构
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { PostgreSQLService } from './postgresql.service'
import { primaryDbConfig, validateConfig } from './postgresql.config'

/**
 * 测试数据库连接
 */
async function testDatabaseConnection(): Promise<void> {
  console.log('=== PostgreSQL数据库连接测试 ===')

  // 验证配置
  if (!validateConfig(primaryDbConfig)) {
    console.error('❌ 数据库配置不完整')
    return
  }

  console.log('📋 数据库配置信息:')
  console.log(`   主机: ${primaryDbConfig.host}`)
  console.log(`   端口: ${primaryDbConfig.port}`)
  console.log(`   数据库: ${primaryDbConfig.database}`)
  console.log(`   用户名: ${primaryDbConfig.username}`)
  console.log(`   SSL: ${primaryDbConfig.ssl ? '启用' : '禁用'}`)
  console.log(`   连接池大小: ${primaryDbConfig.poolSize}`)

  const pgService = new PostgreSQLService()

  try {
    console.log('\n🔄 正在连接数据库...')
    const connected = await pgService.connect(primaryDbConfig)

    if (connected) {
      console.log('✅ 数据库连接成功!')

      // 测试连接状态
      console.log('\n🔄 测试连接状态...')
      const isHealthy = await pgService.testConnection()
      console.log(`连接状态: ${isHealthy ? '✅ 健康' : '❌ 异常'}`)

      // 显示连接信息
      const connectionInfo = pgService.getConnectionInfo()
      console.log('\n📊 连接信息:')
      console.log(JSON.stringify(connectionInfo, null, 2))

      // 测试查询
      console.log('\n🔄 执行测试查询...')
      try {
        const result = await pgService.query('SELECT version() as version, current_database() as database, current_user as user')
        console.log('✅ 查询成功:')
        console.log(`   PostgreSQL版本: ${result.rows[0].version}`)
        console.log(`   当前数据库: ${result.rows[0].database}`)
        console.log(`   当前用户: ${result.rows[0].user}`)
      } catch (queryError) {
        console.error('❌ 查询失败:', queryError)
      }

      // 检查表是否存在
      console.log('\n🔄 检查表结构...')
      try {
        const tablesResult = await pgService.query(`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name IN ('t_assets', 't_organizations', 't_organization_assets', 't_custom_folders', 't_asset_folder_mapping', 't_asset_chains')
          ORDER BY table_name
        `)

        const existingTables = tablesResult.rows.map((row: any) => row.table_name)
        console.log(`✅ 已存在的表 (${existingTables.length}个):`, existingTables)

        // 检查每个表的记录数
        for (const tableName of existingTables) {
          try {
            const countResult = await pgService.query(`SELECT COUNT(*) as count FROM ${tableName}`)
            console.log(`   ${tableName}: ${countResult.rows[0].count} 条记录`)
          } catch (countError) {
            console.log(`   ${tableName}: 无法获取记录数 (${countError})`)
          }
        }
      } catch (tableError) {
        console.error('❌ 检查表结构失败:', tableError)
      }
    } else {
      console.error('❌ 数据库连接失败')
    }
  } catch (error) {
    console.error('❌ 连接过程中发生错误:', error)
  } finally {
    // 断开连接
    console.log('\n🔄 断开数据库连接...')
    await pgService.disconnect()
    console.log('✅ 连接已断开')
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  try {
    await testDatabaseConnection()
  } catch (error) {
    console.error('❌ 测试过程中发生未处理的错误:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

// 导出用于在主进程中调用的函数
export async function runPostgreSQLTest(): Promise<void> {
  await main()
}

export { testDatabaseConnection }
