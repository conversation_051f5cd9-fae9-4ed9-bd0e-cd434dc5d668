<!--
 * Console连接配置组件
 * 功能：提供Console连接的配置界面和连接管理
 * 作者：SOLO Coding
 * 修改时间：2025-01-10
-->

<template>
  <div class="console-connector">
    <div class="connector-header">
      <div class="header-title">
        <CodeOutlined class="title-icon" />
        <span>Console连接配置</span>
      </div>
      <div class="header-actions">
        <a-button
          v-if="!isConnected"
          type="primary"
          :loading="isConnecting"
          @click="handleConnect"
        >
          <PlayCircleOutlined />
          连接
        </a-button>
        <a-button
          v-else
          danger
          @click="handleDisconnect"
        >
          <StopOutlined />
          断开
        </a-button>
      </div>
    </div>

    <div class="connector-content">
      <!-- Console类型选择 -->
      <div class="config-section">
        <div class="section-title">
          <div class="title-indicator"></div>
          连接类型
        </div>

        <a-radio-group
          v-model:value="config.console_type"
          button-style="solid"
          style="width: 100%"
          :disabled="isConnected"
          @change="handleTypeChange"
        >
          <a-radio-button value="serial">
            <UsbOutlined style="margin-right: 4px" />
            串口
          </a-radio-button>
          <a-radio-button value="telnet">
            <GlobalOutlined style="margin-right: 4px" />
            Telnet
          </a-radio-button>
          <a-radio-button value="ssh">
            <DesktopOutlined style="margin-right: 4px" />
            SSH
          </a-radio-button>
        </a-radio-group>
      </div>

      <!-- 基本连接信息 -->
      <div
        v-if="config.console_type !== 'serial'"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          连接信息
        </div>

        <a-form layout="vertical">
          <a-row :gutter="16">
            <a-col :span="16">
              <a-form-item label="主机地址">
                <a-input
                  v-model:value="config.host"
                  placeholder="请输入IP地址或域名"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="端口">
                <a-input-number
                  v-model:value="config.port"
                  :min="1"
                  :max="65535"
                  style="width: 100%"
                  :disabled="isConnected"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="流控制">
                <a-select
                  v-model:value="config.serial_config.flowControl"
                  placeholder="流控制"
                  :disabled="isConnected"
                >
                  <a-select-option value="none">无 (None)</a-select-option>
                  <a-select-option value="rts_cts">RTS/CTS</a-select-option>
                  <a-select-option value="xon_xoff">XON/XOFF</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 串口配置 -->
      <div
        v-if="config.console_type === 'serial'"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          串口配置
        </div>

        <a-form layout="vertical">
          <a-form-item label="设备路径">
            <a-input
              v-model:value="config.serial_config.device"
              placeholder="例如: /dev/ttyUSB0 或 COM1"
              :disabled="isConnected"
            />
          </a-form-item>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="波特率">
                <a-select
                  v-model:value="config.serial_config.baudRate"
                  placeholder="选择波特率"
                  :disabled="isConnected"
                >
                  <a-select-option :value="1200">1200</a-select-option>
                  <a-select-option :value="2400">2400</a-select-option>
                  <a-select-option :value="4800">4800</a-select-option>
                  <a-select-option :value="9600">9600</a-select-option>
                  <a-select-option :value="19200">19200</a-select-option>
                  <a-select-option :value="38400">38400</a-select-option>
                  <a-select-option :value="57600">57600</a-select-option>
                  <a-select-option :value="115200">115200</a-select-option>
                  <a-select-option :value="230400">230400</a-select-option>
                  <a-select-option :value="460800">460800</a-select-option>
                  <a-select-option :value="921600">921600</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="数据位">
                <a-select
                  v-model:value="config.serial_config.dataBits"
                  placeholder="数据位"
                  :disabled="isConnected"
                >
                  <a-select-option :value="5">5</a-select-option>
                  <a-select-option :value="6">6</a-select-option>
                  <a-select-option :value="7">7</a-select-option>
                  <a-select-option :value="8">8</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="停止位">
                <a-select
                  v-model:value="config.serial_config.stopBits"
                  placeholder="停止位"
                  :disabled="isConnected"
                >
                  <a-select-option :value="1">1</a-select-option>
                  <a-select-option :value="1.5">1.5</a-select-option>
                  <a-select-option :value="2">2</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="校验位">
                <a-select
                  v-model:value="config.serial_config.parity"
                  placeholder="校验位"
                  :disabled="isConnected"
                >
                  <a-select-option value="none">无 (None)</a-select-option>
                  <a-select-option value="even">偶校验 (Even)</a-select-option>
                  <a-select-option value="odd">奇校验 (Odd)</a-select-option>
                  <a-select-option value="mark">标记 (Mark)</a-select-option>
                  <a-select-option value="space">空格 (Space)</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- Telnet配置 -->
      <div
        v-if="config.console_type === 'telnet'"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          Telnet配置
        </div>

        <a-form layout="vertical">
          <a-form-item label="超时时间(秒)">
            <a-input-number
              v-model:value="config.telnet_config.timeout"
              :min="1"
              :max="300"
              placeholder="连接超时时间"
              style="width: 100%"
              :disabled="isConnected"
            />
          </a-form-item>
        </a-form>
      </div>

      <!-- SSH认证配置 -->
      <div
        v-if="config.console_type === 'ssh'"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          认证信息
        </div>

        <a-form layout="vertical">
          <a-form-item label="用户名">
            <a-input
              v-model:value="config.username"
              placeholder="请输入用户名"
              :disabled="isConnected"
            />
          </a-form-item>

          <a-form-item label="密码">
            <a-input-password
              v-model:value="config.password"
              placeholder="请输入密码"
              :disabled="isConnected"
            />
          </a-form-item>
        </a-form>
      </div>

      <!-- 连接状态 -->
      <div
        v-if="connectionStatus"
        class="config-section"
      >
        <div class="section-title">
          <div class="title-indicator"></div>
          连接状态
        </div>

        <div class="status-info">
          <div class="status-item">
            <span class="status-label">状态:</span>
            <a-tag :color="getStatusColor(connectionStatus.status)">
              {{ getStatusText(connectionStatus.status) }}
            </a-tag>
          </div>

          <div
            v-if="connectionStatus.connectedAt"
            class="status-item"
          >
            <span class="status-label">连接时间:</span>
            <span>{{ formatTime(connectionStatus.connectedAt) }}</span>
          </div>

          <div
            v-if="connectionStatus.error"
            class="status-item"
          >
            <span class="status-label">错误信息:</span>
            <span class="error-text">{{ connectionStatus.error }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { CodeOutlined, PlayCircleOutlined, StopOutlined, UsbOutlined, GlobalOutlined, DesktopOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// 接口定义
interface ConsoleConfig {
  console_type: 'serial' | 'telnet' | 'ssh'
  host?: string
  port?: number
  username?: string
  password?: string
  serial_config: {
    device?: string
    baudRate?: number
    dataBits?: number
    stopBits?: number
    parity?: 'none' | 'even' | 'odd' | 'mark' | 'space'
    flowControl?: 'none' | 'rts_cts' | 'xon_xoff'
  }
  telnet_config: {
    timeout?: number
  }
}

interface ConnectionStatus {
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  connectedAt?: Date
  error?: string
}

interface Props {
  assetId?: string
  initialConfig?: Partial<ConsoleConfig>
}

const props = withDefaults(defineProps<Props>(), {
  initialConfig: () => ({})
})

const emit = defineEmits<{
  connect: [config: ConsoleConfig]
  disconnect: []
  'config-change': [config: ConsoleConfig]
}>()

// 响应式数据
const isConnecting = ref(false)
const isConnected = ref(false)
const connectionStatus = ref<ConnectionStatus | null>(null)

const config = reactive<ConsoleConfig>({
  console_type: 'telnet',
  host: '',
  port: 23,
  username: '',
  password: '',
  serial_config: {
    device: '',
    baudRate: 9600,
    dataBits: 8,
    stopBits: 1,
    parity: 'none',
    flowControl: 'none'
  },
  telnet_config: {
    timeout: 30
  },
  ...props.initialConfig
})

// 监听配置变化
watch(
  () => config,
  (newConfig) => {
    emit('config-change', { ...newConfig })
  },
  { deep: true }
)

// 方法
const handleTypeChange = () => {
  // 根据Console类型设置默认端口
  switch (config.console_type) {
    case 'telnet':
      config.port = 23
      break
    case 'ssh':
      config.port = 22
      break
    case 'serial':
      config.port = undefined
      break
  }
}

const validateConfig = (): boolean => {
  if (config.console_type === 'serial') {
    if (!config.serial_config.device) {
      message.error('请输入设备路径')
      return false
    }
  } else {
    if (!config.host) {
      message.error('请输入主机地址')
      return false
    }
    if (!config.port || config.port <= 0) {
      message.error('请输入有效的端口号')
      return false
    }
  }

  if (config.console_type === 'ssh') {
    if (!config.username) {
      message.error('请输入用户名')
      return false
    }
    if (!config.password) {
      message.error('请输入密码')
      return false
    }
  }

  return true
}

const handleConnect = async () => {
  if (!validateConfig()) return

  isConnecting.value = true
  connectionStatus.value = {
    status: 'connecting'
  }

  try {
    // 调用后端API建立连接
    const response = await window.electronAPI?.consoleConnect({
      assetId: props.assetId,
      type: config.console_type,
      config: config
    })

    if (response?.success) {
      isConnected.value = true
      connectionStatus.value = {
        status: 'connected',
        connectedAt: new Date()
      }
      message.success('Console连接成功')
      emit('connect', config)
    } else {
      throw new Error(response?.message || '连接失败')
    }
  } catch (error: any) {
    connectionStatus.value = {
      status: 'error',
      error: error.message
    }
    message.error(`连接失败: ${error.message}`)
  } finally {
    isConnecting.value = false
  }
}

const handleDisconnect = async () => {
  try {
    await window.electronAPI?.consoleDisconnect(props.assetId)

    isConnected.value = false
    connectionStatus.value = {
      status: 'disconnected'
    }
    message.success('Console连接已断开')
    emit('disconnect')
  } catch (error: any) {
    message.error(`断开连接失败: ${error.message}`)
  }
}

const getStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
      return 'green'
    case 'connecting':
      return 'blue'
    case 'error':
      return 'red'
    case 'disconnected':
      return 'default'
    default:
      return 'default'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'connected':
      return '已连接'
    case 'connecting':
      return '连接中'
    case 'error':
      return '连接错误'
    case 'disconnected':
      return '已断开'
    default:
      return '未知状态'
  }
}

const formatTime = (date: Date): string => {
  return date.toLocaleString('zh-CN')
}
</script>

<style lang="less" scoped>
.console-connector {
  background: var(--bg-color);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.connector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.title-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.connector-content {
  padding: 20px;
}

.config-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
}

.title-indicator {
  width: 3px;
  height: 14px;
  background: var(--primary-color);
  margin-right: 8px;
  border-radius: 2px;
}

.status-info {
  background: var(--bg-color-secondary);
  padding: 16px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.status-label {
  min-width: 80px;
  color: var(--text-color-secondary);
  font-size: 13px;
}

.error-text {
  color: var(--error-color);
  font-size: 13px;
}

// 深色主题适配
:deep(.ant-radio-button-wrapper) {
  background: var(--bg-color);
  border-color: var(--border-color);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &.ant-radio-button-wrapper-checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
  }
}

:deep(.ant-input),
:deep(.ant-input-number),
:deep(.ant-select-selector),
:deep(.ant-input-password) {
  background: var(--bg-color-secondary);
  border-color: var(--border-color);
  color: var(--text-color);

  &:hover {
    border-color: var(--primary-color);
  }

  &:focus,
  &.ant-input-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

:deep(.ant-form-item-label > label) {
  color: var(--text-color);
}
</style>
