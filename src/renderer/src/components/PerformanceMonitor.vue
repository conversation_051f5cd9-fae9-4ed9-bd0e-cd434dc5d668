<template>
  <div class="performance-monitor-wrapper">
    <div
      v-if="showMonitor"
      class="performance-monitor"
    >
      <div class="monitor-header">
        <h3>性能监控</h3>
        <button
          class="close-btn"
          @click="closePanel"
          >×</button
        >
      </div>
      <div class="monitor-content">
        <div class="metrics-section">
          <h4>实时指标</h4>
          <div class="metrics-grid">
            <div class="metric-item">
              <span class="metric-label">内存使用</span>
              <span class="metric-value">{{ formatMemory(terminalMetrics.memoryUsage) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">行数</span>
              <span class="metric-value">{{ terminalMetrics.lineCount }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">渲染时间</span>
              <span class="metric-value">{{ terminalMetrics.renderTime.toFixed(2) }}ms</span>
            </div>
          </div>
        </div>
        <div class="actions-section">
          <button
            class="action-btn"
            @click="clearMetrics"
            >清空指标</button
          >
          <button
            class="action-btn"
            @click="exportData"
            >导出数据</button
          >
          <button
            class="action-btn primary"
            @click="optimizeTerminal"
            >优化终端</button
          >
        </div>
      </div>
    </div>
    <div
      v-if="!showMonitor"
      class="monitor-toggle"
      @click="toggleMonitor"
    >
      <span class="monitor-icon">📊</span>
      <span class="monitor-text">性能</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  terminalOptimizer?: any
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
}>()

const showMonitor = ref(true) // 默认显示，因为是通过工具栏打开的
const terminalMetrics = ref({
  memoryUsage: 0,
  lineCount: 0,
  renderTime: 0,
  lastCleanup: 0
})

let updateInterval: NodeJS.Timeout | null = null

onMounted(() => {
  startMonitoring()
})

onUnmounted(() => {
  stopMonitoring()
})

const startMonitoring = () => {
  updateInterval = setInterval(() => {
    updateMetrics()
  }, 1000)
}

const stopMonitoring = () => {
  if (updateInterval) {
    clearInterval(updateInterval)
    updateInterval = null
  }
}

const updateMetrics = () => {
  if (props.terminalOptimizer) {
    terminalMetrics.value = props.terminalOptimizer.getMetrics()
  }
}

const toggleMonitor = () => {
  if (showMonitor.value) {
    // 如果当前显示，则关闭并emit close事件
    emit('close')
  } else {
    showMonitor.value = true
  }
}

const closePanel = () => {
  console.log('PerformanceMonitor closePanel clicked')
  emit('close')
}

const clearMetrics = () => {
  if (props.terminalOptimizer) {
    props.terminalOptimizer.clear()
  }
}

const exportData = () => {
  const data = {
    terminal: terminalMetrics.value,
    timestamp: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-data-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const optimizeTerminal = () => {
  if (props.terminalOptimizer) {
    const metrics = props.terminalOptimizer.getMetrics()

    if (metrics.memoryUsage > 50) {
      console.log('[Performance] 执行内存优化...')
    }

    if (metrics.lineCount > 5000) {
      console.log('[Performance] 执行行数优化...')
    }
  }
}

const formatMemory = (bytes: number): string => {
  if (bytes < 1024) return `${bytes.toFixed(1)} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<style scoped lang="less">
.performance-monitor-wrapper {
  position: relative;
}

.performance-monitor {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 12px;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #999;

    &:hover {
      color: #666;
    }
  }
}

.monitor-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.metrics-section,
.actions-section {
  margin-bottom: 16px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    font-weight: 600;
    color: #333;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.metric-label {
  color: #666;
  font-size: 11px;
}

.metric-value {
  font-weight: 600;
  font-size: 11px;
}

.actions-section {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }

  &.primary {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
    }
  }
}

.monitor-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 999;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .monitor-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .monitor-text {
    font-size: 10px;
    color: white;
    font-weight: 500;
  }
}
</style>
