# SMB文件操作工具使用说明

## 概述

这是一个基于Node.js的SMB文件操作工具，从Python版本转换而来。提供文件扫描、共享和远程拷贝功能，支持企业域环境的SMB连接。

## 功能特性

### 🔍 文件扫描功能
- 递归扫描指定目录
- 按文件名和扩展名过滤
- 按父目录分组显示结果
- 支持自定义扫描条件

### 📡 文件共享功能
- 扫描本地文件并准备共享
- 显示本机IP地址
- 提供共享访问指导
- 支持多种文件类型

### 📥 远程文件拷贝
- 连接远程SMB服务器
- 下载共享文件到本地
- 支持域认证
- 自动创建本地目录结构

## 安装依赖

```bash
# 安装SMB客户端库
npm install @marsaud/smb2

# 或者使用yarn
yarn add @marsaud/smb2
```

## 使用方法

### 1. 交互式运行

```bash
# 启动交互式界面
node smb-file-operations.js
```

按提示选择操作模式：
- **选项1**: 旧电脑端 - 扫描文件并准备共享
- **选项2**: 新电脑端 - 从远程SMB服务器拷贝文件

### 2. 编程方式使用

```javascript
const { 
  scanFiles, 
  copySMBFilesFromOldToNew,
  initSMBProtocol 
} = require('./smb-file-operations');

// 扫描本地文件
const files = scanFiles(
  '/path/to/scan',           // 扫描目录
  ['config.txt', 'data.json'], // 目标文件名
  ['.txt', '.json', '.pdf']    // 目标扩展名
);

// 从远程SMB服务器拷贝文件
await copySMBFilesFromOldToNew(
  '************',              // 远程IP
  '<EMAIL>',    // 用户名
  'password',                   // 密码
  './downloads',               // 本地保存路径
  'brbiotech',                 // 域名
  'public'                     // 共享名
);
```

## 配置参数

### SMB连接配置

| 参数 | 说明 | 示例 |
|------|------|------|
| host | SMB服务器IP地址 | `************` |
| username | 用户名（支持邮箱格式） | `<EMAIL>` |
| password | 密码 | `your-password` |
| domain | 域名 | `brbiotech` |
| share | 共享文件夹名 | `public` 或 `it部` |
| port | 端口号（默认445） | `445` |

### 文件扫描配置

| 参数 | 说明 | 示例 |
|------|------|------|
| targetFolder | 扫描的根目录 | `/Users/<USER>/Documents` |
| targetFiles | 目标文件名列表 | `['config.txt', 'data.json']` |
| targetExt | 目标扩展名列表 | `['.txt', '.pdf', '.docx']` |

## 测试功能

```bash
# 运行功能测试
node test-smb-file-operations.js
```

测试包括：
- ✅ 本地文件扫描功能
- ✅ SMB连接配置验证
- ✅ 文件拷贝功能模拟

## 使用场景

### 场景1：旧电脑文件整理
1. 在旧电脑上运行工具
2. 选择"扫描文件并准备共享"
3. 配置扫描参数（目录、文件类型等）
4. 查看扫描结果和共享建议

### 场景2：新电脑文件迁移
1. 在新电脑上运行工具
2. 选择"从远程SMB服务器拷贝文件"
3. 输入旧电脑的IP地址和认证信息
4. 指定本地保存路径
5. 等待文件下载完成

## 错误处理

### 常见问题及解决方案

#### 1. 连接失败
```
❌ SMB操作失败: connect ECONNREFUSED
```
**解决方案：**
- 检查网络连接
- 确认SMB服务器IP地址正确
- 检查防火墙设置
- 确认SMB服务已启动

#### 2. 认证失败
```
❌ SMB操作失败: STATUS_LOGON_FAILURE
```
**解决方案：**
- 检查用户名和密码
- 确认域名设置正确
- 尝试不同的用户名格式（如：`username` vs `<EMAIL>`）

#### 3. 权限不足
```
❌ SMB操作失败: STATUS_ACCESS_DENIED
```
**解决方案：**
- 检查用户是否有访问共享文件夹的权限
- 联系管理员分配适当权限
- 尝试使用管理员账户

#### 4. 共享不存在
```
❌ SMB操作失败: STATUS_BAD_NETWORK_NAME
```
**解决方案：**
- 确认共享文件夹名称正确
- 检查共享文件夹是否已启用
- 使用 `smbutil view //server-ip` 查看可用共享

## 安全注意事项

1. **密码安全**
   - 不要在代码中硬编码密码
   - 使用环境变量或配置文件存储敏感信息
   - 定期更换密码

2. **网络安全**
   - 仅在可信网络环境中使用
   - 考虑使用VPN连接
   - 避免在公共网络上传输敏感文件

3. **权限控制**
   - 使用最小权限原则
   - 定期审查用户权限
   - 监控文件访问日志

## 技术架构

### 核心依赖
- **@marsaud/smb2**: SMB协议客户端库
- **fs**: Node.js文件系统模块
- **path**: 路径处理模块
- **readline**: 交互式输入模块
- **os**: 操作系统信息模块

### 主要模块

```
smb-file-operations.js
├── initSMBProtocol()     # SMB连接初始化
├── scanFiles()           # 本地文件扫描
├── scanFilesAndShare()   # 文件扫描和共享准备
├── copySMBFilesFromOldToNew() # 远程文件拷贝
└── main()                # 交互式主程序
```

## 性能优化建议

1. **大文件处理**
   - 使用流式传输避免内存溢出
   - 实现断点续传功能
   - 添加进度显示

2. **并发控制**
   - 限制同时下载的文件数量
   - 使用连接池管理SMB连接
   - 实现队列机制

3. **错误恢复**
   - 添加重试机制
   - 实现自动重连
   - 记录详细错误日志

## 扩展功能

### 可能的增强功能
- 📊 文件传输进度显示
- 🔄 断点续传支持
- 📝 操作日志记录
- 🎯 文件过滤规则配置
- 📦 批量操作支持
- 🔐 加密传输选项

## 许可证

本工具基于现有项目开发，遵循项目原有许可证。

## 支持

如有问题或建议，请联系开发团队或提交Issue。

---

**最后更新**: 2025-01-15  
**版本**: 1.0.0  
**作者**: SOLO Coding