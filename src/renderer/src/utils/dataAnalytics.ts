/**
 * 数据分析系统
 * 收集和分析用户行为数据，提供智能洞察
 */

export interface UserAction {
  id: string
  type: 'command' | 'connection' | 'file_transfer' | 'navigation' | 'error'
  timestamp: Date
  userId: string
  sessionId: string
  data: Record<string, any>
  metadata?: {
    ip?: string
    userAgent?: string
    location?: string
  }
}

export interface AnalyticsMetrics {
  totalSessions: number
  totalCommands: number
  totalConnections: number
  averageSessionDuration: number
  mostUsedCommands: Array<{ command: string; count: number }>
  connectionSuccess: number
  connectionFailure: number
  errorRate: number
  peakUsageHours: number[]
  userGrowth: Array<{ date: string; users: number }>
}

export interface UserBehaviorPattern {
  userId: string
  patterns: {
    preferredCommands: string[]
    workingHours: { start: number; end: number }
    connectionTypes: Record<string, number>
    errorPatterns: Array<{ error: string; frequency: number }>
    efficiency: {
      commandsPerMinute: number
      errorRate: number
      sessionDuration: number
    }
  }
  recommendations: string[]
}

export interface SystemInsight {
  type: 'performance' | 'security' | 'usage' | 'optimization'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  data: Record<string, any>
  recommendations: string[]
  createdAt: Date
}

/**
 * 数据分析管理器
 */
export class DataAnalyticsManager {
  private static instance: DataAnalyticsManager
  private actions: UserAction[] = []
  private insights: SystemInsight[] = []
  private isCollectionEnabled: boolean = true
  private privacyMode: boolean = false
  private eventListeners: Map<string, Function[]> = new Map()

  static getInstance(): DataAnalyticsManager {
    if (!DataAnalyticsManager.instance) {
      DataAnalyticsManager.instance = new DataAnalyticsManager()
    }
    return DataAnalyticsManager.instance
  }

  /**
   * 记录用户行为
   */
  trackAction(action: Omit<UserAction, 'id' | 'timestamp'>): void {
    if (!this.isCollectionEnabled) return

    const fullAction: UserAction = {
      id: this.generateId(),
      timestamp: new Date(),
      ...action
    }

    // 隐私模式下过滤敏感数据
    if (this.privacyMode) {
      fullAction.data = this.sanitizeData(fullAction.data)
    }

    this.actions.push(fullAction)
    this.emit('actionTracked', fullAction)

    // 限制内存中的数据量
    if (this.actions.length > 10000) {
      this.actions = this.actions.slice(-5000)
    }

    // 实时分析
    this.performRealTimeAnalysis(fullAction)
  }

  /**
   * 清理敏感数据
   */
  private sanitizeData(data: Record<string, any>): Record<string, any> {
    const sanitized = { ...data }

    // 移除敏感字段
    const sensitiveFields = ['password', 'key', 'token', 'secret', 'credential']
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    })

    // 脱敏IP地址
    if (sanitized.ip) {
      sanitized.ip = this.maskIP(sanitized.ip)
    }

    return sanitized
  }

  /**
   * IP地址脱敏
   */
  private maskIP(ip: string): string {
    const parts = ip.split('.')
    if (parts.length === 4) {
      return `${parts[0]}.${parts[1]}.xxx.xxx`
    }
    return 'xxx.xxx.xxx.xxx'
  }

  /**
   * 实时分析
   */
  private performRealTimeAnalysis(action: UserAction): void {
    // 检测异常行为
    this.detectAnomalies(action)

    // 性能监控
    this.monitorPerformance(action)

    // 安全分析
    this.analyzeSecurityEvents(action)
  }

  /**
   * 异常检测
   */
  private detectAnomalies(action: UserAction): void {
    const recentActions = this.getRecentActions(action.userId, 300000) // 5分钟内

    // 检测命令频率异常
    if (action.type === 'command') {
      const commandCount = recentActions.filter((a) => a.type === 'command').length
      if (commandCount > 100) {
        // 5分钟内超过100个命令
        this.generateInsight({
          type: 'performance',
          title: '命令频率异常',
          description: `用户 ${action.userId} 在5分钟内执行了 ${commandCount} 个命令，可能存在自动化脚本或异常行为`,
          severity: 'medium',
          data: { userId: action.userId, commandCount, timeWindow: '5min' },
          recommendations: ['检查是否为自动化脚本', '监控系统资源使用', '考虑限制命令频率']
        })
      }
    }

    // 检测连接失败率
    if (action.type === 'connection') {
      const connectionActions = recentActions.filter((a) => a.type === 'connection')
      const failedConnections = connectionActions.filter((a) => a.data.status === 'failed').length
      const failureRate = failedConnections / connectionActions.length

      if (failureRate > 0.5 && connectionActions.length > 5) {
        this.generateInsight({
          type: 'security',
          title: '连接失败率过高',
          description: `用户 ${action.userId} 的连接失败率为 ${(failureRate * 100).toFixed(1)}%`,
          severity: 'high',
          data: { userId: action.userId, failureRate, attempts: connectionActions.length },
          recommendations: ['检查网络连接', '验证凭据有效性', '检查防火墙设置', '监控可能的攻击行为']
        })
      }
    }
  }

  /**
   * 性能监控
   */
  private monitorPerformance(action: UserAction): void {
    if (action.data.duration && action.data.duration > 10000) {
      // 超过10秒
      this.generateInsight({
        type: 'performance',
        title: '操作响应时间过长',
        description: `${action.type} 操作耗时 ${action.data.duration}ms，超过正常范围`,
        severity: 'medium',
        data: { actionType: action.type, duration: action.data.duration },
        recommendations: ['检查网络延迟', '优化系统性能', '检查资源使用情况']
      })
    }
  }

  /**
   * 安全事件分析
   */
  private analyzeSecurityEvents(action: UserAction): void {
    if (action.type === 'error' && action.data.error) {
      const errorMessage = action.data.error.toLowerCase()

      // 检测潜在的安全威胁
      const securityKeywords = ['permission denied', 'access denied', 'unauthorized', 'forbidden']
      if (securityKeywords.some((keyword) => errorMessage.includes(keyword))) {
        this.generateInsight({
          type: 'security',
          title: '安全相关错误',
          description: `检测到安全相关错误: ${action.data.error}`,
          severity: 'medium',
          data: { error: action.data.error, userId: action.userId },
          recommendations: ['检查用户权限', '验证访问控制', '监控异常访问行为']
        })
      }
    }
  }

  /**
   * 生成系统洞察
   */
  private generateInsight(insight: Omit<SystemInsight, 'createdAt'>): void {
    const fullInsight: SystemInsight = {
      ...insight,
      createdAt: new Date()
    }

    this.insights.push(fullInsight)
    this.emit('insightGenerated', fullInsight)

    // 限制洞察数量
    if (this.insights.length > 1000) {
      this.insights = this.insights.slice(-500)
    }
  }

  /**
   * 获取分析指标
   */
  getAnalyticsMetrics(timeRange?: { start: Date; end: Date }): AnalyticsMetrics {
    let filteredActions = this.actions

    if (timeRange) {
      filteredActions = this.actions.filter((action) => action.timestamp >= timeRange.start && action.timestamp <= timeRange.end)
    }

    const sessions = new Set(filteredActions.map((a) => a.sessionId))
    const commands = filteredActions.filter((a) => a.type === 'command')
    const connections = filteredActions.filter((a) => a.type === 'connection')
    const errors = filteredActions.filter((a) => a.type === 'error')

    // 计算最常用命令
    const commandCounts = new Map<string, number>()
    commands.forEach((cmd) => {
      const command = cmd.data.command || 'unknown'
      commandCounts.set(command, (commandCounts.get(command) || 0) + 1)
    })

    const mostUsedCommands = Array.from(commandCounts.entries())
      .map(([command, count]) => ({ command, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // 计算连接成功率
    const successfulConnections = connections.filter((c) => c.data.status === 'success').length
    const failedConnections = connections.filter((c) => c.data.status === 'failed').length

    // 计算平均会话时长
    const sessionDurations = this.calculateSessionDurations(filteredActions)
    const averageSessionDuration =
      sessionDurations.length > 0 ? sessionDurations.reduce((sum, duration) => sum + duration, 0) / sessionDurations.length : 0

    // 计算错误率
    const errorRate = filteredActions.length > 0 ? errors.length / filteredActions.length : 0

    // 计算峰值使用时间
    const hourlyUsage = new Array(24).fill(0)
    filteredActions.forEach((action) => {
      const hour = action.timestamp.getHours()
      hourlyUsage[hour]++
    })
    const maxUsage = Math.max(...hourlyUsage)
    const peakUsageHours = hourlyUsage
      .map((usage, hour) => ({ hour, usage }))
      .filter(({ usage }) => usage > maxUsage * 0.8)
      .map(({ hour }) => hour)

    return {
      totalSessions: sessions.size,
      totalCommands: commands.length,
      totalConnections: connections.length,
      averageSessionDuration,
      mostUsedCommands,
      connectionSuccess: successfulConnections,
      connectionFailure: failedConnections,
      errorRate,
      peakUsageHours,
      userGrowth: this.calculateUserGrowth(filteredActions)
    }
  }

  /**
   * 计算会话时长
   */
  private calculateSessionDurations(actions: UserAction[]): number[] {
    const sessionMap = new Map<string, { start: Date; end: Date }>()

    actions.forEach((action) => {
      const session = sessionMap.get(action.sessionId)
      if (!session) {
        sessionMap.set(action.sessionId, { start: action.timestamp, end: action.timestamp })
      } else {
        if (action.timestamp < session.start) session.start = action.timestamp
        if (action.timestamp > session.end) session.end = action.timestamp
      }
    })

    return Array.from(sessionMap.values()).map((session) => session.end.getTime() - session.start.getTime())
  }

  /**
   * 计算用户增长
   */
  private calculateUserGrowth(actions: UserAction[]): Array<{ date: string; users: number }> {
    const dailyUsers = new Map<string, Set<string>>()

    actions.forEach((action) => {
      const date = action.timestamp.toISOString().split('T')[0]
      if (!dailyUsers.has(date)) {
        dailyUsers.set(date, new Set())
      }
      dailyUsers.get(date)!.add(action.userId)
    })

    return Array.from(dailyUsers.entries())
      .map(([date, users]) => ({ date, users: users.size }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }

  /**
   * 获取用户行为模式
   */
  getUserBehaviorPattern(userId: string): UserBehaviorPattern {
    const userActions = this.actions.filter((action) => action.userId === userId)

    // 分析偏好命令
    const commandCounts = new Map<string, number>()
    userActions
      .filter((a) => a.type === 'command')
      .forEach((cmd) => {
        const command = cmd.data.command || 'unknown'
        commandCounts.set(command, (commandCounts.get(command) || 0) + 1)
      })

    const preferredCommands = Array.from(commandCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([command]) => command)

    // 分析工作时间
    const workingHours = this.analyzeWorkingHours(userActions)

    // 分析连接类型
    const connectionTypes = new Map<string, number>()
    userActions
      .filter((a) => a.type === 'connection')
      .forEach((conn) => {
        const type = conn.data.type || 'unknown'
        connectionTypes.set(type, (connectionTypes.get(type) || 0) + 1)
      })

    // 分析错误模式
    const errorCounts = new Map<string, number>()
    userActions
      .filter((a) => a.type === 'error')
      .forEach((err) => {
        const error = err.data.error || 'unknown'
        errorCounts.set(error, (errorCounts.get(error) || 0) + 1)
      })

    const errorPatterns = Array.from(errorCounts.entries())
      .map(([error, frequency]) => ({ error, frequency }))
      .sort((a, b) => b.frequency - a.frequency)

    // 计算效率指标
    const efficiency = this.calculateUserEfficiency(userActions)

    // 生成建议
    const recommendations = this.generateUserRecommendations(userActions, {
      preferredCommands,
      workingHours,
      errorPatterns,
      efficiency
    })

    return {
      userId,
      patterns: {
        preferredCommands,
        workingHours,
        connectionTypes: Object.fromEntries(connectionTypes),
        errorPatterns,
        efficiency
      },
      recommendations
    }
  }

  /**
   * 分析工作时间
   */
  private analyzeWorkingHours(actions: UserAction[]): { start: number; end: number } {
    const hours = actions.map((action) => action.timestamp.getHours())
    hours.sort((a, b) => a - b)

    // 找到活跃时间段
    const start = hours[Math.floor(hours.length * 0.1)] || 9
    const end = hours[Math.floor(hours.length * 0.9)] || 17

    return { start, end }
  }

  /**
   * 计算用户效率
   */
  private calculateUserEfficiency(actions: UserAction[]): {
    commandsPerMinute: number
    errorRate: number
    sessionDuration: number
  } {
    const commands = actions.filter((a) => a.type === 'command')
    const errors = actions.filter((a) => a.type === 'error')
    const sessions = new Set(actions.map((a) => a.sessionId))

    const totalTime =
      actions.length > 0 ? Math.max(...actions.map((a) => a.timestamp.getTime())) - Math.min(...actions.map((a) => a.timestamp.getTime())) : 0

    const commandsPerMinute = totalTime > 0 ? commands.length / (totalTime / 60000) : 0
    const errorRate = actions.length > 0 ? errors.length / actions.length : 0
    const sessionDuration = totalTime / sessions.size

    return {
      commandsPerMinute,
      errorRate,
      sessionDuration
    }
  }

  /**
   * 生成用户建议
   */
  private generateUserRecommendations(actions: UserAction[], patterns: any): string[] {
    const recommendations: string[] = []

    // 基于错误率的建议
    if (patterns.efficiency.errorRate > 0.1) {
      recommendations.push('建议复习常用命令语法，减少输入错误')
    }

    // 基于命令使用的建议
    if (patterns.preferredCommands.includes('ls') && patterns.preferredCommands.includes('cd')) {
      recommendations.push('考虑使用文件管理器或快捷键提高导航效率')
    }

    // 基于工作时间的建议
    const workingHourSpan = patterns.workingHours.end - patterns.workingHours.start
    if (workingHourSpan > 12) {
      recommendations.push('工作时间较长，建议适当休息以保持效率')
    }

    return recommendations
  }

  /**
   * 获取最近的用户行为
   */
  private getRecentActions(userId: string, timeWindow: number): UserAction[] {
    const cutoff = new Date(Date.now() - timeWindow)
    return this.actions.filter((action) => action.userId === userId && action.timestamp >= cutoff)
  }

  /**
   * 获取系统洞察
   */
  getInsights(type?: string): SystemInsight[] {
    if (type) {
      return this.insights.filter((insight) => insight.type === type)
    }
    return [...this.insights].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
  }

  /**
   * 设置数据收集状态
   */
  setCollectionEnabled(enabled: boolean): void {
    this.isCollectionEnabled = enabled
    this.emit('collectionStatusChanged', enabled)
  }

  /**
   * 设置隐私模式
   */
  setPrivacyMode(enabled: boolean): void {
    this.privacyMode = enabled
    this.emit('privacyModeChanged', enabled)
  }

  /**
   * 清空数据
   */
  clearData(): void {
    this.actions = []
    this.insights = []
    this.emit('dataCleared')
  }

  /**
   * 导出数据
   */
  exportData(): string {
    return JSON.stringify(
      {
        actions: this.actions,
        insights: this.insights,
        exportedAt: new Date().toISOString()
      },
      null,
      2
    )
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach((callback) => callback(data))
    }
  }
}

// 导出单例实例
export const dataAnalytics = DataAnalyticsManager.getInstance()
