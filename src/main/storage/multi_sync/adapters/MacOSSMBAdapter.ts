/**
 * macOS 专用 SMB 存储适配器
 * 功能：针对 macOS 系统优化的 SMB 文件操作和同步
 * 依赖：@marsaud/smb2、fs、path、child_process
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const SMB2 = require('@marsaud/smb2')
import * as fs from 'fs'
import * as path from 'path'
import { exec, spawn } from 'child_process'
import { promisify } from 'util'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, SMBConfig } from '../types/StorageTypes'

const execAsync = promisify(exec)
