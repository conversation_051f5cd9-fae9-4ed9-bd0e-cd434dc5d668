/**
 * Telnet连接服务
 * 提供网络设备的Telnet连接、通信和管理功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */

import { Telnet } from 'telnet-client'
import {
  ConsoleConnection,
  ConsoleConnectionConfig,
  ConsoleConnectionInfo,
  ConsoleConnectionStatus,
  ConsoleCommandResult,
  TelnetConfig
} from './types'

/**
 * Telnet连接服务类
 * 继承自ConsoleConnection，实现Telnet特定的连接逻辑
 */
export class TelnetService extends ConsoleConnection {
  private telnetClient?: Telnet
  private isConnecting: boolean = false
  private reconnectTimer?: NodeJS.Timeout
  private commandQueue: Array<{
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
  }> = []
  private currentCommand?: {
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
  }
  private bytesSent: number = 0
  private bytesReceived: number = 0
  private isAuthenticated: boolean = false

  /**
   * 构造函数
   * @param id 连接ID
   * @param config 连接配置
   */
  constructor(id: string, config: ConsoleConnectionConfig) {
    super(id, config)
    this.validateConfig()
  }

  /**
   * 建立Telnet连接
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.status === ConsoleConnectionStatus.CONNECTED) {
      return
    }

    this.isConnecting = true
    this.status = ConsoleConnectionStatus.CONNECTING
    this.emit('statusChange', this.status)

    try {
      const telnetConfig = this.config.config as TelnetConfig

      // 创建Telnet客户端实例
      this.telnetClient = new Telnet()

      // 设置连接参数
      const connectionParams = {
        host: telnetConfig.host,
        port: telnetConfig.port,
        timeout: telnetConfig.timeout || 5000,
        shellPrompt: telnetConfig.shellPrompt || /[$#>]\s*$/,
        loginPrompt: telnetConfig.loginPrompt || /login[:\s]*$/i,
        passwordPrompt: telnetConfig.passwordPrompt || /password[:\s]*$/i,
        debug: telnetConfig.debug || false,
        negotiationMandatory: false,
        irs: '\r\n',
        ors: '\n',
        echoLines: 1
      }

      // 建立连接
      await this.telnetClient.connect(connectionParams)

      // 如果配置了用户名和密码，进行身份验证
      if (telnetConfig.username && telnetConfig.password) {
        await this.authenticate(telnetConfig.username, telnetConfig.password)
      }

      this.status = ConsoleConnectionStatus.CONNECTED
      this.startedAt = new Date()
      this.lastActivityAt = new Date()
      this.isConnecting = false
      this.isAuthenticated = true

      this.emit('statusChange', this.status)
      this.emit('connect')
    } catch (error) {
      this.isConnecting = false
      this.status = ConsoleConnectionStatus.ERROR
      this.emit('statusChange', this.status)
      this.emit('error', error instanceof Error ? error : new Error('Telnet连接失败'))

      // 如果启用自动重连，则尝试重连
      if (this.config.autoReconnect) {
        this.scheduleReconnect()
      }

      throw error
    }
  }

  /**
   * 断开Telnet连接
   */
  public async disconnect(): Promise<void> {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = undefined
    }

    // 清理命令队列
    this.clearCommandQueue('连接已断开')

    if (this.telnetClient) {
      try {
        await this.telnetClient.end()
      } catch (error) {
        // 忽略断开连接时的错误
      }
      this.telnetClient = undefined
    }

    this.status = ConsoleConnectionStatus.DISCONNECTED
    this.isAuthenticated = false
    this.emit('statusChange', this.status)
    this.emit('disconnect', '主动断开')
  }

  /**
   * 发送数据到Telnet连接
   * @param data 要发送的数据
   */
  public async send(data: string | Buffer): Promise<void> {
    if (!this.telnetClient || this.status !== ConsoleConnectionStatus.CONNECTED) {
      throw new Error('Telnet未连接')
    }

    const dataStr = Buffer.isBuffer(data) ? data.toString('utf8') : data

    try {
      await this.telnetClient.send(dataStr)
      this.bytesSent += Buffer.byteLength(dataStr, 'utf8')
      this.lastActivityAt = new Date()
      this.emit('data', dataStr)
    } catch (error) {
      throw new Error(`数据发送失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 执行命令
   * @param command 要执行的命令
   * @param timeout 超时时间（毫秒）
   * @returns 命令执行结果
   */
  public async executeCommand(command: string, timeout: number = 30000): Promise<ConsoleCommandResult> {
    if (!this.telnetClient || this.status !== ConsoleConnectionStatus.CONNECTED) {
      throw new Error('Telnet未连接')
    }

    if (!this.isAuthenticated) {
      throw new Error('Telnet未认证')
    }

    return new Promise<ConsoleCommandResult>((resolve, reject) => {
      const startTime = Date.now()

      // 设置超时
      const timeoutHandle = setTimeout(() => {
        reject(new Error(`命令执行超时: ${timeout}ms`))
      }, timeout)

      const commandExecution = {
        command,
        resolve: (result: ConsoleCommandResult) => {
          clearTimeout(timeoutHandle)
          resolve(result)
        },
        reject: (error: Error) => {
          clearTimeout(timeoutHandle)
          reject(error)
        },
        timeout: timeoutHandle,
        startTime
      }

      // 如果当前没有正在执行的命令，立即执行
      if (!this.currentCommand) {
        this.executeNextCommand(commandExecution)
      } else {
        // 否则加入队列
        this.commandQueue.push(commandExecution)
      }
    })
  }

  /**
   * 获取连接信息
   * @returns 连接信息
   */
  public getConnectionInfo(): ConsoleConnectionInfo {
    return {
      id: this.id,
      assetId: this.config.assetId,
      name: this.config.name,
      type: this.config.type,
      status: this.status,
      config: this.config,
      startedAt: this.startedAt || new Date(),
      lastActivityAt: this.lastActivityAt || new Date(),
      reconnectCount: this.reconnectCount,
      bytesSent: this.bytesSent,
      bytesReceived: this.bytesReceived
    }
  }

  /**
   * 检查连接是否活跃
   * @returns 是否活跃
   */
  public isAlive(): boolean {
    return this.telnetClient !== undefined && this.status === ConsoleConnectionStatus.CONNECTED && this.isAuthenticated
  }

  /**
   * 验证配置
   */
  private validateConfig(): void {
    const telnetConfig = this.config.config as TelnetConfig

    if (!telnetConfig.host) {
      throw new Error('Telnet主机地址不能为空')
    }

    if (!telnetConfig.port || telnetConfig.port <= 0 || telnetConfig.port > 65535) {
      throw new Error('Telnet端口必须在1-65535之间')
    }

    if (telnetConfig.timeout && telnetConfig.timeout <= 0) {
      throw new Error('超时时间必须大于0')
    }
  }

  /**
   * 身份验证
   * @param username 用户名
   * @param password 密码
   */
  private async authenticate(username: string, password: string): Promise<void> {
    if (!this.telnetClient) {
      throw new Error('Telnet客户端未初始化')
    }

    try {
      // 等待登录提示符并发送用户名
      const telnetConfig = this.config.config as TelnetConfig
      const loginPrompt = telnetConfig.loginPrompt || /login[:\s]*$/i

      await this.telnetClient.send(username, {
        waitfor: loginPrompt,
        timeout: telnetConfig.timeout || 5000
      })

      // 等待密码提示符并发送密码
      const passwordPrompt = telnetConfig.passwordPrompt || /password[:\s]*$/i

      await this.telnetClient.send(password, {
        waitfor: passwordPrompt,
        timeout: telnetConfig.timeout || 5000
      })

      // 等待shell提示符确认登录成功
      const shellPrompt = telnetConfig.shellPrompt || /[$#>]\s*$/

      await this.telnetClient.send('', {
        waitfor: shellPrompt,
        timeout: telnetConfig.timeout || 5000
      })
    } catch (error) {
      throw new Error(`Telnet身份验证失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 执行下一个命令
   * @param commandExecution 命令执行对象
   */
  private async executeNextCommand(commandExecution: {
    command: string
    resolve: (result: ConsoleCommandResult) => void
    reject: (error: Error) => void
    timeout: NodeJS.Timeout
    startTime: number
  }): Promise<void> {
    this.currentCommand = commandExecution

    try {
      if (!this.telnetClient) {
        throw new Error('Telnet客户端未初始化')
      }

      const telnetConfig = this.config.config as TelnetConfig
      const shellPrompt = telnetConfig.shellPrompt || /[$#>]\s*$/

      // 执行命令并等待响应
      const response = await this.telnetClient.exec(commandExecution.command, {
        timeout: telnetConfig.timeout || 30000,
        shellPrompt: shellPrompt
      })

      const duration = Date.now() - commandExecution.startTime
      const result: ConsoleCommandResult = {
        command: commandExecution.command,
        output: response || '',
        duration,
        timestamp: new Date()
      }

      this.bytesReceived += Buffer.byteLength(response || '', 'utf8')
      this.lastActivityAt = new Date()

      commandExecution.resolve(result)
      this.currentCommand = undefined

      // 处理队列中的下一个命令
      this.processNextCommand()
    } catch (error) {
      this.currentCommand = undefined
      commandExecution.reject(error instanceof Error ? error : new Error('命令执行失败'))
      this.processNextCommand()
    }
  }

  /**
   * 处理队列中的下一个命令
   */
  private processNextCommand(): void {
    if (this.commandQueue.length > 0) {
      const nextCommand = this.commandQueue.shift()!
      this.executeNextCommand(nextCommand)
    }
  }

  /**
   * 清理命令队列
   * @param reason 清理原因
   */
  private clearCommandQueue(reason: string): void {
    // 清理当前命令
    if (this.currentCommand) {
      this.currentCommand.reject(new Error(reason))
      this.currentCommand = undefined
    }

    // 清理队列中的命令
    while (this.commandQueue.length > 0) {
      const command = this.commandQueue.shift()!
      command.reject(new Error(reason))
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      return
    }

    const interval = this.config.reconnectInterval || 5000
    this.reconnectTimer = setTimeout(async () => {
      this.reconnectTimer = undefined
      this.reconnectCount++

      this.emit('reconnect', this.reconnectCount)

      try {
        await this.connect()
        this.reconnectCount = 0 // 重连成功后重置计数
      } catch (error) {
        // 重连失败，如果还有重连次数则继续尝试
        if (this.reconnectCount < (this.config.maxReconnectAttempts || 5)) {
          this.scheduleReconnect()
        } else {
          this.emit('error', new Error('重连次数已达上限'))
        }
      }
    }, interval)
  }
}
