/**
 * 多存储后端数据同步系统 - SMB 存储适配器
 * 功能：实现 SMB 网络共享的文件操作和同步
 * 依赖：node-smb2、fs、path
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

const SMB2 = require('@marsaud/smb2')
import * as fs from 'fs'
import * as path from 'path'
import { BaseStorageAdapter } from './BaseStorageAdapter'
import { StorageConfig, StorageType, FileInfo, FileMetadata, SMBConfig } from '../types/StorageTypes'

/**
 * SMB 存储适配器
 * 实现 SMB 协议的文件操作和同步功能
 */
export class SMBAdapter extends BaseStorageAdapter {
  private client: any = null
  private smbConfig: SMBConfig | null = null
  private readonly maxRetries: number = 3
  private readonly retryDelay: number = 1000 // 1秒

  /**
   * 连接到 SMB 服务
   * @param config SMB 配置
   * @returns 连接是否成功
   */
  async connect(config: StorageConfig): Promise<boolean> {
    try {
      this.smbConfig = config as SMBConfig

      if (!this.smbConfig.host || !this.smbConfig.username || !this.smbConfig.password) {
        throw new Error('SMB 主机地址、用户名和密码必须提供')
      }

      if (!this.smbConfig.share) {
        throw new Error('SMB 共享名称必须提供')
      }

      console.log('开始真实SMB连接:', {
        host: this.smbConfig.host,
        share: this.smbConfig.share,
        username: this.smbConfig.username,
        domain: this.smbConfig.domain || 'WORKGROUP',
        port: this.smbConfig.port || 445
      })

      // 创建真实的SMB2客户端
      this.client = new SMB2({
        share: `\\\\${this.smbConfig.host}\\${this.smbConfig.share}`,
        domain: this.smbConfig.domain || 'WORKGROUP',
        username: this.smbConfig.username,
        password: this.smbConfig.password,
        port: this.smbConfig.port || 445,
        packetConcurrency: 20,
        autoCloseTimeout: 0
      })

      console.log('SMB2客户端创建成功')

      // 测试连接 - 尝试列出根目录
      await this.testSMBConnection()

      this.connected = true
      this.config = config
      console.log('SMB连接建立成功')
      return true
    } catch (error) {
      console.error('SMB 连接失败:', error)
      this.connected = false
      if (this.client) {
        this.client.disconnect()
        this.client = null
      }
      return false
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      try {
        this.client.close()
        console.log('SMB连接已断开')
      } catch (error) {
        console.error('断开SMB连接时出错:', error)
      }
      this.client = null
    }
    this.smbConfig = null
    this.connected = false
    this.config = null
  }

  /**
   * 测试连接
   * @returns 连接是否正常
   */
  async testConnection(): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      await this.testSMBConnection()
      return true
    } catch (error) {
      console.error('SMB 连接测试失败:', error)
      return false
    }
  }

  /**
   * 测试 SMB 连接
   */
  private async testSMBConnection(): Promise<void> {
    return this.retryOperation(async () => {
      return new Promise<void>((resolve, reject) => {
        const testPath = this.smbConfig?.path || '/'

        this.client.readdir(testPath, (err: any, _files: any) => {
          if (err) {
            reject(new Error(`SMB 连接测试失败: ${err.message}`))
          } else {
            resolve()
          }
        })
      })
    }, 'SMB连接测试')
  }

  /**
   * 重试操作机制
   * @param operation 要执行的操作
   * @param operationName 操作名称（用于日志）
   * @param retries 重试次数
   * @returns 操作结果
   */
  private async retryOperation<T>(operation: () => Promise<T>, operationName: string, retries: number = this.maxRetries): Promise<T> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await operation()
      } catch (error: any) {
        lastError = error
        console.warn(`${operationName} 第 ${attempt} 次尝试失败:`, error.message)

        if (attempt < retries) {
          const delay = this.retryDelay * attempt // 递增延迟
          console.log(`等待 ${delay}ms 后重试...`)
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }
    }

    throw new Error(`${operationName} 在 ${retries} 次尝试后仍然失败: ${lastError?.message || '未知错误'}`)
  }

  /**
   * 处理SMB错误，提供详细的错误信息
   * @param error 原始错误
   * @param operation 操作名称
   * @returns 格式化的错误信息
   */
  private formatSMBError(error: any, operation: string): string {
    const errorMessage = error?.message || error?.toString() || '未知错误'

    if (errorMessage.includes('ECONNREFUSED')) {
      return `${operation}失败：连接被拒绝。请检查SMB服务器是否运行，端口445是否开放，防火墙设置是否正确。`
    } else if (errorMessage.includes('ENOTFOUND')) {
      return `${operation}失败：主机名解析失败。请检查主机地址是否正确，网络连接是否正常。`
    } else if (errorMessage.includes('Authentication') || errorMessage.includes('Access denied')) {
      return `${operation}失败：认证失败或访问被拒绝。请检查用户名、密码、域名设置是否正确，账户是否有SMB访问权限。`
    } else if (errorMessage.includes('ENOENT')) {
      return `${operation}失败：文件或目录不存在。`
    } else if (errorMessage.includes('EACCES')) {
      return `${operation}失败：权限不足。请检查用户是否有足够的权限执行此操作。`
    } else {
      return `${operation}失败：${errorMessage}`
    }
  }

  /**
   * 上传文件到 SMB
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  async uploadFile(localPath: string, remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      if (!fs.existsSync(localPath)) {
        throw new Error(`本地文件不存在: ${localPath}`)
      }

      console.log(`开始SMB文件上传: ${localPath} -> ${remotePath}`)

      // 构建完整的远程路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保远程目录存在
      const remoteDir = path.dirname(fullRemotePath)
      if (remoteDir !== '/' && remoteDir !== '\\') {
        await this.ensureRemoteDirectoryExists(remoteDir)
      }

      // 读取本地文件内容
      const fileContent = fs.readFileSync(localPath)
      console.log(`文件大小: ${fileContent.length} 字节`)

      // 真实的文件上传（带重试机制）
      await this.retryOperation(() => this.smbWriteFile(localPath, fullRemotePath), `文件上传 ${remotePath}`)

      console.log(`SMB文件上传完成: ${remotePath}`)
    } catch (error: any) {
      console.error('SMB文件上传失败:', error)
      throw new Error(this.formatSMBError(error, 'SMB文件上传'))
    }
  }

  /**
   * 从 SMB 下载文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  async downloadFile(remotePath: string, localPath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      console.log(`开始SMB文件下载: ${remotePath} -> ${localPath}`)

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 确保本地目录存在
      this.ensureDirectoryExists(path.dirname(localPath))

      // 真实的文件下载（带重试机制）
      await this.retryOperation(() => this.smbReadFile(fullRemotePath, localPath), `文件下载 ${remotePath}`)

      console.log(`SMB文件下载完成: ${localPath}`)
    } catch (error: any) {
      console.error('SMB文件下载失败:', error)
      throw new Error(this.formatSMBError(error, 'SMB文件下载'))
    }
  }

  /**
   * 删除 SMB 文件
   * @param remotePath 远程文件路径
   */
  async deleteFile(remotePath: string): Promise<void> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      console.log(`开始删除SMB文件: ${remotePath}`)

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 真实的文件删除（带重试机制）
      await this.retryOperation(() => this.smbUnlink(fullRemotePath), `文件删除 ${remotePath}`)

      console.log(`SMB文件删除完成: ${remotePath}`)
    } catch (error: any) {
      console.error('SMB文件删除失败:', error)
      throw new Error(this.formatSMBError(error, 'SMB文件删除'))
    }
  }

  /**
   * 列出 SMB 文件
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  async listFiles(remotePath?: string): Promise<FileInfo[]> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      console.log(`开始列出SMB目录: ${remotePath || '/'}`)

      // 构建远程目录路径
      const fullRemotePath = this.buildRemotePath(remotePath || '')

      // 真实的目录列表（带重试机制）
      const fileList = await this.retryOperation(() => this.smbReaddir(fullRemotePath), `目录列表 ${remotePath || '/'}`)
      const files: FileInfo[] = []

      for (const item of fileList) {
        if (!item.isDirectory()) {
          // 只处理文件，不处理目录
          files.push({
            name: item.name,
            path: remotePath ? `${remotePath}/${item.name}` : item.name,
            size: item.size || 0,
            lastModified: item.mtime || new Date(),
            isDirectory: false
          })
        }
      }

      console.log(`SMB目录列表获取完成，共 ${files.length} 个文件`)
      return files
    } catch (error: any) {
      console.error('SMB文件列表获取失败:', error)
      throw new Error(this.formatSMBError(error, 'SMB文件列表获取'))
    }
  }

  /**
   * 获取 SMB 文件元数据
   * @param remotePath 远程文件路径
   * @returns 文件元数据
   */
  async getFileMetadata(remotePath: string): Promise<FileMetadata> {
    try {
      this.checkConnection()

      if (!this.client || !this.smbConfig) {
        throw new Error('SMB 客户端未初始化')
      }

      console.log(`获取SMB文件元数据: ${remotePath}`)

      // 构建远程文件路径
      const fullRemotePath = this.buildRemotePath(remotePath)

      // 真实的文件统计信息获取（带重试机制）
      const stats = await this.retryOperation(() => this.smbStat(fullRemotePath), `文件元数据获取 ${remotePath}`)

      const metadata = {
        name: path.basename(remotePath),
        path: remotePath,
        size: stats.EndOfFile || 0,
        lastModified: new Date(stats.LastWriteTime || Date.now())
      }

      console.log(`SMB文件元数据获取完成: ${metadata.name}, 大小: ${metadata.size} 字节`)
      return metadata
    } catch (error: any) {
      console.error('SMB文件元数据获取失败:', error)
      throw new Error(this.formatSMBError(error, 'SMB文件元数据获取'))
    }
  }

  /**
   * 获取存储类型
   * @returns 存储类型
   */
  getType(): StorageType {
    return StorageType.SMB
  }

  /**
   * 构建远程路径
   * @param relativePath 相对路径
   * @returns 完整的远程路径
   */
  private buildRemotePath(relativePath: string): string {
    if (!this.smbConfig?.path) {
      return relativePath || '/'
    }

    if (!relativePath) {
      return this.smbConfig.path
    }

    // 确保路径正确连接，SMB 使用反斜杠
    const basePath = this.smbConfig.path.replace(/\//g, '\\')
    const relPath = relativePath.replace(/\//g, '\\')

    return path.join(basePath, relPath).replace(/\//g, '\\')
  }

  /**
   * SMB 读取目录
   * @param remotePath 远程目录路径
   * @returns 文件列表
   */
  private async smbReaddir(remotePath: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this.client.readdir(remotePath, { stats: true }, (err: any, files: any) => {
        if (err) {
          reject(err)
        } else {
          resolve(files || [])
        }
      })
    })
  }

  /**
   * SMB 读取文件
   * @param remotePath 远程文件路径
   * @param localPath 本地文件路径
   */
  private async smbReadFile(remotePath: string, localPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.readFile(remotePath, (err: any, data: Buffer) => {
        if (err) {
          reject(err)
        } else {
          try {
            fs.writeFileSync(localPath, data)
            resolve()
          } catch (writeErr) {
            reject(writeErr)
          }
        }
      })
    })
  }

  /**
   * SMB 写入文件
   * @param localPath 本地文件路径
   * @param remotePath 远程文件路径
   */
  private async smbWriteFile(localPath: string, remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const data = fs.readFileSync(localPath)

      this.client.writeFile(remotePath, data, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * SMB 删除文件
   * @param remotePath 远程文件路径
   */
  private async smbUnlink(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.unlink(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * SMB 获取文件统计信息
   * @param remotePath 远程文件路径
   * @returns 文件统计信息
   */
  private async smbStat(remotePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.client.stat(remotePath, (err: any, stats: any) => {
        if (err) {
          reject(err)
        } else {
          resolve({
            EndOfFile: stats.size || 0,
            LastWriteTime: stats.mtime || new Date()
          })
        }
      })
    })
  }

  /**
   * 确保远程目录存在
   * @param remoteDirPath 远程目录路径
   */
  private async ensureRemoteDirectoryExists(remoteDirPath: string): Promise<void> {
    try {
      if (!this.client) {
        throw new Error('SMB 客户端未初始化')
      }

      // 检查目录是否存在
      try {
        await this.smbStat(remoteDirPath)
        return // 目录已存在
      } catch (error) {
        // 目录不存在，需要创建
      }

      // 创建目录
      await this.smbMkdir(remoteDirPath)
    } catch (error) {
      console.error('创建远程目录失败:', error)
      // 不抛出错误，因为目录可能已经存在
    }
  }

  /**
   * SMB 创建目录
   * @param remotePath 远程目录路径
   */
  private async smbMkdir(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.mkdir(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * 检查文件是否存在
   * @param remotePath 远程文件路径
   * @returns 文件是否存在
   */
  async fileExists(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbStat(fullRemotePath)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 创建远程目录
   * @param remotePath 远程目录路径
   * @returns 是否创建成功
   */
  async createDirectory(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbMkdir(fullRemotePath)
      return true
    } catch (error) {
      console.error('创建远程目录失败:', error)
      return false
    }
  }

  /**
   * 删除远程目录
   * @param remotePath 远程目录路径
   * @returns 是否删除成功
   */
  async deleteDirectory(remotePath: string): Promise<boolean> {
    try {
      this.checkConnection()

      if (!this.client) {
        return false
      }

      const fullRemotePath = this.buildRemotePath(remotePath)
      await this.smbRmdir(fullRemotePath)
      return true
    } catch (error) {
      console.error('删除远程目录失败:', error)
      return false
    }
  }

  /**
   * SMB 删除目录
   * @param remotePath 远程目录路径
   */
  private async smbRmdir(remotePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.rmdir(remotePath, (err: any) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }
}
