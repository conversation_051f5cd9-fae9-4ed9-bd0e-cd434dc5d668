/// <reference types="vite/client" />

import type { ElectronAPI } from '@electron-toolkit/preload'
import type { ApiType } from '../../preload/index.d.ts'

declare global {
  interface Window {
    // 与 preload/index.d.ts 对齐
    electron: ElectronAPI
    api: ApiType

    // 兼容旧代码中直接挂在 window 上的 electronAPI 调用
    electronAPI?: {
      // Console connection methods
      consoleConnect: (config: {
        assetId: string
        type: string
        config: any
      }) => Promise<{ success: boolean; connectionId?: string; message?: string }>
      consoleDisconnect: (assetId: string) => Promise<{ success: boolean; message?: string }>
      // RDP connection methods
      rdpConnect: (config: {
        assetId: string
        config: any
        displaySettings?: any
      }) => Promise<{ success: boolean; connectionId?: string; message?: string }>
      rdpDisconnect: (assetId: string) => Promise<{ success: boolean; message?: string }>
    }
  }
}

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}
