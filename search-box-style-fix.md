# 企业资产管理搜索框样式修复

## 🎯 问题描述

用户反馈企业资产管理页面的搜索框颜色和样式不对，搜索框显示为黑色，与个人资产管理页面的白色搜索框样式不一致。

## 🔍 问题分析

通过对比个人资产管理页面 (`assetConfig.vue`) 和企业资产管理页面 (`enterpriseAssetConfig.vue`) 的代码，发现了以下差异：

### 个人资产管理页面
- 使用专门的 `AssetSearch` 组件
- 使用 CSS 变量适配主题：`var(--bg-color-secondary)`、`var(--text-color)` 等
- 使用 LESS 预处理器
- 使用 `:deep()` 选择器来覆盖 Ant Design 组件样式

### 企业资产管理页面（修复前）
- 直接使用 `a-input-search` 组件
- 使用硬编码的颜色值
- 使用普通 CSS
- 没有适配主题变量

## ✅ 修复方案

### 1. 更换搜索组件
将 `a-input-search` 改为 `a-input` 并添加搜索图标：

```vue
<!-- 修复前 -->
<a-input-search
  v-model:value="searchKeyword"
  placeholder="搜索企业资产..."
  style="width: 300px"
  @search="handleSearch"
/>

<!-- 修复后 -->
<a-input
  v-model:value="searchKeyword"
  placeholder="搜索企业资产..."
  class="search-input"
  @input="handleSearch"
  @change="handleSearch"
>
  <template #suffix>
    <search-outlined />
  </template>
</a-input>
```

### 2. 添加搜索图标导入
```typescript
import { DesktopOutlined, CloudServerOutlined, DatabaseOutlined, SearchOutlined } from '@ant-design/icons-vue'
```

### 3. 更新样式系统
将样式从普通 CSS 改为 LESS：
```vue
<style lang="less" scoped>
```

### 4. 添加主题适配样式
```less
.search-input {
  width: 300px;
  background-color: var(--bg-color-secondary) !important;
  border: 1px solid var(--border-color) !important;

  :deep(.ant-input) {
    background-color: var(--bg-color-secondary) !important;
    color: var(--text-color) !important;
    &::placeholder {
      color: var(--text-color-tertiary) !important;
    }
  }

  :deep(.ant-input-suffix) {
    color: var(--text-color-tertiary) !important;
  }
}
```

## 🎨 样式变量说明

使用的 CSS 变量与个人资产管理页面保持一致：

- `--bg-color-secondary`: 次要背景色（搜索框背景）
- `--border-color`: 边框颜色
- `--text-color`: 主要文本颜色
- `--text-color-tertiary`: 三级文本颜色（占位符和图标）

这些变量会根据当前主题（亮色/暗色）自动调整，确保在不同主题下都有良好的视觉效果。

## 🔧 修复的文件

1. **src/renderer/src/views/components/LeftTab/enterpriseAssetConfig.vue**
   - 更换搜索组件
   - 添加搜索图标导入
   - 更新样式系统为 LESS
   - 添加主题适配样式

## ✨ 修复效果

修复后的企业资产管理搜索框将具有以下特性：

1. **视觉一致性**: 与个人资产管理页面的搜索框样式完全一致
2. **主题适配**: 自动适配亮色和暗色主题
3. **交互体验**: 保持原有的搜索功能，支持输入和变更事件
4. **图标显示**: 搜索框右侧显示搜索图标

## 🧪 测试验证

1. 切换到企业资源工作空间
2. 点击"资产管理"按钮
3. 验证搜索框显示为白色背景（亮色主题下）
4. 验证搜索框右侧显示搜索图标
5. 验证搜索功能正常工作

## 📝 技术要点

- **CSS 变量**: 使用 CSS 变量实现主题适配
- **LESS 预处理器**: 支持嵌套选择器和变量
- **:deep() 选择器**: 穿透 Vue 的 scoped 样式，修改第三方组件样式
- **!important**: 确保样式优先级，覆盖 Ant Design 默认样式

修复完成后，企业资产管理页面的搜索框样式与个人资产管理页面完全一致，提供了统一的用户体验。
