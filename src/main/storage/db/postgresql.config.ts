/**
 * PostgreSQL数据库配置文件
 * 功能：配置primary-db数据库连接信息
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { PostgreSQLConfig } from './postgresql.service'

/**
 * primary-db数据库配置
 */
export const primaryDbConfig: PostgreSQLConfig = {
  host: '***************',
  port: 5432,
  database: 'postgres', // 默认数据库，如果需要特定数据库请修改
  username: 'postgres',
  password: 'E5z`+wk%YjtO:@zE^YI<',
  ssl: false, // 根据需要启用SSL
  poolSize: 10
}

/**
 * 获取数据库配置
 * @param dbName 可选的数据库名称，默认使用postgres
 * @returns PostgreSQL配置对象
 */
export function getPrimaryDbConfig(dbName?: string): PostgreSQLConfig {
  return {
    ...primaryDbConfig,
    database: dbName || primaryDbConfig.database
  }
}

/**
 * 验证配置是否完整
 */
export function validateConfig(config: PostgreSQLConfig): boolean {
  return !!(config.host && config.port && config.database && config.username && config.password)
}
