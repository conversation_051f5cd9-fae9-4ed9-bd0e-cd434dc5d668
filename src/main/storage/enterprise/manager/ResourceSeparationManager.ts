/**
 * 企业资源同步系统 - 资源分离管理器
 * 功能：实现个人资源和企业资源的分离逻辑和同步策略
 * 依赖：企业类型定义、多存储同步管理器、数据同步控制器
 * 作者：SOLO Coding
 * 修改时间：2025-01-15
 */

import { EventEmitter } from 'events'
import { ResourceSeparationStrategy, UserRole, ResourceType, EnterpriseResource, User } from '../types/EnterpriseTypes'
import { MultiStorageSyncManager } from '../../multi_sync/manager/MultiStorageSyncManager'
import { SyncController } from '../../data_sync/core/SyncController'
import { Asset, AssetChain } from '../../data_sync/models/SyncTypes'

/**
 * 资源分离策略配置
 */
interface SeparationConfig {
  strategy: ResourceSeparationStrategy
  personalSyncEnabled: boolean
  enterpriseSyncEnabled: boolean
  conflictResolution: 'personal_priority' | 'enterprise_priority' | 'manual'
}

/**
 * 同步上下文接口
 */
interface SyncContext {
  user: User
  resourceType: ResourceType
  isPersonalResource: boolean
  targetBackends: string[]
}

/**
 * 资源分离管理器
 * 负责协调个人资源和企业资源的同步策略
 */
export class ResourceSeparationManager extends EventEmitter {
  private static instance: ResourceSeparationManager | null = null
  private config: SeparationConfig
  private personalSyncManager: MultiStorageSyncManager
  private personalSyncController: SyncController
  private currentUser: User | null = null

  /**
   * 获取管理器单例实例
   * @returns 管理器实例
   */
  static getInstance(): ResourceSeparationManager {
    if (!ResourceSeparationManager.instance) {
      ResourceSeparationManager.instance = new ResourceSeparationManager()
    }
    return ResourceSeparationManager.instance
  }

  /**
   * 私有构造函数，实现单例模式
   */
  private constructor() {
    super()

    // 初始化默认配置
    this.config = {
      strategy: {
        personal_backends: [],
        enterprise_backends: [],
        isolation_mode: 'strict'
      },
      personalSyncEnabled: true,
      enterpriseSyncEnabled: false,
      conflictResolution: 'personal_priority'
    }

    // 获取现有的同步管理器实例
    this.personalSyncManager = MultiStorageSyncManager.getInstance()
    this.personalSyncController = new SyncController()

    this.initializeEventListeners()
  }

  /**
   * 设置当前用户
   * @param user 用户信息
   */
  setCurrentUser(user: User): void {
    this.currentUser = user
    this.updateSyncStrategy()
    this.emit('userChanged', user)
  }

  /**
   * 获取当前用户
   * @returns 当前用户信息
   */
  getCurrentUser(): User | null {
    return this.currentUser
  }

  /**
   * 更新资源分离策略
   * @param strategy 分离策略
   */
  updateSeparationStrategy(strategy: ResourceSeparationStrategy): void {
    this.config.strategy = strategy
    this.updateSyncStrategy()
    this.emit('strategyUpdated', strategy)
  }

  /**
   * 获取当前分离策略
   * @returns 分离策略
   */
  getSeparationStrategy(): ResourceSeparationStrategy {
    return this.config.strategy
  }

  /**
   * 启用/禁用个人资源同步
   * @param enabled 是否启用
   */
  setPersonalSyncEnabled(enabled: boolean): void {
    this.config.personalSyncEnabled = enabled
    this.updateSyncStrategy()
    this.emit('personalSyncToggled', enabled)
  }

  /**
   * 启用/禁用企业资源同步
   * @param enabled 是否启用
   */
  setEnterpriseSyncEnabled(enabled: boolean): void {
    this.config.enterpriseSyncEnabled = enabled
    this.updateSyncStrategy()
    this.emit('enterpriseSyncToggled', enabled)
  }

  /**
   * 判断资源是否为个人资源
   * @param resource 资源对象
   * @returns 是否为个人资源
   */
  isPersonalResource(resource: Asset | AssetChain | EnterpriseResource): boolean {
    // 如果是企业资源类型，直接返回false
    if ('backend_id' in resource) {
      return false
    }

    // 如果用户是个人用户，所有资源都是个人资源
    if (this.currentUser?.role_type === UserRole.PERSONAL) {
      return true
    }

    // 对于企业用户，需要根据资源来源判断
    // 这里可以根据具体业务逻辑来判断
    // 例如：检查资源的创建者、标签、分组等
    return this.isResourceCreatedByCurrentUser(resource)
  }

  /**
   * 获取资源应该同步到的后端列表
   * @param resource 资源对象
   * @returns 后端ID列表
   */
  getTargetBackends(resource: Asset | AssetChain | EnterpriseResource): string[] {
    const isPersonal = this.isPersonalResource(resource)

    if (isPersonal) {
      return this.config.strategy.personal_backends
    } else {
      return this.config.strategy.enterprise_backends
    }
  }

  /**
   * 同步个人资源
   * @param resources 资源列表
   * @returns 同步结果
   */
  async syncPersonalResources(resources?: (Asset | AssetChain)[]): Promise<{
    success: boolean
    message: string
    synced_count: number
    failed_count: number
  }> {
    if (!this.config.personalSyncEnabled) {
      return {
        success: false,
        message: '个人资源同步已禁用',
        synced_count: 0,
        failed_count: 0
      }
    }

    if (!this.currentUser) {
      return {
        success: false,
        message: '用户未登录',
        synced_count: 0,
        failed_count: 0
      }
    }

    try {
      // 使用现有的个人数据同步控制器
      const result = await this.personalSyncController.fullSyncAll()

      // 同时同步到配置的个人后端存储
      if (this.config.strategy.personal_backends.length > 0) {
        await this.syncToPersonalBackends(resources)
      }

      this.emit('personalSyncCompleted', result)
      return result
    } catch (error: any) {
      const errorResult = {
        success: false,
        message: `个人资源同步失败: ${error.message}`,
        synced_count: 0,
        failed_count: 1
      }

      this.emit('personalSyncFailed', errorResult)
      return errorResult
    }
  }

  /**
   * 同步企业资源
   * @param resources 企业资源列表
   * @returns 同步结果
   */
  async syncEnterpriseResources(resources?: EnterpriseResource[]): Promise<{
    success: boolean
    message: string
    synced_count: number
    failed_count: number
  }> {
    if (!this.config.enterpriseSyncEnabled) {
      return {
        success: false,
        message: '企业资源同步已禁用',
        synced_count: 0,
        failed_count: 0
      }
    }

    if (!this.currentUser) {
      return {
        success: false,
        message: '用户未登录',
        synced_count: 0,
        failed_count: 0
      }
    }

    // 检查用户权限
    if (!this.hasEnterpriseAccess()) {
      return {
        success: false,
        message: '用户无企业资源访问权限',
        synced_count: 0,
        failed_count: 0
      }
    }

    try {
      // TODO: 实现企业资源同步逻辑
      // 这里需要与企业配置后端管理器协作
      const result = await this.syncToEnterpriseBackends(resources)

      this.emit('enterpriseSyncCompleted', result)
      return result
    } catch (error: any) {
      const errorResult = {
        success: false,
        message: `企业资源同步失败: ${error.message}`,
        synced_count: 0,
        failed_count: 1
      }

      this.emit('enterpriseSyncFailed', errorResult)
      return errorResult
    }
  }

  /**
   * 执行完整同步（个人+企业）
   * @returns 同步结果
   */
  async performFullSync(): Promise<{
    personal: any
    enterprise: any
    overall_success: boolean
  }> {
    const results = {
      personal: null as any,
      enterprise: null as any,
      overall_success: false
    }

    // 并行执行个人和企业资源同步
    const [personalResult, enterpriseResult] = await Promise.allSettled([this.syncPersonalResources(), this.syncEnterpriseResources()])

    // 处理个人资源同步结果
    if (personalResult.status === 'fulfilled') {
      results.personal = personalResult.value
    } else {
      results.personal = {
        success: false,
        message: `个人资源同步异常: ${personalResult.reason}`,
        synced_count: 0,
        failed_count: 1
      }
    }

    // 处理企业资源同步结果
    if (enterpriseResult.status === 'fulfilled') {
      results.enterprise = enterpriseResult.value
    } else {
      results.enterprise = {
        success: false,
        message: `企业资源同步异常: ${enterpriseResult.reason}`,
        synced_count: 0,
        failed_count: 1
      }
    }

    // 判断整体同步是否成功
    results.overall_success =
      (!this.config.personalSyncEnabled || results.personal?.success) && (!this.config.enterpriseSyncEnabled || results.enterprise?.success)

    this.emit('fullSyncCompleted', results)
    return results
  }

  /**
   * 处理资源冲突
   * @param personalResource 个人资源
   * @param enterpriseResource 企业资源
   * @returns 解决后的资源
   */
  resolveResourceConflict(personalResource: Asset | AssetChain, enterpriseResource: EnterpriseResource): Asset | AssetChain | EnterpriseResource {
    switch (this.config.conflictResolution) {
      case 'personal_priority':
        return personalResource
      case 'enterprise_priority':
        return enterpriseResource
      case 'manual':
        // 触发手动解决事件
        this.emit('conflictRequiresManualResolution', {
          personal: personalResource,
          enterprise: enterpriseResource
        })
        // 默认返回个人资源
        return personalResource
      default:
        return personalResource
    }
  }

  /**
   * 检查用户是否有企业资源访问权限
   * @returns 是否有权限
   */
  private hasEnterpriseAccess(): boolean {
    if (!this.currentUser) {
      return false
    }

    return [UserRole.ENTERPRISE_ADMIN, UserRole.SYSTEM_ADMIN].includes(this.currentUser.role_type)
  }

  /**
   * 判断资源是否由当前用户创建
   * @param resource 资源对象
   * @returns 是否由当前用户创建
   */
  private isResourceCreatedByCurrentUser(resource: Asset | AssetChain | EnterpriseResource): boolean {
    // 这里可以根据具体的业务逻辑来判断
    // 例如：检查资源的uid字段、创建时间、标签等

    if ('uid' in resource && resource.uid && this.currentUser) {
      return resource.uid === this.currentUser.id
    }

    // 默认认为是个人资源
    return true
  }

  /**
   * 同步到个人后端存储
   * @param resources 资源列表
   */
  private async syncToPersonalBackends(resources?: (Asset | AssetChain)[]): Promise<void> {
    for (const backendId of this.config.strategy.personal_backends) {
      try {
        // 使用多存储同步管理器进行同步
        await this.personalSyncManager.syncToStorage(backendId)
      } catch (error) {
        console.warn(`同步到个人后端失败 [${backendId}]:`, error)
      }
    }
  }

  /**
   * 同步到企业后端存储
   * @param resources 企业资源列表
   */
  private async syncToEnterpriseBackends(resources?: EnterpriseResource[]): Promise<{
    success: boolean
    message: string
    synced_count: number
    failed_count: number
  }> {
    let syncedCount = 0
    let failedCount = 0
    const errors: string[] = []

    for (const backendId of this.config.strategy.enterprise_backends) {
      try {
        // TODO: 实现企业后端同步逻辑
        // 这里需要与企业配置后端管理器协作
        console.log(`同步到企业后端 [${backendId}]...`)
        syncedCount++
      } catch (error: any) {
        console.warn(`同步到企业后端失败 [${backendId}]:`, error)
        errors.push(`${backendId}: ${error.message}`)
        failedCount++
      }
    }

    return {
      success: failedCount === 0,
      message: failedCount === 0 ? '企业资源同步成功' : `部分同步失败: ${errors.join(', ')}`,
      synced_count: syncedCount,
      failed_count: failedCount
    }
  }

  /**
   * 更新同步策略
   */
  private updateSyncStrategy(): void {
    if (!this.currentUser) {
      return
    }

    // 根据用户角色调整同步策略
    switch (this.currentUser.role_type) {
      case UserRole.PERSONAL:
        // 个人用户只能同步个人资源
        this.config.enterpriseSyncEnabled = false
        break
      case UserRole.ENTERPRISE_ADMIN:
      case UserRole.SYSTEM_ADMIN:
        // 企业用户可以同步两种资源
        break
    }

    // 根据隔离模式调整策略
    if (this.config.strategy.isolation_mode === 'strict') {
      // 严格隔离模式：确保个人和企业资源使用不同的后端
      const personalBackends = new Set(this.config.strategy.personal_backends)
      const enterpriseBackends = new Set(this.config.strategy.enterprise_backends)

      // 检查是否有重叠
      const overlap = [...personalBackends].filter((id) => enterpriseBackends.has(id))
      if (overlap.length > 0) {
        console.warn('严格隔离模式下检测到后端重叠:', overlap)
        this.emit('isolationViolation', { overlap })
      }
    }
  }

  /**
   * 初始化事件监听器
   */
  private initializeEventListeners(): void {
    // 监听个人同步管理器事件
    this.personalSyncManager.on('syncCompleted', (result) => {
      this.emit('personalBackendSyncCompleted', result)
    })

    this.personalSyncManager.on('syncFailed', (error) => {
      this.emit('personalBackendSyncFailed', error)
    })
  }

  /**
   * 获取同步状态统计
   * @returns 同步状态统计
   */
  getSyncStats(): {
    personal: {
      enabled: boolean
      backends_count: number
      last_sync?: Date
    }
    enterprise: {
      enabled: boolean
      backends_count: number
      last_sync?: Date
    }
  } {
    return {
      personal: {
        enabled: this.config.personalSyncEnabled,
        backends_count: this.config.strategy.personal_backends.length
      },
      enterprise: {
        enabled: this.config.enterpriseSyncEnabled,
        backends_count: this.config.strategy.enterprise_backends.length
      }
    }
  }

  /**
   * 销毁管理器，清理资源
   */
  destroy(): void {
    this.currentUser = null
    this.removeAllListeners()
  }
}
