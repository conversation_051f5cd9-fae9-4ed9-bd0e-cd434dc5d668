<template>
  <div class="script-execution">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <button class="btn btn-back" @click="goBack">
          <ArrowLeft class="btn-icon" />
          <span class="btn-text">返回</span>
        </button>
        <div class="header-content">
          <h1 class="page-title">
            <Terminal class="title-icon" />
            自动化脚本执行
          </h1>
          <p class="page-description">选择目标主机执行自动化运维脚本</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 脚本配置区域 -->
      <div class="script-config">
        <div class="config-section">
          <h3>脚本配置</h3>
          <div class="form-group">
            <label>脚本名称:</label>
            <input
              v-model="scriptConfig.name"
              type="text"
              placeholder="输入脚本名称"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>脚本类型:</label>
            <select v-model="scriptConfig.type" class="form-select">
              <option value="shell">Shell脚本</option>
              <option value="python">Python脚本</option>
              <option value="powershell">PowerShell脚本</option>
            </select>
          </div>
          <div class="form-group">
            <label>脚本内容:</label>
            <textarea
              v-model="scriptConfig.content"
              placeholder="输入脚本内容..."
              class="form-textarea"
              rows="8"
            ></textarea>
          </div>
        </div>

        <!-- 目标主机选择 -->
        <div class="target-selection">
          <h3>目标主机选择</h3>
          <div class="selection-header">
            <div class="selection-info">
              已选择 {{ selectedTargets.length }} 个目标主机
            </div>
            <button @click="showTargetSelector = true" class="btn btn-primary">
              <Plus class="btn-icon" />
              选择目标主机
            </button>
          </div>

          <!-- 已选择的目标列表 -->
          <div class="selected-targets" v-if="selectedTargets.length > 0">
            <div
              v-for="target in selectedTargets"
              :key="target.id"
              class="target-item"
            >
              <div class="target-info">
                <div class="target-name">{{ target.name }}</div>
                <div class="target-details">
                  <span class="target-type">{{ getTargetTypeLabel(target.type) }}</span>
                  <span class="target-ip">{{ target.ip }}</span>
                  <span class="target-status" :class="target.status">
                    {{ getStatusLabel(target.status) }}
                  </span>
                </div>
              </div>
              <button @click="removeTarget(target)" class="btn-remove">
                <X class="icon" />
              </button>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-targets">
            <Terminal class="empty-icon" />
            <p>请选择要执行脚本的目标主机</p>
            <p class="empty-hint">支持选择主机设备、网络设备等可执行目标</p>
          </div>
        </div>

        <!-- 执行选项 -->
        <div class="execution-options">
          <h3>执行选项</h3>
          <div class="options-grid">
            <div class="option-item">
              <label>
                <input
                  v-model="executionOptions.parallel"
                  type="checkbox"
                />
                并行执行
              </label>
            </div>
            <div class="option-item">
              <label>
                <input
                  v-model="executionOptions.continueOnError"
                  type="checkbox"
                />
                出错时继续
              </label>
            </div>
            <div class="option-item">
              <label>超时时间 (秒):</label>
              <input
                v-model.number="executionOptions.timeout"
                type="number"
                min="1"
                max="3600"
                class="form-input-small"
              />
            </div>
          </div>
        </div>

        <!-- 执行按钮 -->
        <div class="execution-actions">
          <button
            @click="executeScript"
            :disabled="!canExecute"
            class="btn btn-execute"
          >
            <Play class="btn-icon" />
            执行脚本
          </button>
          <button @click="clearAll" class="btn btn-secondary">
            <RotateCcw class="btn-icon" />
            清空配置
          </button>
        </div>
      </div>

      <!-- 执行结果区域 -->
      <div class="execution-results" v-if="executionResults.length > 0">
        <h3>执行结果</h3>
        <div class="results-list">
          <div
            v-for="result in executionResults"
            :key="result.targetId"
            class="result-item"
            :class="result.status"
          >
            <div class="result-header">
              <div class="result-target">
                <span class="target-name">{{ result.targetName }}</span>
                <span class="target-ip">{{ result.targetIp }}</span>
              </div>
              <div class="result-status">
                <span class="status-badge" :class="result.status">
                  {{ getExecutionStatusLabel(result.status) }}
                </span>
                <span class="execution-time">{{ result.duration }}ms</span>
              </div>
            </div>
            <div class="result-content">
              <div class="result-output" v-if="result.output">
                <h4>输出:</h4>
                <pre>{{ result.output }}</pre>
              </div>
              <div class="result-error" v-if="result.error">
                <h4>错误:</h4>
                <pre>{{ result.error }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 目标选择器弹窗 -->
    <div v-if="showTargetSelector" class="modal-overlay" @click="closeTargetSelector">
      <div class="modal-content" @click.stop>
        <UnifiedResourceSelector
          title="选择执行目标"
          :multiple="true"
          :resource-types="['host', 'network_device']"
          :filters="{ status: 'online' }"
          :exclude-ids="selectedTargets.map(t => t.id)"
          @select="handleTargetSelection"
          @cancel="closeTargetSelector"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Terminal,
  Plus,
  X,
  Play,
  RotateCcw
} from 'lucide-vue-next'
import UnifiedResourceSelector from '../../components/enterprise/UnifiedResourceSelector.vue'
import { UnifiedResource, HostResource, NetworkDevice } from '../../services/unifiedEnterpriseDataService'

const router = useRouter()

// 脚本配置
const scriptConfig = ref({
  name: '',
  type: 'shell',
  content: ''
})

// 执行选项
const executionOptions = ref({
  parallel: true,
  continueOnError: false,
  timeout: 300
})

// 目标选择
const selectedTargets = ref<(HostResource | NetworkDevice)[]>([])
const showTargetSelector = ref(false)

// 执行结果
const executionResults = ref<Array<{
  targetId: string | number
  targetName: string
  targetIp: string
  status: 'running' | 'success' | 'error' | 'timeout'
  output?: string
  error?: string
  duration: number
}>>([])

// 计算属性
const canExecute = computed(() => {
  return scriptConfig.value.name && 
         scriptConfig.value.content && 
         selectedTargets.value.length > 0
})

// 方法
const goBack = () => {
  router.push('/')
}

const handleTargetSelection = (targets: UnifiedResource[]) => {
  // 过滤出可执行的目标（主机和网络设备）
  const executableTargets = targets.filter(target => 
    target.type === 'host' || target.type === 'network_device'
  ) as (HostResource | NetworkDevice)[]
  
  selectedTargets.value.push(...executableTargets)
  closeTargetSelector()
}

const removeTarget = (target: HostResource | NetworkDevice) => {
  const index = selectedTargets.value.findIndex(t => t.id === target.id)
  if (index > -1) {
    selectedTargets.value.splice(index, 1)
  }
}

const closeTargetSelector = () => {
  showTargetSelector.value = false
}

const executeScript = async () => {
  if (!canExecute.value) return

  // 清空之前的结果
  executionResults.value = []

  // 为每个目标创建执行结果
  const results = selectedTargets.value.map(target => ({
    targetId: target.id,
    targetName: target.name,
    targetIp: target.ip,
    status: 'running' as const,
    duration: 0
  }))

  executionResults.value = results

  // 模拟脚本执行
  for (const result of results) {
    const startTime = Date.now()
    
    try {
      // 模拟执行延迟
      await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000))
      
      // 模拟执行结果
      const success = Math.random() > 0.2 // 80% 成功率
      
      if (success) {
        result.status = 'success'
        result.output = `脚本在 ${result.targetName} (${result.targetIp}) 上执行成功\n输出结果:\n${generateMockOutput()}`
      } else {
        result.status = 'error'
        result.error = `脚本在 ${result.targetName} (${result.targetIp}) 上执行失败\n错误信息: ${generateMockError()}`
      }
      
      result.duration = Date.now() - startTime
      
    } catch (error) {
      result.status = 'error'
      result.error = `执行异常: ${error}`
      result.duration = Date.now() - startTime
    }

    // 如果不是并行执行且出错时不继续，则停止执行
    if (!executionOptions.value.parallel && 
        result.status === 'error' && 
        !executionOptions.value.continueOnError) {
      break
    }
  }
}

const clearAll = () => {
  scriptConfig.value = {
    name: '',
    type: 'shell',
    content: ''
  }
  selectedTargets.value = []
  executionResults.value = []
}

const getTargetTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    host: '主机设备',
    network_device: '网络设备'
  }
  return labels[type] || type
}

const getStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    online: '在线',
    offline: '离线',
    normal: '正常',
    warning: '警告',
    error: '错误'
  }
  return labels[status] || status
}

const getExecutionStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    running: '执行中',
    success: '成功',
    error: '失败',
    timeout: '超时'
  }
  return labels[status] || status
}

const generateMockOutput = (): string => {
  const outputs = [
    'System information collected successfully',
    'Service status: Active (running)',
    'Disk usage: 45% of 100GB',
    'Memory usage: 2.1GB of 8GB',
    'CPU usage: 15%'
  ]
  return outputs[Math.floor(Math.random() * outputs.length)]
}

const generateMockError = (): string => {
  const errors = [
    'Permission denied',
    'Command not found',
    'Connection timeout',
    'Authentication failed',
    'Service unavailable'
  ]
  return errors[Math.floor(Math.random() * errors.length)]
}
</script>

<style scoped>
.script-execution {
  width: 100%;
  height: 100vh;
  background: #f8fafc;
  overflow-y: auto;
}

.page-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #1e293b;
  padding: 20px 30px;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(30, 41, 59, 0.1);
  border: 2px solid rgba(30, 41, 59, 0.2);
  border-radius: 6px;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-back:hover {
  background: rgba(30, 41, 59, 0.15);
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 4px 0 0 0;
  color: #64748b;
  font-size: 14px;
}

.main-content {
  padding: 30px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 1400px;
  margin: 0 auto;
}

.script-config {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.config-section,
.target-selection,
.execution-options {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-section h3,
.target-selection h3,
.execution-options h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #374151;
  font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
}

.form-textarea {
  resize: vertical;
  font-family: 'Courier New', monospace;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.selection-info {
  color: #64748b;
  font-size: 14px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
}

.btn-secondary:hover {
  background: #e2e8f0;
}

.btn-execute {
  background: #10b981;
  color: white;
  border: 1px solid #10b981;
}

.btn-execute:hover:not(:disabled) {
  background: #059669;
}

.btn-execute:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.selected-targets {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.target-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
}

.target-info {
  flex: 1;
}

.target-name {
  font-weight: 500;
  color: #1e293b;
}

.target-details {
  display: flex;
  gap: 10px;
  margin-top: 4px;
  font-size: 12px;
}

.target-type {
  background: #dbeafe;
  color: #1e40af;
  padding: 2px 6px;
  border-radius: 4px;
}

.target-ip {
  color: #64748b;
}

.target-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.target-status.online {
  background: #dcfce7;
  color: #166534;
}

.btn-remove {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.btn-remove:hover {
  background: #fee2e2;
}

.empty-targets {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 15px;
  opacity: 0.5;
}

.empty-hint {
  font-size: 12px;
  margin-top: 5px;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.option-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.form-input-small {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

.execution-actions {
  display: flex;
  gap: 10px;
}

.execution-results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.execution-results h3 {
  margin: 0 0 15px 0;
  color: #1e293b;
  font-size: 16px;
  font-weight: 600;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-item {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.result-target {
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.running {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.success {
  background: #dcfce7;
  color: #166534;
}

.status-badge.error {
  background: #fee2e2;
  color: #dc2626;
}

.execution-time {
  color: #64748b;
  font-size: 12px;
}

.result-content {
  padding: 15px;
}

.result-output,
.result-error {
  margin-bottom: 10px;
}

.result-output h4,
.result-error h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.result-output h4 {
  color: #059669;
}

.result-error h4 {
  color: #dc2626;
}

.result-content pre {
  background: #f1f5f9;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: pre-wrap;
  margin: 0;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
}
</style>
