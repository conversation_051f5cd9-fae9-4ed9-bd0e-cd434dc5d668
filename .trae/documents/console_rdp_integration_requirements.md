## 1. Product Overview

本项目为Chaterm终端管理工具添加Console连接（网络交换机）和RDP连接（Windows远程桌面）功能，扩展现有的个人资产和企业资产管理能力。

- 主要目的：为用户提供统一的网络设备管理和Windows远程桌面连接能力，解决多种连接方式分散管理的问题
- 目标用户：需要管理网络设备和Windows服务器的IT运维人员、系统管理员和企业用户
- 产品价值：通过集成多种连接方式，提升运维效率，降低工具切换成本，实现统一的资产管理体验

## 2. Core Features

### 2.1 User Roles

| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 个人用户 | 现有用户系统 | 可管理个人Console/RDP资产，建立连接，查看连接状态 |
| 企业用户 | 现有企业用户系统 | 可管理企业共享Console/RDP资产，具备权限控制和审计功能 |
| 管理员 | 现有管理员权限 | 可配置连接参数，管理全局设置，查看所有连接日志 |

### 2.2 Feature Module

我们的Console和RDP集成需求包含以下主要页面：

1. **个人资产管理页面**：资产类型扩展、Console连接配置、RDP连接配置、连接状态监控
2. **企业资产管理页面**：企业Console/RDP资产管理、权限控制、批量操作、同步功能
3. **连接管理页面**：活动连接列表、连接状态监控、会话管理、日志查看
4. **配置管理页面**：连接参数配置、安全设置、性能优化、功能开关

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 个人资产管理页面 | 资产类型扩展 | 在现有资产类型基础上添加Console和RDP选项，支持串口、Telnet、SSH Console连接和标准RDP连接 |
| 个人资产管理页面 | Console连接配置 | 配置串口参数（波特率、数据位）、Telnet参数（端口、超时）、认证信息，支持连接测试 |
| 个人资产管理页面 | RDP连接配置 | 配置RDP服务器地址、端口、用户凭据、显示设置、安全选项，支持连接预览 |
| 企业资产管理页面 | 企业资产扩展 | 扩展企业资产类型支持Console和RDP，继承现有的组织架构和权限体系 |
| 企业资产管理页面 | 批量管理 | 支持批量添加、编辑、删除Console/RDP资产，批量连接测试，导入导出功能 |
| 企业资产管理页面 | 权限控制 | 基于组织架构的资产访问权限，连接权限审批，操作日志记录 |
| 连接管理页面 | 活动连接监控 | 显示当前所有活动的Console和RDP连接，实时状态更新，连接质量监控 |
| 连接管理页面 | 会话管理 | 连接会话的创建、暂停、恢复、终止，会话共享，多窗口管理 |
| 连接管理页面 | 日志查看 | 连接历史记录，操作日志，错误日志，性能统计，审计报告 |
| 配置管理页面 | 全局设置 | Console和RDP功能开关，默认连接参数，超时设置，重连策略 |
| 配置管理页面 | 安全配置 | 连接加密设置，认证方式配置，访问控制策略，安全审计 |

## 3. Core Process

### 个人用户流程

1. 用户在个人资产管理页面选择添加新资产
2. 选择资产类型为Console或RDP
3. 填写连接配置信息（地址、端口、认证等）
4. 测试连接并保存配置
5. 在资产列表中双击或右键选择连接
6. 系统建立连接并打开相应的控制界面
7. 用户进行设备操作或远程桌面控制
8. 连接结束后查看连接日志和统计信息

### 企业用户流程

1. 管理员在企业资产管理页面批量导入Console/RDP资产
2. 配置资产的组织归属和访问权限
3. 普通企业用户登录后查看有权限的资产列表
4. 用户申请连接权限（如需要）
5. 管理员审批连接请求
6. 用户建立连接并进行操作
7. 系统记录所有操作日志供审计

```mermaid
graph TD
    A[个人/企业资产管理页面] --> B[选择资产类型]
    B --> C[Console配置]
    B --> D[RDP配置]
    C --> E[连接测试]
    D --> E[连接测试]
    E --> F[保存资产]
    F --> G[资产列表]
    G --> H[建立连接]
    H --> I[Console控制台]
    H --> J[RDP远程桌面]
    I --> K[连接管理]
    J --> K[连接管理]
    K --> L[日志审计]
```

## 4. User Interface Design

### 4.1 Design Style

- 主色调：沿用Chaterm现有的深色主题，主色为#1890ff，辅助色为#52c41a
- 按钮样式：圆角按钮，支持悬停效果和点击反馈
- 字体：系统默认字体，标题14px，正文12px，代码字体使用等宽字体
- 布局风格：卡片式布局，左侧导航，顶部工具栏，响应式设计
- 图标风格：使用Ant Design图标库，线性风格，支持Console和RDP专用图标

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 个人资产管理页面 | 资产类型选择 | 单选按钮组，包含Server、Database、Network、Console、RDP选项，图标+文字形式 |
| 个人资产管理页面 | Console配置表单 | 分组表单，包含连接类型（串口/Telnet/SSH）、地址端口、串口参数、认证信息等字段 |
| 个人资产管理页面 | RDP配置表单 | 分组表单，包含服务器地址、端口、用户名密码、显示设置、安全选项等字段 |
| 企业资产管理页面 | 资产列表 | 表格形式，支持筛选排序，显示资产名称、类型、状态、最后连接时间等信息 |
| 企业资产管理页面 | 批量操作工具栏 | 顶部工具栏，包含批量添加、导入、导出、删除按钮，支持选择计数显示 |
| 连接管理页面 | 活动连接卡片 | 卡片式布局，显示连接类型图标、目标地址、连接时长、状态指示灯 |
| 连接管理页面 | 连接控制按钮 | 每个连接卡片包含暂停、恢复、断开、详情按钮，支持快捷键操作 |
| 配置管理页面 | 设置面板 | 分组的设置面板，使用开关、滑块、输入框等控件，实时保存 |

### 4.3 Responsiveness

产品采用桌面优先设计，支持1920x1080及以上分辨率的最佳体验。在较小屏幕上采用响应式布局，侧边栏可折叠，表格支持横向滚动。考虑触摸交互优化，按钮和点击区域不小于44px。